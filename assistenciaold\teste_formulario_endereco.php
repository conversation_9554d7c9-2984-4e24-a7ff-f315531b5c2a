<?php
/**
 * Teste do formulário de endereço
 */

session_start();

// Verificar se está logado
if (!isset($_SESSION['usuario_id']) || $_SESSION['tipo_usuario'] !== 'assistencia') {
    echo "<h2>❌ Erro de Autenticação</h2>";
    echo "<p>Você precisa estar logado como assistência.</p>";
    echo "<p><a href='../login.php'>Fazer <PERSON>gin</a></p>";
    exit();
}

require_once 'config/auth.php';
require_once 'config/database.php';

// Obter dados do usuário
$auth = getAuth();
$usuario = $auth->getUsuarioLogado();

echo "<h2>🧪 Teste do Formulário de Endereço</h2>";

if (!$usuario) {
    echo "<p style='color: red;'>❌ Erro: Não foi possível carregar os dados do usuário.</p>";
    exit();
}

echo "<h3>📋 Dados Carregados:</h3>";
echo "<ul>";
echo "<li><strong>Nome:</strong> " . htmlspecialchars($usuario['nome'] ?? 'Não informado') . "</li>";
echo "<li><strong>Empresa:</strong> " . htmlspecialchars($usuario['nome_empresa'] ?? 'Não informado') . "</li>";
echo "<li><strong>CEP:</strong> " . htmlspecialchars($usuario['cep'] ?? 'Não informado') . "</li>";
echo "<li><strong>Cidade:</strong> " . htmlspecialchars($usuario['cidade'] ?? 'Não informado') . "</li>";
echo "<li><strong>Estado:</strong> " . htmlspecialchars($usuario['estado'] ?? 'Não informado') . "</li>";
echo "</ul>";

?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste - Formulário de Endereço</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h3>📝 Formulário de Teste</h3>
        
        <form method="POST" action="perfil.php">
            <input type="hidden" name="acao" value="dados_empresa">
            
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="nome_empresa" class="form-label">Nome da Empresa</label>
                    <input type="text" class="form-control" id="nome_empresa" name="nome_empresa" 
                           value="<?php echo htmlspecialchars($usuario['nome_empresa'] ?? ''); ?>" required>
                </div>
                
                <div class="col-md-6 mb-3">
                    <label for="telefone_empresa" class="form-label">Telefone da Empresa</label>
                    <input type="tel" class="form-control" id="telefone_empresa" name="telefone_empresa" 
                           value="<?php echo htmlspecialchars($usuario['telefone_empresa'] ?? ''); ?>">
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-3 mb-3">
                    <label for="cep" class="form-label">CEP</label>
                    <input type="text" class="form-control" id="cep" name="cep" 
                           value="<?php echo htmlspecialchars($usuario['cep'] ?? ''); ?>"
                           placeholder="00000-000" maxlength="9" required>
                </div>
                
                <div class="col-md-2 mb-3">
                    <label for="numero_endereco" class="form-label">Número</label>
                    <input type="text" class="form-control" id="numero_endereco" name="numero_endereco" 
                           value="<?php echo htmlspecialchars($usuario['numero_endereco'] ?? ''); ?>"
                           placeholder="123" required>
                </div>
                
                <div class="col-md-7 mb-3">
                    <label for="endereco" class="form-label">Logradouro</label>
                    <input type="text" class="form-control" id="endereco" name="endereco" 
                           value="<?php echo htmlspecialchars($usuario['endereco'] ?? ''); ?>"
                           placeholder="Será preenchido automaticamente" readonly required>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-4 mb-3">
                    <label for="bairro" class="form-label">Bairro</label>
                    <input type="text" class="form-control" id="bairro" name="bairro" 
                           value="<?php echo htmlspecialchars($usuario['bairro'] ?? ''); ?>"
                           placeholder="Será preenchido automaticamente" readonly required>
                </div>
                
                <div class="col-md-6 mb-3">
                    <label for="cidade" class="form-label">Cidade</label>
                    <input type="text" class="form-control" id="cidade" name="cidade" 
                           value="<?php echo htmlspecialchars($usuario['cidade'] ?? ''); ?>"
                           placeholder="Será preenchido automaticamente" readonly required>
                </div>
                
                <div class="col-md-2 mb-3">
                    <label for="estado" class="form-label">Estado</label>
                    <input type="text" class="form-control" id="estado" name="estado" 
                           value="<?php echo htmlspecialchars($usuario['estado'] ?? ''); ?>"
                           placeholder="UF" readonly required>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="email_empresa" class="form-label">Email da Empresa</label>
                    <input type="email" class="form-control" id="email_empresa" name="email_empresa" 
                           value="<?php echo htmlspecialchars($usuario['email_empresa'] ?? ''); ?>">
                </div>
                
                <div class="col-md-6 mb-3">
                    <label for="ponto_referencia" class="form-label">Ponto de Referência</label>
                    <input type="text" class="form-control" id="ponto_referencia" name="ponto_referencia" 
                           value="<?php echo htmlspecialchars($usuario['ponto_referencia'] ?? ''); ?>"
                           placeholder="Ex: Próximo ao mercado">
                </div>
            </div>
            
            <div class="mb-3">
                <label for="site" class="form-label">Site</label>
                <input type="url" class="form-control" id="site" name="site" 
                       value="<?php echo htmlspecialchars($usuario['site'] ?? ''); ?>"
                       placeholder="https://www.exemplo.com">
            </div>
            
            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                <button type="submit" class="btn btn-primary">💾 Salvar Dados da Empresa</button>
            </div>
        </form>
        
        <hr class="my-4">
        
        <h3>🔍 Debug dos Valores</h3>
        <pre><?php print_r($usuario); ?></pre>
        
        <p class="mt-3">
            <a href="perfil.php" class="btn btn-secondary">← Voltar ao Perfil</a>
            <a href="debug_consulta_usuario.php" class="btn btn-info">🔍 Debug Consulta</a>
        </p>
    </div>

    <script>
        // Busca automática de CEP
        document.getElementById('cep').addEventListener('blur', function() {
            const cep = this.value.replace(/\D/g, '');
            
            if (cep.length === 8) {
                console.log('Buscando CEP:', cep);
                
                // Buscar endereço
                fetch(`https://viacep.com.br/ws/${cep}/json/`)
                    .then(response => response.json())
                    .then(data => {
                        console.log('Dados do CEP:', data);
                        
                        if (data.erro) {
                            alert('CEP não encontrado');
                        } else {
                            document.getElementById('endereco').value = data.logradouro || '';
                            document.getElementById('bairro').value = data.bairro || '';
                            document.getElementById('cidade').value = data.localidade || '';
                            document.getElementById('estado').value = data.uf || '';
                        }
                    })
                    .catch(error => {
                        console.error('Erro ao buscar CEP:', error);
                        alert('Erro ao buscar CEP. Verifique sua conexão.');
                    });
            }
        });

        // Máscara para CEP
        document.getElementById('cep').addEventListener('input', function() {
            let valor = this.value.replace(/\D/g, '');
            valor = valor.replace(/(\d{5})(\d)/, '$1-$2');
            this.value = valor;
        });
    </script>
</body>
</html>
