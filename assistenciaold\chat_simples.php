<?php
/**
 * Página de Chat Simplificada
 * FixFácil Assistências - Sistema Novo
 */

// Ativar exibição de erros
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>💬 Chat Simplificado</h1>";
echo "<style>body { font-family: Arial, sans-serif; margin: 20px; }</style>";

try {
    // Incluir arquivos necessários
    require_once 'config/auth.php';
    require_once 'config/database.php';
    
    echo "<p>✅ Arquivos incluídos com sucesso</p>";
    
    // Verificar autenticação
    $auth = getAuth();
    echo "<p>✅ Auth inicializado</p>";
    
    // Verificar se está logado
    if (!isset($_SESSION['usuario_id']) || $_SESSION['tipo_usuario'] !== 'assistencia') {
        echo "<p>❌ Usuário não está logado como assistência</p>";
        echo "<p><a href='../login.php'><PERSON>azer <PERSON></a></p>";
        exit();
    }
    
    echo "<p>✅ Usuário autenticado</p>";
    
    // Obter dados do usuário
    $usuario = $auth->getUsuarioLogado();
    if (!$usuario) {
        echo "<p>❌ Erro ao obter dados do usuário</p>";
        exit();
    }
    
    echo "<p>✅ Dados do usuário obtidos: " . htmlspecialchars($usuario['nome']) . "</p>";
    
    // Verificar acesso ao chat
    $temAcessoChat = $auth->hasAccess('chat');
    if (!$temAcessoChat) {
        echo "<p>❌ Sem acesso ao chat</p>";
        echo "<p>Seu plano atual não inclui acesso ao chat</p>";
        echo "<p><a href='upgrade_plano.php?feature=chat'>Fazer Upgrade</a></p>";
        exit();
    }
    
    echo "<p>✅ Acesso ao chat confirmado</p>";
    
    // Obter banco de dados
    $db = getDatabase();
    echo "<p>✅ Conexão com banco estabelecida</p>";
    
    // Obter conversas (versão simplificada)
    $conversas = [];
    if ($usuario['assistencia_id']) {
        try {
            $sql = "
                SELECT
                    pa.id as proposta_id,
                    pa.status,
                    sr.dispositivo,
                    sr.marca,
                    sr.modelo,
                    u.nome as cliente_nome,
                    u.telefone as cliente_telefone
                FROM propostas_assistencia pa
                JOIN solicitacoes_reparo sr ON pa.solicitacao_id = sr.id
                JOIN usuarios u ON sr.usuario_id = u.id
                WHERE pa.assistencia_id = ?
                AND pa.status IN ('aceita', 'Em Andamento', 'Concluída')
                ORDER BY pa.data_proposta DESC
                LIMIT 10
            ";
            
            $result = $db->query($sql, [$usuario['assistencia_id']]);
            
            while ($row = $result->fetch_assoc()) {
                $conversas[] = $row;
            }
            
            echo "<p>✅ Encontradas " . count($conversas) . " conversas</p>";
            
        } catch (Exception $e) {
            echo "<p>❌ Erro ao obter conversas: " . $e->getMessage() . "</p>";
        }
    }
    
} catch (Exception $e) {
    echo "<p>❌ Erro geral: " . $e->getMessage() . "</p>";
    echo "<p>Stack trace:</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
    exit();
}

?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chat - FixFácil Assistências</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-light">

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i class="fas fa-comments me-2"></i>
                        Chat com Clientes
                    </h4>
                    <small class="text-muted">Versão simplificada para teste</small>
                </div>
                <div class="card-body">
                    
                    <div class="alert alert-success">
                        <h5>✅ Sistema Funcionando!</h5>
                        <p class="mb-0">O chat foi carregado com sucesso. Todos os componentes estão funcionais.</p>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <h5>Informações do Usuário:</h5>
                            <ul class="list-group">
                                <li class="list-group-item">
                                    <strong>Nome:</strong> <?php echo htmlspecialchars($usuario['nome']); ?>
                                </li>
                                <li class="list-group-item">
                                    <strong>Email:</strong> <?php echo htmlspecialchars($usuario['email']); ?>
                                </li>
                                <li class="list-group-item">
                                    <strong>Empresa:</strong> <?php echo htmlspecialchars($usuario['nome_empresa'] ?? 'Não definido'); ?>
                                </li>
                                <li class="list-group-item">
                                    <strong>Assistência ID:</strong> <?php echo $usuario['assistencia_id'] ?? 'Não definido'; ?>
                                </li>
                            </ul>
                        </div>
                        
                        <div class="col-md-8">
                            <h5>Conversas Disponíveis:</h5>
                            
                            <?php if (empty($conversas)): ?>
                            <div class="alert alert-info">
                                <h6>Nenhuma conversa ativa</h6>
                                <p class="mb-0">As conversas aparecerão aqui quando houver propostas aceitas pelos clientes.</p>
                            </div>
                            <?php else: ?>
                            <div class="list-group">
                                <?php foreach ($conversas as $conversa): ?>
                                <div class="list-group-item">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div>
                                            <h6 class="mb-1"><?php echo htmlspecialchars($conversa['cliente_nome']); ?></h6>
                                            <p class="mb-1">
                                                <?php echo htmlspecialchars($conversa['marca'] . ' ' . $conversa['modelo']); ?>
                                            </p>
                                            <small class="text-muted">Status: <?php echo $conversa['status']; ?></small>
                                        </div>
                                        <div>
                                            <a href="tel:<?php echo htmlspecialchars($conversa['cliente_telefone']); ?>" 
                                               class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-phone"></i>
                                            </a>
                                            <button class="btn btn-sm btn-primary" 
                                                    onclick="abrirChat(<?php echo $conversa['proposta_id']; ?>)">
                                                <i class="fas fa-comments"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <div class="row mt-4">
                        <div class="col-12">
                            <h5>Links de Navegação:</h5>
                            <div class="btn-group" role="group">
                                <a href="dashboard.php" class="btn btn-outline-primary">
                                    <i class="fas fa-home me-1"></i> Dashboard
                                </a>
                                <a href="chat.php" class="btn btn-primary">
                                    <i class="fas fa-comments me-1"></i> Chat Completo
                                </a>
                                <a href="teste_chat.php" class="btn btn-outline-info">
                                    <i class="fas fa-bug me-1"></i> Diagnóstico
                                </a>
                                <a href="solicitacoes.php" class="btn btn-outline-secondary">
                                    <i class="fas fa-inbox me-1"></i> Solicitações
                                </a>
                            </div>
                        </div>
                    </div>
                    
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
<script>
function abrirChat(propostaId) {
    // Redirecionar para o chat completo com a proposta selecionada
    window.location.href = 'chat.php?proposta_id=' + propostaId;
}

// Mostrar que JavaScript está funcionando
console.log('Chat simplificado carregado com sucesso!');
</script>

</body>
</html>
