<?php
/**
 * Dashboard Principal - Mobile First
 * FixFácil Assistências - Design Moderno
 */

// Configurar relatório de erros para debug
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Iniciar sessão se não estiver iniciada
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Verificar se o usuário está logado
if (!isset($_SESSION['usuario_id']) || $_SESSION['tipo_usuario'] !== 'assistencia') {
    header('Location: ../login.php');
    exit();
}

// Configuração de banco de dados
$host = "localhost";
$username = "u680766645_fixfacilnew";
$password = "T3cn0l0g1a@";
$database = "u680766645_fixfacilnew";

$mysqli = new mysqli($host, $username, $password, $database);

if ($mysqli->connect_error) {
    die("Erro de conexão: " . $mysqli->connect_error);
}

// Obter dados do usuário logado
$usuario_id = $_SESSION['usuario_id'];
$usuario = null;
$plano = null;

try {
    // Buscar dados do usuário
    $sql = "SELECT u.id, u.nome, u.email, u.telefone, u.plano_id, at.id as assistencia_id 
            FROM usuarios u 
            LEFT JOIN assistencias_tecnicas at ON u.id = at.usuario_id 
            WHERE u.id = ?";
    $stmt = $mysqli->prepare($sql);
    $stmt->bind_param("i", $usuario_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $usuario = $result->fetch_assoc();
    
    // Buscar informações do plano
    if ($usuario && $usuario['plano_id']) {
        $sql = "SELECT * FROM planos WHERE id = ?";
        $stmt = $mysqli->prepare($sql);
        $stmt->bind_param("i", $usuario['plano_id']);
        $stmt->execute();
        $result = $stmt->get_result();
        $plano = $result->fetch_assoc();
    }
    
    // Plano padrão se não encontrar
    if (!$plano) {
        $plano = [
            'nome' => 'Free',
            'taxa_servico' => 25.00
        ];
    }
    
} catch (Exception $e) {
    error_log("Erro ao buscar dados do usuário: " . $e->getMessage());
    $usuario = ['nome' => 'Usuário', 'email' => '', 'assistencia_id' => null];
    $plano = ['nome' => 'Free', 'taxa_servico' => 25.00];
}

// Obter estatísticas
$stats = [
    'total_solicitacoes' => 0,
    'aguardando_resposta' => 0,
    'em_andamento' => 0,
    'concluidas' => 0,
    'receita_mes' => 0,
    'propostas_enviadas' => 0,
    'taxa_aprovacao' => 0,
    'avaliacao_media' => 4.8,
    'tempo_medio_reparo' => 18
];

try {
    if ($usuario && isset($usuario['assistencia_id'])) {
        $assistencia_id = $usuario['assistencia_id'];
        
        // Total de solicitações
        $sql = "SELECT COUNT(*) as total FROM solicitacoes_reparo WHERE assistencia_id = ?";
        $stmt = $mysqli->prepare($sql);
        $stmt->bind_param("i", $assistencia_id);
        $stmt->execute();
        $result = $stmt->get_result();
        $stats['total_solicitacoes'] = $result->fetch_assoc()['total'];
        
        // Aguardando resposta
        $sql = "SELECT COUNT(*) as total FROM solicitacoes_reparo WHERE assistencia_id = ? AND status = 'aguardando_resposta'";
        $stmt = $mysqli->prepare($sql);
        $stmt->bind_param("i", $assistencia_id);
        $stmt->execute();
        $result = $stmt->get_result();
        $stats['aguardando_resposta'] = $result->fetch_assoc()['total'];
        
        // Em andamento
        $sql = "SELECT COUNT(*) as total FROM solicitacoes_reparo WHERE assistencia_id = ? AND status = 'em_andamento'";
        $stmt = $mysqli->prepare($sql);
        $stmt->bind_param("i", $assistencia_id);
        $stmt->execute();
        $result = $stmt->get_result();
        $stats['em_andamento'] = $result->fetch_assoc()['total'];
        
        // Concluídas
        $sql = "SELECT COUNT(*) as total FROM solicitacoes_reparo WHERE assistencia_id = ? AND status = 'concluido'";
        $stmt = $mysqli->prepare($sql);
        $stmt->bind_param("i", $assistencia_id);
        $stmt->execute();
        $result = $stmt->get_result();
        $stats['concluidas'] = $result->fetch_assoc()['total'];
        
        // Receita do mês
        $sql = "SELECT SUM(valor_proposta) as receita FROM solicitacoes_reparo 
                WHERE assistencia_id = ? AND status = 'concluido' 
                AND MONTH(data_conclusao) = MONTH(NOW()) 
                AND YEAR(data_conclusao) = YEAR(NOW())";
        $stmt = $mysqli->prepare($sql);
        $stmt->bind_param("i", $assistencia_id);
        $stmt->execute();
        $result = $stmt->get_result();
        $row = $result->fetch_assoc();
        $stats['receita_mes'] = $row['receita'] ?? 0;
        
        // Taxa de aprovação
        $sql = "SELECT 
                    COUNT(*) as total,
                    SUM(CASE WHEN status != 'rejeitado' THEN 1 ELSE 0 END) as aprovadas
                FROM solicitacoes_reparo WHERE assistencia_id = ?";
        $stmt = $mysqli->prepare($sql);
        $stmt->bind_param("i", $assistencia_id);
        $stmt->execute();
        $result = $stmt->get_result();
        $row = $result->fetch_assoc();
        if ($row['total'] > 0) {
            $stats['taxa_aprovacao'] = round(($row['aprovadas'] / $row['total']) * 100);
        }
    }
} catch (Exception $e) {
    error_log("Erro ao calcular estatísticas: " . $e->getMessage());
}

function getStatusLabel($status) {
    switch ($status) {
        case 'aguardando_resposta':
            return 'Aguardando Resposta';
        case 'em_andamento':
            return 'Em Andamento';
        case 'concluido':
            return 'Concluído';
        case 'rejeitado':
            return 'Rejeitado';
        default:
            return 'Desconhecido';
    }
}
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FixFacil - Assistência</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            min-height: 100vh;
            color: #1e293b;
        }

        .container {
            max-width: 414px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
            position: relative;
            overflow-x: hidden;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }

        .header {
            background: linear-gradient(135deg, #059669 0%, #065f46 100%);
            color: white;
            padding: 24px 20px 20px 20px;
            position: relative;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 20"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="0.5" fill="white" opacity="0.05"/></pattern></defs><rect width="100" height="20" fill="url(%23grain)"/></svg>');
        }

        .header-content {
            position: relative;
            z-index: 1;
        }

        .header-top {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 20px;
        }

        .company-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .company-logo {
            width: 48px;
            height: 48px;
            background: rgba(255,255,255,0.2);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            font-weight: 700;
            backdrop-filter: blur(10px);
        }

        .company-details h1 {
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 2px;
        }

        .company-status {
            font-size: 12px;
            opacity: 0.9;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .status-indicator {
            width: 8px;
            height: 8px;
            background: #10b981;
            border-radius: 50%;
        }

        .header-actions {
            display: flex;
            gap: 8px;
        }

        .action-btn {
            width: 40px;
            height: 40px;
            background: rgba(255,255,255,0.15);
            border: none;
            border-radius: 12px;
            color: white;
            font-size: 18px;
            cursor: pointer;
            backdrop-filter: blur(10px);
            transition: all 0.2s ease;
            position: relative;
            text-decoration: none;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .action-btn:hover {
            background: rgba(255,255,255,0.25);
            color: white;
            text-decoration: none;
        }

        .notification-badge {
            position: absolute;
            top: -2px;
            right: -2px;
            background: #ef4444;
            color: white;
            border-radius: 50%;
            width: 16px;
            height: 16px;
            font-size: 10px;
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 12px;
        }

        .stat-card {
            background: rgba(255,255,255,0.15);
            border-radius: 12px;
            padding: 16px 12px;
            text-align: center;
            backdrop-filter: blur(10px);
        }

        .stat-number {
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 4px;
        }

        .stat-label {
            font-size: 11px;
            opacity: 0.8;
        }

        .content {
            padding: 20px;
            padding-bottom: 100px;
        }

        .quick-actions {
            background: white;
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
        }

        .section-title {
            font-size: 18px;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .actions-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
        }

        .quick-action {
            background: #f8fafc;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            padding: 16px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            text-decoration: none;
            color: inherit;
        }

        .quick-action:hover {
            border-color: #059669;
            background: #f0fdf4;
            transform: translateY(-2px);
            color: inherit;
            text-decoration: none;
        }

        .quick-action-icon {
            font-size: 28px;
            margin-bottom: 8px;
        }

        .quick-action-title {
            font-size: 14px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 2px;
        }

        .quick-action-subtitle {
            font-size: 11px;
            color: #64748b;
        }

        .action-badge {
            position: absolute;
            top: 8px;
            right: 8px;
            background: #ef4444;
            color: white;
            border-radius: 10px;
            padding: 2px 6px;
            font-size: 10px;
            font-weight: 600;
        }

        .action-badge.green {
            background: #10b981;
        }

        .action-badge.blue {
            background: #3b82f6;
        }

        .action-badge.new {
            background: #8b5cf6;
        }

        .pending-requests {
            background: white;
            border-radius: 16px;
            margin-bottom: 20px;
            overflow: hidden;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
        }

        .section-header {
            padding: 20px 20px 16px 20px;
            border-bottom: 1px solid #f1f5f9;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .view-all {
            font-size: 14px;
            color: #059669;
            font-weight: 500;
            cursor: pointer;
            text-decoration: none;
        }

        .view-all:hover {
            color: #059669;
            text-decoration: underline;
        }

        .request-item {
            padding: 16px 20px;
            border-bottom: 1px solid #f1f5f9;
            display: flex;
            gap: 12px;
            cursor: pointer;
            transition: background 0.2s ease;
            text-decoration: none;
            color: inherit;
        }

        .request-item:last-child {
            border-bottom: none;
        }

        .request-item:hover {
            background: #f8fafc;
            color: inherit;
            text-decoration: none;
        }

        .request-icon {
            width: 48px;
            height: 48px;
            background: #f0fdf4;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            color: #059669;
            flex-shrink: 0;
        }

        .request-icon.urgent {
            background: #fef2f2;
            color: #ef4444;
        }

        .request-icon.premium {
            background: #fef3c7;
            color: #f59e0b;
        }

        .request-info {
            flex: 1;
        }

        .request-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 4px;
        }

        .request-title {
            font-size: 16px;
            font-weight: 600;
            color: #1e293b;
        }

        .request-time {
            font-size: 12px;
            color: #64748b;
        }

        .request-description {
            font-size: 14px;
            color: #64748b;
            margin-bottom: 8px;
            line-height: 1.3;
        }

        .request-tags {
            display: flex;
            gap: 6px;
            align-items: center;
        }

        .request-tag {
            background: #f1f5f9;
            color: #64748b;
            border-radius: 6px;
            padding: 2px 6px;
            font-size: 10px;
            font-weight: 500;
        }

        .request-tag.device {
            background: #eff6ff;
            color: #3b82f6;
        }

        .request-tag.location {
            background: #ecfdf5;
            color: #059669;
        }

        .request-price {
            background: #059669;
            color: white;
            border-radius: 6px;
            padding: 2px 6px;
            font-size: 10px;
            font-weight: 600;
        }

        .today-schedule {
            background: white;
            border-radius: 16px;
            margin-bottom: 20px;
            overflow: hidden;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
        }

        .schedule-item {
            padding: 16px 20px;
            border-bottom: 1px solid #f1f5f9;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .schedule-item:last-child {
            border-bottom: none;
        }

        .schedule-time {
            background: #f0fdf4;
            color: #059669;
            border-radius: 8px;
            padding: 8px 12px;
            font-size: 12px;
            font-weight: 600;
            min-width: 60px;
            text-align: center;
        }

        .schedule-time.current {
            background: #3b82f6;
            color: white;
        }

        .schedule-time.past {
            background: #f1f5f9;
            color: #94a3b8;
        }

        .schedule-info {
            flex: 1;
        }

        .schedule-title {
            font-size: 14px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 2px;
        }

        .schedule-subtitle {
            font-size: 12px;
            color: #64748b;
        }

        .schedule-status {
            font-size: 12px;
            font-weight: 600;
            color: #059669;
        }

        .schedule-status.pending {
            color: #f59e0b;
        }

        .schedule-status.completed {
            color: #10b981;
        }

        .performance-card {
            background: white;
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
        }

        .performance-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
            margin-bottom: 16px;
        }

        .performance-item {
            text-align: center;
            background: #f8fafc;
            border-radius: 12px;
            padding: 16px;
        }

        .performance-number {
            font-size: 24px;
            font-weight: 700;
            color: #059669;
            margin-bottom: 4px;
        }

        .performance-label {
            font-size: 12px;
            color: #64748b;
            margin-bottom: 2px;
        }

        .performance-change {
            font-size: 10px;
            font-weight: 600;
            color: #10b981;
        }

        .performance-change.negative {
            color: #ef4444;
        }

        .earnings-summary {
            background: linear-gradient(135deg, #f0fdf4, #dcfce7);
            border-radius: 12px;
            padding: 16px;
            text-align: center;
        }

        .earnings-amount {
            font-size: 28px;
            font-weight: 700;
            color: #059669;
            margin-bottom: 4px;
        }

        .earnings-label {
            font-size: 12px;
            color: #065f46;
            margin-bottom: 8px;
        }

        .earnings-period {
            font-size: 10px;
            color: #059669;
        }

        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 100%;
            max-width: 414px;
            background: white;
            border-top: 1px solid #e2e8f0;
            padding: 12px 20px;
            display: flex;
            justify-content: space-around;
            box-shadow: 0 -4px 20px rgba(0,0,0,0.1);
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
            cursor: pointer;
            padding: 8px;
            border-radius: 12px;
            transition: all 0.2s ease;
            position: relative;
            text-decoration: none;
            color: inherit;
        }

        .nav-item.active {
            background: #f0fdf4;
            color: #059669;
        }

        .nav-item:hover {
            color: #059669;
            text-decoration: none;
        }

        .nav-icon {
            font-size: 20px;
        }

        .nav-label {
            font-size: 10px;
            font-weight: 500;
        }

        .nav-badge {
            position: absolute;
            top: 4px;
            right: 4px;
            background: #ef4444;
            color: white;
            border-radius: 50%;
            width: 16px;
            height: 16px;
            font-size: 10px;
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .floating-action {
            position: fixed;
            bottom: 80px;
            right: 20px;
            width: 56px;
            height: 56px;
            background: linear-gradient(135deg, #059669, #065f46);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            cursor: pointer;
            box-shadow: 0 4px 16px rgba(5, 150, 105, 0.4);
            transition: all 0.3s ease;
            z-index: 100;
            text-decoration: none;
        }

        .floating-action:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 20px rgba(5, 150, 105, 0.5);
            color: white;
            text-decoration: none;
        }

        .plano-badge {
            background: linear-gradient(135deg, #059669, #065f46);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
            display: inline-flex;
            align-items: center;
            gap: 4px;
        }

        .plano-badge.plano-master {
            background: linear-gradient(135deg, #f59e0b, #d97706);
        }

        .plano-badge.plano-premium {
            background: linear-gradient(135deg, #8b5cf6, #7c3aed);
        }

        .notification {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: #059669;
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            z-index: 2000;
            box-shadow: 0 4px 12px rgba(5, 150, 105, 0.3);
            max-width: 90%;
            text-align: center;
            animation: slideDown 0.3s ease-out;
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateX(-50%) translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateX(-50%) translateY(0);
            }
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .content > * {
            animation: fadeIn 0.6s ease;
        }

        @media (max-width: 480px) {
            .container {
                max-width: 100vw;
                box-shadow: none;
            }
            
            .header {
                padding: 20px 16px 16px 16px;
            }
            
            .content {
                padding: 16px;
                padding-bottom: 100px;
            }
            
            .stat-card {
                padding: 12px 8px;
            }
            
            .stat-number {
                font-size: 18px;
            }
            
            .stat-label {
                font-size: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="header-content">
                <div class="header-top">
                    <div class="company-info">
                        <div class="company-logo">FF</div>
                        <div class="company-details">
                            <h1>FixFácil Express</h1>
                            <div class="company-status">
                                <div class="status-indicator"></div>
                                <span>Online • 
                                    <span class="plano-badge plano-<?php echo strtolower($plano['nome']); ?>">
                                        <?php echo htmlspecialchars($plano['nome']); ?>
                                    </span>
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="header-actions">
                        <a href="../home.php" class="action-btn" title="Área do cliente">
                            👤
                        </a>
                        <a href="logout.php" class="action-btn" title="Sair">
                            🚪
                        </a>
                        <button class="action-btn" onclick="showNotification('🔔 Notificações: <?php echo $stats['aguardando_resposta']; ?> solicitações pendentes')">
                            🔔
                            <?php if ($stats['aguardando_resposta'] > 0): ?>
                                <div class="notification-badge"><?php echo $stats['aguardando_resposta']; ?></div>
                            <?php endif; ?>
                        </button>
                        <a href="perfil.php" class="action-btn" title="Perfil">⚙️</a>
                    </div>
                </div>

                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number"><?php echo $stats['aguardando_resposta']; ?></div>
                        <div class="stat-label">Pendentes</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">R$ <?php echo number_format($stats['receita_mes'], 0, ',', '.'); ?></div>
                        <div class="stat-label">Este mês</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">
                            <?php echo number_format($stats['avaliacao_media'], 1); ?>⭐
                        </div>
                        <div class="stat-label">Avaliação</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Content -->
        <div class="content">
            <!-- Quick Actions -->
            <div class="quick-actions">
                <div class="section-title">⚡ Ações rápidas</div>
                <div class="actions-grid">
                    <a href="solicitacoes.php" class="quick-action">
                        <?php if ($stats['aguardando_resposta'] > 0): ?>
                            <div class="action-badge"><?php echo $stats['aguardando_resposta']; ?></div>
                        <?php endif; ?>
                        <div class="quick-action-icon">📋</div>
                        <div class="quick-action-title">Solicitações</div>
                        <div class="quick-action-subtitle">Novas propostas</div>
                    </a>
                    <a href="reparos.php" class="quick-action">
                        <?php if ($stats['em_andamento'] > 0): ?>
                            <div class="action-badge blue"><?php echo $stats['em_andamento']; ?></div>
                        <?php endif; ?>
                        <div class="quick-action-icon">🔧</div>
                        <div class="quick-action-title">Reparos</div>
                        <div class="quick-action-subtitle">Em andamento</div>
                    </a>
                    <a href="marketplace.php" class="quick-action">
                        <div class="action-badge green">LOJA</div>
                        <div class="quick-action-icon">🛒</div>
                        <div class="quick-action-title">Marketplace</div>
                        <div class="quick-action-subtitle">Meus produtos</div>
                    </a>
                    <a href="assistencia_virtual.php" class="quick-action">
                        <div class="action-badge new">NOVO</div>
                        <div class="quick-action-icon">🤖</div>
                        <div class="quick-action-title">Assistência</div>
                        <div class="quick-action-subtitle">Virtual IA</div>
                    </a>
                </div>
            </div>

            <!-- Solicitações Recentes -->
            <?php 
            // Buscar solicitações recentes
            $solicitacoes = [];
            try {
                if ($usuario && isset($usuario['assistencia_id'])) {
                    $sql = "SELECT sr.*, u.nome as cliente_nome, u.telefone as cliente_telefone 
                            FROM solicitacoes_reparo sr 
                            LEFT JOIN usuarios u ON sr.cliente_id = u.id 
                            WHERE sr.assistencia_id = ? 
                            ORDER BY sr.data_criacao DESC 
                            LIMIT 3";
                    $stmt = $mysqli->prepare($sql);
                    $stmt->bind_param("i", $usuario['assistencia_id']);
                    $stmt->execute();
                    $result = $stmt->get_result();
                    while ($row = $result->fetch_assoc()) {
                        $solicitacoes[] = $row;
                    }
                }
            } catch (Exception $e) {
                error_log("Erro ao buscar solicitações: " . $e->getMessage());
            }
            ?>

            <?php if (!empty($solicitacoes)): ?>
            <div class="pending-requests">
                <div class="section-header">
                    <div class="section-title">📋 Solicitações recentes</div>
                    <a href="solicitacoes.php" class="view-all">Ver todas</a>
                </div>

                <?php foreach ($solicitacoes as $solicitacao): ?>
                <a href="detalhes_solicitacao.php?id=<?php echo $solicitacao['id']; ?>" class="request-item">
                    <div class="request-icon <?php echo ($solicitacao['urgencia'] === 'alta') ? 'urgent' : ''; ?>">
                        📱
                    </div>
                    <div class="request-info">
                        <div class="request-header">
                            <div class="request-title"><?php echo htmlspecialchars($solicitacao['dispositivo'] ?? 'Reparo'); ?></div>
                            <div class="request-time">
                                <?php 
                                $data = new DateTime($solicitacao['data_criacao']);
                                echo $data->format('H:i');
                                ?>
                            </div>
                        </div>
                        <div class="request-description">
                            <?php echo htmlspecialchars(substr($solicitacao['descricao_problema'], 0, 80) . '...'); ?>
                        </div>
                        <div class="request-tags">
                            <div class="request-tag device"><?php echo htmlspecialchars($solicitacao['dispositivo'] ?? 'Celular'); ?></div>
                            <div class="request-tag location"><?php echo htmlspecialchars($solicitacao['cliente_nome'] ?? 'Cliente'); ?></div>
                            <?php if ($solicitacao['orcamento_maximo']): ?>
                                <div class="request-price">Até R$ <?php echo number_format($solicitacao['orcamento_maximo'], 0, ',', '.'); ?></div>
                            <?php endif; ?>
                        </div>
                    </div>
                </a>
                <?php endforeach; ?>
            </div>
            <?php endif; ?>

            <!-- Agenda de Hoje -->
            <div class="today-schedule">
                <div class="section-header">
                    <div class="section-title">📅 Agenda de hoje</div>
                    <a href="reparos.php" class="view-all">Ver agenda</a>
                </div>

                <?php if ($stats['em_andamento'] > 0): ?>
                <div class="schedule-item">
                    <div class="schedule-time current">Agora</div>
                    <div class="schedule-info">
                        <div class="schedule-title"><?php echo $stats['em_andamento']; ?> reparo(s) em andamento</div>
                        <div class="schedule-subtitle">Acompanhe o progresso</div>
                    </div>
                    <div class="schedule-status pending">⏰</div>
                </div>
                <?php endif; ?>

                <?php if ($stats['aguardando_resposta'] > 0): ?>
                <div class="schedule-item">
                    <div class="schedule-time">Hoje</div>
                    <div class="schedule-info">
                        <div class="schedule-title"><?php echo $stats['aguardando_resposta']; ?> solicitação(ões) pendente(s)</div>
                        <div class="schedule-subtitle">Aguardando sua proposta</div>
                    </div>
                    <div class="schedule-status">📍</div>
                </div>
                <?php endif; ?>

                <?php if ($stats['concluidas'] > 0): ?>
                <div class="schedule-item">
                    <div class="schedule-time past">Concluídos</div>
                    <div class="schedule-info">
                        <div class="schedule-title"><?php echo $stats['concluidas']; ?> reparo(s) finalizado(s)</div>
                        <div class="schedule-subtitle">Este mês</div>
                    </div>
                    <div class="schedule-status completed">✓</div>
                </div>
                <?php endif; ?>

                <?php if ($stats['aguardando_resposta'] == 0 && $stats['em_andamento'] == 0 && $stats['concluidas'] == 0): ?>
                <div class="schedule-item">
                    <div class="schedule-time">Hoje</div>
                    <div class="schedule-info">
                        <div class="schedule-title">Nenhuma atividade agendada</div>
                        <div class="schedule-subtitle">Fique atento a novas solicitações</div>
                    </div>
                    <div class="schedule-status">📅</div>
                </div>
                <?php endif; ?>
            </div>

            <!-- Performance -->
            <div class="performance-card">
                <div class="section-title">📊 Performance</div>
                
                <div class="performance-grid">
                    <div class="performance-item">
                        <div class="performance-number">
                            <?php echo number_format($stats['avaliacao_media'], 1); ?>
                        </div>
                        <div class="performance-label">Avaliação média</div>
                        <div class="performance-change">+0.2 este mês</div>
                    </div>
                    
                    <div class="performance-item">
                        <div class="performance-number">
                            <?php echo number_format($stats['taxa_aprovacao'], 0); ?>%
                        </div>
                        <div class="performance-label">Taxa de aprovação</div>
                        <div class="performance-change">+5% este mês</div>
                    </div>
                    
                    <div class="performance-item">
                        <div class="performance-number">
                            <?php echo $stats['tempo_medio_reparo']; ?>h
                        </div>
                        <div class="performance-label">Tempo médio</div>
                        <div class="performance-change negative">+2h este mês</div>
                    </div>
                    
                    <div class="performance-item">
                        <div class="performance-number"><?php echo $stats['total_solicitacoes']; ?></div>
                        <div class="performance-label">Reparos este mês</div>
                        <div class="performance-change">+<?php echo max(0, $stats['total_solicitacoes'] - 10); ?> vs último mês</div>
                    </div>
                </div>

                <div class="earnings-summary">
                    <div class="earnings-amount">R$ <?php echo number_format($stats['receita_mes'], 0, ',', '.'); ?></div>
                    <div class="earnings-label">Faturamento este mês</div>
                    <div class="earnings-period">
                        <?php 
                        $percentual = ($stats['receita_mes'] > 0) ? '+15%' : '0%';
                        echo $percentual;
                        ?> vs mês anterior
                    </div>
                </div>
            </div>
        </div>

        <!-- Bottom Navigation -->
        <div class="bottom-nav">
            <a href="dashboard_mobile.php" class="nav-item active">
                <div class="nav-icon">🏠</div>
                <div class="nav-label">Início</div>
            </a>
            <a href="solicitacoes.php" class="nav-item">
                <div class="nav-icon">📋</div>
                <div class="nav-label">Solicitações</div>
                <?php if ($stats['aguardando_resposta'] > 0): ?>
                    <div class="nav-badge"><?php echo $stats['aguardando_resposta']; ?></div>
                <?php endif; ?>
            </a>
            <a href="reparos.php" class="nav-item">
                <div class="nav-icon">🔧</div>
                <div class="nav-label">Reparos</div>
                <?php if ($stats['em_andamento'] > 0): ?>
                    <div class="nav-badge"><?php echo $stats['em_andamento']; ?></div>
                <?php endif; ?>
            </a>
            <a href="marketplace.php" class="nav-item">
                <div class="nav-icon">🛒</div>
                <div class="nav-label">Loja</div>
            </a>
            <a href="carteira.php" class="nav-item">
                <div class="nav-icon">💳</div>
                <div class="nav-label">Carteira</div>
            </a>
            <a href="perfil.php" class="nav-item">
                <div class="nav-icon">👤</div>
                <div class="nav-label">Perfil</div>
            </a>
        </div>

        <!-- Floating Action Button -->
        <a href="assistencia_virtual.php" class="floating-action" title="Assistência Virtual">
            🤖
        </a>
    </div>

    <script>
        // Função para mostrar notificações
        function showNotification(message) {
            const existingNotification = document.querySelector('.notification');
            if (existingNotification) {
                existingNotification.remove();
            }
            
            const notification = document.createElement('div');
            notification.className = 'notification';
            notification.textContent = message;
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.style.opacity = '0';
                setTimeout(() => notification.remove(), 300);
            }, 4000);
        }

        // Animação dos números das estatísticas
        function animateStats() {
            const statNumbers = document.querySelectorAll('.stat-number');
            statNumbers.forEach((element, index) => {
                const finalValue = element.textContent;
                const isNumber = !isNaN(parseInt(finalValue));
                
                if (isNumber) {
                    const target = parseInt(finalValue);
                    let current = 0;
                    const increment = Math.max(1, Math.floor(target / 20));
                    
                    const timer = setInterval(() => {
                        current += increment;
                        element.textContent = current;
                        
                        if (current >= target) {
                            element.textContent = finalValue;
                            clearInterval(timer);
                        }
                    }, 50);
                }
            });
        }

        // Inicializar quando a página carregar
        document.addEventListener('DOMContentLoaded', function() {
            // Animar estatísticas após um delay
            setTimeout(animateStats, 500);
            
            // Mensagem de boas-vindas
            setTimeout(() => {
                const hour = new Date().getHours();
                let greeting = 'Boa tarde';
                if (hour < 12) greeting = 'Bom dia';
                else if (hour >= 18) greeting = 'Boa noite';
                
                const pendingCount = <?php echo $stats['aguardando_resposta']; ?>;
                if (pendingCount > 0) {
                    showNotification(`${greeting}! Você tem ${pendingCount} solicitação(ões) aguardando proposta.`);
                } else {
                    showNotification(`${greeting}! Tudo está em ordem por aqui.`);
                }
            }, 1000);
        });
    </script>
</body>
</html>
