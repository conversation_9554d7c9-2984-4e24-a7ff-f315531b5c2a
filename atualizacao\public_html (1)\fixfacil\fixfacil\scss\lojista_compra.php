<?php
session_start();
require_once 'db.php';

if (!isset($_SESSION['user_id']) || $_SESSION['nivel_acesso'] != 'lojista') {
    header("Location: login.php");
    exit();
}

if (isset($_GET['logout']) && $_GET['logout'] == 'true') {
    session_destroy();
    header("Location: login.php");
    exit();
}

$lojista_id = $_SESSION['user_id'];

// Função para finalizar a compra
function finalizarCompra($pdo, $lojista_id, $fornecedor_id, $produto_id, $quantidade, $observacao, $data_programada)
{
    try {
        // Inserir os detalhes da compra na tabela de compras
        $query = "INSERT INTO compras (lojista_id, fornecedor_id, produto_id, quantidade, observacao, data_programada) VALUES (:lojista_id, :fornecedor_id, :produto_id, :quantidade, :observacao, :data_programada)";
        $stmt = $pdo->prepare($query);
        $stmt->bindParam(':lojista_id', $lojista_id);
        $stmt->bindParam(':fornecedor_id', $fornecedor_id);
        $stmt->bindParam(':produto_id', $produto_id);
        $stmt->bindParam(':quantidade', $quantidade);
        $stmt->bindParam(':observacao', $observacao);
        $stmt->bindParam(':data_programada', $data_programada, PDO::PARAM_STR); // Definir o tipo de dado para a data programada
        $stmt->execute();

        return true;
    } catch (PDOException $e) {
        echo "Erro ao finalizar a compra: " . $e->getMessage();
        return false;
    }
}

// Verificar se o formulário foi submetido para finalizar a compra
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['finalizar_compra'])) {
    $lojista_id = $_SESSION['user_id'];
    $fornecedor_id = $_SESSION['fornecedor_id']; // Recuperar o ID do fornecedor da sessão
    $produto_id = $_POST['produto_id'];
    $quantidade = $_POST['quantidade'];
    $observacao = $_POST['observacao'];
    $data_programada = isset($_POST['data_programada']) ? $_POST['data_programada'] : date('Y-m-d'); // Definir a data programada como a data atual caso não seja fornecida pelo usuário

    if (finalizarCompra($pdo, $lojista_id, $fornecedor_id, $produto_id, $quantidade, $observacao, $data_programada)) {
        // Exibir um alerta informando que o produto foi adicionado com sucesso
        echo '<script>alert("Produto adicionado ao carrinho com sucesso!");</script>';
    }
}

// Recuperar o bairro do lojista
$query = "SELECT bairro FROM usuarios WHERE id = :lojista_id";
$stmt = $pdo->prepare($query);
$stmt->bindParam(':lojista_id', $lojista_id);
$stmt->execute();
$result = $stmt->fetch(PDO::FETCH_ASSOC);
$lojista_bairro = $result['bairro'];

// Recuperar todos os fornecedores do mesmo bairro do lojista
$query = "SELECT * FROM usuarios WHERE nivel_acesso = 'fornecedor' AND bairro = :lojista_bairro";
$stmt = $pdo->prepare($query);
$stmt->bindParam(':lojista_bairro', $lojista_bairro);
$stmt->execute();
$fornecedores = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Verificar se um fornecedor foi selecionado e definir o fornecedor_id na sessão
if (isset($_POST['fornecedor_id'])) {
    $fornecedor_id = $_POST['fornecedor_id'];
    $_SESSION['fornecedor_id'] = $fornecedor_id;
} elseif (isset($_SESSION['fornecedor_id'])) {
    unset($_SESSION['fornecedor_id']); // Limpar o fornecedor_id da sessão
    $fornecedor_id = ''; // Limpar o fornecedor_id da variável
}

// Recuperar produtos de um fornecedor específico (se selecionado)
$produtos = array();
if (!empty($_SESSION['fornecedor_id'])) {
    $fornecedor_id = $_SESSION['fornecedor_id'];
    $query = "SELECT * FROM produtos WHERE fornecedor_id = :fornecedor_id";
    $stmt = $pdo->prepare($query);
    $stmt->bindParam(':fornecedor_id', $fornecedor_id);
    $stmt->execute();
    $produtos = $stmt->fetchAll(PDO::FETCH_ASSOC);
}

// Pesquisa por nome de produto
if (isset($_POST['search'])) {
    $search = $_POST['search'];
    $query = "SELECT * FROM produtos WHERE nome LIKE :search";
    $stmt = $pdo->prepare($query);
    $stmt->bindValue(':search', '%' . $search . '%');
    $stmt->execute();
    $produtos = $stmt->fetchAll(PDO::FETCH_ASSOC);
}
?>
<!DOCTYPE html>
<html lang="pt-br">

<head>

    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="description" content="">
    <meta name="author" content="">

    <title>LOJISTA - Dashboard</title>

    <!-- Custom fonts for this template-->
    <link href="vendor/fontawesome-free/css/all.min.css" rel="stylesheet" type="text/css">
    <link href="https://fonts.googleapis.com/css?family=Nunito:200,200i,300,300i,400,400i,600,600i,700,700i,800,800i,900,900i"
        rel="stylesheet">

    <!-- Custom styles for this template-->
    <link href="css/sb-admin-2.min.css" rel="stylesheet">

</head>

<body id="page-top">

    <!-- Page Wrapper -->
    <div id="wrapper">

        <!-- Sidebar -->
        <ul class="navbar-nav bg-gradient-primary sidebar sidebar-dark accordion" id="accordionSidebar">

            <!-- Sidebar - Brand -->
            <a class="sidebar-brand d-flex align-items-center justify-content-center" href="admin_dashboard.php">
                <div class="sidebar-brand-icon rotate-n-15">
                    <i class="fas fa-laugh-wink"></i>
                </div>
                <div class="sidebar-brand-text mx-3"> <sup>Avos Brasil</sup></div>
            </a>

            <!-- Divider -->
            <hr class="sidebar-divider my-0">

            <!-- Nav Item - Dashboard -->
            <li class="nav-item active">
                <a class="nav-link" href="minhas_compras.php">
                    <i class="fas fa-fw fa-tachometer-alt"></i>
                    <span>Meus Pedidos</span></a>
            </li>
            <!-- Nav Item - Charts -->
            <li class="nav-item">
                <a class="nav-link" href="lojista_compra.php">
                    <i class="fas fa-fw fa-chart-area"></i>
                    <span>Nova Cotação</span></a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="lojista_favorito.php">
                    <i class="fas fa-fw fa-chart-area"></i>
                    <span>Favorito</span></a>
            </li>

            <!-- Divider -->
            <hr class="sidebar-divider d-none d-md-block">

            <!-- Sidebar Toggler (Sidebar) -->
            <div class="text-center d-none d-md-inline">
                <button class="rounded-circle border-0" id="sidebarToggle"></button>
            </div>

            <!-- Sidebar Message -->
            <div class="sidebar-card d-none d-lg-flex">
                <img class="sidebar-card-illustration mb-2" src="img/undraw_rocket.svg" alt="...">
                <p class="text-center mb-2"><strong>Avos Brasil Pro</strong>Controle LFM_Consultoria</p>
            </div>

        </ul>
        <!-- End of Sidebar -->

        <!-- Content Wrapper -->
        <div id="content-wrapper" class="d-flex flex-column">

            <!-- Main Content -->
            <div id="content">

                <!-- Topbar -->
                <nav class="navbar navbar-expand navbar-light bg-white topbar mb-4 static-top shadow">

                    <!-- Sidebar Toggle (Topbar) -->
                    <button id="sidebarToggleTop" class="btn btn-link d-md-none rounded-circle mr-3">
                        <i class="fa fa-bars"></i>
                    </button>

                    <!-- Topbar Search -->
                    <form class="d-none d-sm-inline-block form-inline mr-auto ml-md-3 my-2 my-md-0 mw-100 navbar-search">
                        <div class="input-group">
                            <input type="text" class="form-control bg-light border-0 small" placeholder="Search for..."
                                aria-label="Search" aria-describedby="basic-addon2">
                            <div class="input-group-append">
                                <button class="btn btn-primary" type="button">
                                    <i class="fas fa-search fa-sm"></i>
                                </button>
                            </div>
                        </div>
                    </form>

                    <!-- Topbar Navbar -->
                    <ul class="navbar-nav ml-auto">

                        <!-- Nav Item - User Information -->
                        <li class="nav-item dropdown no-arrow">
                            <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button"
                                data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                <span class="mr-2 d-none d-lg-inline text-gray-600 small">LOJISTA</span>
                                <img class="img-profile rounded-circle"
                                    src="img/undraw_profile.svg">
                            </a>
                            <!-- Dropdown - User Information -->
                            <div class="dropdown-menu dropdown-menu-right shadow animated--grow-in"
                                aria-labelledby="userDropdown">
                                <a class="dropdown-item" href="#">
                                    <i class="fas fa-user fa-sm fa-fw mr-2 text-gray-400"></i>
                                    Profile
                                </a>
                                <a class="dropdown-item" href="#">
                                    <i class="fas fa-cogs fa-sm fa-fw mr-2 text-gray-400"></i>
                                    Settings
                                </a>
                                <a class="dropdown-item" href="#">
                                    <i class="fas fa-list fa-sm fa-fw mr-2 text-gray-400"></i>
                                    Activity Log
                                </a>
                                <div class="dropdown-divider"></div>
                                <a class="dropdown-item" href="#" data-toggle="modal" data-target="#logoutModal">
                                    <i class="fas fa-sign-out-alt fa-sm fa-fw mr-2 text-gray-400"></i>
                                    Logout
                                </a>
                            </div>
                        </li>

                    </ul>

                </nav>
                <!-- End of Topbar -->

                <!-- Begin Page Content -->
                <div class="container-fluid">

                    <!-- Page Heading -->
                    <h1 class="h3 mb-4 text-gray-800">Nova Cotação</h1>

                    <div class="row">
                        <div class="col-lg-12">
                            <div class="card shadow mb-4">
                                <div class="card-header py-3">
                                    <h6 class="m-0 font-weight-bold text-primary">Selecione o fornecedor</h6>
                                </div>
                                <div class="card-body">
                                    <form method="post" action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>">
                                        <div class="form-group">
                                            <label for="fornecedor_id">Fornecedor:</label>
                                            <select class="form-control" id="fornecedor_id" name="fornecedor_id"
                                                onchange="this.form.submit()">
                                                <option value="">Selecione um fornecedor</option>
                                                <?php foreach ($fornecedores as $fornecedor) : ?>
                                                    <option value="<?php echo $fornecedor['id']; ?>" <?php if ($fornecedor['id'] == $_SESSION['fornecedor_id']) echo 'selected'; ?>>
                                                        <?php echo $fornecedor['nome']; ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>

                    <?php if (!empty($_SESSION['fornecedor_id'])) : ?>
                        <div class="row">
                            <div class="col-lg-12">
                                  <div class="card shadow mb-4">
                                <div class="card-header py-3">
                                    <h6 class="m-0 font-weight-bold text-primary">Produtos Disponíveis</h6>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                                            <thead>
                                                <tr>
                                                    <th>Nome</th>
                                                    <th>Descrição</th>
                                                    <th>Preço</th>
                                                    <th>Foto</th>
                                                    <th>Ação</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($produtos as $produto) : ?>
                                                    <tr>
                                                        <td><?php echo $produto['nome']; ?></td>
                                                        <td><?php echo $produto['descricao']; ?></td>
                                                        <td>R$ <?php echo number_format($produto['preco'], 2, ',', '.'); ?></td>
                                                        <td><img src="data:image/jpeg;base64,<?php echo base64_encode($produto['foto']); ?>" alt="Imagem do Produto" style="width: 50px;"></td>

                                                        <td>
                                                            <form method="post" action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>">
                                                                <input type="hidden" name="produto_id" value="<?php echo $produto['id']; ?>">
                                                                <div class="form-group">
                                                                    <label for="quantidade">Quantidade:</label>
                                                                    <input type="number" class="form-control" id="quantidade"
                                                                        name="quantidade" required>
                                                                </div>
                                                                <div class="form-group">
                                                                    <label for="observacao">Observação:</label>
                                                                    <textarea class="form-control" id="observacao" name="observacao"
                                                                        rows="3"></textarea>
                                                                </div>
                                                                <div class="form-group">
                                                                    <label for="data_programada">Data de Entrega:</label>
                                                                    <input type="date" class="form-control" id="data_programada" name="data_programada" value="<?php echo date('Y-m-d'); ?>">
                                                                </div>
                                                                <button type="submit" class="btn btn-primary" name="finalizar_compra">Finalizar Compra</button>
                                                            </form>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                           
                    <?php endif; ?>

                </div>
                <!-- /.container-fluid -->

            </div>
            <!-- End of Main Content -->

            <!-- Footer -->
            <footer class="sticky-footer bg-white">
                <div class="container my-auto">
                    <div class="text-center my-auto">
                        <span>Desenvolvido por LFM_Consultoria</span>
                    </div>
                </div>
            </footer>
            <!-- End of Footer -->

        </div>
        <!-- End of Content Wrapper -->

    </div>
    <!-- End of Page Wrapper -->

    <!-- Scroll to Top Button-->
    <a class="scroll-to-top rounded" href="#page-top">
        <i class="fas fa-angle-up"></i>
    </a>

    <!-- Logout Modal-->
    <div class="modal fade" id="logoutModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
        aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalLabel">Pronto para sair?</h5>
                    <button class="close" type="button" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">×</span>
                    </button>
                </div>
                <div class="modal-body">Selecione "Logout" abaixo se você estiver pronto para terminar sua sessão atual.</div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" type="button" data-dismiss="modal">Cancelar</button>
                    <a class="btn btn-primary" href="?logout=true">Logout</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap core JavaScript-->
    <script src="vendor/jquery/jquery.min.js"></script>
    <script src="vendor/bootstrap/js/bootstrap.bundle.min.js"></script>

    <!-- Core plugin JavaScript-->
    <script src="vendor/jquery-easing/jquery.easing.min.js"></script>

    <!-- Custom scripts for all pages-->
    <script src="js/sb-admin-2.min.js"></script>

</body>

</html>
