<?php
/**
 * AJAX - Obter estatísticas em tempo real
 */

header('Content-Type: application/json');
header('Cache-Control: no-cache, must-revalidate');

session_start();

try {
    require_once '../config/auth.php';
    require_once '../config/database.php';

    $auth = getAuth();
    $auth->checkAssistenciaAuth();

    $usuario = $auth->getUsuarioLogado();
    if (!$usuario) {
        throw new Exception("Usuário não encontrado");
    }

    $db = getDatabase();
    if (!$db) {
        throw new Exception("Erro na conexão com banco de dados");
    }

    $assistencia_id = $usuario['id'];
    $stats = [];

    // Solicitações pendentes
    $stmt = $db->prepare("SELECT COUNT(*) as total FROM solicitacoes_reparo WHERE status = 'enviado'");
    $stmt->execute();
    $stats['pendentes'] = $stmt->fetch()['total'] ?? 0;

    // Ganhos do dia
    $stmt = $db->prepare("SELECT SUM(valor) as total FROM propostas WHERE DATE(data_criacao) = CURDATE() AND status = 'aceita'");
    $stmt->execute();
    $stats['ganhos_hoje'] = $stmt->fetch()['total'] ?? 0;

    // Avaliação média
    $stmt = $db->prepare("SELECT AVG(avaliacao) as media FROM avaliacoes WHERE assistencia_id = ?");
    $stmt->execute([$assistencia_id]);
    $stats['avaliacao'] = round($stmt->fetch()['media'] ?? 4.5, 1);

    // Reparos em andamento
    $stmt = $db->prepare("SELECT COUNT(*) as total FROM propostas WHERE status = 'em_andamento' AND assistencia_id = ?");
    $stmt->execute([$assistencia_id]);
    $stats['em_andamento'] = $stmt->fetch()['total'] ?? 0;

    // Produtos no marketplace
    $stmt = $db->prepare("SELECT COUNT(*) as total FROM produtos WHERE assistencia_id = ?");
    $stmt->execute([$assistencia_id]);
    $stats['produtos'] = $stmt->fetch()['total'] ?? 0;

    // Formatação dos valores
    $stats['ganhos_hoje_formatted'] = 'R$ ' . number_format($stats['ganhos_hoje'], 0, ',', '.');
    $stats['avaliacao_formatted'] = $stats['avaliacao'] . '⭐';

    echo json_encode([
        'success' => true,
        'data' => $stats
    ]);

} catch (Exception $e) {
    error_log("Erro no AJAX stats: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'error' => 'Erro interno do servidor'
    ]);
}
?>
