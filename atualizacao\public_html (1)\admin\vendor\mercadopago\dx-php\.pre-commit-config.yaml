repos:
- repo: https://github.com/pre-commit/pre-commit-hooks
  rev: v3.2.0
  hooks:
  - id: trailing-whitespace
  - id: end-of-file-fixer
  - id: check-yaml
  - id: check-added-large-files
- repo: https://github.com/digitalpulp/pre-commit-php.git
  rev: 1.4.0
  hooks:
  - id: php-lint
  - id: php-lint-all
  - id: php-cs
    files: \.(php)$
    args: [--standard=PSR1 -p]
  - id: php-cs-fixer
    files: \.(php)$
