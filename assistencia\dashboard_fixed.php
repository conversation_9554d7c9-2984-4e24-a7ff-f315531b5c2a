<?php
/**
 * Dashboard Principal - Mobile First Corrigido
 * FixFácil Assistências - Sistema Novo
 */

// Configurar relatório de erros para debug
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Iniciar sessão se não estiver iniciada
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Verificar se o usuário está logado
if (!isset($_SESSION['usuario_id']) || $_SESSION['tipo_usuario'] !== 'assistencia') {
    header('Location: ../login.php');
    exit();
}

// Incluir configurações básicas
require_once 'config/database.php';

// Configuração de banco de dados
$db = getDatabase();
$mysqli = $db->getConnection();

// Obter dados do usuário logado
$usuario_id = $_SESSION['usuario_id'];
$usuario = null;
$plano = null;

try {
    // Buscar dados do usuário
    $sql = "SELECT u.id, u.nome, u.email, u.telefone, u.plano_id, at.id as assistencia_id 
            FROM usuarios u 
            LEFT JOIN assistencia_tecnica at ON u.id = at.usuario_id 
            WHERE u.id = ?";
    $stmt = $mysqli->prepare($sql);
    $stmt->bind_param("i", $usuario_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $usuario = $result->fetch_assoc();
    
    // Buscar informações do plano
    if ($usuario && $usuario['plano_id']) {
        $sql = "SELECT * FROM planos WHERE id = ?";
        $stmt = $mysqli->prepare($sql);
        $stmt->bind_param("i", $usuario['plano_id']);
        $stmt->execute();
        $result = $stmt->get_result();
        $plano = $result->fetch_assoc();
    }
    
    // Plano padrão se não encontrar
    if (!$plano) {
        $plano = [
            'nome' => 'Free',
            'taxa_servico' => 25.00
        ];
    }
    
} catch (Exception $e) {
    error_log("Erro ao buscar dados do usuário: " . $e->getMessage());
    $usuario = ['nome' => 'Usuário', 'email' => '', 'assistencia_id' => null];
    $plano = ['nome' => 'Free', 'taxa_servico' => 25.00];
}

// Obter estatísticas
$stats = [
    'solicitacoes_pendentes' => 0,
    'propostas_enviadas' => 0,
    'reparos_andamento' => 0,
    'reparos_concluidos' => 0,
    'receita_mes' => 0,
    'propostas_aceitas' => 0,
    'total_propostas' => 0,
    'mensagens_nao_lidas' => 0
];

try {
    if ($usuario && isset($usuario['assistencia_id']) && $usuario['assistencia_id']) {
        $assistencia_id = $usuario['assistencia_id'];
        
        // Solicitações pendentes (todas as solicitações com status 'enviado')
        $sql = "SELECT COUNT(*) as count FROM solicitacoes_reparo WHERE status = 'enviado' AND visivel = 1";
        $stmt = $mysqli->prepare($sql);
        $stmt->execute();
        $result = $stmt->get_result();
        $row = $result->fetch_assoc();
        $stats['solicitacoes_pendentes'] = $row ? (int)$row['count'] : 0;
        
        // Propostas enviadas por esta assistência
        $sql = "SELECT COUNT(*) as count FROM propostas_assistencia WHERE assistencia_id = ? AND status = 'enviada'";
        $stmt = $mysqli->prepare($sql);
        $stmt->bind_param("i", $assistencia_id);
        $stmt->execute();
        $result = $stmt->get_result();
        $row = $result->fetch_assoc();
        $stats['propostas_enviadas'] = $row ? (int)$row['count'] : 0;
        
        // Reparos em andamento
        $sql = "SELECT COUNT(*) as count FROM propostas_assistencia WHERE assistencia_id = ? AND status = 'Em Andamento'";
        $stmt = $mysqli->prepare($sql);
        $stmt->bind_param("i", $assistencia_id);
        $stmt->execute();
        $result = $stmt->get_result();
        $row = $result->fetch_assoc();
        $stats['reparos_andamento'] = $row ? (int)$row['count'] : 0;
        
        // Reparos concluídos este mês
        $sql = "SELECT COUNT(*) as count FROM propostas_assistencia WHERE assistencia_id = ? AND status = 'Concluída' AND MONTH(data_proposta) = MONTH(CURRENT_DATE()) AND YEAR(data_proposta) = YEAR(CURRENT_DATE())";
        $stmt = $mysqli->prepare($sql);
        $stmt->bind_param("i", $assistencia_id);
        $stmt->execute();
        $result = $stmt->get_result();
        $row = $result->fetch_assoc();
        $stats['reparos_concluidos'] = $row ? (int)$row['count'] : 0;
        
        // Receita este mês
        $taxa_servico = $plano['taxa_servico'] ?? 25;
        $sql = "SELECT COALESCE(SUM(preco * (1 - ?/100)), 0) as receita FROM propostas_assistencia WHERE assistencia_id = ? AND status = 'Concluída' AND pago = 1 AND MONTH(data_proposta) = MONTH(CURRENT_DATE()) AND YEAR(data_proposta) = YEAR(CURRENT_DATE())";
        $stmt = $mysqli->prepare($sql);
        $stmt->bind_param("di", $taxa_servico, $assistencia_id);
        $stmt->execute();
        $result = $stmt->get_result();
        $row = $result->fetch_assoc();
        $stats['receita_mes'] = $row ? (float)$row['receita'] : 0;
        
        // Propostas aceitas
        $sql = "SELECT COUNT(*) as count FROM propostas_assistencia WHERE assistencia_id = ? AND status = 'aceita'";
        $stmt = $mysqli->prepare($sql);
        $stmt->bind_param("i", $assistencia_id);
        $stmt->execute();
        $result = $stmt->get_result();
        $row = $result->fetch_assoc();
        $stats['propostas_aceitas'] = $row ? (int)$row['count'] : 0;
        
        // Total de propostas
        $sql = "SELECT COUNT(*) as count FROM propostas_assistencia WHERE assistencia_id = ?";
        $stmt = $mysqli->prepare($sql);
        $stmt->bind_param("i", $assistencia_id);
        $stmt->execute();
        $result = $stmt->get_result();
        $row = $result->fetch_assoc();
        $stats['total_propostas'] = $row ? (int)$row['count'] : 0;
    }
    
} catch (Exception $e) {
    error_log("Erro ao obter estatísticas: " . $e->getMessage());
}

// Obter atividades recentes
$atividades = [];
try {
    if ($usuario && isset($usuario['assistencia_id']) && $usuario['assistencia_id']) {
        $assistencia_id = $usuario['assistencia_id'];
        
        // Buscar solicitações recentes
        $sql = "SELECT 'solicitacao' as tipo, sr.id, CONCAT('Nova solicitação: ', sr.marca, ' ', sr.modelo) as descricao, sr.data_solicitacao as data FROM solicitacoes_reparo sr WHERE sr.status = 'enviado' AND sr.visivel = 1 ORDER BY sr.data_solicitacao DESC LIMIT 3";
        $stmt = $mysqli->prepare($sql);
        $stmt->execute();
        $result = $stmt->get_result();
        while ($row = $result->fetch_assoc()) {
            $atividades[] = $row;
        }
        
        // Buscar propostas recentes
        $sql = "SELECT 'proposta' as tipo, pa.id, CONCAT('Proposta ', pa.status, ' - R$ ', FORMAT(pa.preco, 2)) as descricao, pa.data_proposta as data FROM propostas_assistencia pa WHERE pa.assistencia_id = ? ORDER BY pa.data_proposta DESC LIMIT 3";
        $stmt = $mysqli->prepare($sql);
        $stmt->bind_param("i", $assistencia_id);
        $stmt->execute();
        $result = $stmt->get_result();
        while ($row = $result->fetch_assoc()) {
            $atividades[] = $row;
        }
        
        // Ordenar por data
        usort($atividades, function($a, $b) {
            return strtotime($b['data']) - strtotime($a['data']);
        });
        
        $atividades = array_slice($atividades, 0, 5);
    }
    
} catch (Exception $e) {
    error_log("Erro ao obter atividades: " . $e->getMessage());
}

// Dados da empresa
$empresa_nome = $usuario['nome'] ?? 'Assistência Técnica';
$empresa_logo = strtoupper(substr($empresa_nome, 0, 2));
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - FixFácil Assistências</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            min-height: 100vh;
            color: #1e293b;
        }

        .container {
            max-width: 414px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
            position: relative;
            overflow-x: hidden;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }

        .header {
            background: linear-gradient(135deg, #059669 0%, #065f46 100%);
            color: white;
            padding: 24px 20px 20px 20px;
            position: relative;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 20"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="0.5" fill="white" opacity="0.05"/></pattern></defs><rect width="100" height="20" fill="url(%23grain)"/></svg>');
        }

        .header-content {
            position: relative;
            z-index: 1;
        }

        .header-top {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 20px;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .user-avatar {
            width: 48px;
            height: 48px;
            background: rgba(255,255,255,0.15);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            font-weight: 700;
            backdrop-filter: blur(10px);
        }

        .user-details h3 {
            font-size: 16px;
            font-weight: 700;
            margin-bottom: 4px;
        }

        .user-details p {
            font-size: 14px;
            opacity: 0.8;
        }

        .header-actions {
            display: flex;
            gap: 8px;
        }

        .action-btn {
            background: rgba(255,255,255,0.15);
            border: none;
            color: white;
            padding: 8px;
            border-radius: 12px;
            font-size: 16px;
            cursor: pointer;
            backdrop-filter: blur(10px);
            transition: all 0.2s ease;
        }

        .action-btn:hover {
            background: rgba(255,255,255,0.25);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
        }

        .stat-card {
            background: rgba(255,255,255,0.15);
            border-radius: 12px;
            padding: 16px;
            text-align: center;
            backdrop-filter: blur(10px);
        }

        .stat-number {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 4px;
        }

        .stat-label {
            font-size: 12px;
            opacity: 0.8;
        }

        .content {
            padding: 20px;
            padding-bottom: 100px;
        }

        .section {
            background: white;
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
        }

        .section-title {
            font-size: 16px;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .quick-actions {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
        }

        .quick-action {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            padding: 16px;
            text-align: center;
            text-decoration: none;
            color: #1e293b;
            transition: all 0.2s ease;
        }

        .quick-action:hover {
            background: #f0fdf4;
            border-color: #059669;
            transform: translateY(-2px);
        }

        .quick-action-icon {
            font-size: 24px;
            margin-bottom: 8px;
        }

        .quick-action-title {
            font-size: 14px;
            font-weight: 600;
        }

        .activity-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 0;
            border-bottom: 1px solid #f1f5f9;
        }

        .activity-item:last-child {
            border-bottom: none;
        }

        .activity-icon {
            width: 40px;
            height: 40px;
            background: #f0fdf4;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            color: #059669;
            flex-shrink: 0;
        }

        .activity-content {
            flex: 1;
        }

        .activity-title {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 4px;
        }

        .activity-time {
            font-size: 12px;
            color: #64748b;
        }

        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 100%;
            max-width: 414px;
            background: white;
            border-top: 1px solid #e2e8f0;
            padding: 12px 20px;
            display: flex;
            justify-content: space-around;
            box-shadow: 0 -4px 20px rgba(0,0,0,0.1);
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
            cursor: pointer;
            padding: 8px;
            border-radius: 12px;
            transition: all 0.2s ease;
            position: relative;
            text-decoration: none;
            color: #64748b;
        }

        .nav-item.active {
            background: #f0fdf4;
            color: #059669;
        }

        .nav-item:hover {
            color: #059669;
        }

        .nav-icon {
            font-size: 20px;
        }

        .nav-label {
            font-size: 10px;
            font-weight: 500;
        }

        .nav-badge {
            position: absolute;
            top: 4px;
            right: 4px;
            background: #ef4444;
            color: white;
            border-radius: 50%;
            width: 16px;
            height: 16px;
            font-size: 10px;
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .no-activities {
            text-align: center;
            color: #64748b;
            font-size: 14px;
            padding: 20px;
        }

        @media (max-width: 480px) {
            .container {
                max-width: 100%;
            }
            
            .header {
                padding: 20px 15px 16px 15px;
            }
            
            .content {
                padding: 15px;
                padding-bottom: 100px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="header-content">
                <div class="header-top">
                    <div class="user-info">
                        <div class="user-avatar"><?php echo $empresa_logo; ?></div>
                        <div class="user-details">
                            <h3><?php echo htmlspecialchars($empresa_nome); ?></h3>
                            <p>Plano <?php echo htmlspecialchars($plano['nome']); ?></p>
                        </div>
                    </div>
                    <div class="header-actions">
                        <button class="action-btn" onclick="window.location.reload()">🔄</button>
                        <button class="action-btn" onclick="window.location.href='logout.php'">🚪</button>
                    </div>
                </div>
                
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number"><?php echo $stats['solicitacoes_pendentes']; ?></div>
                        <div class="stat-label">Solicitações</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number"><?php echo $stats['propostas_enviadas']; ?></div>
                        <div class="stat-label">Propostas</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number"><?php echo $stats['reparos_andamento']; ?></div>
                        <div class="stat-label">Em Andamento</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">R$ <?php echo number_format($stats['receita_mes'], 2, ',', '.'); ?></div>
                        <div class="stat-label">Receita/Mês</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Content -->
        <div class="content">
            <!-- Ações Rápidas -->
            <div class="section">
                <div class="section-title">🚀 Ações Rápidas</div>
                <div class="quick-actions">
                    <a href="solicitacoes.php" class="quick-action">
                        <div class="quick-action-icon">📋</div>
                        <div class="quick-action-title">Solicitações</div>
                    </a>
                    <a href="reparos.php" class="quick-action">
                        <div class="quick-action-icon">🔧</div>
                        <div class="quick-action-title">Reparos</div>
                    </a>
                    <a href="marketplace.php" class="quick-action">
                        <div class="quick-action-icon">🛒</div>
                        <div class="quick-action-title">Marketplace</div>
                    </a>
                    <a href="perfil.php" class="quick-action">
                        <div class="quick-action-icon">👤</div>
                        <div class="quick-action-title">Perfil</div>
                    </a>
                </div>
            </div>

            <!-- Atividades Recentes -->
            <div class="section">
                <div class="section-title">⏰ Atividades Recentes</div>
                <?php if (!empty($atividades)): ?>
                    <?php foreach ($atividades as $atividade): ?>
                    <div class="activity-item">
                        <div class="activity-icon">
                            <?php echo $atividade['tipo'] === 'solicitacao' ? '📋' : '✈️'; ?>
                        </div>
                        <div class="activity-content">
                            <div class="activity-title"><?php echo htmlspecialchars($atividade['descricao']); ?></div>
                            <div class="activity-time"><?php echo date('d/m/Y H:i', strtotime($atividade['data'])); ?></div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="no-activities">
                        <p>Nenhuma atividade recente</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Bottom Navigation -->
        <div class="bottom-nav">
            <a href="dashboard.php" class="nav-item active">
                <div class="nav-icon">🏠</div>
                <div class="nav-label">Início</div>
            </a>
            <a href="solicitacoes.php" class="nav-item">
                <div class="nav-icon">📋</div>
                <div class="nav-label">Solicitações</div>
                <?php if ($stats['solicitacoes_pendentes'] > 0): ?>
                    <div class="nav-badge"><?php echo $stats['solicitacoes_pendentes']; ?></div>
                <?php endif; ?>
            </a>
            <a href="reparos.php" class="nav-item">
                <div class="nav-icon">🔧</div>
                <div class="nav-label">Reparos</div>
            </a>
            <a href="marketplace.php" class="nav-item">
                <div class="nav-icon">🛒</div>
                <div class="nav-label">Loja</div>
            </a>
            <a href="perfil.php" class="nav-item">
                <div class="nav-icon">👤</div>
                <div class="nav-label">Perfil</div>
            </a>
        </div>
    </div>
</body>
</html>
