<?php
/**
 * Detalhes da Solicitação - Versão Responsiva Mobile-First
 * FixFácil Assistências - Sistema Novo
 */

// Redirecionar para versão principal
header('Location: detalhes_solicitacao.php?' . $_SERVER['QUERY_STRING']);
exit();
?>

// Obter dados da solicitação
$solicitacao = null;
$propostas = [];

try {
    // Dados da solicitação
    $sql = "
        SELECT 
            sr.*,
            u.nome as cliente_nome,
            u.telefone as cliente_telefone,
            u.endereco as cliente_endereco,
            u.email as cliente_email
        FROM solicitacoes_reparo sr
        JOIN usuarios u ON sr.usuario_id = u.id
        WHERE sr.id = ?
    ";
    
    $result = $db->query($sql, [$solicitacao_id]);
    $solicitacao = $result->fetch_assoc();
    
    if (!$solicitacao) {
        safeRedirect('solicitacoes.php');
    }
    
    // Propostas para esta solicitação
    $sql = "
        SELECT 
            pa.*,
            at.nome_empresa,
            u.nome as assistencia_nome
        FROM propostas_assistencia pa
        JOIN assistencias_tecnicas at ON pa.assistencia_id = at.id
        JOIN usuarios u ON at.usuario_id = u.id
        WHERE pa.solicitacao_id = ?
        ORDER BY pa.data_proposta DESC
    ";
    
    $result = $db->query($sql, [$solicitacao_id]);
    while ($row = $result->fetch_assoc()) {
        $propostas[] = $row;
    }
    
} catch (Exception $e) {
    error_log("Erro ao obter detalhes da solicitação: " . $e->getMessage());
    safeRedirect('solicitacoes.php');
}

// Verificar se já enviei proposta
$minha_proposta = null;
foreach ($propostas as $proposta) {
    if ($proposta['assistencia_id'] == $usuario['assistencia_id']) {
        $minha_proposta = $proposta;
        break;
    }
}

// Função para obter ícone do dispositivo
function getDeviceIcon($dispositivo) {
    $device = strtolower($dispositivo);
    if (strpos($device, 'iphone') !== false) return '📱';
    if (strpos($device, 'samsung') !== false) return '📱';
    if (strpos($device, 'tablet') !== false) return '📲';
    if (strpos($device, 'notebook') !== false) return '💻';
    if (strpos($device, 'smartwatch') !== false) return '⌚';
    return '📱';
}
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Detalhes da Solicitação - FixFácil Assistências</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            min-height: 100vh;
            color: #1e293b;
            line-height: 1.5;
        }

        .container {
            max-width: 414px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
            position: relative;
            overflow-x: hidden;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            padding-bottom: 100px;
        }

        /* Header */
        .header {
            background: linear-gradient(135deg, #059669 0%, #065f46 100%);
            color: white;
            padding: 24px 20px 20px 20px;
            position: relative;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 20"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="0.5" fill="white" opacity="0.05"/></pattern></defs><rect width="100" height="20" fill="url(%23grain)"/></svg>');
        }

        .header-content {
            position: relative;
            z-index: 1;
        }

        .header-top {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 16px;
        }

        .header-title {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .back-button {
            background: rgba(255,255,255,0.15);
            border: none;
            border-radius: 12px;
            color: white;
            width: 40px;
            height: 40px;
            font-size: 18px;
            cursor: pointer;
            backdrop-filter: blur(10px);
            transition: all 0.2s ease;
        }

        .back-button:hover {
            background: rgba(255,255,255,0.25);
        }

        .page-title {
            font-size: 18px;
            font-weight: 700;
            letter-spacing: -0.025em;
        }

        .header-actions {
            display: flex;
            gap: 8px;
        }

        .action-btn {
            width: 40px;
            height: 40px;
            background: rgba(255,255,255,0.15);
            border: none;
            border-radius: 12px;
            color: white;
            font-size: 16px;
            cursor: pointer;
            backdrop-filter: blur(10px);
            transition: all 0.2s ease;
        }

        .action-btn:hover {
            background: rgba(255,255,255,0.25);
        }

        .device-info {
            background: rgba(255,255,255,0.15);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            padding: 16px;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .device-icon {
            font-size: 32px;
        }

        .device-details h3 {
            font-size: 16px;
            font-weight: 700;
            margin-bottom: 4px;
        }

        .device-details p {
            font-size: 14px;
            opacity: 0.9;
        }

        /* Content */
        .content {
            padding: 20px;
        }

        .section {
            background: white;
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
        }

        .section-title {
            font-size: 16px;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
        }

        .info-item {
            background: #f8fafc;
            border-radius: 8px;
            padding: 12px;
        }

        .info-label {
            font-size: 12px;
            color: #64748b;
            margin-bottom: 4px;
        }

        .info-value {
            font-size: 14px;
            font-weight: 600;
            color: #1e293b;
        }

        .problem-description {
            background: #fef3c7;
            border: 1px solid #f59e0b;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 16px;
        }

        .verification-badges {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-top: 12px;
        }

        .badge {
            background: #dbeafe;
            color: #1e40af;
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 11px;
            font-weight: 600;
        }

        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
        }

        .status-warning {
            background: #fef3c7;
            color: #92400e;
        }

        .status-success {
            background: #d1fae5;
            color: #065f46;
        }

        .status-danger {
            background: #fee2e2;
            color: #991b1b;
        }

        /* Propostas */
        .proposta-card {
            background: #f8fafc;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 12px;
            border-left: 4px solid #e2e8f0;
        }

        .proposta-card.minha {
            background: #f0f9ff;
            border-left-color: #3b82f6;
        }

        .proposta-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 8px;
        }

        .proposta-empresa {
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 4px;
        }

        .proposta-valores {
            display: flex;
            gap: 16px;
            margin-bottom: 8px;
        }

        .valor-item {
            text-align: center;
        }

        .valor-numero {
            font-weight: 700;
            font-size: 16px;
            color: #059669;
        }

        .valor-label {
            font-size: 11px;
            color: #64748b;
        }

        /* Botões flutuantes */
        .floating-actions {
            position: fixed;
            bottom: 80px;
            right: 20px;
            z-index: 1000;
        }

        .fab {
            width: 56px;
            height: 56px;
            background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
            border: none;
            border-radius: 50%;
            color: white;
            font-size: 20px;
            cursor: pointer;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
            transition: all 0.2s ease;
            margin-bottom: 12px;
            display: block;
        }

        .fab:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
        }

        .fab.success {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
        }

        /* Modal */
        .modal {
            display: none;
            position: fixed;
            z-index: 2000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            backdrop-filter: blur(4px);
        }

        .modal-content {
            background: white;
            margin: 20px;
            border-radius: 16px;
            max-height: calc(100vh - 40px);
            overflow-y: auto;
        }

        .modal-header {
            background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
            color: white;
            padding: 20px;
            border-radius: 16px 16px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-title {
            font-size: 18px;
            font-weight: 700;
            margin: 0;
        }

        .modal-close {
            background: none;
            border: none;
            color: white;
            font-size: 24px;
            cursor: pointer;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: background 0.2s ease;
        }

        .modal-close:hover {
            background: rgba(255,255,255,0.2);
        }

        .modal-body {
            padding: 20px;
        }

        .modal-footer {
            padding: 20px;
            border-top: 1px solid #e2e8f0;
            display: flex;
            gap: 12px;
        }

        .form-group {
            margin-bottom: 16px;
        }

        .form-label {
            display: block;
            margin-bottom: 4px;
            font-weight: 600;
            color: #374151;
            font-size: 14px;
        }

        .form-input, .form-select, .form-textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.2s ease;
        }

        .form-input:focus, .form-select:focus, .form-textarea:focus {
            outline: none;
            border-color: #3b82f6;
        }

        .input-group {
            display: flex;
        }

        .input-group-text {
            background: #f3f4f6;
            border: 2px solid #e5e7eb;
            border-right: none;
            padding: 12px;
            border-radius: 8px 0 0 8px;
            font-weight: 600;
        }

        .input-group .form-input {
            border-left: none;
            border-radius: 0 8px 8px 0;
        }

        .form-check {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .form-check-input {
            width: 20px;
            height: 20px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 14px;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
            color: white;
            flex: 1;
        }

        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }

        .btn-secondary {
            background: #f1f5f9;
            color: #64748b;
            flex: 1;
        }

        .btn-secondary:hover {
            background: #e2e8f0;
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none !important;
        }

        .resume-card {
            background: #f0f9ff;
            border: 1px solid #bfdbfe;
            border-radius: 12px;
            padding: 16px;
            margin-top: 16px;
        }

        .resume-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
        }

        .resume-item {
            text-align: center;
        }

        .resume-value {
            font-weight: 700;
            font-size: 16px;
            margin-bottom: 2px;
        }

        .resume-label {
            font-size: 11px;
            color: #64748b;
        }

        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            max-width: 414px;
            width: 100%;
            background: white;
            border-top: 1px solid #e2e8f0;
            display: flex;
            justify-content: space-around;
            padding: 8px 0;
            z-index: 1000;
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-decoration: none;
            color: #64748b;
            transition: color 0.2s ease;
            padding: 8px 12px;
            border-radius: 8px;
            min-width: 60px;
        }

        .nav-item:hover, .nav-item.active {
            color: #3b82f6;
        }

        .nav-icon {
            font-size: 20px;
            margin-bottom: 4px;
        }

        .nav-label {
            font-size: 11px;
            font-weight: 500;
        }

        .empty-state {
            text-align: center;
            padding: 40px 20px;
            color: #64748b;
        }

        .empty-icon {
            font-size: 48px;
            margin-bottom: 16px;
        }

        /* Responsive */
        @media (min-width: 768px) {
            .container {
                max-width: 1200px;
                padding-bottom: 40px;
            }

            .header {
                padding: 40px 40px 30px 40px;
            }

            .content {
                padding: 30px 40px;
            }

            .info-grid {
                grid-template-columns: repeat(3, 1fr);
            }

            .bottom-nav {
                display: none;
            }

            .floating-actions {
                bottom: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="header-content">
                <div class="header-top">
                    <div class="header-title">
                        <button class="back-button" onclick="window.location.href='solicitacoes.php'">
                            <i class="fas fa-arrow-left"></i>
                        </button>
                        <h1 class="page-title">Detalhes</h1>
                    </div>
                    <div class="header-actions">
                        <button class="action-btn" onclick="window.location.href='index.php'" title="Área do cliente">
                            <i class="fas fa-user"></i>
                        </button>
                        <button class="action-btn" onclick="window.location.href='logout.php'" title="Sair">
                            <i class="fas fa-sign-out-alt"></i>
                        </button>
                    </div>
                </div>

                <div class="device-info">
                    <div class="device-icon">
                        <?php echo getDeviceIcon($solicitacao['dispositivo']); ?>
                    </div>
                    <div class="device-details">
                        <h3><?php echo e($solicitacao['marca'] . ' ' . $solicitacao['modelo']); ?></h3>
                        <p>Solicitação #<?php echo $solicitacao['id']; ?> • <?php echo e($solicitacao['cliente_nome']); ?></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Content -->
        <div class="content">
            <!-- Informações do Dispositivo -->
            <div class="section">
                <div class="section-title">
                    <i class="fas fa-mobile-alt"></i>
                    Informações do Dispositivo
                </div>
                <div class="info-grid">
                    <div class="info-item">
                        <div class="info-label">Dispositivo</div>
                        <div class="info-value"><?php echo e($solicitacao['dispositivo']); ?></div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Marca</div>
                        <div class="info-value"><?php echo e($solicitacao['marca']); ?></div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Modelo</div>
                        <div class="info-value"><?php echo e($solicitacao['modelo']); ?></div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Memória</div>
                        <div class="info-value"><?php echo e($solicitacao['memoria']); ?></div>
                    </div>
                </div>
            </div>

            <!-- Descrição do Problema -->
            <div class="section">
                <div class="section-title">
                    <i class="fas fa-exclamation-triangle"></i>
                    Problema Relatado
                </div>
                <div class="problem-description">
                    <strong>Descrição:</strong><br>
                    <?php echo nl2br(e($solicitacao['descricao_problema'])); ?>
                    
                    <?php if (!empty($solicitacao['descricao_detalhada'])): ?>
                    <br><br>
                    <strong>Detalhes:</strong><br>
                    <?php echo nl2br(e($solicitacao['descricao_detalhada'])); ?>
                    <?php endif; ?>
                </div>

                <?php if (!empty($solicitacao['verificacoes'])): ?>
                <div>
                    <strong>Verificações Realizadas:</strong>
                    <div class="verification-badges">
                        <?php 
                        $verificacoes = explode(',', $solicitacao['verificacoes']);
                        $verificacoes_labels = [
                            'conector_carga' => 'Conector de Carga',
                            'entrada_fone' => 'Entrada de Fone',
                            'riscos' => 'Riscos na Tela',
                            'cameras' => 'Câmeras',
                            'wifi' => 'Wi-Fi',
                            'bluetooth' => 'Bluetooth',
                            'nao_possivel_verificar' => 'Não foi possível verificar'
                        ];
                        
                        foreach ($verificacoes as $verificacao) {
                            $verificacao = trim($verificacao);
                            if (isset($verificacoes_labels[$verificacao])) {
                                echo '<span class="badge">' . $verificacoes_labels[$verificacao] . '</span>';
                            }
                        }
                        ?>
                    </div>
                </div>
                <?php endif; ?>

                <?php if (!empty($solicitacao['video'])): ?>
                <div style="margin-top: 16px;">
                    <button type="button" class="btn btn-secondary" onclick="verVideo('<?php echo e($solicitacao['video']); ?>')">
                        <i class="fas fa-play"></i>
                        Ver Vídeo do Problema
                    </button>
                </div>
                <?php endif; ?>
            </div>

            <!-- Informações do Cliente -->
            <div class="section">
                <div class="section-title">
                    <i class="fas fa-user"></i>
                    Cliente
                </div>
                <div class="info-grid">
                    <div class="info-item">
                        <div class="info-label">Nome</div>
                        <div class="info-value"><?php echo e($solicitacao['cliente_nome']); ?></div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Telefone</div>
                        <div class="info-value">
                            <a href="tel:<?php echo e($solicitacao['cliente_telefone']); ?>" style="color: inherit; text-decoration: none;">
                                <?php echo e($solicitacao['cliente_telefone']); ?>
                            </a>
                        </div>
                    </div>
                </div>
                <div style="margin-top: 12px;">
                    <div class="info-label">Endereço</div>
                    <div class="info-value"><?php echo e($solicitacao['cliente_endereco']); ?></div>
                </div>
            </div>

            <!-- Informações da Solicitação -->
            <div class="section">
                <div class="section-title">
                    <i class="fas fa-info-circle"></i>
                    Status da Solicitação
                </div>
                <div style="margin-bottom: 16px;">
                    <?php
                    $status_class = [
                        'enviado' => 'status-warning',
                        'aceita' => 'status-success',
                        'rejeitada' => 'status-danger',
                        'concluido' => 'status-success'
                    ];
                    $status_text = [
                        'enviado' => 'Pendente',
                        'aceita' => 'Aceita',
                        'rejeitada' => 'Rejeitada',
                        'concluido' => 'Concluída'
                    ];
                    ?>
                    <span class="status-badge <?php echo $status_class[$solicitacao['status']]; ?>">
                        <?php echo $status_text[$solicitacao['status']]; ?>
                    </span>
                </div>
                <div class="info-grid">
                    <div class="info-item">
                        <div class="info-label">Data</div>
                        <div class="info-value"><?php echo formatDate($solicitacao['data_solicitacao'], 'd/m/Y H:i'); ?></div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Entrega</div>
                        <div class="info-value"><?php echo e($solicitacao['metodo_entrega']); ?></div>
                    </div>
                </div>
            </div>

            <!-- Propostas -->
            <div class="section">
                <div class="section-title">
                    <i class="fas fa-paper-plane"></i>
                    Propostas (<?php echo count($propostas); ?>)
                </div>

                <?php if (empty($propostas)): ?>
                    <div class="empty-state">
                        <div class="empty-icon">📄</div>
                        <h6>Nenhuma proposta ainda</h6>
                        <p>Seja o primeiro a enviar uma proposta!</p>
                    </div>
                <?php else: ?>
                    <?php foreach ($propostas as $proposta): ?>
                        <div class="proposta-card <?php echo $proposta['assistencia_id'] == $usuario['assistencia_id'] ? 'minha' : ''; ?>">
                            <div class="proposta-empresa">
                                <?php echo e($proposta['nome_empresa']); ?>
                                <?php if ($proposta['assistencia_id'] == $usuario['assistencia_id']): ?>
                                    <span class="badge" style="background: #3b82f6; color: white; margin-left: 8px;">Sua Proposta</span>
                                <?php endif; ?>
                            </div>
                            
                            <div class="proposta-valores">
                                <div class="valor-item">
                                    <div class="valor-numero"><?php echo formatCurrency($proposta['preco']); ?></div>
                                    <div class="valor-label">Preço</div>
                                </div>
                                <div class="valor-item">
                                    <div class="valor-numero"><?php echo $proposta['prazo']; ?> dias</div>
                                    <div class="valor-label">Prazo</div>
                                </div>
                            </div>

                            <?php if (!empty($proposta['observacoes'])): ?>
                            <div style="margin-top: 8px; padding-top: 8px; border-top: 1px solid #e2e8f0;">
                                <div style="font-size: 13px; color: #64748b;">
                                    <?php echo nl2br(e($proposta['observacoes'])); ?>
                                </div>
                            </div>
                            <?php endif; ?>

                            <div style="margin-top: 8px; display: flex; justify-content: space-between; align-items: center;">
                                <small style="color: #64748b;">
                                    <?php echo formatDate($proposta['data_proposta'], 'd/m/Y H:i'); ?>
                                </small>
                                <span class="status-badge status-<?php echo $proposta['status'] === 'enviada' ? 'warning' : ($proposta['status'] === 'aceita' ? 'success' : 'danger'); ?>">
                                    <?php echo e($proposta['status']); ?>
                                </span>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Floating Action Button -->
    <?php if ($solicitacao['status'] === 'enviado' && !$minha_proposta): ?>
    <div class="floating-actions">
        <button class="fab success" onclick="abrirModalProposta()" title="Enviar Proposta">
            <i class="fas fa-paper-plane"></i>
        </button>
    </div>
    <?php endif; ?>

    <!-- Modal Enviar Proposta -->
    <div id="propostaModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-paper-plane"></i>
                    Enviar Proposta
                </h5>
                <button class="modal-close" onclick="fecharModal()">&times;</button>
            </div>
            <div class="modal-body">
                <form id="formProposta">
                    <input type="hidden" name="solicitacao_id" value="<?php echo $solicitacao['id']; ?>">
                    
                    <div class="form-group">
                        <label for="preco" class="form-label">
                            <i class="fas fa-dollar-sign"></i> Preço do Reparo *
                        </label>
                        <div class="input-group">
                            <span class="input-group-text">R$</span>
                            <input type="text" class="form-input" id="preco" name="preco" 
                                   placeholder="0,00" required>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="prazo" class="form-label">
                            <i class="fas fa-calendar"></i> Prazo (dias) *
                        </label>
                        <select class="form-select" id="prazo" name="prazo" required>
                            <option value="">Selecione o prazo</option>
                            <option value="1">1 dia (Express)</option>
                            <option value="2">2 dias</option>
                            <option value="3">3 dias</option>
                            <option value="5">5 dias</option>
                            <option value="7">1 semana</option>
                            <option value="10">10 dias</option>
                            <option value="15">15 dias</option>
                            <option value="20">20 dias</option>
                            <option value="30">30 dias</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="observacoes" class="form-label">
                            <i class="fas fa-comment"></i> Observações
                        </label>
                        <textarea class="form-textarea" id="observacoes" name="observacoes" rows="3"
                                  placeholder="Descreva detalhes sobre o reparo, peças, garantia..."></textarea>
                    </div>
                    
                    <div class="form-group">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="retirada_expressa" name="retirada_expressa">
                            <label class="form-check-label" for="retirada_expressa">
                                <i class="fas fa-motorcycle"></i>
                                <strong>Retirada Express</strong><br>
                                <small>Buscar e entregar na residência</small>
                            </label>
                        </div>
                    </div>
                    
                    <!-- Resumo -->
                    <div class="resume-card">
                        <div class="section-title" style="margin-bottom: 12px;">
                            <i class="fas fa-calculator"></i>
                            Resumo da Proposta
                        </div>
                        <div class="resume-grid">
                            <div class="resume-item">
                                <div class="resume-value" style="color: #059669;" id="resumoPreco">R$ 0,00</div>
                                <div class="resume-label">Valor Total</div>
                            </div>
                            <div class="resume-item">
                                <div class="resume-value" style="color: #3b82f6;" id="resumoPrazo">-</div>
                                <div class="resume-label">Prazo</div>
                            </div>
                            <div class="resume-item">
                                <div class="resume-value" style="color: #dc2626;" id="resumoTaxa">R$ 0,00</div>
                                <div class="resume-label">Taxa (15%)</div>
                            </div>
                            <div class="resume-item">
                                <div class="resume-value" style="color: #059669;" id="resumoRecebera">R$ 0,00</div>
                                <div class="resume-label">Você Recebe</div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="fecharModal()">
                    <i class="fas fa-times"></i> Cancelar
                </button>
                <button type="button" class="btn btn-primary" onclick="enviarProposta()" id="btnEnviarProposta">
                    <i class="fas fa-paper-plane"></i> Enviar Proposta
                </button>
            </div>
        </div>
    </div>

    <!-- Modal Video -->
    <div id="videoModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-play"></i>
                    Vídeo do Problema
                </h5>
                <button class="modal-close" onclick="fecharVideoModal()">&times;</button>
            </div>
            <div class="modal-body">
                <video id="videoPlayer" style="width: 100%; border-radius: 8px;" controls>
                    Seu navegador não suporta vídeos.
                </video>
            </div>
        </div>
    </div>

    <!-- Bottom Navigation -->
    <div class="bottom-nav">
        <a href="dashboard.php" class="nav-item">
            <div class="nav-icon"><i class="fas fa-home"></i></div>
            <div class="nav-label">Início</div>
        </a>
        <a href="solicitacoes.php" class="nav-item active">
            <div class="nav-icon"><i class="fas fa-clipboard-list"></i></div>
            <div class="nav-label">Solicitações</div>
        </a>
        <a href="reparos.php" class="nav-item">
            <div class="nav-icon"><i class="fas fa-tools"></i></div>
            <div class="nav-label">Reparos</div>
        </a>
        <a href="marketplace.php" class="nav-item">
            <div class="nav-icon"><i class="fas fa-shopping-cart"></i></div>
            <div class="nav-label">Loja</div>
        </a>
        <a href="perfil.php" class="nav-item">
            <div class="nav-icon"><i class="fas fa-user"></i></div>
            <div class="nav-label">Perfil</div>
        </a>
    </div>

    <script>
        function abrirModalProposta() {
            document.getElementById('propostaModal').style.display = 'block';
            document.getElementById('formProposta').reset();
            atualizarResumo();
        }

        function fecharModal() {
            document.getElementById('propostaModal').style.display = 'none';
        }

        function verVideo(videoUrl) {
            const modal = document.getElementById('videoModal');
            const video = document.getElementById('videoPlayer');
            video.src = videoUrl;
            modal.style.display = 'block';
        }

        function fecharVideoModal() {
            const modal = document.getElementById('videoModal');
            const video = document.getElementById('videoPlayer');
            modal.style.display = 'none';
            video.pause();
            video.currentTime = 0;
        }

        function formatarPreco(input) {
            let valor = input.value.replace(/\D/g, '');
            
            if (valor.length === 0) {
                input.value = '';
                atualizarResumo();
                return;
            }
            
            valor = valor.padStart(3, '0');
            valor = valor.slice(0, -2) + ',' + valor.slice(-2);
            valor = valor.replace(/\B(?=(\d{3})+(?!\d))/g, '.');
            
            input.value = valor;
            atualizarResumo();
        }

        function atualizarResumo() {
            const precoInput = document.getElementById('preco').value;
            const prazoSelect = document.getElementById('prazo');
            
            // Converter preço para número
            let preco = 0;
            if (precoInput) {
                preco = parseFloat(precoInput.replace(/\./g, '').replace(',', '.')) || 0;
            }
            
            // Calcular valores
            const taxa = preco * 0.15;
            const recebera = preco - taxa;
            
            // Atualizar resumo
            document.getElementById('resumoPreco').textContent = 'R$ ' + preco.toLocaleString('pt-BR', {minimumFractionDigits: 2});
            document.getElementById('resumoTaxa').textContent = 'R$ ' + taxa.toLocaleString('pt-BR', {minimumFractionDigits: 2});
            document.getElementById('resumoRecebera').textContent = 'R$ ' + recebera.toLocaleString('pt-BR', {minimumFractionDigits: 2});
            
            // Atualizar prazo
            const prazoTexto = prazoSelect.value ? prazoSelect.options[prazoSelect.selectedIndex].text : '-';
            document.getElementById('resumoPrazo').textContent = prazoTexto;
        }

        function enviarProposta() {
            const form = document.getElementById('formProposta');
            const btnEnviar = document.getElementById('btnEnviarProposta');
            
            // Validar campos obrigatórios
            const preco = document.getElementById('preco').value;
            const prazo = document.getElementById('prazo').value;
            
            if (!preco || !prazo) {
                alert('Por favor, preencha o preço e o prazo.');
                return;
            }
            
            // Desabilitar botão durante envio
            btnEnviar.disabled = true;
            btnEnviar.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Enviando...';
            
            // Preparar dados do formulário
            const formData = new FormData(form);
            
            // Enviar via AJAX
            fetch('ajax/enviar_proposta.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    fecharModal();
                    alert('Proposta enviada com sucesso!');
                    window.location.reload();
                } else {
                    alert('Erro: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Erro:', error);
                alert('Erro ao enviar proposta. Tente novamente.');
            })
            .finally(() => {
                btnEnviar.disabled = false;
                btnEnviar.innerHTML = '<i class="fas fa-paper-plane"></i> Enviar Proposta';
            });
        }

        // Event listeners
        document.addEventListener('DOMContentLoaded', function() {
            // Formatação de preço
            document.getElementById('preco').addEventListener('input', function() {
                formatarPreco(this);
            });
            
            // Atualizar resumo quando prazo mudar
            document.getElementById('prazo').addEventListener('change', atualizarResumo);
            
            // Fechar modal clicando fora
            window.onclick = function(event) {
                const modalProposta = document.getElementById('propostaModal');
                const modalVideo = document.getElementById('videoModal');
                if (event.target === modalProposta) {
                    fecharModal();
                }
                if (event.target === modalVideo) {
                    fecharVideoModal();
                }
            }
            
            // Inicializar resumo
            atualizarResumo();
        });
    </script>
</body>
</html>
