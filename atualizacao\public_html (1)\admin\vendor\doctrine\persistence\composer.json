{"name": "doctrine/persistence", "type": "library", "description": "The Doctrine Persistence project is a set of shared interfaces and functionality that the different Doctrine object mappers share.", "keywords": ["persistence", "object", "mapper", "orm", "odm"], "homepage": "https://www.doctrine-project.org/projects/persistence.html", "license": "MIT", "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "require": {"php": "^7.2 || ^8.0", "doctrine/event-manager": "^1 || ^2", "psr/cache": "^1.0 || ^2.0 || ^3.0"}, "require-dev": {"phpstan/phpstan": "1.12.7", "phpstan/phpstan-phpunit": "^1", "phpstan/phpstan-strict-rules": "^1.1", "doctrine/coding-standard": "^12", "doctrine/common": "^3.0", "phpunit/phpunit": "^8.5.38 || ^9.5", "symfony/cache": "^4.4 || ^5.4 || ^6.0 || ^7.0"}, "conflict": {"doctrine/common": "<2.10"}, "autoload": {"psr-4": {"Doctrine\\Persistence\\": "src/Persistence"}}, "autoload-dev": {"psr-4": {"Doctrine\\Tests\\": "tests", "Doctrine\\Tests_PHP74\\": "tests_php74", "Doctrine\\Tests_PHP81\\": "tests_php81"}}, "config": {"allow-plugins": {"dealerdirect/phpcodesniffer-composer-installer": true, "composer/package-versions-deprecated": true}}}