<?php
/**
 * Chat - Mobile First
 * FixFácil Assistências
 */

// Redirecionar para versão mobile final
header('Location: chat.php?' . $_SERVER['QUERY_STRING']);
exit();
?>

// Iniciar sessão se não estiver iniciada
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Verificar se o usuário está logado
if (!isset($_SESSION['usuario_id']) || $_SESSION['tipo_usuario'] !== 'assistencia') {
    header('Location: ../login.php');
    exit();
}

// Incluir configurações básicas
require_once 'config/database.php';

// Configuração de banco de dados
$db = getDatabase();
$mysqli = $db->getConnection();

// Obter dados do usuário logado
$usuario_id = $_SESSION['usuario_id'];
$usuario = null;

try {
    // Buscar dados do usuário
    $sql = "SELECT u.id, u.nome, u.email, u.telefone, u.plano_id, at.id as assistencia_id 
            FROM usuarios u 
            LEFT JOIN assistencia_tecnica at ON u.id = at.usuario_id 
            WHERE u.id = ?";
    $stmt = $mysqli->prepare($sql);
    $stmt->bind_param("i", $usuario_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $usuario = $result->fetch_assoc();
    
} catch (Exception $e) {
    error_log("Erro ao buscar dados do usuário: " . $e->getMessage());
    $usuario = ['nome' => 'Usuário', 'email' => '', 'assistencia_id' => null];
}

// Obter conversas ativas
$conversas = [];
try {
    if ($usuario && isset($usuario['assistencia_id'])) {
        $sql = "SELECT DISTINCT
                    c.id,
                    c.usuario_id,
                    c.assistencia_id,
                    u.nome as cliente_nome,
                    u.telefone as cliente_telefone,
                    sr.id as solicitacao_id,
                    sr.titulo as solicitacao_titulo,
                    sr.status as solicitacao_status,
                    (SELECT mensagem FROM mensagens_chat mc WHERE mc.conversa_id = c.id ORDER BY mc.data_envio DESC LIMIT 1) as ultima_mensagem,
                    (SELECT data_envio FROM mensagens_chat mc WHERE mc.conversa_id = c.id ORDER BY mc.data_envio DESC LIMIT 1) as ultima_data,
                    (SELECT COUNT(*) FROM mensagens_chat mc WHERE mc.conversa_id = c.id AND mc.remetente_tipo = 'usuario' AND mc.lida = 0) as mensagens_nao_lidas
                FROM conversas_chat c
                JOIN usuarios u ON c.usuario_id = u.id
                LEFT JOIN solicitacoes_reparo sr ON c.solicitacao_id = sr.id
                WHERE c.assistencia_id = ?
                ORDER BY ultima_data DESC";
        
        $stmt = $mysqli->prepare($sql);
        $stmt->bind_param("i", $usuario['assistencia_id']);
        $stmt->execute();
        $result = $stmt->get_result();
        
        while ($row = $result->fetch_assoc()) {
            $conversas[] = $row;
        }
    }
} catch (Exception $e) {
    error_log("Erro ao buscar conversas: " . $e->getMessage());
}

// Obter estatísticas
$stats = [
    'conversas_ativas' => 0,
    'mensagens_nao_lidas' => 0,
    'resposta_media' => 0
];

try {
    if ($usuario && isset($usuario['assistencia_id'])) {
        $assistencia_id = $usuario['assistencia_id'];
        
        // Conversas ativas
        $sql = "SELECT COUNT(*) as total FROM conversas_chat WHERE assistencia_id = ? AND status = 'ativa'";
        $stmt = $mysqli->prepare($sql);
        $stmt->bind_param("i", $assistencia_id);
        $stmt->execute();
        $result = $stmt->get_result();
        $stats['conversas_ativas'] = $result->fetch_assoc()['total'];
        
        // Mensagens não lidas
        $sql = "SELECT COUNT(*) as total FROM mensagens_chat mc 
                JOIN conversas_chat c ON mc.conversa_id = c.id 
                WHERE c.assistencia_id = ? AND mc.remetente_tipo = 'usuario' AND mc.lida = 0";
        $stmt = $mysqli->prepare($sql);
        $stmt->bind_param("i", $assistencia_id);
        $stmt->execute();
        $result = $stmt->get_result();
        $stats['mensagens_nao_lidas'] = $result->fetch_assoc()['total'];
        
        // Tempo médio de resposta (em minutos)
        $sql = "SELECT AVG(TIMESTAMPDIFF(MINUTE, 
                    (SELECT MAX(data_envio) FROM mensagens_chat mc1 
                     WHERE mc1.conversa_id = mc.conversa_id 
                     AND mc1.remetente_tipo = 'usuario' 
                     AND mc1.data_envio < mc.data_envio),
                    mc.data_envio
                )) as tempo_medio
                FROM mensagens_chat mc 
                JOIN conversas_chat c ON mc.conversa_id = c.id 
                WHERE c.assistencia_id = ? 
                AND mc.remetente_tipo = 'assistencia'
                AND DATE(mc.data_envio) = CURDATE()";
        $stmt = $mysqli->prepare($sql);
        $stmt->bind_param("i", $assistencia_id);
        $stmt->execute();
        $result = $stmt->get_result();
        $tempo = $result->fetch_assoc()['tempo_medio'];
        $stats['resposta_media'] = $tempo ? round($tempo) : 0;
    }
} catch (Exception $e) {
    error_log("Erro ao obter estatísticas: " . $e->getMessage());
}

// Função para tempo decorrido
function tempoDecorrido($data) {
    if (!$data) return 'Nunca';
    
    $now = new DateTime();
    $past = new DateTime($data);
    $diff = $now->diff($past);
    
    if ($diff->d > 0) {
        return $diff->d . ' dias';
    } elseif ($diff->h > 0) {
        return $diff->h . 'h';
    } else {
        return $diff->i . ' min';
    }
}

// Função para obter status da solicitação
function getStatusIcon($status) {
    switch ($status) {
        case 'aguardando_resposta':
            return '⏳';
        case 'em_andamento':
            return '🔧';
        case 'concluido':
            return '✅';
        case 'cancelado':
            return '❌';
        default:
            return '💬';
    }
}
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FixFacil - Chat</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            min-height: 100vh;
            color: #1e293b;
        }

        .container {
            max-width: 414px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
            position: relative;
            overflow-x: hidden;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }

        .header {
            background: linear-gradient(135deg, #059669 0%, #065f46 100%);
            color: white;
            padding: 24px 20px 20px 20px;
            position: relative;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 20"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="0.5" fill="white" opacity="0.05"/></pattern></defs><rect width="100" height="20" fill="url(%23grain)"/></svg>');
        }

        .header-content {
            position: relative;
            z-index: 1;
        }

        .header-top {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 20px;
        }

        .page-title {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .back-btn {
            width: 40px;
            height: 40px;
            background: rgba(255,255,255,0.15);
            border: none;
            border-radius: 12px;
            color: white;
            font-size: 18px;
            cursor: pointer;
            backdrop-filter: blur(10px);
            transition: all 0.2s ease;
            text-decoration: none;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .back-btn:hover {
            background: rgba(255,255,255,0.25);
        }

        .page-info h1 {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 2px;
        }

        .page-info p {
            font-size: 12px;
            opacity: 0.9;
        }

        .header-actions {
            display: flex;
            gap: 8px;
        }

        .action-btn {
            width: 40px;
            height: 40px;
            background: rgba(255,255,255,0.15);
            border: none;
            border-radius: 12px;
            color: white;
            font-size: 18px;
            cursor: pointer;
            backdrop-filter: blur(10px);
            transition: all 0.2s ease;
            text-decoration: none;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }

        .action-btn:hover {
            background: rgba(255,255,255,0.25);
        }

        .notification-badge {
            position: absolute;
            top: -2px;
            right: -2px;
            background: #ef4444;
            color: white;
            border-radius: 50%;
            width: 16px;
            height: 16px;
            font-size: 10px;
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 12px;
        }

        .stat-card {
            background: rgba(255,255,255,0.15);
            border-radius: 12px;
            padding: 16px 12px;
            text-align: center;
            backdrop-filter: blur(10px);
        }

        .stat-number {
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 4px;
        }

        .stat-label {
            font-size: 11px;
            opacity: 0.8;
        }

        .content {
            padding: 20px;
            padding-bottom: 100px;
        }

        .conversations-list {
            background: white;
            border-radius: 16px;
            margin-bottom: 20px;
            overflow: hidden;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
        }

        .section-title {
            font-size: 18px;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .conversation-item {
            padding: 16px 20px;
            border-bottom: 1px solid #f1f5f9;
            display: flex;
            gap: 12px;
            cursor: pointer;
            transition: background 0.2s ease;
            text-decoration: none;
            color: inherit;
        }

        .conversation-item:last-child {
            border-bottom: none;
        }

        .conversation-item:hover {
            background: #f8fafc;
        }

        .conversation-avatar {
            width: 48px;
            height: 48px;
            background: #f0fdf4;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            color: #059669;
            flex-shrink: 0;
            position: relative;
        }

        .conversation-avatar.unread {
            background: #fef2f2;
            color: #ef4444;
        }

        .conversation-avatar.active {
            background: #eff6ff;
            color: #3b82f6;
        }

        .unread-badge {
            position: absolute;
            top: -2px;
            right: -2px;
            background: #ef4444;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 10px;
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .conversation-info {
            flex: 1;
        }

        .conversation-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 4px;
        }

        .conversation-name {
            font-size: 16px;
            font-weight: 600;
            color: #1e293b;
        }

        .conversation-time {
            font-size: 12px;
            color: #64748b;
        }

        .conversation-preview {
            font-size: 14px;
            color: #64748b;
            margin-bottom: 8px;
            line-height: 1.3;
        }

        .conversation-preview.unread {
            color: #1e293b;
            font-weight: 600;
        }

        .conversation-tags {
            display: flex;
            gap: 6px;
            align-items: center;
        }

        .conversation-tag {
            background: #f1f5f9;
            color: #64748b;
            border-radius: 6px;
            padding: 2px 6px;
            font-size: 10px;
            font-weight: 500;
        }

        .conversation-tag.solicitation {
            background: #eff6ff;
            color: #3b82f6;
        }

        .conversation-tag.status {
            background: #ecfdf5;
            color: #059669;
        }

        .empty-state {
            text-align: center;
            padding: 40px 20px;
            color: #64748b;
        }

        .empty-state-icon {
            font-size: 48px;
            margin-bottom: 16px;
        }

        .empty-state-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .empty-state-description {
            font-size: 14px;
            margin-bottom: 20px;
        }

        .quick-actions {
            background: white;
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
        }

        .actions-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
        }

        .quick-action {
            background: #f8fafc;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            padding: 16px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            color: inherit;
        }

        .quick-action:hover {
            border-color: #059669;
            background: #f0fdf4;
            transform: translateY(-2px);
        }

        .quick-action-icon {
            font-size: 28px;
            margin-bottom: 8px;
        }

        .quick-action-title {
            font-size: 14px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 2px;
        }

        .quick-action-subtitle {
            font-size: 11px;
            color: #64748b;
        }

        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 100%;
            max-width: 414px;
            background: white;
            border-top: 1px solid #e2e8f0;
            padding: 12px 20px;
            display: flex;
            justify-content: space-around;
            box-shadow: 0 -4px 20px rgba(0,0,0,0.1);
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
            cursor: pointer;
            padding: 8px;
            border-radius: 12px;
            transition: all 0.2s ease;
            position: relative;
            text-decoration: none;
            color: #64748b;
        }

        .nav-item.active {
            background: #f0fdf4;
            color: #059669;
        }

        .nav-icon {
            font-size: 20px;
        }

        .nav-label {
            font-size: 10px;
            font-weight: 500;
        }

        .nav-badge {
            position: absolute;
            top: 4px;
            right: 4px;
            background: #ef4444;
            color: white;
            border-radius: 50%;
            width: 16px;
            height: 16px;
            font-size: 10px;
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .floating-action {
            position: fixed;
            bottom: 80px;
            right: 20px;
            width: 56px;
            height: 56px;
            background: linear-gradient(135deg, #059669, #065f46);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            cursor: pointer;
            box-shadow: 0 4px 16px rgba(5, 150, 105, 0.4);
            transition: all 0.3s ease;
            z-index: 100;
            text-decoration: none;
        }

        .floating-action:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 20px rgba(5, 150, 105, 0.5);
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .content > * {
            animation: fadeIn 0.6s ease;
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="header-content">
                <div class="header-top">
                    <div class="page-title">
                        <a href="dashboard_mobile_final.php" class="back-btn">←</a>
                        <div class="page-info">
                            <h1>💬 Chat</h1>
                            <p>Conversas com clientes</p>
                        </div>
                    </div>
                    <div class="header-actions">
                        <button class="action-btn" onclick="markAllAsRead()" title="Marcar tudo como lido">
                            ✅
                            <?php if ($stats['mensagens_nao_lidas'] > 0): ?>
                                <div class="notification-badge"><?php echo $stats['mensagens_nao_lidas']; ?></div>
                            <?php endif; ?>
                        </button>
                        <button class="action-btn" onclick="refreshConversations()" title="Atualizar">
                            🔄
                        </button>
                    </div>
                </div>

                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number"><?php echo $stats['conversas_ativas']; ?></div>
                        <div class="stat-label">Conversas</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number"><?php echo $stats['mensagens_nao_lidas']; ?></div>
                        <div class="stat-label">Não lidas</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number"><?php echo $stats['resposta_media']; ?>min</div>
                        <div class="stat-label">Resp. média</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Content -->
        <div class="content">
            <!-- Quick Actions -->
            <div class="quick-actions">
                <div class="section-title">⚡ Ações rápidas</div>
                <div class="actions-grid">
                    <div class="quick-action" onclick="showTemplates()">
                        <div class="quick-action-icon">📝</div>
                        <div class="quick-action-title">Modelos</div>
                        <div class="quick-action-subtitle">Respostas rápidas</div>
                    </div>
                    <div class="quick-action" onclick="showNotifications()">
                        <div class="quick-action-icon">🔔</div>
                        <div class="quick-action-title">Notificações</div>
                        <div class="quick-action-subtitle">Configurar alertas</div>
                    </div>
                    <div class="quick-action" onclick="showHistory()">
                        <div class="quick-action-icon">📊</div>
                        <div class="quick-action-title">Histórico</div>
                        <div class="quick-action-subtitle">Conversas antigas</div>
                    </div>
                    <div class="quick-action" onclick="showSettings()">
                        <div class="quick-action-icon">⚙️</div>
                        <div class="quick-action-title">Configurações</div>
                        <div class="quick-action-subtitle">Personalizar chat</div>
                    </div>
                </div>
            </div>

            <!-- Conversations -->
            <div class="conversations-list">
                <div style="padding: 20px 20px 16px 20px; border-bottom: 1px solid #f1f5f9;">
                    <div class="section-title">💬 Conversas ativas</div>
                </div>

                <?php if (empty($conversas)): ?>
                    <div class="empty-state">
                        <div class="empty-state-icon">💬</div>
                        <div class="empty-state-title">Nenhuma conversa ativa</div>
                        <div class="empty-state-description">
                            Quando clientes entrarem em contato, as conversas aparecerão aqui.
                        </div>
                    </div>
                <?php else: ?>
                    <?php foreach ($conversas as $conversa): ?>
                        <a href="conversa.php?id=<?php echo $conversa['id']; ?>" class="conversation-item">
                            <div class="conversation-avatar <?php echo $conversa['mensagens_nao_lidas'] > 0 ? 'unread' : ($conversa['solicitacao_status'] === 'em_andamento' ? 'active' : ''); ?>">
                                <?php if ($conversa['mensagens_nao_lidas'] > 0): ?>
                                    <div class="unread-badge"><?php echo $conversa['mensagens_nao_lidas']; ?></div>
                                <?php endif; ?>
                                👤
                            </div>
                            <div class="conversation-info">
                                <div class="conversation-header">
                                    <div class="conversation-name"><?php echo htmlspecialchars($conversa['cliente_nome']); ?></div>
                                    <div class="conversation-time"><?php echo tempoDecorrido($conversa['ultima_data']); ?></div>
                                </div>
                                <div class="conversation-preview <?php echo $conversa['mensagens_nao_lidas'] > 0 ? 'unread' : ''; ?>">
                                    <?php 
                                    if ($conversa['ultima_mensagem']) {
                                        echo htmlspecialchars(substr($conversa['ultima_mensagem'], 0, 50));
                                        if (strlen($conversa['ultima_mensagem']) > 50) echo '...';
                                    } else {
                                        echo 'Conversa iniciada';
                                    }
                                    ?>
                                </div>
                                <div class="conversation-tags">
                                    <?php if ($conversa['solicitacao_titulo']): ?>
                                        <div class="conversation-tag solicitation">
                                            <?php echo getStatusIcon($conversa['solicitacao_status']); ?>
                                            <?php echo htmlspecialchars(substr($conversa['solicitacao_titulo'], 0, 20)); ?>
                                        </div>
                                    <?php endif; ?>
                                    <?php if ($conversa['cliente_telefone']): ?>
                                        <div class="conversation-tag">
                                            📞 <?php echo htmlspecialchars($conversa['cliente_telefone']); ?>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </a>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>

        <!-- Floating Action Button -->
        <a href="nova_conversa.php" class="floating-action" title="Nova conversa">
            <span>+</span>
        </a>

        <!-- Bottom Navigation -->
        <div class="bottom-nav">
            <a href="dashboard_mobile_final.php" class="nav-item">
                <div class="nav-icon">🏠</div>
                <div class="nav-label">Início</div>
            </a>
            <a href="solicitacoes.php" class="nav-item">
                <div class="nav-icon">📋</div>
                <div class="nav-label">Solicitações</div>
            </a>
            <a href="reparos.php" class="nav-item">
                <div class="nav-icon">🔧</div>
                <div class="nav-label">Reparos</div>
            </a>
            <a href="marketplace.php" class="nav-item">
                <div class="nav-icon">🛒</div>
                <div class="nav-label">Loja</div>
            </a>
            <a href="chat.php" class="nav-item active">
                <div class="nav-icon">💬</div>
                <div class="nav-label">Chat</div>
                <?php if ($stats['mensagens_nao_lidas'] > 0): ?>
                    <div class="nav-badge"><?php echo $stats['mensagens_nao_lidas']; ?></div>
                <?php endif; ?>
            </a>
        </div>
    </div>

    <script>
        // Função para mostrar notificações
        function showNotification(message) {
            const existingNotification = document.querySelector('.notification');
            if (existingNotification) {
                existingNotification.remove();
            }
            
            const notification = document.createElement('div');
            notification.className = 'notification';
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                left: 50%;
                transform: translateX(-50%);
                background: #059669;
                color: white;
                padding: 12px 20px;
                border-radius: 8px;
                font-size: 14px;
                font-weight: 600;
                z-index: 2000;
                box-shadow: 0 4px 12px rgba(5, 150, 105, 0.3);
                animation: slideDown 0.3s ease;
                max-width: 90%;
                text-align: center;
            `;
            notification.textContent = message;
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.style.animation = 'slideOut 0.3s ease';
                setTimeout(() => notification.remove(), 300);
            }, 3000);
        }

        // Funções das ações rápidas
        function showTemplates() {
            showNotification('📝 Modelos de resposta em desenvolvimento...');
        }

        function showNotifications() {
            showNotification('🔔 Configurações de notificação em desenvolvimento...');
        }

        function showHistory() {
            showNotification('📊 Histórico completo em desenvolvimento...');
        }

        function showSettings() {
            showNotification('⚙️ Configurações do chat em desenvolvimento...');
        }

        // Função para marcar todas como lidas
        function markAllAsRead() {
            const unreadCount = <?php echo $stats['mensagens_nao_lidas']; ?>;
            if (unreadCount > 0) {
                // Aqui você faria uma requisição AJAX para marcar como lidas
                showNotification(`✅ ${unreadCount} mensagem(ns) marcada(s) como lida(s)`);
                
                // Remover badges visuais
                document.querySelectorAll('.unread-badge').forEach(badge => {
                    badge.remove();
                });
                
                document.querySelectorAll('.conversation-preview.unread').forEach(preview => {
                    preview.classList.remove('unread');
                });
            } else {
                showNotification('✅ Nenhuma mensagem não lida encontrada');
            }
        }

        // Função para atualizar conversas
        function refreshConversations() {
            showNotification('🔄 Atualizando conversas...');
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        }

        // Atualização automática
        function autoUpdate() {
            // Simular nova mensagem ocasionalmente
            if (Math.random() < 0.1) { // 10% de chance
                const badges = document.querySelectorAll('.nav-badge');
                badges.forEach(badge => {
                    const current = parseInt(badge.textContent) || 0;
                    badge.textContent = current + 1;
                    badge.classList.add('pulse');
                    setTimeout(() => badge.classList.remove('pulse'), 2000);
                });
            }
        }

        // Inicialização
        document.addEventListener('DOMContentLoaded', function() {
            // Mensagem de boas-vindas
            setTimeout(() => {
                const conversasAtivas = <?php echo $stats['conversas_ativas']; ?>;
                const mensagensNaoLidas = <?php echo $stats['mensagens_nao_lidas']; ?>;
                
                if (mensagensNaoLidas > 0) {
                    showNotification(`💬 ${mensagensNaoLidas} mensagem(ns) não lida(s) aguardando resposta!`);
                } else if (conversasAtivas > 0) {
                    showNotification(`💬 ${conversasAtivas} conversa(s) ativa(s). Tudo em dia!`);
                } else {
                    showNotification('💬 Nenhuma conversa ativa no momento.');
                }
            }, 1000);
            
            // Atualização automática a cada 30 segundos
            setInterval(autoUpdate, 30000);
        });

        // Adicionar CSS das animações
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideDown {
                from {
                    opacity: 0;
                    transform: translateX(-50%) translateY(-20px);
                }
                to {
                    opacity: 1;
                    transform: translateX(-50%) translateY(0);
                }
            }
            
            @keyframes slideOut {
                from {
                    opacity: 1;
                    transform: translateX(-50%) translateY(0);
                }
                to {
                    opacity: 0;
                    transform: translateX(-50%) translateY(-20px);
                }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
