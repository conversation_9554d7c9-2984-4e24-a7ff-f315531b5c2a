<?php
/**
 * Dashboard Principal
 * FixFácil Assistências - Sistema Novo
 */

// Configurar relatório de erros para debug
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Iniciar sessão se não estiver iniciada
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

try {
    require_once 'config/auth.php';
    require_once 'config/database.php';
    require_once 'includes/layout.php';

    // Verificar autenticação
    $auth = getAuth();
    $auth->checkAssistenciaAuth();

    // Obter dados do usuário
    $usuario = $auth->getUsuarioLogado();
    if (!$usuario) {
        throw new Exception("Usuário não encontrado");
    }

    $plano = $auth->getPlanoInfo($usuario['id']);
    if (!$plano) {
        throw new Exception("Plano não encontrado");
    }

    $db = getDatabase();
    if (!$db) {
        throw new Exception("Erro na conexão com banco de dados");
    }

} catch (Exception $e) {
    error_log("Erro no dashboard: " . $e->getMessage());
    die("Erro interno do servidor. Verifique os logs para mais detalhes.");
}

// Obter estatísticas
$stats = [];

try {
    // Verificar se assistencia_id existe
    $assistencia_id = $usuario['assistencia_id'] ?? null;
    if (!$assistencia_id) {
        throw new Exception("ID da assistência não encontrado");
    }

    // Solicitações pendentes (todas as solicitações com status 'enviado')
    $result = $db->query("SELECT COUNT(*) as count FROM solicitacoes_reparo WHERE status = 'enviado' AND visivel = 1");
    $row = $result->fetch_assoc();
    $stats['solicitacoes_pendentes'] = $row ? (int)$row['count'] : 0;

    // Propostas enviadas por esta assistência
    $result = $db->query("SELECT COUNT(*) as count FROM propostas_assistencia WHERE assistencia_id = ? AND status = 'enviada'", [$assistencia_id]);
    $row = $result->fetch_assoc();
    $stats['propostas_enviadas'] = $row ? (int)$row['count'] : 0;

    // Reparos em andamento
    $result = $db->query("SELECT COUNT(*) as count FROM propostas_assistencia WHERE assistencia_id = ? AND status = 'Em Andamento'", [$assistencia_id]);
    $row = $result->fetch_assoc();
    $stats['reparos_andamento'] = $row ? (int)$row['count'] : 0;

    // Reparos concluídos este mês
    $result = $db->query("SELECT COUNT(*) as count FROM propostas_assistencia WHERE assistencia_id = ? AND status = 'Concluída' AND MONTH(data_proposta) = MONTH(CURRENT_DATE()) AND YEAR(data_proposta) = YEAR(CURRENT_DATE())", [$assistencia_id]);
    $row = $result->fetch_assoc();
    $stats['reparos_concluidos'] = $row ? (int)$row['count'] : 0;

    // Receita este mês (valor que a assistência recebe após desconto da taxa)
    $taxa_servico = $plano['taxa_servico'] ?? 25;
    $result = $db->query("
        SELECT COALESCE(SUM(preco * (1 - ?/100)), 0) as receita
        FROM propostas_assistencia
        WHERE assistencia_id = ? AND status = 'Concluída' AND pago = 1
        AND MONTH(data_proposta) = MONTH(CURRENT_DATE())
        AND YEAR(data_proposta) = YEAR(CURRENT_DATE())
    ", [$taxa_servico, $assistencia_id]);
    $row = $result->fetch_assoc();
    $stats['receita_mes'] = $row ? (float)$row['receita'] : 0;

    // Propostas aceitas aguardando início
    $result = $db->query("SELECT COUNT(*) as count FROM propostas_assistencia WHERE assistencia_id = ? AND status = 'aceita'", [$assistencia_id]);
    $row = $result->fetch_assoc();
    $stats['propostas_aceitas'] = $row ? (int)$row['count'] : 0;

    // Total de propostas desta assistência
    $result = $db->query("SELECT COUNT(*) as count FROM propostas_assistencia WHERE assistencia_id = ?", [$assistencia_id]);
    $row = $result->fetch_assoc();
    $stats['total_propostas'] = $row ? (int)$row['count'] : 0;

    // Mensagens não lidas (se a tabela existir)
    $result = $db->query("SHOW TABLES LIKE 'mensagens_chat'");
    if ($result->num_rows > 0) {
        $result = $db->query("
            SELECT COUNT(*) as count FROM mensagens_chat mc
            JOIN propostas_assistencia pa ON mc.proposta_id = pa.id
            WHERE pa.assistencia_id = ? AND mc.remetente_tipo = 'usuario' AND mc.lida = 0
        ", [$assistencia_id]);
        $row = $result->fetch_assoc();
        $stats['mensagens_nao_lidas'] = $row ? (int)$row['count'] : 0;
    } else {
        $stats['mensagens_nao_lidas'] = 0;
    }

} catch (Exception $e) {
    error_log("Erro ao obter estatísticas: " . $e->getMessage());
    $stats = [
        'solicitacoes_pendentes' => 0,
        'propostas_enviadas' => 0,
        'reparos_andamento' => 0,
        'reparos_concluidos' => 0,
        'receita_mes' => 0,
        'propostas_aceitas' => 0,
        'total_propostas' => 0,
        'mensagens_nao_lidas' => 0
    ];
}

// Obter atividades recentes
$atividades = [];
try {
    // Buscar solicitações recentes (últimas 3)
    $sql_solicitacoes = "
        SELECT
            'solicitacao' as tipo,
            sr.id,
            CONCAT('Nova solicitação: ', sr.marca, ' ', sr.modelo) as descricao,
            sr.data_solicitacao as data,
            sr.descricao_problema as detalhes
        FROM solicitacoes_reparo sr
        WHERE sr.status = 'enviado' AND sr.visivel = 1
        ORDER BY sr.data_solicitacao DESC
        LIMIT 3
    ";

    $result = $db->query($sql_solicitacoes);
    while ($row = $result->fetch_assoc()) {
        $atividades[] = $row;
    }

    // Buscar propostas recentes desta assistência (últimas 3)
    $sql_propostas = "
        SELECT
            'proposta' as tipo,
            pa.id,
            CONCAT('Proposta ', pa.status, ' - R$ ', FORMAT(pa.preco, 2)) as descricao,
            pa.data_proposta as data,
            CONCAT('Prazo: ', pa.prazo, ' dia(s)') as detalhes
        FROM propostas_assistencia pa
        WHERE pa.assistencia_id = ?
        ORDER BY pa.data_proposta DESC
        LIMIT 3
    ";

    $result = $db->query($sql_propostas, [$assistencia_id]);
    while ($row = $result->fetch_assoc()) {
        $atividades[] = $row;
    }

    // Ordenar todas as atividades por data (mais recente primeiro)
    usort($atividades, function($a, $b) {
        return strtotime($b['data']) - strtotime($a['data']);
    });

    // Limitar a 5 atividades mais recentes
    $atividades = array_slice($atividades, 0, 5);

} catch (Exception $e) {
    error_log("Erro ao obter atividades: " . $e->getMessage());
    $atividades = [];
}

// Inicializar layout
try {
    $layout = new Layout();
} catch (Exception $e) {
    error_log("Erro ao inicializar layout: " . $e->getMessage());
    die("Erro ao carregar interface. Verifique os logs.");
}
?>

<?php $layout->renderHead("Dashboard - FixFácil Assistências"); ?>

<div class="main-wrapper">
    <?php $layout->renderSidebar('dashboard'); ?>
    
    <main class="main-content">
        <!-- Header -->
        <div class="content-header">
            <h1 class="page-title">
                <i class="fas fa-home me-3"></i>
                Dashboard
            </h1>
            <p class="page-subtitle">
                Bem-vindo de volta, <?php echo htmlspecialchars($usuario['nome']); ?>! 
                Aqui está um resumo das suas atividades.
            </p>
            
            <div class="d-flex align-items-center mt-3">
                <div class="plano-badge plano-<?php echo strtolower($plano['nome']); ?>">
                    <?php if ($plano['nome'] === 'Master'): ?>
                        <i class="fas fa-crown me-2"></i>
                    <?php elseif ($plano['nome'] === 'Premium'): ?>
                        <i class="fas fa-star me-2"></i>
                    <?php else: ?>
                        <i class="fas fa-user me-2"></i>
                    <?php endif; ?>
                    Plano <?php echo $plano['nome']; ?>
                </div>
                <div class="ms-3 text-muted">
                    Taxa de serviço: <strong><?php echo number_format($plano['taxa_servico'], 1); ?>%</strong>
                </div>
            </div>
        </div>
        
        <!-- Estatísticas -->
        <div class="row g-4 mb-4">
            <div class="col-lg-3 col-md-6">
                <div class="card h-100">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <div class="bg-warning bg-opacity-10 p-3 rounded-circle">
                                    <i class="fas fa-inbox text-warning fs-4"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h3 class="mb-1"><?php echo $stats['solicitacoes_pendentes']; ?></h3>
                                <p class="text-muted mb-0">Solicitações Disponíveis</p>
                                <small class="text-muted">Para enviar propostas</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6">
                <div class="card h-100">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <div class="bg-info bg-opacity-10 p-3 rounded-circle">
                                    <i class="fas fa-paper-plane text-info fs-4"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h3 class="mb-1"><?php echo $stats['propostas_enviadas']; ?></h3>
                                <p class="text-muted mb-0">Propostas Enviadas</p>
                                <small class="text-muted">Aguardando resposta</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6">
                <div class="card h-100">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <div class="bg-primary bg-opacity-10 p-3 rounded-circle">
                                    <i class="fas fa-tools text-primary fs-4"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h3 class="mb-1"><?php echo $stats['reparos_andamento']; ?></h3>
                                <p class="text-muted mb-0">Reparos em Andamento</p>
                                <small class="text-muted">Em execução</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6">
                <div class="card h-100">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <div class="bg-success bg-opacity-10 p-3 rounded-circle">
                                    <i class="fas fa-check-circle text-success fs-4"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h3 class="mb-1"><?php echo $stats['reparos_concluidos']; ?></h3>
                                <p class="text-muted mb-0">Concluídos este Mês</p>
                                <small class="text-muted">Finalizados</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Estatísticas Adicionais -->
        <div class="row g-4 mb-4">
            <div class="col-lg-4">
                <div class="card h-100">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <div class="bg-secondary bg-opacity-10 p-3 rounded-circle">
                                    <i class="fas fa-handshake text-secondary fs-4"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h3 class="mb-1"><?php echo $stats['propostas_aceitas']; ?></h3>
                                <p class="text-muted mb-0">Propostas Aceitas</p>
                                <small class="text-muted">Aguardando início</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-4">
                <div class="card h-100">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <div class="bg-dark bg-opacity-10 p-3 rounded-circle">
                                    <i class="fas fa-chart-bar text-dark fs-4"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h3 class="mb-1"><?php echo $stats['total_propostas']; ?></h3>
                                <p class="text-muted mb-0">Total de Propostas</p>
                                <small class="text-muted">Histórico completo</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-4">
                <div class="card h-100">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <div class="bg-purple bg-opacity-10 p-3 rounded-circle" style="background-color: rgba(139, 69, 19, 0.1) !important;">
                                    <i class="fas fa-percentage text-warning fs-4"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h3 class="mb-1"><?php echo number_format($plano['taxa_servico'], 1); ?>%</h3>
                                <p class="text-muted mb-0">Taxa de Serviço</p>
                                <small class="text-muted">Plano <?php echo $plano['nome']; ?></small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Receita e Ações Rápidas -->
        <div class="row g-4 mb-4">
            <div class="col-lg-4">
                <div class="card h-100">
                    <div class="card-body text-center">
                        <div class="bg-success bg-opacity-10 p-4 rounded-circle d-inline-flex mb-3">
                            <i class="fas fa-dollar-sign text-success fs-2"></i>
                        </div>
                        <h2 class="text-success mb-2">R$ <?php echo number_format($stats['receita_mes'], 2, ',', '.'); ?></h2>
                        <p class="text-muted mb-0">Receita este Mês</p>
                        <small class="text-muted">Após taxa de <?php echo number_format($plano['taxa_servico'], 1); ?>%</small>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-8">
                <div class="card h-100">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-bolt me-2"></i>
                            Ações Rápidas
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <a href="solicitacoes.php" class="btn btn-outline-warning w-100">
                                    <i class="fas fa-inbox me-2"></i>
                                    Ver Solicitações
                                    <?php if ($stats['solicitacoes_pendentes'] > 0): ?>
                                        <span class="badge bg-warning ms-2"><?php echo $stats['solicitacoes_pendentes']; ?></span>
                                    <?php endif; ?>
                                </a>
                            </div>
                            <div class="col-md-6">
                                <a href="propostas.php" class="btn btn-outline-info w-100">
                                    <i class="fas fa-paper-plane me-2"></i>
                                    Minhas Propostas
                                </a>
                            </div>
                            <div class="col-md-6">
                                <a href="reparos.php" class="btn btn-outline-primary w-100">
                                    <i class="fas fa-tools me-2"></i>
                                    Reparos Ativos
                                </a>
                            </div>
                            <div class="col-md-6">
                                <a href="carteira.php" class="btn btn-outline-success w-100">
                                    <i class="fas fa-wallet me-2"></i>
                                    Ver Carteira
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Atividades Recentes -->
        <?php if (!empty($atividades)): ?>
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-clock me-2"></i>
                    Atividades Recentes
                </h5>
            </div>
            <div class="card-body">
                <div class="list-group list-group-flush">
                    <?php foreach ($atividades as $atividade): ?>
                    <div class="list-group-item border-0 px-0 py-3">
                        <div class="d-flex align-items-start">
                            <div class="flex-shrink-0">
                                <?php if ($atividade['tipo'] === 'solicitacao'): ?>
                                    <div class="bg-warning bg-opacity-10 p-2 rounded-circle">
                                        <i class="fas fa-inbox text-warning"></i>
                                    </div>
                                <?php else: ?>
                                    <div class="bg-info bg-opacity-10 p-2 rounded-circle">
                                        <i class="fas fa-paper-plane text-info"></i>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <p class="mb-1 fw-medium"><?php echo htmlspecialchars($atividade['descricao']); ?></p>
                                        <?php if (isset($atividade['detalhes'])): ?>
                                            <small class="text-muted d-block"><?php echo htmlspecialchars($atividade['detalhes']); ?></small>
                                        <?php endif; ?>
                                    </div>
                                    <small class="text-muted">
                                        <?php echo date('d/m/Y H:i', strtotime($atividade['data'])); ?>
                                    </small>
                                </div>
                                <?php if ($atividade['tipo'] === 'solicitacao'): ?>
                                    <div class="mt-2">
                                        <a href="solicitacoes.php" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-eye me-1"></i>Ver Detalhes
                                        </a>
                                    </div>
                                <?php else: ?>
                                    <div class="mt-2">
                                        <a href="propostas.php" class="btn btn-sm btn-outline-info">
                                            <i class="fas fa-edit me-1"></i>Gerenciar Proposta
                                        </a>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>

                <div class="text-center mt-3">
                    <a href="solicitacoes.php" class="btn btn-outline-primary me-2">
                        <i class="fas fa-inbox me-1"></i>Ver Todas as Solicitações
                    </a>
                    <a href="propostas.php" class="btn btn-outline-info">
                        <i class="fas fa-paper-plane me-1"></i>Ver Todas as Propostas
                    </a>
                </div>
            </div>
        </div>
        <?php else: ?>
        <div class="card">
            <div class="card-body text-center py-5">
                <div class="mb-3">
                    <i class="fas fa-clock text-muted" style="font-size: 3rem;"></i>
                </div>
                <h5 class="text-muted">Nenhuma atividade recente</h5>
                <p class="text-muted mb-4">Quando houver solicitações ou propostas, elas aparecerão aqui.</p>
                <a href="solicitacoes.php" class="btn btn-primary">
                    <i class="fas fa-search me-1"></i>Buscar Solicitações
                </a>
            </div>
        </div>
        <?php endif; ?>
    </main>
</div>

<?php $layout->renderFooter(); ?>
