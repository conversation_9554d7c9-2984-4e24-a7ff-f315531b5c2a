<?php
session_start();
require_once 'db.php';

$mensagem = '';

try {
    $query = "SELECT l.*, p.nome as produto_nome FROM leilao l
              INNER JOIN produtos p ON l.produto_id = p.id
              WHERE l.lojista_id = :lojista_id";
    $stmt = $pdo->prepare($query);
    $stmt->bindParam(':lojista_id', $_SESSION['user_id']);
    $stmt->execute();
    $leiloes = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $mensagem = 'Erro ao buscar os leilões: ' . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="utf-8">
    <title>Listagem de Leilões</title>
</head>
<body>
    <?php if ($mensagem): ?>
        <div><?= $mensagem ?></div>
    <?php endif; ?>

    <h1>Meus <PERSON></h1>

    <table>
        <thead>
            <tr>
                <th>ID</th>
                <th>Produto</th>
                <th>Status</th>
                <th>Data Início</th>
                <th>Data Término</th>
            </tr>
        </thead>
        <tbody>
            <?php foreach ($leiloes as $leilao): ?>
                <tr>
                    <td><?= $leilao['id'] ?></td>
                    <td><?= $leilao['produto_nome'] ?></td>
                    <td><?= $leilao['status_leilao'] ?></td>
                    <td><?= $leilao['data_inicio'] ?></td>
                    <td><?= $leilao['data_termino'] ?></td>
                </tr>
            <?php endforeach; ?>
        </tbody>
    </table>
</body>
</html>
