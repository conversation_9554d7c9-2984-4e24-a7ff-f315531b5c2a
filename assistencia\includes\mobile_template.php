<?php
/**
 * Template Base Mobile-First - FixFácil Assistências
 * Padrão baseado na dashboard_mobile_final.php
 */

// Função para renderizar o CSS base
function getMobileCSS() {
    return '
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            background: #f8fafc;
            min-height: 100vh;
            color: #1e293b;
        }

        .container {
            max-width: 414px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
            position: relative;
            overflow-x: hidden;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }

        .header {
            background: linear-gradient(135deg, #059669 0%, #065f46 100%);
            color: white;
            padding: 24px 20px 20px 20px;
            position: relative;
        }

        .header::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 100%;
            background: url("data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 20\"><defs><pattern id=\"grain\" width=\"100\" height=\"100\" patternUnits=\"userSpaceOnUse\"><circle cx=\"50\" cy=\"50\" r=\"0.5\" fill=\"white\" opacity=\"0.05\"/></pattern></defs><rect width=\"100\" height=\"20\" fill=\"url(%23grain)\"/></svg>");
        }

        .header-content {
            position: relative;
            z-index: 1;
        }

        .header-top {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 20px;
        }

        .company-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .company-logo {
            width: 48px;
            height: 48px;
            background: rgba(255,255,255,0.2);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            font-weight: 700;
            backdrop-filter: blur(10px);
        }

        .company-details h1 {
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 2px;
        }

        .company-status {
            font-size: 12px;
            opacity: 0.9;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .status-indicator {
            width: 8px;
            height: 8px;
            background: #10b981;
            border-radius: 50%;
        }

        .header-actions {
            display: flex;
            gap: 8px;
        }

        .action-btn {
            width: 40px;
            height: 40px;
            background: rgba(255,255,255,0.15);
            border: none;
            border-radius: 12px;
            color: white;
            font-size: 18px;
            cursor: pointer;
            backdrop-filter: blur(10px);
            transition: all 0.2s ease;
            position: relative;
            text-decoration: none;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .action-btn:hover {
            background: rgba(255,255,255,0.25);
        }

        .notification-badge {
            position: absolute;
            top: -2px;
            right: -2px;
            background: #ef4444;
            color: white;
            border-radius: 50%;
            width: 16px;
            height: 16px;
            font-size: 10px;
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 12px;
        }

        .stat-card {
            background: rgba(255,255,255,0.15);
            border-radius: 12px;
            padding: 16px 12px;
            text-align: center;
            backdrop-filter: blur(10px);
        }

        .stat-number {
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 4px;
        }

        .stat-label {
            font-size: 11px;
            opacity: 0.8;
        }

        .content {
            padding: 20px;
            padding-bottom: 100px;
        }

        .section-card {
            background: white;
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
        }

        .section-title {
            font-size: 18px;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .actions-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
        }

        .quick-action {
            background: #f8fafc;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            padding: 16px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            text-decoration: none;
            color: inherit;
        }

        .quick-action:hover {
            border-color: #059669;
            background: #f0fdf4;
            transform: translateY(-2px);
        }

        .quick-action-icon {
            font-size: 28px;
            margin-bottom: 8px;
        }

        .quick-action-title {
            font-size: 14px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 2px;
        }

        .quick-action-subtitle {
            font-size: 11px;
            color: #64748b;
        }

        .action-badge {
            position: absolute;
            top: 8px;
            right: 8px;
            background: #ef4444;
            color: white;
            border-radius: 10px;
            padding: 2px 6px;
            font-size: 10px;
            font-weight: 600;
        }

        .action-badge.green {
            background: #10b981;
        }

        .action-badge.blue {
            background: #3b82f6;
        }

        .action-badge.new {
            background: #8b5cf6;
            animation: pulse 2s infinite;
        }

        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 100%;
            max-width: 414px;
            background: white;
            border-top: 1px solid #e2e8f0;
            padding: 12px 20px;
            display: flex;
            justify-content: space-around;
            box-shadow: 0 -4px 20px rgba(0,0,0,0.1);
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
            cursor: pointer;
            padding: 8px;
            border-radius: 12px;
            transition: all 0.2s ease;
            position: relative;
            text-decoration: none;
            color: #64748b;
        }

        .nav-item.active {
            background: #f0fdf4;
            color: #059669;
        }

        .nav-icon {
            font-size: 20px;
        }

        .nav-label {
            font-size: 10px;
            font-weight: 500;
        }

        .nav-badge {
            position: absolute;
            top: 4px;
            right: 4px;
            background: #ef4444;
            color: white;
            border-radius: 50%;
            width: 16px;
            height: 16px;
            font-size: 10px;
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .floating-action {
            position: fixed;
            bottom: 80px;
            right: 20px;
            width: 56px;
            height: 56px;
            background: linear-gradient(135deg, #059669, #065f46);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            cursor: pointer;
            box-shadow: 0 4px 16px rgba(5, 150, 105, 0.4);
            transition: all 0.3s ease;
            z-index: 100;
            text-decoration: none;
        }

        .floating-action:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 20px rgba(5, 150, 105, 0.5);
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .content > * {
            animation: fadeIn 0.6s ease;
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.02);
            }
        }

        /* Desktop Responsive */
        @media (min-width: 768px) {
            .container {
                max-width: 1200px;
                box-shadow: none;
                background: #f8fafc;
            }

            .header {
                border-radius: 20px;
                margin: 20px;
                margin-bottom: 0;
            }

            .content {
                padding: 40px;
                padding-bottom: 40px;
            }

            .bottom-nav {
                display: none;
            }

            .floating-action {
                display: none;
            }

            .actions-grid {
                grid-template-columns: repeat(4, 1fr);
            }

            .stats-grid {
                grid-template-columns: repeat(6, 1fr);
            }
        }
    ';
}

// Função para renderizar o header
function renderMobileHeader($pageTitle, $userName, $stats = []) {
    $aguardando = $stats['aguardando_resposta'] ?? 0;
    
    return '
    <div class="header">
        <div class="header-content">
            <div class="header-top">
                <div class="company-info">
                    <div class="company-logo">FF</div>
                    <div class="company-details">
                        <h1>' . htmlspecialchars(substr($userName, 0, 20)) . '</h1>
                        <div class="company-status">
                            <div class="status-indicator"></div>
                            <span>Online • Verificado</span>
                        </div>
                    </div>
                </div>
                <div class="header-actions">
                    <a href="../" class="action-btn" title="Acessar área do cliente">👤</a>
                    <a href="logout.php" class="action-btn" title="Sair">🚪</a>
                    <button class="action-btn" onclick="openNotifications()">
                        🔔
                        ' . ($aguardando > 0 ? '<span class="notification-badge">' . $aguardando . '</span>' : '') . '
                    </button>
                </div>
            </div>
            
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number">' . ($stats['total_solicitacoes'] ?? 0) . '</div>
                    <div class="stat-label">Total</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">' . ($stats['aguardando_resposta'] ?? 0) . '</div>
                    <div class="stat-label">Pendentes</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">' . ($stats['em_andamento'] ?? 0) . '</div>
                    <div class="stat-label">Andamento</div>
                </div>
            </div>
        </div>
    </div>';
}

// Função para renderizar o menu de navegação
function renderBottomNav($activePage = '') {
    $pages = [
        'dashboard' => ['icon' => '🏠', 'label' => 'Início', 'url' => 'dashboard_mobile_final.php'],
        'solicitacoes' => ['icon' => '📋', 'label' => 'Solicitações', 'url' => 'solicitacoes.php'],
        'reparos' => ['icon' => '🔧', 'label' => 'Reparos', 'url' => 'reparos_new.php'],
        'propostas' => ['icon' => '💼', 'label' => 'Propostas', 'url' => 'propostas.php'],
        'marketplace' => ['icon' => '🛒', 'label' => 'Loja', 'url' => 'marketplace.php'],
        'carteira' => ['icon' => '💳', 'label' => 'Carteira', 'url' => 'carteira.php']
    ];
    
    $html = '<div class="bottom-nav">';
    
    foreach ($pages as $key => $page) {
        $activeClass = ($activePage === $key) ? ' active' : '';
        $html .= '
            <a href="' . $page['url'] . '" class="nav-item' . $activeClass . '">
                <div class="nav-icon">' . $page['icon'] . '</div>
                <div class="nav-label">' . $page['label'] . '</div>
            </a>';
    }
    
    $html .= '</div>';
    return $html;
}

// Função para renderizar o floating action button
function renderFloatingAction($url = 'solicitacoes.php', $icon = '+') {
    return '
    <a href="' . $url . '" class="floating-action" title="Nova solicitação">
        <span>' . $icon . '</span>
    </a>';
}
?>
