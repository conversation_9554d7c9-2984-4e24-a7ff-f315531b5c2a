<?php
/**
 * AJAX - Otimizar <PERSON> de Dados
 * FixFácil Assistências - Sistema Novo
 */

header('Content-Type: application/json');

require_once '../config/auth.php';
require_once '../config/database.php';

// Verificar autenticação e permissão de admin
$auth = getAuth();
try {
    $auth->checkAssistenciaAuth();
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'Não autorizado']);
    exit();
}

$usuario = $auth->getUsuarioLogado();

// Verificar se é admin
if ($usuario['email'] !== '<EMAIL>') {
    echo json_encode(['success' => false, 'message' => 'Acesso negado']);
    exit();
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Método não permitido']);
    exit();
}

try {
    $db = getDatabase();
    $conn = $db->getConnection();
    
    // Obter lista de tabelas
    $result = $conn->query("SHOW TABLES");
    $tabelas_otimizadas = 0;
    
    while ($row = $result->fetch_array()) {
        $tabela = $row[0];
        
        // Otimizar cada tabela
        $conn->query("OPTIMIZE TABLE `$tabela`");
        $tabelas_otimizadas++;
    }
    
    echo json_encode([
        'success' => true,
        'message' => "Banco otimizado! $tabelas_otimizadas tabelas processadas."
    ]);
    
} catch (Exception $e) {
    error_log("Erro ao otimizar banco: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Erro interno do servidor']);
}
?>
