<?php
// scripts/assistencia_actions.php

require '../config.php';
require 'assistencia_functions.php';

// Definir o tipo de resposta como JSON
header('Content-Type: application/json');

// Verificar se a requisição é via POST
if ($_SERVER['REQUEST_METHOD'] != 'POST') {
    echo json_encode(['success' => false, 'message' => 'Método de requisição inválido.']);
    exit();
}

// Verificar qual ação está sendo solicitada
$action = isset($_POST['action']) ? $_POST['action'] : '';

switch ($action) {
    case 'criar_proposta':
        $solicitacao_id = isset($_POST['solicitacao_id']) ? intval($_POST['solicitacao_id']) : 0;
        $valor = isset($_POST['valor']) ? floatval($_POST['valor']) : 0.00;
        $tempo_estimado = isset($_POST['tempo_estimado']) ? trim($_POST['tempo_estimado']) : '';
        $detalhes = isset($_POST['detalhes']) ? trim($_POST['detalhes']) : '';
        $assistencia_id = $_SESSION['assistencia_id']; // Assumindo que o ID da assistência está na sessão

        // Validação
        if ($solicitacao_id <= 0 || $valor <= 0 || empty($tempo_estimado)) {
            echo json_encode(['success' => false, 'message' => 'Por favor, preencha todos os campos obrigatórios.']);
            exit();
        }

        // Criar a proposta
        if (criar_proposta($conn, $solicitacao_id, $assistencia_id, $valor, $tempo_estimado, $detalhes)) {
            echo json_encode(['success' => true, 'message' => 'Proposta enviada com sucesso.']);
        } else {
            echo json_encode(['success' => false, 'message' => 'Erro ao enviar a proposta.']);
        }
        break;

    case 'aceitar':
    case 'rejeitar':
        $solicitacao_id = isset($_POST['solicitacao_id']) ? intval($_POST['solicitacao_id']) : 0;
        $novo_status = $action === 'aceitar' ? 'aceita' : 'rejeitada';

        // Validação
        if ($solicitacao_id <= 0) {
            echo json_encode(['success' => false, 'message' => 'ID da solicitação inválido.']);
            exit();
        }

        // Atualizar o status da solicitação
        if (atualizar_status_solicitacao($conn, $solicitacao_id, $novo_status)) {
            echo json_encode(['success' => true, 'message' => 'Solicitação ' . ($novo_status === 'aceita' ? 'aceita' : 'rejeitada') . ' com sucesso.']);
        } else {
            echo json_encode(['success' => false, 'message' => 'Erro ao atualizar o status da solicitação.']);
        }
        break;

    default:
        echo json_encode(['success' => false, 'message' => 'Ação desconhecida.']);
        break;
}
?>
