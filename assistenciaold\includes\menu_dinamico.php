<?php
/**
 * Menu Dinâmico baseado no Plano do Usuário
 * Inclui menu desktop (sidebar) e mobile (bottom navigation)
 */

function getMenuItems($usuario_id, $taxaCalculator) {
    $plano_info = $taxaCalculator->getPlanoInfo($usuario_id);

    // Debug: verificar se plano_info está correto
    if (!$plano_info) {
        error_log("Menu Dinâmico: Plano não encontrado para usuário $usuario_id");
    }
    
    // Menu base (disponível para todos os planos)
    $menu_items = [
        'dashboard' => [
            'titulo' => 'Dashboard',
            'url' => 'home.php',
            'icone' => 'fas fa-home',
            'descricao' => 'Visão geral e estatísticas',
            'disponivel' => true,
            'plano_minimo' => 'Free'
        ],
        'solicitacoes' => [
            'titulo' => 'Solicitações',
            'url' => 'solicitacoes.php',
            'icone' => 'fas fa-inbox',
            'descricao' => 'Solicitações de reparo recebidas',
            'disponivel' => true,
            'plano_minimo' => 'Free',
            'badge' => 'getNovasSolicitacoes'
        ],
        'propostas' => [
            'titulo' => 'Propostas',
            'url' => 'propostas_enviadas.php',
            'icone' => 'fas fa-paper-plane',
            'descricao' => 'Propostas enviadas aos clientes',
            'disponivel' => true,
            'plano_minimo' => 'Free'
        ],
        'reparos' => [
            'titulo' => 'Reparos',
            'url' => 'reparos_em_andamento.php',
            'icone' => 'fas fa-tools',
            'descricao' => 'Reparos em andamento',
            'disponivel' => true,
            'plano_minimo' => 'Free'
        ],
        'carteira' => [
            'titulo' => 'Carteira',
            'url' => 'carteira.php',
            'icone' => 'fas fa-wallet',
            'descricao' => 'Ganhos e estatísticas financeiras',
            'disponivel' => true,
            'plano_minimo' => 'Free'
        ],
        'marketplace' => [
            'titulo' => 'Marketplace',
            'url' => 'meumarktplace.php',
            'icone' => 'fas fa-store',
            'descricao' => 'Venda produtos e peças',
            'disponivel' => ($plano_info['acesso_marketplace'] ?? 0) == 1,
            'plano_minimo' => 'Premium',
            'badge_premium' => true
        ],
        'chat' => [
            'titulo' => 'Chat',
            'url' => 'chat.php',
            'icone' => 'fas fa-comments',
            'descricao' => 'Conversar com clientes',
            'disponivel' => ($plano_info['acesso_chat'] ?? 0) == 1,
            'plano_minimo' => 'Premium',
            'badge_premium' => true
        ],
        'link_fix' => [
            'titulo' => 'Link Fix',
            'url' => 'link_fix.php',
            'icone' => 'fas fa-link',
            'descricao' => 'Seu link personalizado',
            'disponivel' => ($plano_info['link_personalizado'] ?? 0) == 1,
            'plano_minimo' => 'Master',
            'badge_master' => true
        ],
        'perfil' => [
            'titulo' => 'Perfil',
            'url' => 'perfil.php',
            'icone' => 'fas fa-user',
            'descricao' => 'Dados pessoais e da empresa',
            'disponivel' => true,
            'plano_minimo' => 'Free'
        ]
    ];
    
    return $menu_items;
}

function renderMenuDesktop($usuario_id, $taxaCalculator, $pagina_atual = '') {
    $menu_items = getMenuItems($usuario_id, $taxaCalculator);
    $plano_info = $taxaCalculator->getPlanoInfo($usuario_id);
    
    ob_start();
    ?>
    <nav class="sidebar-menu">
        <div class="menu-header">
            <div class="logo">
                <i class="fas fa-tools"></i>
                <span>FixFácil</span>
            </div>
            <div class="plano-badge plano-<?php echo strtolower($plano_info['nome']); ?>">
                <?php if ($plano_info['nome'] === 'Master'): ?>
                    <i class="fas fa-crown"></i>
                <?php elseif ($plano_info['nome'] === 'Premium'): ?>
                    <i class="fas fa-star"></i>
                <?php else: ?>
                    <i class="fas fa-user"></i>
                <?php endif; ?>
                <?php echo $plano_info['nome']; ?>
            </div>
        </div>
        
        <div class="menu-items">
            <?php foreach ($menu_items as $key => $item): ?>
                <?php if ($item['disponivel']): ?>
                    <a href="<?php echo $item['url']; ?>" 
                       class="menu-item <?php echo ($pagina_atual === $key) ? 'active' : ''; ?>"
                       title="<?php echo $item['descricao']; ?>">
                        <i class="<?php echo $item['icone']; ?>"></i>
                        <span><?php echo $item['titulo']; ?></span>
                        <?php if (isset($item['badge']) && function_exists($item['badge'])): ?>
                            <?php $badge_count = call_user_func($item['badge'], $usuario_id); ?>
                            <?php if ($badge_count > 0): ?>
                                <span class="badge"><?php echo $badge_count; ?></span>
                            <?php endif; ?>
                        <?php endif; ?>
                    </a>
                <?php else: ?>
                    <div class="menu-item disabled" title="Disponível no plano <?php echo $item['plano_minimo']; ?>">
                        <i class="<?php echo $item['icone']; ?>"></i>
                        <span><?php echo $item['titulo']; ?></span>
                        <span class="upgrade-badge">
                            <?php if (isset($item['badge_master'])): ?>
                                <i class="fas fa-crown"></i>
                            <?php elseif (isset($item['badge_premium'])): ?>
                                <i class="fas fa-star"></i>
                            <?php endif; ?>
                        </span>
                    </div>
                <?php endif; ?>
            <?php endforeach; ?>
        </div>
        
        <div class="menu-footer">
            <?php if ($plano_info['nome'] !== 'Master'): ?>
                <a href="upgrade_plano.php" class="upgrade-btn">
                    <i class="fas fa-arrow-up"></i>
                    <span>Fazer Upgrade</span>
                </a>
            <?php endif; ?>
            
            <a href="../logout.php" class="menu-item logout">
                <i class="fas fa-sign-out-alt"></i>
                <span>Sair</span>
            </a>
        </div>
    </nav>
    <?php
    return ob_get_clean();
}

function renderMenuMobile($usuario_id, $taxaCalculator, $pagina_atual = '') {
    $menu_items = getMenuItems($usuario_id, $taxaCalculator);
    
    // Filtrar apenas os 5 itens principais para mobile
    $mobile_items = [];
    $priority_order = ['dashboard', 'solicitacoes', 'propostas', 'carteira', 'perfil'];
    
    // Adicionar itens prioritários disponíveis
    foreach ($priority_order as $key) {
        if (isset($menu_items[$key]) && $menu_items[$key]['disponivel']) {
            $mobile_items[$key] = $menu_items[$key];
        }
    }
    
    // Se tiver marketplace ou chat disponível, substituir perfil
    if (isset($menu_items['marketplace']) && $menu_items['marketplace']['disponivel']) {
        unset($mobile_items['perfil']);
        $mobile_items['marketplace'] = $menu_items['marketplace'];
    }
    
    if (isset($menu_items['chat']) && $menu_items['chat']['disponivel']) {
        if (count($mobile_items) >= 5) {
            unset($mobile_items['carteira']);
        }
        $mobile_items['chat'] = $menu_items['chat'];
    }
    
    // Garantir que perfil sempre apareça se não tiver outros itens premium
    if (count($mobile_items) < 5 && !isset($mobile_items['perfil'])) {
        $mobile_items['perfil'] = $menu_items['perfil'];
    }
    
    ob_start();
    ?>
    <nav class="mobile-menu d-lg-none">
        <div class="mobile-menu-items">
            <?php foreach ($mobile_items as $key => $item): ?>
                <a href="<?php echo $item['url']; ?>" 
                   class="mobile-menu-item <?php echo ($pagina_atual === $key) ? 'active' : ''; ?>">
                    <i class="<?php echo $item['icone']; ?>"></i>
                    <span><?php echo $item['titulo']; ?></span>
                    <?php if (isset($item['badge']) && function_exists($item['badge'])): ?>
                        <?php $badge_count = call_user_func($item['badge'], $usuario_id); ?>
                        <?php if ($badge_count > 0): ?>
                            <span class="mobile-badge"><?php echo $badge_count; ?></span>
                        <?php endif; ?>
                    <?php endif; ?>
                </a>
            <?php endforeach; ?>
        </div>
    </nav>
    <?php
    return ob_get_clean();
}

function getMenuCSS() {
    ob_start();
    ?>
    <style>
        /* Sidebar Menu Desktop */
        .sidebar-menu {
            width: 280px;
            height: 100vh;
            background: var(--white);
            box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
            position: fixed;
            left: 0;
            top: 0;
            z-index: 1000;
            display: flex;
            flex-direction: column;
            transition: all 0.3s ease;
        }
        
        .menu-header {
            padding: 2rem 1.5rem;
            border-bottom: 1px solid var(--gray-200);
            background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
            color: var(--white);
        }
        
        .menu-header .logo {
            font-size: 1.5rem;
            font-weight: 800;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }
        
        .plano-badge {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            border-radius: 2rem;
            font-size: 0.85rem;
            font-weight: 600;
            background: rgba(255, 255, 255, 0.2);
        }
        
        .plano-badge.plano-master {
            background: linear-gradient(135deg, #ffd700, #ffed4e);
            color: #92400e;
        }
        
        .plano-badge.plano-premium {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: var(--white);
        }
        
        .menu-items {
            flex: 1;
            padding: 1rem 0;
            overflow-y: auto;
        }
        
        .menu-item {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem 1.5rem;
            color: var(--gray-700);
            text-decoration: none;
            transition: all 0.2s;
            position: relative;
        }
        
        .menu-item:hover {
            background: var(--gray-50);
            color: var(--primary);
        }
        
        .menu-item.active {
            background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
            color: var(--white);
            box-shadow: 0 2px 10px rgba(102, 126, 234, 0.3);
        }
        
        .menu-item.active::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 4px;
            background: var(--white);
        }
        
        .menu-item.disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        
        .menu-item i {
            width: 1.25rem;
            text-align: center;
            font-size: 1.1rem;
        }
        
        .menu-item span {
            font-weight: 500;
        }
        
        .badge {
            background: var(--danger);
            color: var(--white);
            border-radius: 50%;
            width: 1.5rem;
            height: 1.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.75rem;
            font-weight: 600;
            margin-left: auto;
        }
        
        .upgrade-badge {
            margin-left: auto;
            opacity: 0.6;
        }
        
        .menu-footer {
            padding: 1rem;
            border-top: 1px solid var(--gray-200);
        }
        
        .upgrade-btn {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.75rem 1rem;
            background: linear-gradient(135deg, var(--success) 0%, #38a169 100%);
            color: var(--white);
            border-radius: 0.5rem;
            text-decoration: none;
            font-weight: 600;
            margin-bottom: 0.5rem;
            transition: all 0.2s;
        }
        
        .upgrade-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(72, 187, 120, 0.3);
            color: var(--white);
        }
        
        .menu-item.logout {
            color: var(--danger);
        }
        
        .menu-item.logout:hover {
            background: rgba(245, 101, 101, 0.1);
            color: var(--danger);
        }
        
        /* Mobile Menu */
        .mobile-menu {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: var(--white);
            box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
            padding: 0.75rem 0;
            z-index: 1000;
        }
        
        .mobile-menu-items {
            display: flex;
            justify-content: space-around;
            align-items: center;
        }
        
        .mobile-menu-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-decoration: none;
            color: var(--gray-600);
            font-size: 0.75rem;
            padding: 0.5rem;
            border-radius: 0.5rem;
            transition: all 0.2s;
            position: relative;
            min-width: 60px;
        }
        
        .mobile-menu-item.active {
            color: var(--primary);
            background: rgba(102, 126, 234, 0.1);
        }
        
        .mobile-menu-item i {
            font-size: 1.25rem;
            margin-bottom: 0.25rem;
        }
        
        .mobile-badge {
            position: absolute;
            top: 0.25rem;
            right: 0.25rem;
            background: var(--danger);
            color: var(--white);
            border-radius: 50%;
            width: 1rem;
            height: 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.6rem;
            font-weight: 600;
        }
        
        /* Layout com sidebar */
        .main-content-with-sidebar {
            margin-left: 280px;
            min-height: 100vh;
        }
        
        /* Responsive */
        @media (max-width: 1024px) {
            .sidebar-menu {
                transform: translateX(-100%);
            }
            
            .sidebar-menu.show {
                transform: translateX(0);
            }
            
            .main-content-with-sidebar {
                margin-left: 0;
                margin-bottom: 5rem;
            }
        }
        
        @media (max-width: 768px) {
            body {
                margin-bottom: 5rem;
            }
        }
    </style>
    <?php
    return ob_get_clean();
}

// Função auxiliar para contar novas solicitações (exemplo)
function getNovasSolicitacoes($usuario_id) {
    // Esta função deve ser implementada para contar solicitações não lidas
    // Por enquanto, retorna 0
    return 0;
}
?>
