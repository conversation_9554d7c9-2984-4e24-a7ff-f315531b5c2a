<?php
// assistencia_tecnica.php

// Iniciar a sessão
session_start();

// Verificar se a assistência técnica está logada
if (!isset($_SESSION['assistencia_id'])) {
    // Redirecionar para a página de login ou exibir uma mensagem de erro
    die("Acesso negado. Por favor, faça login como assistência técnica.");
}

// Configurações do Banco de Dados
$servername = "localhost";
$username_db = "u680766645_fixfacilnew";
$password_db = "T3cn0l0g1a@";
$dbname = "u680766645_fixfacilnew";

// Criar conexão com o banco de dados
$conn = new mysqli($servername, $username_db, $password_db, $dbname);

// Verificar a conexão
if ($conn->connect_error) {
    die("Falha na conexão: " . $conn->connect_error);
}

// Definir o tipo de resposta como JSON para requisições AJAX
header('Content-Type: application/json');

// Função para sanitizar entradas
function sanitize_input($data) {
    return htmlspecialchars(stripslashes(trim($data)));
}

// Verificar se é uma requisição AJAX via POST
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    $response = ['success' => false, 'message' => 'Ação inválida.'];

    // Obter o ID da assistência técnica da sessão
    $assistencia_id = intval($_SESSION['assistencia_id']);

    switch ($_POST['action']) {
        case 'aceitar':
            // Aceitar uma solicitação de reparo
            $solicitacao_id = intval($_POST['solicitacao_id']);

            // Atualizar o status da solicitação para 'aceita'
            $stmt = $conn->prepare("UPDATE solicitacoes_reparo SET status = 'aceita' WHERE id = ?");
            $stmt->bind_param("i", $solicitacao_id);

            if ($stmt->execute()) {
                $response['success'] = true;
                $response['message'] = 'Solicitação aceita com sucesso.';
            } else {
                $response['message'] = 'Erro ao aceitar a solicitação.';
            }

            $stmt->close();
            break;

        case 'rejeitar':
            // Rejeitar uma solicitação de reparo
            $solicitacao_id = intval($_POST['solicitacao_id']);

            // Atualizar o status da solicitação para 'rejeitada'
            $stmt = $conn->prepare("UPDATE solicitacoes_reparo SET status = 'rejeitada' WHERE id = ?");
            $stmt->bind_param("i", $solicitacao_id);

            if ($stmt->execute()) {
                $response['success'] = true;
                $response['message'] = 'Solicitação rejeitada com sucesso.';
            } else {
                $response['message'] = 'Erro ao rejeitar a solicitação.';
            }

            $stmt->close();
            break;

        case 'fazer_proposta':
            // Fazer uma proposta para uma solicitação de reparo
            $solicitacao_id = intval($_POST['solicitacao_id']);
            $preco = floatval($_POST['preco']);
            $prazo = intval($_POST['prazo']);
            $observacoes = sanitize_input($_POST['observacoes']);
            $retirada_expressa = isset($_POST['retirada_expressa']) ? 1 : 0;

            // Validação dos dados
            if ($solicitacao_id <= 0 || $preco <= 0 || $prazo <= 0) {
                $response['message'] = 'Por favor, preencha todos os campos obrigatórios corretamente.';
                break;
            }

            // Inserir a proposta no banco de dados
            $stmt = $conn->prepare("INSERT INTO propostas_assistencia (solicitacao_id, assistencia_id, preco, prazo, observacoes, status, data_proposta, retirada_expressa, pago) VALUES (?, ?, ?, ?, ?, 'enviada', NOW(), ?, 0)");
            $stmt->bind_param("iidssi", $solicitacao_id, $assistencia_id, $preco, $prazo, $observacoes, $retirada_expressa);

            if ($stmt->execute()) {
                $response['success'] = true;
                $response['message'] = 'Proposta enviada com sucesso.';
            } else {
                $response['message'] = 'Erro ao enviar a proposta.';
            }

            $stmt->close();
            break;

        default:
            $response['message'] = 'Ação desconhecida.';
            break;
    }

    // Retornar a resposta em JSON e encerrar o script
    echo json_encode($response);
    exit;
}

// Se não for uma requisição AJAX, exibir a página HTML

?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <title>Assistência Técnica - FixFácil</title>
    <!-- Meta Tags para Responsividade -->
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <!-- Bootstrap CSS (Versão 5) -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome para Ícones -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css?family=Poppins:300,400,500,600,700&display=swap" rel="stylesheet">
    <!-- CSS Personalizado -->
    <style>
        body {
            padding-top: 70px;
            font-family: 'Poppins', sans-serif;
        }
        .modal-lg {
            max-width: 80% !important;
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">FixFácil - Assistência Técnica</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" 
                    aria-controls="navbarNav" aria-expanded="false" aria-label="Alternar navegação">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse justify-content-end" id="navbarNav">
                <ul class="navbar-nav">
                    <!-- Adicione outros links se necessário -->
                    <li class="nav-item">
                        <a class="nav-link active" href="#">Solicitações</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#">Perfil</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="logout.php"><i class="fas fa-sign-out-alt"></i> Sair</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Container Principal -->
    <div class="container">
        <h2 class="mb-4">Solicitações de Reparo</h2>
        <div id="solicitacoes-container">
            <?php
            // Consultar todas as solicitações de reparo com status 'enviado' ou 'aceita'
            $sql = "SELECT sr.*, u.nome AS cliente_nome FROM solicitacoes_reparo sr
                    JOIN usuarios u ON sr.usuario_id = u.id
                    WHERE sr.status IN ('enviado', 'aceita')
                    ORDER BY sr.data_solicitacao DESC";
            $result = $conn->query($sql);

            if ($result && $result->num_rows > 0) {
                while ($solicitacao = $result->fetch_assoc()) {
                    ?>
                    <div class="card mb-3">
                        <div class="card-header">
                            <strong>ID:</strong> <?php echo htmlspecialchars($solicitacao['id']); ?> | 
                            <strong>Cliente:</strong> <?php echo htmlspecialchars($solicitacao['cliente_nome']); ?> |
                            <strong>Data:</strong> <?php echo date("d/m/Y H:i", strtotime($solicitacao['data_solicitacao'])); ?>
                        </div>
                        <div class="card-body">
                            <p><strong>Dispositivo:</strong> <?php echo htmlspecialchars($solicitacao['dispositivo']); ?></p>
                            <p><strong>Marca:</strong> <?php echo htmlspecialchars($solicitacao['marca']); ?></p>
                            <p><strong>Modelo:</strong> <?php echo htmlspecialchars($solicitacao['modelo']); ?></p>
                            <p><strong>Memória:</strong> <?php echo htmlspecialchars($solicitacao['memoria']); ?></p>
                            <p><strong>Descrição do Problema:</strong><br><?php echo nl2br(htmlspecialchars($solicitacao['descricao_problema'])); ?></p>
                            <p><strong>Método de Entrega:</strong> <?php echo htmlspecialchars($solicitacao['metodo_entrega']); ?></p>
                            <?php if (!empty($solicitacao['endereco'])) { ?>
                                <p><strong>Endereço:</strong> <?php echo htmlspecialchars($solicitacao['endereco']); ?></p>
                            <?php } ?>
                            <?php if (!empty($solicitacao['video'])) { ?>
                                <p><strong>Vídeo:</strong></p>
                                <video width="320" height="240" controls>
                                    <source src="<?php echo htmlspecialchars($solicitacao['video']); ?>" type="video/mp4">
                                    Seu navegador não suporta o elemento de vídeo.
                                </video>
                            <?php } ?>
                            <hr>
                            <button class="btn btn-success btn-sm aceitar-solicitacao" data-id="<?php echo $solicitacao['id']; ?>">
                                <i class="fas fa-check-circle"></i> Aceitar
                            </button>
                            <button class="btn btn-danger btn-sm rejeitar-solicitacao" data-id="<?php echo $solicitacao['id']; ?>">
                                <i class="fas fa-times-circle"></i> Rejeitar
                            </button>
                            <button class="btn btn-primary btn-sm fazer-proposta" data-id="<?php echo $solicitacao['id']; ?>">
                                <i class="fas fa-handshake"></i> Fazer Proposta
                            </button>
                        </div>
                    </div>
                    <?php
                }
            } else {
                echo "<p>Nenhuma solicitação de reparo encontrada.</p>";
            }

            // Fechar a conexão com o banco de dados
            $conn->close();
            ?>
        </div>
    </div>

    <!-- Modal para Fazer Proposta -->
    <div class="modal fade" id="propostaModal" tabindex="-1" aria-labelledby="propostaModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <form id="propostaForm">
                    <div class="modal-header">
                        <h5 class="modal-title" id="propostaModalLabel">Fazer Proposta</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Fechar"></button>
                    </div>
                    <div class="modal-body">
                        <input type="hidden" name="solicitacao_id" id="solicitacao_id">
                        <div class="mb-3">
                            <label for="preco" class="form-label">Preço (R$)</label>
                            <input type="number" step="0.01" class="form-control" id="preco" name="preco" required>
                        </div>
                        <div class="mb-3">
                            <label for="prazo" class="form-label">Prazo (dias)</label>
                            <input type="number" class="form-control" id="prazo" name="prazo" min="1" required>
                        </div>
                        <div class="mb-3">
                            <label for="observacoes" class="form-label">Observações</label>
                            <textarea class="form-control" id="observacoes" name="observacoes" rows="3" placeholder="Detalhes adicionais..."></textarea>
                        </div>
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" value="1" id="retirada_expressa" name="retirada_expressa">
                            <label class="form-check-label" for="retirada_expressa">
                                Retirada Expressa
                            </label>
                        </div>
                        <div id="proposta-feedback" class="text-danger"></div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                        <button type="submit" class="btn btn-primary">Enviar Proposta</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Modal para Confirmação de Aceitar/Rejeitar -->
    <div class="modal fade" id="confirmacaoModal" tabindex="-1" aria-labelledby="confirmacaoModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <form id="acaoForm">
                    <div class="modal-header">
                        <h5 class="modal-title" id="confirmacaoModalLabel">Confirmar Ação</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Fechar"></button>
                    </div>
                    <div class="modal-body">
                        <input type="hidden" name="solicitacao_id" id="confirmacao_solicitacao_id">
                        <input type="hidden" name="action" id="confirmacao_acao">
                        <p id="confirmacao_mensagem"></p>
                        <div id="acao-feedback" class="text-danger"></div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                        <button type="submit" class="btn btn-primary">Confirmar</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Incluindo jQuery, Bootstrap JS e Font Awesome JS -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- JavaScript Personalizado -->
    <script>
    $(document).ready(function() {
        // Manipular o clique no botão "Fazer Proposta"
        $('.fazer-proposta').click(function() {
            var solicitacao_id = $(this).data('id');
            $('#solicitacao_id').val(solicitacao_id);
            $('#propostaForm')[0].reset();
            $('#proposta-feedback').text('');
            $('#retirada_expressa').prop('checked', false);
            var propostaModal = new bootstrap.Modal(document.getElementById('propostaModal'));
            propostaModal.show();
        });

        // Enviar a proposta via AJAX
        $('#propostaForm').submit(function(e) {
            e.preventDefault();
            var formData = {
                action: 'fazer_proposta',
                solicitacao_id: $('#solicitacao_id').val(),
                preco: $('#preco').val(),
                prazo: $('#prazo').val(),
                observacoes: $('#observacoes').val(),
                retirada_expressa: $('#retirada_expressa').is(':checked') ? 1 : 0
            };

            $.ajax({
                url: 'assistencia_tecnica.php',
                type: 'POST',
                data: formData,
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        alert(response.message);
                        $('#propostaModal').modal('hide');
                        location.reload();
                    } else {
                        $('#proposta-feedback').text(response.message);
                    }
                },
                error: function() {
                    $('#proposta-feedback').text('Ocorreu um erro ao enviar a proposta.');
                }
            });
        });

        // Manipular o clique nos botões "Aceitar" e "Rejeitar"
        $('.aceitar-solicitacao, .rejeitar-solicitacao').click(function() {
            var solicitacao_id = $(this).data('id');
            var acao = $(this).hasClass('aceitar-solicitacao') ? 'aceitar' : 'rejeitar';
            $('#confirmacao_solicitacao_id').val(solicitacao_id);
            $('#confirmacao_acao').val(acao);
            $('#confirmacao_mensagem').text(acao === 'aceitar' ? 
                'Tem certeza que deseja aceitar esta solicitação?' : 
                'Tem certeza que deseja rejeitar esta solicitação?');
            $('#acao-feedback').text('');
            var confirmacaoModal = new bootstrap.Modal(document.getElementById('confirmacaoModal'));
            confirmacaoModal.show();
        });

        // Enviar a ação de aceitar/rejeitar via AJAX
        $('#acaoForm').submit(function(e) {
            e.preventDefault();
            var formData = {
                action: $('#confirmacao_acao').val(),
                solicitacao_id: $('#confirmacao_solicitacao_id').val()
            };

            $.ajax({
                url: 'assistencia_tecnica.php',
                type: 'POST',
                data: formData,
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        alert(response.message);
                        $('#confirmacaoModal').modal('hide');
                        location.reload();
                    } else {
                        $('#acao-feedback').text(response.message);
                    }
                },
                error: function() {
                    $('#acao-feedback').text('Ocorreu um erro ao processar a ação.');
                }
            });
        });
    });
    </script>
</body>
</html>
