<?php

ini_set('display_errors', 1);

ini_set('display_startup_errors', 1);

error_reporting(E_ALL);



session_start();

require_once 'db.php';



if (!isset($_SESSION['user_id']) || $_SESSION['nivel_acesso'] != 'fornecedor') {

    header("Location: login.php");

    exit();

}



if (isset($_GET['logout']) && $_GET['logout'] == 'true') {

    session_destroy();

    header("Location: login.php");

    exit();

}



// Excluir produto, se solicitado

if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['excluir_produto'])) {

    try {

        $produto_id = $_POST['produto_id'];

        $query = "DELETE FROM produtos WHERE id = :produto_id";

        $stmt = $pdo->prepare($query);

        $stmt->bindParam(':produto_id', $produto_id, PDO::PARAM_INT);

        $stmt->execute();

        header("Location: dashboard.php");

        exit();

    } catch (PDOException $e) {

        die('Erro ao excluir produto: ' . $e->getMessage());

    }

}



// Finalizar pedido, se solicitado

if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['finalizar_pedido'])) {

    try {

        $pedido_id = $_POST['pedido_id'];

        $query = "UPDATE compras SET status_pedido = 'Finalizado' WHERE id = :pedido_id";

        $stmt = $pdo->prepare($query);

        $stmt->bindParam(':pedido_id', $pedido_id, PDO::PARAM_INT);

        $stmt->execute();

        header("Location: dashboard.php");

        exit();

    } catch (PDOException $e) {

        die('Erro ao finalizar pedido: ' . $e->getMessage());

    }

}



// Atualizar valor do produto, se solicitado

if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['atualizar_produto'])) {

    try {

        $pedido_id = $_POST['pedido_id'];

        $novo_valor = $_POST['novo_valor'];

        $query = "UPDATE compras SET valor_pedido = :novo_valor WHERE id = :pedido_id";

        $stmt = $pdo->prepare($query);

        $stmt->bindParam(':novo_valor', $novo_valor, PDO::PARAM_STR);

        $stmt->bindParam(':pedido_id', $pedido_id, PDO::PARAM_INT);

        $stmt->execute();

        header("Location: dashboard.php");

        exit();

    } catch (PDOException $e) {

        die('Erro ao atualizar valor do produto: ' . $e->getMessage());

    }

}



// Recuperar os pedidos dos lojistas relacionados aos produtos do fornecedor logado

try {

    $pedidos = array();

    $fornecedor_id = $_SESSION['user_id'];

    $queryPedidos = "SELECT compras.*, produtos.nome AS produto_nome, produtos.preco AS produto_preco, usuarios.nome AS lojista_nome

                     FROM compras

                     JOIN produtos ON compras.produto_id = produtos.id

                     JOIN usuarios ON compras.lojista_id = usuarios.id

                     WHERE compras.fornecedor_id = :fornecedor_id";

    $stmtPedidos = $pdo->prepare($queryPedidos);

    $stmtPedidos->bindParam(':fornecedor_id', $fornecedor_id, PDO::PARAM_INT);

    $stmtPedidos->execute();

    $pedidos = $stmtPedidos->fetchAll(PDO::FETCH_ASSOC);

} catch (PDOException $e) {

    die('Erro ao recuperar pedidos: ' . $e->getMessage());

}

?>

<!DOCTYPE html>

<html lang="pt-br">



<head>



    <meta charset="utf-8">

    <meta http-equiv="X-UA-Compatible" content="IE=edge">

    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

    <meta name="description" content="">

    <meta name="author" content="">

    <bootstrap.min.css">

    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">

    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <title>LOJISTA - Dashboard</title>



    <!-- Custom fonts for this template-->

    <link href="vendor/fontawesome-free/css/all.min.css" rel="stylesheet" type="text/css">

    <link href="https://fonts.googleapis.com/css?family=Nunito:200,200i,300,300i,400,400i,600,600i,700,700i,800,800i,900,900i"

        rel="stylesheet">



    <!-- Custom styles for this template-->

    <link href="css/sb-admin-2.min.css" rel="stylesheet">



</head>



<body id="page-top">



    <!-- Page Wrapper -->

    <div id="wrapper">



        <!-- Sidebar -->

        <ul class="navbar-nav bg-gradient-primary sidebar sidebar-dark accordion" id="accordionSidebar">



            <!-- Sidebar - Brand -->

            <a class="sidebar-brand d-flex align-items-center justify-content-center" href="fornecedor_dashboard.php">

                <div class="sidebar-brand-icon rotate-n-15">

                    <i class="fas fa-laugh-wink"></i>

                </div>

                <div class="sidebar-brand-text mx-3"> <sup>Avos Brasil</sup></div>

            </a>



            <!-- Divider -->

            <hr class="sidebar-divider my-0">



            <!-- Nav Item - Dashboard -->

        <li class="nav-item active">

                <a class="nav-link" href="fornecedor_pedidos.php">

                    <i class="fas fa-fw fa-tachometer-alt"></i>

                    <span>Minhas Cotacao</span></a>

            </li>

            <!-- Nav Item - Charts -->

            <li class="nav-item">

                <a class="nav-link" href="cadastro_produto.php">

                    <i class="fas fa-fw fa-chart-area"></i>

                    <span>Cadastrar Produto</span></a>

            </li>

             <li class="nav-item">

                <a class="nav-link" href="fornecedor_produto.php">

                    <i class="fas fa-fw fa-chart-area"></i>

                    <span>Meus Produto</span></a>

            </li>

            <li class="nav-item">

                <a class="nav-link" href="fornecedor_informacoes.php">

                    <i class="fas fa-fw fa-chart-area"></i>

                    <span>Dashboard Financeiro</span></a>

            </li>



            <!-- Divider -->

            <hr class="sidebar-divider d-none d-md-block">



            <!-- Sidebar Toggler (Sidebar) -->

            <div class="text-center d-none d-md-inline">

                <button class="rounded-circle border-0" id="sidebarToggle"></button>

            </div>



            <!-- Sidebar Message -->

            <div class="sidebar-card d-none d-lg-flex">

                <img class="sidebar-card-illustration mb-2" src="img/undraw_rocket.svg" alt="...">

                <p class="text-center mb-2"><strong>Avos Brasil Pro</strong>Controle LFM_Consultoria</p>

            </div>



        </ul>

        <!-- End of Sidebar -->



        <!-- Content Wrapper -->

        <div id="content-wrapper" class="d-flex flex-column">



            <!-- Main Content -->

            <div id="content">



                <!-- Topbar -->

                <nav class="navbar navbar-expand navbar-light bg-white topbar mb-4 static-top shadow">



                    <!-- Sidebar Toggle (Topbar) -->

                    <button id="sidebarToggleTop" class="btn btn-link d-md-none rounded-circle mr-3">

                        <i class="fa fa-bars"></i>

                    </button>



                    <!-- Topbar Search -->

                    <form class="d-none d-sm-inline-block form-inline mr-auto ml-md-3 my-2 my-md-0 mw-100 navbar-search">

                        <div class="input-group">

                            <input type="text" class="form-control bg-light border-0 small" placeholder="Search for..."

                                aria-label="Search" aria-describedby="basic-addon2">

                            <div class="input-group-append">

                                <button class="btn btn-primary" type="button">

                                    <i class="fas fa-search fa-sm"></i>

                                </button>

                            </div>

                        </div>

                    </form>



                    <!-- Topbar Navbar -->

                    <ul class="navbar-nav ml-auto">



                        <!-- Nav Item - User Information -->

                        <li class="nav-item dropdown no-arrow">

                            <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button"

                                data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">

                                <span class="mr-2 d-none d-lg-inline text-gray-600 small">LOJISTA</span>

                                <img class="img-profile rounded-circle"

                                    src="img/undraw_profile.svg">

                            </a>

                            <!-- Dropdown - User Information -->

                            <div class="dropdown-menu dropdown-menu-right shadow animated--grow-in"

                                aria-labelledby="userDropdown">

                                <a class="dropdown-item" href="#">

                                    <i class="fas fa-user fa-sm fa-fw mr-2 text-gray-400"></i>

                                    Profile

                                </a>

                                <a class="dropdown-item" href="#">

                                    <i class="fas fa-cogs fa-sm fa-fw mr-2 text-gray-400"></i>

                                    Settings

                                </a>

                                <a class="dropdown-item" href="#">

                                    <i class="fas fa-list fa-sm fa-fw mr-2 text-gray-400"></i>

                                    Activity Log

                                </a>

                                <div class="dropdown-divider"></div>

                                <a class="dropdown-item" href="#" data-toggle="modal" data-target="#logoutModal">

                                    <i class="fas fa-sign-out-alt fa-sm fa-fw mr-2 text-gray-400"></i>

                                    Logout

                                </a>

                            </div>

                        </li>



                    </ul>



                </nav>

                <!-- End of Topbar -->



                <!-- Begin Page Content -->

                <div class="container-fluid">

                <h2 class="mt-5">Dashboard - Fornecedor</h2>

       

       <h4 class="mt-4">Pedidos dos Lojistas</h4>

       <div class="form-row">

           <div class="form-group col-md-6">

               <label for="filtro-pedidos">Filtrar pedidos dos lojistas</label>

               <input class="form-control mb-3" id="filtro-pedidos" type="text" placeholder="Pesquisar...">

           </div>

           <div class="form-group col-md-3">

               <label for="filtro-status">Filtrar por status</label>

               <select class="form-control" id="filtro-status">

                   <option value="">Todos</option>

                   <option value="Finalizado">Finalizado</option>

                   <option value="Não finalizado">Não finalizado</option>

               </select>

           </div>

           <div class="form-group col-md-3">

               <label for="filtro-data">Filtrar por data</label>

               <select class="form-control" id="filtro-data">

                   <option value="">Todos</option>

                   <option value="Agendado">Agendado</option>

                   <option value="Não agendado">Não agendado</option>

               </select>

           </div>

       </div>



       <div class="table-responsive">

           <table class="table table-bordered mt-3">

               <thead>

                   <tr>

                       

                       <th>Lojista</th>

                       <th>Produto</th>

                       <th>Quantidade</th>

                       <th>Valor Pedido</th>

                       <th>Status Pedido</th>

                       <th>Data Programada</th>

                       <th>Ações</th>

                   </tr>

               </thead>

               <tbody>

                   <?php if (!empty($pedidos)): ?>

                       <?php foreach ($pedidos as $key => $pedido): ?>

                           <tr>

                               

                               <td><?php echo $pedido['lojista_nome']; ?></td>

                               <td><?php echo $pedido['produto_nome']; ?></td>

                               <td><?php echo $pedido['quantidade']; ?></td>

                               <td>

                                   <form method="post" action="">

                                       <input type="hidden" name="pedido_id" value="<?php echo $pedido['id']; ?>">

                                       <input type="hidden" name="produto_id" value="<?php echo $pedido['produto_id']; ?>">

                                       <div class="form-group">

                                           <input type="text" name="novo_valor" class="form-control" value="<?php echo $pedido['valor_pedido']; ?>">

                                       </div>

                                       <button type="submit" class="btn btn-sm btn-primary" name="atualizar_produto">Atualizar</button>

                                   </form>

                               </td>

                               <td><?php echo $pedido['status_pedido']; ?></td>

                               <td>

                                   <?php if ($pedido['data_programada']): ?>

                                       <?php echo date('d/m/Y', strtotime($pedido['data_programada'])); ?>

                                   <?php else: ?>

                                       Não agendado

                                   <?php endif; ?>

                               </td>

                               <td>

                                   <?php if ($pedido['status_pedido'] != 'Finalizado'): ?>

                                       <form method="post" action="">

                                           <input type="hidden" name="pedido_id" value="<?php echo $pedido['id']; ?>">

                                           <button type="submit" class="btn btn-sm btn-success" name="finalizar_pedido">Finalizar Pedido</button>

                                       </form>

                                   <?php endif; ?>

                                   <a href="informacoes_pedido.php?pedido_id=<?php echo $pedido['id']; ?>" class="btn btn-sm btn-info">Detalhes</a>

                               </td>

                           </tr>

                       <?php endforeach; ?>

                   <?php else: ?>

                       <tr>

                           <td colspan="8">Nenhum pedido encontrado.</td>

                       </tr>

                   <?php endif; ?>

               </tbody>

           </table>

       </div>

                </div>





                </div>

                <!-- /.container-fluid -->



            </div>

            <!-- End of Main Content -->



            <!-- End of Footer -->



        </div>

        <!-- End of Content Wrapper -->



    </div>

    <!-- End of Page Wrapper -->



    <!-- Scroll to Top Button-->

    <a class="scroll-to-top rounded" href="#page-top">

        <i class="fas fa-angle-up"></i>

    </a>



    <!-- Logout Modal-->

    <div class="modal fade" id="logoutModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"

        aria-hidden="true">

        <div class="modal-dialog" role="document">

            <div class="modal-content">

                <div class="modal-header">

                    <h5 class="modal-title" id="exampleModalLabel">Pronto para sair?</h5>

                    <button class="close" type="button" data-dismiss="modal" aria-label="Close">

                        <span aria-hidden="true">×</span>

                    </button>

                </div>

                <div class="modal-body">Selecione "Logout" abaixo se você estiver pronto para terminar sua sessão atual.</div>

                <div class="modal-footer">

                    <button class="btn btn-secondary" type="button" data-dismiss="modal">Cancelar</button>

                    <a class="btn btn-primary" href="?logout=true">Logout</a>

                </div>

            </div>

        </div>

    </div>



    <!-- Bootstrap core JavaScript-->

    <script src="vendor/jquery/jquery.min.js"></script>

    <script src="vendor/bootstrap/js/bootstrap.bundle.min.js"></script>



    <!-- Core plugin JavaScript-->

    <script src="vendor/jquery-easing/jquery.easing.min.js"></script>



    <!-- Custom scripts for all pages-->

    <script src="js/sb-admin-2.min.js"></script>

    

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>

    // Gráfico de pedidos

    var ctxPedidos = document.getElementById('pedidosChart').getContext('2d');

    var pedidosChart = new Chart(ctxPedidos, {

        type: 'bar',

        data: {

            labels: ['Pedidos'],

            datasets: [{

                label: 'Total de Pedidos',

                data: [<?php echo $totalPedidos['total_pedidos']; ?>],

                backgroundColor: 'rgba(255, 99, 132, 0.2)',

                borderColor: 'rgba(255, 99, 132, 1)',

                borderWidth: 1

            }]

        },

        options: {

            scales: {

                y: {

                    beginAtZero: true

                }

            }

        }

    });



</script>



</script>

 <!-- Script de Inicialização do Tawk.to -->

    <script type="text/javascript">

        var Tawk_API = Tawk_API || {}, Tawk_LoadStart = new Date();

        (function () {

            var s1 = document.createElement("script"), s0 = document.getElementsByTagName("script")[0];

            s1.async = true;

            s1.src = 'https://embed.tawk.to/66269f961ec1082f04e59b7a/1hs3dupor';

            s1.charset = 'UTF-8';

            s1.setAttribute('crossorigin', '*');

            s0.parentNode.insertBefore(s1, s0);

        })();

    </script>





</body>



</html>

