<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="terminal" baseProfile="full" viewBox="0 0 703 489" width="703" version="1.1">
    <defs>
        <termtosvg:template_settings xmlns:termtosvg="https://github.com/nbedos/termtosvg">
            <termtosvg:screen_geometry columns="82" rows="24"/>
        </termtosvg:template_settings>
        <style type="text/css" id="generated-style"><![CDATA[:root {
            --animation-duration: 46358ms;
        }
        
        #screen {
                font-family: 'DejaVu Sans Mono', monospace;
                font-style: normal;
                font-size: 14px;
            }

        text {
            dominant-baseline: text-before-edge;
            white-space: pre;
        }]]></style>
        <style type="text/css" id="user-style">
            /* gjm8 color theme (source: https://terminal.sexy/) */
            .foreground {fill: #f8f8f2}
            .background {fill: #272822}
            .color0 {fill: #272822}
            .color1 {fill: #f92672}
            .color2 {fill: #a6e22e}
            .color3 {fill: #f4bf75}
            .color4 {fill: #66d9ef}
            .color5 {fill: #ae81ff}
            .color6 {fill: #a1efe4}
            .color7 {fill: #f8f8f2}
            .color8 {fill: #75715e}
            .color9 {fill: #fd971f}
            .color10 {fill: #383830}
            .color11 {fill: #49483e}
            .color12 {fill: #a59f85}
            .color13 {fill: #f5f4f1}
            .color14 {fill: #cc6633}
            .color15 {fill: #f9f8f5}
        </style>
    </defs>
    <rect id="terminalui" class="background" width="100%" height="100%" ry="4.5826941"/>
    <circle cx="24" cy="23" r="7" class="color1"/>
    <circle cx="44" cy="23" r="7" class="color3"/>
    <circle cx="64" cy="23" r="7" class="color2"/>
    <svg id="screen" width="656" x="23" y="50" viewBox="0 0 656 408" preserveAspectRatio="xMidYMin meet">
        <rect class="background" height="100%" width="100%" x="0" y="0"/><defs><g id="g1"><text class="background" textLength="8" x="0"> </text></g></defs><g display="none"><rect class="foreground" height="17" width="8" x="0" y="0"/><use y="0" xlink:href="#g1"/><animate attributeName="display" begin="0ms; anim_last.end" dur="377ms" from="inline" to="inline"/></g><defs><g id="g2"><text class="background" textLength="8" x="0"> </text><text class="foreground" textLength="648" x="8">                                                                                 </text></g></defs><g display="none"><rect class="foreground" height="17" width="8" x="0" y="0"/><use y="0" xlink:href="#g2"/><animate attributeName="display" begin="377ms; anim_last.end+377ms" dur="1ms" from="inline" to="inline"/></g><g display="none"><rect class="foreground" height="17" width="8" x="0" y="0"/><use y="0" xlink:href="#g2"/><animate attributeName="display" begin="378ms; anim_last.end+378ms" dur="5ms" from="inline" to="inline"/></g><g display="none"><rect class="foreground" height="17" width="8" x="0" y="0"/><use y="0" xlink:href="#g2"/><animate attributeName="display" begin="383ms; anim_last.end+383ms" dur="81ms" from="inline" to="inline"/></g><defs><g id="g3"><text class="color10" font-weight="bold" textLength="24" x="0">&#10140;  </text><text class="color14" font-weight="bold" textLength="64" x="24">demo-php</text><text class="foreground" textLength="8" x="88"> </text><text class="background" textLength="8" x="96"> </text><text class="foreground" textLength="552" x="104">                                                                     </text></g></defs><g display="none"><rect class="foreground" height="17" width="8" x="96" y="0"/><use y="0" xlink:href="#g3"/><animate attributeName="display" begin="464ms; anim_last.end+464ms" dur="1ms" from="inline" to="inline"/></g><g display="none"><rect class="foreground" height="17" width="8" x="96" y="0"/><use y="0" xlink:href="#g3"/><animate attributeName="display" begin="465ms; anim_last.end+465ms" dur="503ms" from="inline" to="inline"/></g><defs><g id="g4"><text class="color10" font-weight="bold" textLength="24" x="0">&#10140;  </text><text class="color14" font-weight="bold" textLength="64" x="24">demo-php</text><text class="foreground" textLength="16" x="88"> c</text><text class="background" textLength="8" x="104"> </text><text class="foreground" textLength="544" x="112">                                                                    </text></g></defs><g display="none"><rect class="foreground" height="17" width="8" x="104" y="0"/><use y="0" xlink:href="#g4"/><animate attributeName="display" begin="968ms; anim_last.end+968ms" dur="70ms" from="inline" to="inline"/></g><defs><g id="g5"><text class="color10" font-weight="bold" textLength="24" x="0">&#10140;  </text><text class="color14" font-weight="bold" textLength="64" x="24">demo-php</text><text class="foreground" textLength="24" x="88"> co</text><text class="background" textLength="8" x="112"> </text><text class="foreground" textLength="536" x="120">                                                                   </text></g></defs><g display="none"><rect class="foreground" height="17" width="8" x="112" y="0"/><use y="0" xlink:href="#g5"/><animate attributeName="display" begin="1038ms; anim_last.end+1038ms" dur="80ms" from="inline" to="inline"/></g><defs><g id="g6"><text class="color10" font-weight="bold" textLength="24" x="0">&#10140;  </text><text class="color14" font-weight="bold" textLength="64" x="24">demo-php</text><text class="foreground" textLength="32" x="88"> com</text><text class="background" textLength="8" x="120"> </text><text class="foreground" textLength="528" x="128">                                                                  </text></g></defs><g display="none"><rect class="foreground" height="17" width="8" x="120" y="0"/><use y="0" xlink:href="#g6"/><animate attributeName="display" begin="1118ms; anim_last.end+1118ms" dur="231ms" from="inline" to="inline"/></g><defs><g id="g7"><text class="color10" font-weight="bold" textLength="24" x="0">&#10140;  </text><text class="color14" font-weight="bold" textLength="64" x="24">demo-php</text><text class="foreground" textLength="40" x="88"> comp</text><text class="background" textLength="8" x="128"> </text><text class="foreground" textLength="520" x="136">                                                                 </text></g></defs><g display="none"><rect class="foreground" height="17" width="8" x="128" y="0"/><use y="0" xlink:href="#g7"/><animate attributeName="display" begin="1349ms; anim_last.end+1349ms" dur="71ms" from="inline" to="inline"/></g><defs><g id="g8"><text class="color10" font-weight="bold" textLength="24" x="0">&#10140;  </text><text class="color14" font-weight="bold" textLength="64" x="24">demo-php</text><text class="foreground" textLength="48" x="88"> compo</text><text class="background" textLength="8" x="136"> </text><text class="foreground" textLength="512" x="144">                                                                </text></g></defs><g display="none"><rect class="foreground" height="17" width="8" x="136" y="0"/><use y="0" xlink:href="#g8"/><animate attributeName="display" begin="1420ms; anim_last.end+1420ms" dur="151ms" from="inline" to="inline"/></g><defs><g id="g9"><text class="color10" font-weight="bold" textLength="24" x="0">&#10140;  </text><text class="color14" font-weight="bold" textLength="64" x="24">demo-php</text><text class="foreground" textLength="56" x="88"> compos</text><text class="background" textLength="8" x="144"> </text><text class="foreground" textLength="504" x="152">                                                               </text></g></defs><g display="none"><rect class="foreground" height="17" width="8" x="144" y="0"/><use y="0" xlink:href="#g9"/><animate attributeName="display" begin="1571ms; anim_last.end+1571ms" dur="224ms" from="inline" to="inline"/></g><defs><g id="g10"><text class="color10" font-weight="bold" textLength="24" x="0">&#10140;  </text><text class="color14" font-weight="bold" textLength="64" x="24">demo-php</text><text class="foreground" textLength="64" x="88"> compose</text><text class="background" textLength="8" x="152"> </text><text class="foreground" textLength="496" x="160">                                                              </text></g></defs><g display="none"><rect class="foreground" height="17" width="8" x="152" y="0"/><use y="0" xlink:href="#g10"/><animate attributeName="display" begin="1795ms; anim_last.end+1795ms" dur="63ms" from="inline" to="inline"/></g><defs><g id="g11"><text class="color10" font-weight="bold" textLength="24" x="0">&#10140;  </text><text class="color14" font-weight="bold" textLength="64" x="24">demo-php</text><text class="foreground" textLength="72" x="88"> composer</text><text class="background" textLength="8" x="160"> </text><text class="foreground" textLength="488" x="168">                                                             </text></g></defs><g display="none"><rect class="foreground" height="17" width="8" x="160" y="0"/><use y="0" xlink:href="#g11"/><animate attributeName="display" begin="1858ms; anim_last.end+1858ms" dur="192ms" from="inline" to="inline"/></g><defs><g id="g12"><text class="color10" font-weight="bold" textLength="24" x="0">&#10140;  </text><text class="color14" font-weight="bold" textLength="64" x="24">demo-php</text><text class="foreground" textLength="80" x="88"> composer </text><text class="background" textLength="8" x="168"> </text><text class="foreground" textLength="480" x="176">                                                            </text></g></defs><g display="none"><rect class="foreground" height="17" width="8" x="168" y="0"/><use y="0" xlink:href="#g12"/><animate attributeName="display" begin="2050ms; anim_last.end+2050ms" dur="144ms" from="inline" to="inline"/></g><defs><g id="g13"><text class="color10" font-weight="bold" textLength="24" x="0">&#10140;  </text><text class="color14" font-weight="bold" textLength="64" x="24">demo-php</text><text class="foreground" textLength="88" x="88"> composer -</text><text class="background" textLength="8" x="176"> </text><text class="foreground" textLength="472" x="184">                                                           </text></g></defs><g display="none"><rect class="foreground" height="17" width="8" x="176" y="0"/><use y="0" xlink:href="#g13"/><animate attributeName="display" begin="2194ms; anim_last.end+2194ms" dur="175ms" from="inline" to="inline"/></g><defs><g id="g14"><text class="color10" font-weight="bold" textLength="24" x="0">&#10140;  </text><text class="color14" font-weight="bold" textLength="64" x="24">demo-php</text><text class="foreground" textLength="96" x="88"> composer --</text><text class="background" textLength="8" x="184"> </text><text class="foreground" textLength="464" x="192">                                                          </text></g></defs><g display="none"><rect class="foreground" height="17" width="8" x="184" y="0"/><use y="0" xlink:href="#g14"/><animate attributeName="display" begin="2369ms; anim_last.end+2369ms" dur="127ms" from="inline" to="inline"/></g><defs><g id="g15"><text class="color10" font-weight="bold" textLength="24" x="0">&#10140;  </text><text class="color14" font-weight="bold" textLength="64" x="24">demo-php</text><text class="foreground" textLength="104" x="88"> composer --v</text><text class="background" textLength="8" x="192"> </text><text class="foreground" textLength="456" x="200">                                                         </text></g></defs><g display="none"><rect class="foreground" height="17" width="8" x="192" y="0"/><use y="0" xlink:href="#g15"/><animate attributeName="display" begin="2496ms; anim_last.end+2496ms" dur="128ms" from="inline" to="inline"/></g><defs><g id="g16"><text class="color10" font-weight="bold" textLength="24" x="0">&#10140;  </text><text class="color14" font-weight="bold" textLength="64" x="24">demo-php</text><text class="foreground" textLength="112" x="88"> composer --ve</text><text class="background" textLength="8" x="200"> </text><text class="foreground" textLength="448" x="208">                                                        </text></g></defs><g display="none"><rect class="foreground" height="17" width="8" x="200" y="0"/><use y="0" xlink:href="#g16"/><animate attributeName="display" begin="2624ms; anim_last.end+2624ms" dur="95ms" from="inline" to="inline"/></g><defs><g id="g17"><text class="color10" font-weight="bold" textLength="24" x="0">&#10140;  </text><text class="color14" font-weight="bold" textLength="64" x="24">demo-php</text><text class="foreground" textLength="120" x="88"> composer --ver</text><text class="background" textLength="8" x="208"> </text><text class="foreground" textLength="440" x="216">                                                       </text></g></defs><g display="none"><rect class="foreground" height="17" width="8" x="208" y="0"/><use y="0" xlink:href="#g17"/><animate attributeName="display" begin="2719ms; anim_last.end+2719ms" dur="159ms" from="inline" to="inline"/></g><defs><g id="g18"><text class="color10" font-weight="bold" textLength="24" x="0">&#10140;  </text><text class="color14" font-weight="bold" textLength="64" x="24">demo-php</text><text class="foreground" textLength="128" x="88"> composer --vers</text><text class="background" textLength="8" x="216"> </text><text class="foreground" textLength="432" x="224">                                                      </text></g></defs><g display="none"><rect class="foreground" height="17" width="8" x="216" y="0"/><use y="0" xlink:href="#g18"/><animate attributeName="display" begin="2878ms; anim_last.end+2878ms" dur="71ms" from="inline" to="inline"/></g><defs><g id="g19"><text class="color10" font-weight="bold" textLength="24" x="0">&#10140;  </text><text class="color14" font-weight="bold" textLength="64" x="24">demo-php</text><text class="foreground" textLength="136" x="88"> composer --versi</text><text class="background" textLength="8" x="224"> </text><text class="foreground" textLength="424" x="232">                                                     </text></g></defs><g display="none"><rect class="foreground" height="17" width="8" x="224" y="0"/><use y="0" xlink:href="#g19"/><animate attributeName="display" begin="2949ms; anim_last.end+2949ms" dur="232ms" from="inline" to="inline"/></g><defs><g id="g20"><text class="color10" font-weight="bold" textLength="24" x="0">&#10140;  </text><text class="color14" font-weight="bold" textLength="64" x="24">demo-php</text><text class="foreground" textLength="144" x="88"> composer --versio</text><text class="background" textLength="8" x="232"> </text><text class="foreground" textLength="416" x="240">                                                    </text></g></defs><g display="none"><rect class="foreground" height="17" width="8" x="232" y="0"/><use y="0" xlink:href="#g20"/><animate attributeName="display" begin="3181ms; anim_last.end+3181ms" dur="64ms" from="inline" to="inline"/></g><defs><g id="g21"><text class="color10" font-weight="bold" textLength="24" x="0">&#10140;  </text><text class="color14" font-weight="bold" textLength="64" x="24">demo-php</text><text class="foreground" textLength="152" x="88"> composer --version</text><text class="background" textLength="8" x="240"> </text><text class="foreground" textLength="408" x="248">                                                   </text></g></defs><g display="none"><rect class="foreground" height="17" width="8" x="240" y="0"/><use y="0" xlink:href="#g21"/><animate attributeName="display" begin="3245ms; anim_last.end+3245ms" dur="271ms" from="inline" to="inline"/></g><g display="none"><rect class="foreground" height="17" width="8" x="0" y="17"/><use y="17" xlink:href="#g1"/><animate attributeName="display" begin="3516ms; anim_last.end+3516ms" dur="1ms" from="inline" to="inline"/></g><g display="none"><rect class="foreground" height="17" width="8" x="0" y="17"/><use y="17" xlink:href="#g1"/><animate attributeName="display" begin="3517ms; anim_last.end+3517ms" dur="114ms" from="inline" to="inline"/></g><g display="none"><rect class="foreground" height="17" width="8" x="0" y="34"/><use y="34" xlink:href="#g1"/><animate attributeName="display" begin="3631ms; anim_last.end+3631ms" dur="6ms" from="inline" to="inline"/></g><g display="none"><rect class="foreground" height="17" width="8" x="0" y="34"/><use y="34" xlink:href="#g2"/><animate attributeName="display" begin="3637ms; anim_last.end+3637ms" dur="1ms" from="inline" to="inline"/></g><g display="none"><rect class="foreground" height="17" width="8" x="0" y="34"/><use y="34" xlink:href="#g2"/><animate attributeName="display" begin="3638ms; anim_last.end+3638ms" dur="6ms" from="inline" to="inline"/></g><g display="none"><rect class="foreground" height="17" width="8" x="0" y="34"/><use y="34" xlink:href="#g2"/><animate attributeName="display" begin="3644ms; anim_last.end+3644ms" dur="66ms" from="inline" to="inline"/></g><g display="none"><rect class="foreground" height="17" width="8" x="96" y="34"/><use y="34" xlink:href="#g3"/><animate attributeName="display" begin="3710ms; anim_last.end+3710ms" dur="1ms" from="inline" to="inline"/></g><g display="none"><rect class="foreground" height="17" width="8" x="96" y="34"/><use y="34" xlink:href="#g3"/><animate attributeName="display" begin="3711ms; anim_last.end+3711ms" dur="3154ms" from="inline" to="inline"/></g><defs><g id="g22"><text class="color10" font-weight="bold" textLength="24" x="0">&#10140;  </text><text class="color14" font-weight="bold" textLength="64" x="24">demo-php</text><text class="foreground" textLength="8" x="88"> </text><text class="background" textLength="352" x="96">composer require "mercadopago/dx-php:1.2.1" </text><text class="foreground" textLength="208" x="448">                          </text></g></defs><g display="none"><rect class="foreground" height="17" width="352" x="96" y="34"/><use y="34" xlink:href="#g22"/><animate attributeName="display" begin="6865ms; anim_last.end+6865ms" dur="246ms" from="inline" to="inline"/></g><defs><g id="g23"><text class="color10" font-weight="bold" textLength="24" x="0">&#10140;  </text><text class="color14" font-weight="bold" textLength="64" x="24">demo-php</text><text class="foreground" textLength="352" x="88"> composer require "mercadopago/dx-php:1.2.1"</text><text class="background" textLength="8" x="440"> </text><text class="foreground" textLength="208" x="448">                          </text></g></defs><g display="none"><rect class="foreground" height="17" width="8" x="440" y="34"/><use y="34" xlink:href="#g23"/><animate attributeName="display" begin="7111ms; anim_last.end+7111ms" dur="2ms" from="inline" to="inline"/></g><g display="none"><rect class="foreground" height="17" width="8" x="0" y="51"/><use y="51" xlink:href="#g1"/><animate attributeName="display" begin="7113ms; anim_last.end+7113ms" dur="3ms" from="inline" to="inline"/></g><g display="none"><rect class="foreground" height="17" width="8" x="0" y="51"/><use y="51" xlink:href="#g1"/><animate attributeName="display" begin="7116ms; anim_last.end+7116ms" dur="5690ms" from="inline" to="inline"/></g><g display="none"><rect class="foreground" height="17" width="8" x="0" y="68"/><use y="68" xlink:href="#g1"/><animate attributeName="display" begin="12806ms; anim_last.end+12806ms" dur="356ms" from="inline" to="inline"/></g><g display="none"><rect class="foreground" height="17" width="8" x="0" y="85"/><use y="85" xlink:href="#g1"/><animate attributeName="display" begin="13162ms; anim_last.end+13162ms" dur="802ms" from="inline" to="inline"/></g><g display="none"><rect class="foreground" height="17" width="8" x="0" y="102"/><use y="102" xlink:href="#g1"/><animate attributeName="display" begin="13964ms; anim_last.end+13964ms" dur="803ms" from="inline" to="inline"/></g><g display="none"><rect class="foreground" height="17" width="8" x="0" y="119"/><use y="119" xlink:href="#g1"/><animate attributeName="display" begin="14767ms; anim_last.end+14767ms" dur="1ms" from="inline" to="inline"/></g><defs><g id="g24"><text class="foreground" textLength="120" x="0">  - Installing </text><text class="color2" textLength="112" x="120">doctrine/lexer</text><text class="foreground" textLength="16" x="232"> (</text><text class="color3" textLength="48" x="248">v1.0.1</text><text class="foreground" textLength="24" x="296">): </text><text class="background" textLength="8" x="320"> </text></g></defs><g display="none"><rect class="foreground" height="17" width="8" x="320" y="119"/><use y="119" xlink:href="#g24"/><animate attributeName="display" begin="14768ms; anim_last.end+14768ms" dur="1ms" from="inline" to="inline"/></g><defs><g id="g25"><text class="foreground" textLength="120" x="0">  - Installing </text><text class="color2" textLength="112" x="120">doctrine/lexer</text><text class="foreground" textLength="16" x="232"> (</text><text class="color3" textLength="48" x="248">v1.0.1</text><text class="foreground" textLength="128" x="296">): Downloading (</text><text class="color3" textLength="104" x="424">connecting...</text><text class="foreground" textLength="8" x="528">)</text><text class="background" textLength="8" x="536"> </text></g></defs><g display="none"><rect class="foreground" height="17" width="8" x="536" y="119"/><use y="119" xlink:href="#g25"/><animate attributeName="display" begin="14769ms; anim_last.end+14769ms" dur="1943ms" from="inline" to="inline"/></g><defs><g id="g26"><text class="foreground" textLength="120" x="0">  - Installing </text><text class="color2" textLength="112" x="120">doctrine/lexer</text><text class="foreground" textLength="16" x="232"> (</text><text class="color3" textLength="48" x="248">v1.0.1</text><text class="foreground" textLength="24" x="296">): </text><text class="background" textLength="8" x="320">D</text><text class="foreground" textLength="96" x="328">ownloading (</text><text class="color3" textLength="104" x="424">connecting...</text><text class="foreground" textLength="8" x="528">)</text></g></defs><g display="none"><rect class="foreground" height="17" width="8" x="320" y="119"/><use y="119" xlink:href="#g26"/><animate attributeName="display" begin="16712ms; anim_last.end+16712ms" dur="1ms" from="inline" to="inline"/></g><defs><g id="g27"><text class="foreground" textLength="120" x="0">  - Installing </text><text class="color2" textLength="112" x="120">doctrine/lexer</text><text class="foreground" textLength="16" x="232"> (</text><text class="color3" textLength="48" x="248">v1.0.1</text><text class="foreground" textLength="128" x="296">): Downloading (</text><text class="color3" textLength="16" x="424">0%</text><text class="foreground" textLength="8" x="440">)</text><text class="background" textLength="8" x="448"> </text><text class="foreground" textLength="80" x="456">          </text></g></defs><g display="none"><rect class="foreground" height="17" width="8" x="448" y="119"/><use y="119" xlink:href="#g27"/><animate attributeName="display" begin="16713ms; anim_last.end+16713ms" dur="3ms" from="inline" to="inline"/></g><g display="none"><rect class="foreground" height="17" width="8" x="0" y="136"/><use y="136" xlink:href="#g1"/><animate attributeName="display" begin="16716ms; anim_last.end+16716ms" dur="63ms" from="inline" to="inline"/></g><defs><g id="g28"><text class="foreground" textLength="120" x="0">  - Installing </text><text class="color2" textLength="144" x="120">doctrine/inflector</text><text class="foreground" textLength="16" x="264"> (</text><text class="color3" textLength="48" x="280">v1.3.0</text><text class="foreground" textLength="24" x="328">): </text><text class="background" textLength="8" x="352"> </text></g></defs><g display="none"><rect class="foreground" height="17" width="8" x="352" y="136"/><use y="136" xlink:href="#g28"/><animate attributeName="display" begin="16779ms; anim_last.end+16779ms" dur="1ms" from="inline" to="inline"/></g><defs><g id="g29"><text class="foreground" textLength="120" x="0">  - Installing </text><text class="color2" textLength="144" x="120">doctrine/inflector</text><text class="foreground" textLength="16" x="264"> (</text><text class="color3" textLength="48" x="280">v1.3.0</text><text class="foreground" textLength="128" x="328">): Downloading (</text><text class="color3" textLength="104" x="456">connecting...</text><text class="foreground" textLength="8" x="560">)</text><text class="background" textLength="8" x="568"> </text></g></defs><g display="none"><rect class="foreground" height="17" width="8" x="568" y="136"/><use y="136" xlink:href="#g29"/><animate attributeName="display" begin="16780ms; anim_last.end+16780ms" dur="1526ms" from="inline" to="inline"/></g><defs><g id="g30"><text class="foreground" textLength="120" x="0">  - Installing </text><text class="color2" textLength="144" x="120">doctrine/inflector</text><text class="foreground" textLength="16" x="264"> (</text><text class="color3" textLength="48" x="280">v1.3.0</text><text class="foreground" textLength="24" x="328">): </text><text class="background" textLength="8" x="352">D</text><text class="foreground" textLength="96" x="360">ownloading (</text><text class="color3" textLength="104" x="456">connecting...</text><text class="foreground" textLength="8" x="560">)</text></g></defs><g display="none"><rect class="foreground" height="17" width="8" x="352" y="136"/><use y="136" xlink:href="#g30"/><animate attributeName="display" begin="18306ms; anim_last.end+18306ms" dur="2ms" from="inline" to="inline"/></g><defs><g id="g31"><text class="foreground" textLength="120" x="0">  - Installing </text><text class="color2" textLength="144" x="120">doctrine/inflector</text><text class="foreground" textLength="16" x="264"> (</text><text class="color3" textLength="48" x="280">v1.3.0</text><text class="foreground" textLength="128" x="328">): Downloading (</text><text class="color3" textLength="24" x="456">70%</text><text class="foreground" textLength="8" x="480">)</text><text class="background" textLength="8" x="488"> </text><text class="foreground" textLength="72" x="496">         </text></g></defs><g display="none"><rect class="foreground" height="17" width="8" x="488" y="136"/><use y="136" xlink:href="#g31"/><animate attributeName="display" begin="18308ms; anim_last.end+18308ms" dur="2ms" from="inline" to="inline"/></g><g display="none"><rect class="foreground" height="17" width="8" x="0" y="153"/><use y="153" xlink:href="#g1"/><animate attributeName="display" begin="18310ms; anim_last.end+18310ms" dur="53ms" from="inline" to="inline"/></g><defs><g id="g32"><text class="foreground" textLength="120" x="0">  - Installing </text><text class="color2" textLength="160" x="120">doctrine/collections</text><text class="foreground" textLength="16" x="280"> (</text><text class="color3" textLength="48" x="296">v1.5.0</text><text class="foreground" textLength="24" x="344">): </text><text class="background" textLength="8" x="368"> </text></g></defs><g display="none"><rect class="foreground" height="17" width="8" x="368" y="153"/><use y="153" xlink:href="#g32"/><animate attributeName="display" begin="18363ms; anim_last.end+18363ms" dur="2ms" from="inline" to="inline"/></g><defs><g id="g33"><text class="foreground" textLength="120" x="0">  - Installing </text><text class="color2" textLength="160" x="120">doctrine/collections</text><text class="foreground" textLength="16" x="280"> (</text><text class="color3" textLength="48" x="296">v1.5.0</text><text class="foreground" textLength="128" x="344">): Downloading (</text><text class="color3" textLength="104" x="472">connecting...</text><text class="foreground" textLength="8" x="576">)</text><text class="background" textLength="8" x="584"> </text></g></defs><g display="none"><rect class="foreground" height="17" width="8" x="584" y="153"/><use y="153" xlink:href="#g33"/><animate attributeName="display" begin="18365ms; anim_last.end+18365ms" dur="1514ms" from="inline" to="inline"/></g><defs><g id="g34"><text class="foreground" textLength="120" x="0">  - Installing </text><text class="color2" textLength="160" x="120">doctrine/collections</text><text class="foreground" textLength="16" x="280"> (</text><text class="color3" textLength="48" x="296">v1.5.0</text><text class="foreground" textLength="24" x="344">): </text><text class="background" textLength="8" x="368">D</text><text class="foreground" textLength="96" x="376">ownloading (</text><text class="color3" textLength="104" x="472">connecting...</text><text class="foreground" textLength="8" x="576">)</text></g></defs><g display="none"><rect class="foreground" height="17" width="8" x="368" y="153"/><use y="153" xlink:href="#g34"/><animate attributeName="display" begin="19879ms; anim_last.end+19879ms" dur="1ms" from="inline" to="inline"/></g><defs><g id="g35"><text class="foreground" textLength="120" x="0">  - Installing </text><text class="color2" textLength="160" x="120">doctrine/collections</text><text class="foreground" textLength="16" x="280"> (</text><text class="color3" textLength="48" x="296">v1.5.0</text><text class="foreground" textLength="128" x="344">): Downloading (</text><text class="color3" textLength="16" x="472">5%</text><text class="foreground" textLength="8" x="488">)</text><text class="background" textLength="8" x="496"> </text><text class="foreground" textLength="80" x="504">          </text></g></defs><g display="none"><rect class="foreground" height="17" width="8" x="496" y="153"/><use y="153" xlink:href="#g35"/><animate attributeName="display" begin="19880ms; anim_last.end+19880ms" dur="150ms" from="inline" to="inline"/></g><defs><g id="g36"><text class="foreground" textLength="120" x="0">  - Installing </text><text class="color2" textLength="160" x="120">doctrine/collections</text><text class="foreground" textLength="16" x="280"> (</text><text class="color3" textLength="48" x="296">v1.5.0</text><text class="foreground" textLength="24" x="344">): </text><text class="background" textLength="8" x="368">D</text><text class="foreground" textLength="96" x="376">ownloading (</text><text class="color3" textLength="16" x="472">5%</text><text class="foreground" textLength="96" x="488">)           </text></g></defs><g display="none"><rect class="foreground" height="17" width="8" x="368" y="153"/><use y="153" xlink:href="#g36"/><animate attributeName="display" begin="20030ms; anim_last.end+20030ms" dur="1ms" from="inline" to="inline"/></g><defs><g id="g37"><text class="foreground" textLength="120" x="0">  - Installing </text><text class="color2" textLength="160" x="120">doctrine/collections</text><text class="foreground" textLength="16" x="280"> (</text><text class="color3" textLength="48" x="296">v1.5.0</text><text class="foreground" textLength="128" x="344">): Downloading (</text><text class="color3" textLength="24" x="472">60%</text><text class="foreground" textLength="8" x="496">)</text><text class="background" textLength="8" x="504"> </text><text class="foreground" textLength="72" x="512">         </text></g></defs><g display="none"><rect class="foreground" height="17" width="8" x="504" y="153"/><use y="153" xlink:href="#g37"/><animate attributeName="display" begin="20031ms; anim_last.end+20031ms" dur="2ms" from="inline" to="inline"/></g><defs><g id="g38"><text class="foreground" textLength="120" x="0">  - Installing </text><text class="color2" textLength="160" x="120">doctrine/collections</text><text class="foreground" textLength="16" x="280"> (</text><text class="color3" textLength="48" x="296">v1.5.0</text><text class="foreground" textLength="128" x="344">): Downloading (</text><text class="color3" textLength="24" x="472">95%</text><text class="foreground" textLength="8" x="496">)</text><text class="background" textLength="8" x="504"> </text><text class="foreground" textLength="72" x="512">         </text></g></defs><g display="none"><rect class="foreground" height="17" width="8" x="504" y="153"/><use y="153" xlink:href="#g38"/><animate attributeName="display" begin="20033ms; anim_last.end+20033ms" dur="2ms" from="inline" to="inline"/></g><g display="none"><rect class="foreground" height="17" width="8" x="0" y="170"/><use y="170" xlink:href="#g1"/><animate attributeName="display" begin="20035ms; anim_last.end+20035ms" dur="43ms" from="inline" to="inline"/></g><defs><g id="g39"><text class="foreground" textLength="120" x="0">  - Installing </text><text class="color2" textLength="112" x="120">doctrine/cache</text><text class="foreground" textLength="16" x="232"> (</text><text class="color3" textLength="48" x="248">v1.8.0</text><text class="foreground" textLength="24" x="296">): </text><text class="background" textLength="8" x="320"> </text></g></defs><g display="none"><rect class="foreground" height="17" width="8" x="320" y="170"/><use y="170" xlink:href="#g39"/><animate attributeName="display" begin="20078ms; anim_last.end+20078ms" dur="2ms" from="inline" to="inline"/></g><g display="none"><rect class="foreground" height="17" width="8" x="0" y="187"/><use y="187" xlink:href="#g1"/><animate attributeName="display" begin="20080ms; anim_last.end+20080ms" dur="72ms" from="inline" to="inline"/></g><defs><g id="g40"><text class="foreground" textLength="120" x="0">  - Installing </text><text class="color2" textLength="160" x="120">doctrine/annotations</text><text class="foreground" textLength="16" x="280"> (</text><text class="color3" textLength="48" x="296">v1.6.0</text><text class="foreground" textLength="24" x="344">): </text><text class="background" textLength="8" x="368"> </text></g></defs><g display="none"><rect class="foreground" height="17" width="8" x="368" y="187"/><use y="187" xlink:href="#g40"/><animate attributeName="display" begin="20152ms; anim_last.end+20152ms" dur="1ms" from="inline" to="inline"/></g><defs><g id="g41"><text class="foreground" textLength="120" x="0">  - Installing </text><text class="color2" textLength="160" x="120">doctrine/annotations</text><text class="foreground" textLength="16" x="280"> (</text><text class="color3" textLength="48" x="296">v1.6.0</text><text class="foreground" textLength="128" x="344">): Downloading (</text><text class="color3" textLength="104" x="472">connecting...</text><text class="foreground" textLength="8" x="576">)</text><text class="background" textLength="8" x="584"> </text></g></defs><g display="none"><rect class="foreground" height="17" width="8" x="584" y="187"/><use y="187" xlink:href="#g41"/><animate attributeName="display" begin="20153ms; anim_last.end+20153ms" dur="1444ms" from="inline" to="inline"/></g><defs><g id="g42"><text class="foreground" textLength="120" x="0">  - Installing </text><text class="color2" textLength="160" x="120">doctrine/annotations</text><text class="foreground" textLength="16" x="280"> (</text><text class="color3" textLength="48" x="296">v1.6.0</text><text class="foreground" textLength="24" x="344">): </text><text class="background" textLength="8" x="368">D</text><text class="foreground" textLength="96" x="376">ownloading (</text><text class="color3" textLength="104" x="472">connecting...</text><text class="foreground" textLength="8" x="576">)</text></g></defs><g display="none"><rect class="foreground" height="17" width="8" x="368" y="187"/><use y="187" xlink:href="#g42"/><animate attributeName="display" begin="21597ms; anim_last.end+21597ms" dur="1ms" from="inline" to="inline"/></g><defs><g id="g43"><text class="foreground" textLength="120" x="0">  - Installing </text><text class="color2" textLength="160" x="120">doctrine/annotations</text><text class="foreground" textLength="16" x="280"> (</text><text class="color3" textLength="48" x="296">v1.6.0</text><text class="foreground" textLength="128" x="344">): Downloading (</text><text class="color3" textLength="24" x="472">15%</text><text class="foreground" textLength="8" x="496">)</text><text class="background" textLength="8" x="504"> </text><text class="foreground" textLength="72" x="512">         </text></g></defs><g display="none"><rect class="foreground" height="17" width="8" x="504" y="187"/><use y="187" xlink:href="#g43"/><animate attributeName="display" begin="21598ms; anim_last.end+21598ms" dur="347ms" from="inline" to="inline"/></g><defs><g id="g44"><text class="foreground" textLength="120" x="0">  - Installing </text><text class="color2" textLength="160" x="120">doctrine/annotations</text><text class="foreground" textLength="16" x="280"> (</text><text class="color3" textLength="48" x="296">v1.6.0</text><text class="foreground" textLength="128" x="344">): Downloading (</text><text class="color3" textLength="24" x="472">30%</text><text class="foreground" textLength="8" x="496">)</text><text class="background" textLength="8" x="504"> </text><text class="foreground" textLength="72" x="512">         </text></g></defs><g display="none"><rect class="foreground" height="17" width="8" x="504" y="187"/><use y="187" xlink:href="#g44"/><animate attributeName="display" begin="21945ms; anim_last.end+21945ms" dur="4ms" from="inline" to="inline"/></g><defs><g id="g45"><text class="foreground" textLength="120" x="0">  - Installing </text><text class="color2" textLength="160" x="120">doctrine/annotations</text><text class="foreground" textLength="16" x="280"> (</text><text class="color3" textLength="48" x="296">v1.6.0</text><text class="foreground" textLength="128" x="344">): Downloading (</text><text class="color3" textLength="24" x="472">80%</text><text class="foreground" textLength="8" x="496">)</text><text class="background" textLength="8" x="504"> </text><text class="foreground" textLength="72" x="512">         </text></g></defs><g display="none"><rect class="foreground" height="17" width="8" x="504" y="187"/><use y="187" xlink:href="#g45"/><animate attributeName="display" begin="21949ms; anim_last.end+21949ms" dur="151ms" from="inline" to="inline"/></g><defs><g id="g46"><text class="foreground" textLength="120" x="0">  - Installing </text><text class="color2" textLength="160" x="120">doctrine/annotations</text><text class="foreground" textLength="16" x="280"> (</text><text class="color3" textLength="48" x="296">v1.6.0</text><text class="foreground" textLength="24" x="344">): </text><text class="background" textLength="8" x="368">D</text><text class="foreground" textLength="96" x="376">ownloading (</text><text class="color3" textLength="24" x="472">80%</text><text class="foreground" textLength="88" x="496">)          </text></g></defs><g display="none"><rect class="foreground" height="17" width="8" x="368" y="187"/><use y="187" xlink:href="#g46"/><animate attributeName="display" begin="22100ms; anim_last.end+22100ms" dur="3ms" from="inline" to="inline"/></g><defs><g id="g47"><text class="foreground" textLength="120" x="0">  - Installing </text><text class="color2" textLength="160" x="120">doctrine/annotations</text><text class="foreground" textLength="16" x="280"> (</text><text class="color3" textLength="48" x="296">v1.6.0</text><text class="foreground" textLength="128" x="344">): Downloading (</text><text class="color3" textLength="32" x="472">100%</text><text class="foreground" textLength="8" x="504">)</text><text class="background" textLength="8" x="512"> </text><text class="foreground" textLength="64" x="520">        </text></g></defs><g display="none"><rect class="foreground" height="17" width="8" x="512" y="187"/><use y="187" xlink:href="#g47"/><animate attributeName="display" begin="22103ms; anim_last.end+22103ms" dur="2ms" from="inline" to="inline"/></g><g display="none"><rect class="foreground" height="17" width="8" x="0" y="204"/><use y="204" xlink:href="#g1"/><animate attributeName="display" begin="22105ms; anim_last.end+22105ms" dur="50ms" from="inline" to="inline"/></g><defs><g id="g48"><text class="foreground" textLength="120" x="0">  - Installing </text><text class="color2" textLength="120" x="120">doctrine/common</text><text class="foreground" textLength="16" x="240"> (</text><text class="color3" textLength="48" x="256">v2.6.2</text><text class="foreground" textLength="24" x="304">): </text><text class="background" textLength="8" x="328"> </text></g></defs><g display="none"><rect class="foreground" height="17" width="8" x="328" y="204"/><use y="204" xlink:href="#g48"/><animate attributeName="display" begin="22155ms; anim_last.end+22155ms" dur="2ms" from="inline" to="inline"/></g><defs><g id="g49"><text class="foreground" textLength="120" x="0">  - Installing </text><text class="color2" textLength="120" x="120">doctrine/common</text><text class="foreground" textLength="16" x="240"> (</text><text class="color3" textLength="48" x="256">v2.6.2</text><text class="foreground" textLength="168" x="304">): Loading from cache</text><text class="background" textLength="8" x="472"> </text></g></defs><g display="none"><rect class="foreground" height="17" width="8" x="472" y="204"/><use y="204" xlink:href="#g49"/><animate attributeName="display" begin="22157ms; anim_last.end+22157ms" dur="1ms" from="inline" to="inline"/></g><g display="none"><rect class="foreground" height="17" width="8" x="0" y="221"/><use y="221" xlink:href="#g1"/><animate attributeName="display" begin="22158ms; anim_last.end+22158ms" dur="116ms" from="inline" to="inline"/></g><defs><g id="g50"><text class="foreground" textLength="120" x="0">  - Installing </text><text class="color2" textLength="144" x="120">mercadopago/dx-php</text><text class="foreground" textLength="16" x="264"> (</text><text class="color3" textLength="40" x="280">1.2.1</text><text class="foreground" textLength="24" x="320">): </text><text class="background" textLength="8" x="344"> </text></g></defs><g display="none"><rect class="foreground" height="17" width="8" x="344" y="221"/><use y="221" xlink:href="#g50"/><animate attributeName="display" begin="22274ms; anim_last.end+22274ms" dur="10ms" from="inline" to="inline"/></g><g display="none"><rect class="foreground" height="17" width="8" x="0" y="238"/><use y="238" xlink:href="#g1"/><animate attributeName="display" begin="22284ms; anim_last.end+22284ms" dur="1128ms" from="inline" to="inline"/></g><g display="none"><rect class="foreground" height="17" width="8" x="0" y="272"/><use y="272" xlink:href="#g1"/><animate attributeName="display" begin="23412ms; anim_last.end+23412ms" dur="2ms" from="inline" to="inline"/></g><g display="none"><rect class="foreground" height="17" width="8" x="0" y="306"/><use y="306" xlink:href="#g1"/><animate attributeName="display" begin="23414ms; anim_last.end+23414ms" dur="152ms" from="inline" to="inline"/></g><g display="none"><rect class="foreground" height="17" width="8" x="0" y="306"/><use y="306" xlink:href="#g2"/><animate attributeName="display" begin="23566ms; anim_last.end+23566ms" dur="1ms" from="inline" to="inline"/></g><g display="none"><rect class="foreground" height="17" width="8" x="0" y="306"/><use y="306" xlink:href="#g2"/><animate attributeName="display" begin="23567ms; anim_last.end+23567ms" dur="4ms" from="inline" to="inline"/></g><g display="none"><rect class="foreground" height="17" width="8" x="0" y="306"/><use y="306" xlink:href="#g2"/><animate attributeName="display" begin="23571ms; anim_last.end+23571ms" dur="43ms" from="inline" to="inline"/></g><g display="none"><rect class="foreground" height="17" width="8" x="96" y="306"/><use y="306" xlink:href="#g3"/><animate attributeName="display" begin="23614ms; anim_last.end+23614ms" dur="1ms" from="inline" to="inline"/></g><g display="none"><rect class="foreground" height="17" width="8" x="96" y="306"/><use y="306" xlink:href="#g3"/><animate attributeName="display" begin="23615ms; anim_last.end+23615ms" dur="1639ms" from="inline" to="inline"/></g><defs><g id="g51"><text class="color10" font-weight="bold" textLength="24" x="0">&#10140;  </text><text class="color14" font-weight="bold" textLength="64" x="24">demo-php</text><text class="foreground" textLength="16" x="88"> t</text><text class="background" textLength="8" x="104"> </text><text class="foreground" textLength="544" x="112">                                                                    </text></g></defs><g display="none"><rect class="foreground" height="17" width="8" x="104" y="306"/><use y="306" xlink:href="#g51"/><animate attributeName="display" begin="25254ms; anim_last.end+25254ms" dur="88ms" from="inline" to="inline"/></g><defs><g id="g52"><text class="color10" font-weight="bold" textLength="24" x="0">&#10140;  </text><text class="color14" font-weight="bold" textLength="64" x="24">demo-php</text><text class="foreground" textLength="24" x="88"> tr</text><text class="background" textLength="8" x="112"> </text><text class="foreground" textLength="536" x="120">                                                                   </text></g></defs><g display="none"><rect class="foreground" height="17" width="8" x="112" y="306"/><use y="306" xlink:href="#g52"/><animate attributeName="display" begin="25342ms; anim_last.end+25342ms" dur="231ms" from="inline" to="inline"/></g><defs><g id="g53"><text class="color10" font-weight="bold" textLength="24" x="0">&#10140;  </text><text class="color14" font-weight="bold" textLength="64" x="24">demo-php</text><text class="foreground" textLength="32" x="88"> tre</text><text class="background" textLength="8" x="120"> </text><text class="foreground" textLength="528" x="128">                                                                  </text></g></defs><g display="none"><rect class="foreground" height="17" width="8" x="120" y="306"/><use y="306" xlink:href="#g53"/><animate attributeName="display" begin="25573ms; anim_last.end+25573ms" dur="167ms" from="inline" to="inline"/></g><defs><g id="g54"><text class="color10" font-weight="bold" textLength="24" x="0">&#10140;  </text><text class="color14" font-weight="bold" textLength="64" x="24">demo-php</text><text class="foreground" textLength="40" x="88"> tree</text><text class="background" textLength="8" x="128"> </text><text class="foreground" textLength="520" x="136">                                                                 </text></g></defs><g display="none"><rect class="foreground" height="17" width="8" x="128" y="306"/><use y="306" xlink:href="#g54"/><animate attributeName="display" begin="25740ms; anim_last.end+25740ms" dur="128ms" from="inline" to="inline"/></g><defs><g id="g55"><text class="color10" font-weight="bold" textLength="24" x="0">&#10140;  </text><text class="color14" font-weight="bold" textLength="64" x="24">demo-php</text><text class="foreground" textLength="48" x="88"> tree </text><text class="background" textLength="8" x="136"> </text><text class="foreground" textLength="512" x="144">                                                                </text></g></defs><g display="none"><rect class="foreground" height="17" width="8" x="136" y="306"/><use y="306" xlink:href="#g55"/><animate attributeName="display" begin="25868ms; anim_last.end+25868ms" dur="1095ms" from="inline" to="inline"/></g><defs><g id="g56"><text class="color10" font-weight="bold" textLength="24" x="0">&#10140;  </text><text class="color14" font-weight="bold" textLength="64" x="24">demo-php</text><text class="foreground" textLength="56" x="88"> tree -</text><text class="background" textLength="8" x="144"> </text><text class="foreground" textLength="504" x="152">                                                               </text></g></defs><g display="none"><rect class="foreground" height="17" width="8" x="144" y="306"/><use y="306" xlink:href="#g56"/><animate attributeName="display" begin="26963ms; anim_last.end+26963ms" dur="232ms" from="inline" to="inline"/></g><defs><g id="g57"><text class="color10" font-weight="bold" textLength="24" x="0">&#10140;  </text><text class="color14" font-weight="bold" textLength="64" x="24">demo-php</text><text class="foreground" textLength="64" x="88"> tree -L</text><text class="background" textLength="8" x="152"> </text><text class="foreground" textLength="496" x="160">                                                              </text></g></defs><g display="none"><rect class="foreground" height="17" width="8" x="152" y="306"/><use y="306" xlink:href="#g57"/><animate attributeName="display" begin="27195ms; anim_last.end+27195ms" dur="191ms" from="inline" to="inline"/></g><defs><g id="g58"><text class="color10" font-weight="bold" textLength="24" x="0">&#10140;  </text><text class="color14" font-weight="bold" textLength="64" x="24">demo-php</text><text class="foreground" textLength="72" x="88"> tree -L </text><text class="background" textLength="8" x="160"> </text><text class="foreground" textLength="488" x="168">                                                             </text></g></defs><g display="none"><rect class="foreground" height="17" width="8" x="160" y="306"/><use y="306" xlink:href="#g58"/><animate attributeName="display" begin="27386ms; anim_last.end+27386ms" dur="240ms" from="inline" to="inline"/></g><defs><g id="g59"><text class="color10" font-weight="bold" textLength="24" x="0">&#10140;  </text><text class="color14" font-weight="bold" textLength="64" x="24">demo-php</text><text class="foreground" textLength="80" x="88"> tree -L 3</text><text class="background" textLength="8" x="168"> </text><text class="foreground" textLength="480" x="176">                                                            </text></g></defs><g display="none"><rect class="foreground" height="17" width="8" x="168" y="306"/><use y="306" xlink:href="#g59"/><animate attributeName="display" begin="27626ms; anim_last.end+27626ms" dur="534ms" from="inline" to="inline"/></g><g display="none"><rect class="foreground" height="17" width="8" x="168" y="306"/><use y="306" xlink:href="#g59"/><animate attributeName="display" begin="28160ms; anim_last.end+28160ms" dur="1ms" from="inline" to="inline"/></g><g display="none"><rect class="foreground" height="17" width="8" x="0" y="323"/><use y="323" xlink:href="#g1"/><animate attributeName="display" begin="28161ms; anim_last.end+28161ms" dur="2ms" from="inline" to="inline"/></g><defs><g id="g60"><text class="color10" font-weight="bold" textLength="24" x="0">&#10140;  </text><text class="color14" font-weight="bold" textLength="64" x="24">demo-php</text><text class="foreground" textLength="568" x="88"> composer --version                                                    </text></g></defs><g display="none"><use y="0" xlink:href="#g60"/><animate attributeName="display" begin="3516ms; anim_last.end+3516ms" dur="24655ms" from="inline" to="inline"/></g><defs><g id="g61"><text class="color2" textLength="64" x="0">Composer</text><text class="foreground" textLength="72" x="64"> version </text><text class="color3" textLength="40" x="136">1.6.5</text><text class="foreground" textLength="160" x="176"> 2018-05-04 11:44:59</text></g></defs><g display="none"><use y="17" xlink:href="#g61"/><animate attributeName="display" begin="3631ms; anim_last.end+3631ms" dur="24540ms" from="inline" to="inline"/></g><defs><g id="g62"><text class="color10" font-weight="bold" textLength="24" x="0">&#10140;  </text><text class="color14" font-weight="bold" textLength="64" x="24">demo-php</text><text class="foreground" textLength="568" x="88"> composer require "mercadopago/dx-php:1.2.1"                           </text></g></defs><g display="none"><use y="34" xlink:href="#g62"/><animate attributeName="display" begin="7113ms; anim_last.end+7113ms" dur="21058ms" from="inline" to="inline"/></g><defs><g id="g63"><text class="color2" textLength="256" x="0">./composer.json has been created</text></g></defs><g display="none"><use y="51" xlink:href="#g63"/><animate attributeName="display" begin="12806ms; anim_last.end+12806ms" dur="15365ms" from="inline" to="inline"/></g><defs><g id="g64"><text class="color2" textLength="432" x="0">Loading composer repositories with package information</text></g></defs><g display="none"><use y="68" xlink:href="#g64"/><animate attributeName="display" begin="13162ms; anim_last.end+13162ms" dur="15009ms" from="inline" to="inline"/></g><defs><g id="g65"><text class="color2" textLength="360" x="0">Updating dependencies (including require-dev)</text></g></defs><g display="none"><use y="85" xlink:href="#g65"/><animate attributeName="display" begin="13964ms; anim_last.end+13964ms" dur="14207ms" from="inline" to="inline"/></g><defs><g id="g66"><text class="color2" textLength="424" x="0">Package operations: 7 installs, 0 updates, 0 removals</text></g></defs><g display="none"><use y="102" xlink:href="#g66"/><animate attributeName="display" begin="14767ms; anim_last.end+14767ms" dur="13404ms" from="inline" to="inline"/></g><defs><g id="g67"><text class="foreground" textLength="120" x="0">  - Installing </text><text class="color2" textLength="112" x="120">doctrine/lexer</text><text class="foreground" textLength="16" x="232"> (</text><text class="color3" textLength="48" x="248">v1.0.1</text><text class="foreground" textLength="128" x="296">): Downloading (</text><text class="color3" textLength="32" x="424">100%</text><text class="foreground" textLength="80" x="456">)         </text></g></defs><g display="none"><use y="119" xlink:href="#g67"/><animate attributeName="display" begin="16716ms; anim_last.end+16716ms" dur="11455ms" from="inline" to="inline"/></g><defs><g id="g68"><text class="foreground" textLength="120" x="0">  - Installing </text><text class="color2" textLength="144" x="120">doctrine/inflector</text><text class="foreground" textLength="16" x="264"> (</text><text class="color3" textLength="48" x="280">v1.3.0</text><text class="foreground" textLength="128" x="328">): Downloading (</text><text class="color3" textLength="32" x="456">100%</text><text class="foreground" textLength="80" x="488">)         </text></g></defs><g display="none"><use y="136" xlink:href="#g68"/><animate attributeName="display" begin="18310ms; anim_last.end+18310ms" dur="9861ms" from="inline" to="inline"/></g><defs><g id="g69"><text class="foreground" textLength="120" x="0">  - Installing </text><text class="color2" textLength="160" x="120">doctrine/collections</text><text class="foreground" textLength="16" x="280"> (</text><text class="color3" textLength="48" x="296">v1.5.0</text><text class="foreground" textLength="128" x="344">): Downloading (</text><text class="color3" textLength="32" x="472">100%</text><text class="foreground" textLength="80" x="504">)         </text></g></defs><g display="none"><use y="153" xlink:href="#g69"/><animate attributeName="display" begin="20035ms; anim_last.end+20035ms" dur="8136ms" from="inline" to="inline"/></g><defs><g id="g70"><text class="foreground" textLength="120" x="0">  - Installing </text><text class="color2" textLength="112" x="120">doctrine/cache</text><text class="foreground" textLength="16" x="232"> (</text><text class="color3" textLength="48" x="248">v1.8.0</text><text class="foreground" textLength="168" x="296">): Loading from cache</text></g></defs><g display="none"><use y="170" xlink:href="#g70"/><animate attributeName="display" begin="20080ms; anim_last.end+20080ms" dur="8091ms" from="inline" to="inline"/></g><defs><g id="g71"><text class="foreground" textLength="120" x="0">  - Installing </text><text class="color2" textLength="160" x="120">doctrine/annotations</text><text class="foreground" textLength="16" x="280"> (</text><text class="color3" textLength="48" x="296">v1.6.0</text><text class="foreground" textLength="128" x="344">): Downloading (</text><text class="color3" textLength="32" x="472">100%</text><text class="foreground" textLength="80" x="504">)         </text></g></defs><g display="none"><use y="187" xlink:href="#g71"/><animate attributeName="display" begin="22105ms; anim_last.end+22105ms" dur="6066ms" from="inline" to="inline"/></g><defs><g id="g72"><text class="foreground" textLength="120" x="0">  - Installing </text><text class="color2" textLength="120" x="120">doctrine/common</text><text class="foreground" textLength="16" x="240"> (</text><text class="color3" textLength="48" x="256">v2.6.2</text><text class="foreground" textLength="168" x="304">): Loading from cache</text></g></defs><g display="none"><use y="204" xlink:href="#g72"/><animate attributeName="display" begin="22158ms; anim_last.end+22158ms" dur="6013ms" from="inline" to="inline"/></g><defs><g id="g73"><text class="foreground" textLength="120" x="0">  - Installing </text><text class="color2" textLength="144" x="120">mercadopago/dx-php</text><text class="foreground" textLength="16" x="264"> (</text><text class="color3" textLength="40" x="280">1.2.1</text><text class="foreground" textLength="168" x="320">): Loading from cache</text></g></defs><g display="none"><use y="221" xlink:href="#g73"/><animate attributeName="display" begin="22284ms; anim_last.end+22284ms" dur="5887ms" from="inline" to="inline"/></g><defs><g id="g74"><text class="foreground" textLength="656" x="0">doctrine/cache suggests installing alcaeus/mongo-php-adapter (Required to use lega</text></g></defs><defs><g id="g75"><text class="foreground" textLength="144" x="0">cy MongoDB driver)</text></g></defs><g display="none"><use y="238" xlink:href="#g74"/><use y="255" xlink:href="#g75"/><animate attributeName="display" begin="23412ms; anim_last.end+23412ms" dur="4759ms" from="inline" to="inline"/></g><defs><g id="g76"><text class="color2" textLength="136" x="0">Writing lock file</text></g></defs><defs><g id="g77"><text class="color2" textLength="200" x="0">Generating autoload files</text></g></defs><g display="none"><use y="272" xlink:href="#g76"/><use y="289" xlink:href="#g77"/><animate attributeName="display" begin="23414ms; anim_last.end+23414ms" dur="4757ms" from="inline" to="inline"/></g><defs><g id="g78"><text class="color10" font-weight="bold" textLength="24" x="0">&#10140;  </text><text class="color14" font-weight="bold" textLength="64" x="24">demo-php</text><text class="foreground" textLength="568" x="88"> tree -L 3                                                             </text></g></defs><g display="none"><use y="306" xlink:href="#g78"/><animate attributeName="display" begin="28161ms; anim_last.end+28161ms" dur="10ms" from="inline" to="inline"/></g><g display="none"><rect class="foreground" height="17" width="8" x="0" y="323"/><use y="323" xlink:href="#g1"/><animate attributeName="display" begin="28163ms; anim_last.end+28163ms" dur="8ms" from="inline" to="inline"/></g><g display="none"><rect class="foreground" height="17" width="8" x="0" y="391"/><use y="391" xlink:href="#g1"/><animate attributeName="display" begin="28171ms; anim_last.end+28171ms" dur="1ms" from="inline" to="inline"/></g><g display="none"><rect class="foreground" height="17" width="8" x="0" y="391"/><use y="391" xlink:href="#g2"/><animate attributeName="display" begin="28172ms; anim_last.end+28172ms" dur="18ms" from="inline" to="inline"/></g><g display="none"><rect class="foreground" height="17" width="8" x="0" y="391"/><use y="391" xlink:href="#g2"/><animate attributeName="display" begin="28190ms; anim_last.end+28190ms" dur="29ms" from="inline" to="inline"/></g><g display="none"><rect class="foreground" height="17" width="8" x="96" y="391"/><use y="391" xlink:href="#g3"/><animate attributeName="display" begin="28219ms; anim_last.end+28219ms" dur="1ms" from="inline" to="inline"/></g><g display="none"><rect class="foreground" height="17" width="8" x="96" y="391"/><use y="391" xlink:href="#g3"/><animate attributeName="display" begin="28220ms; anim_last.end+28220ms" dur="3689ms" from="inline" to="inline"/></g><defs><g id="g79"><text class="color10" font-weight="bold" textLength="24" x="0">&#10140;  </text><text class="color14" font-weight="bold" textLength="64" x="24">demo-php</text><text class="foreground" textLength="16" x="88"> p</text><text class="background" textLength="8" x="104"> </text><text class="foreground" textLength="544" x="112">                                                                    </text></g></defs><g display="none"><rect class="foreground" height="17" width="8" x="104" y="391"/><use y="391" xlink:href="#g79"/><animate attributeName="display" begin="31909ms; anim_last.end+31909ms" dur="135ms" from="inline" to="inline"/></g><defs><g id="g80"><text class="color10" font-weight="bold" textLength="24" x="0">&#10140;  </text><text class="color14" font-weight="bold" textLength="64" x="24">demo-php</text><text class="foreground" textLength="24" x="88"> ph</text><text class="background" textLength="8" x="112"> </text><text class="foreground" textLength="536" x="120">                                                                   </text></g></defs><g display="none"><rect class="foreground" height="17" width="8" x="112" y="391"/><use y="391" xlink:href="#g80"/><animate attributeName="display" begin="32044ms; anim_last.end+32044ms" dur="103ms" from="inline" to="inline"/></g><defs><g id="g81"><text class="color10" font-weight="bold" textLength="24" x="0">&#10140;  </text><text class="color14" font-weight="bold" textLength="64" x="24">demo-php</text><text class="foreground" textLength="32" x="88"> php</text><text class="background" textLength="8" x="120"> </text><text class="foreground" textLength="528" x="128">                                                                  </text></g></defs><g display="none"><rect class="foreground" height="17" width="8" x="120" y="391"/><use y="391" xlink:href="#g81"/><animate attributeName="display" begin="32147ms; anim_last.end+32147ms" dur="111ms" from="inline" to="inline"/></g><defs><g id="g82"><text class="color10" font-weight="bold" textLength="24" x="0">&#10140;  </text><text class="color14" font-weight="bold" textLength="64" x="24">demo-php</text><text class="foreground" textLength="40" x="88"> php </text><text class="background" textLength="8" x="128"> </text><text class="foreground" textLength="520" x="136">                                                                 </text></g></defs><g display="none"><rect class="foreground" height="17" width="8" x="128" y="391"/><use y="391" xlink:href="#g82"/><animate attributeName="display" begin="32258ms; anim_last.end+32258ms" dur="608ms" from="inline" to="inline"/></g><defs><g id="g83"><text class="color10" font-weight="bold" textLength="24" x="0">&#10140;  </text><text class="color14" font-weight="bold" textLength="64" x="24">demo-php</text><text class="foreground" textLength="48" x="88"> php -</text><text class="background" textLength="8" x="136"> </text><text class="foreground" textLength="512" x="144">                                                                </text></g></defs><g display="none"><rect class="foreground" height="17" width="8" x="136" y="391"/><use y="391" xlink:href="#g83"/><animate attributeName="display" begin="32866ms; anim_last.end+32866ms" dur="160ms" from="inline" to="inline"/></g><defs><g id="g84"><text class="color10" font-weight="bold" textLength="24" x="0">&#10140;  </text><text class="color14" font-weight="bold" textLength="64" x="24">demo-php</text><text class="foreground" textLength="56" x="88"> php -a</text><text class="background" textLength="8" x="144"> </text><text class="foreground" textLength="504" x="152">                                                               </text></g></defs><g display="none"><rect class="foreground" height="17" width="8" x="144" y="391"/><use y="391" xlink:href="#g84"/><animate attributeName="display" begin="33026ms; anim_last.end+33026ms" dur="247ms" from="inline" to="inline"/></g><defs><g id="g85"><text class="color10" font-weight="bold" textLength="24" x="0">&#10140;  </text><text class="color14" font-weight="bold" textLength="64" x="24">demo-php</text><text class="foreground" textLength="64" x="88"> php -a </text><text class="background" textLength="8" x="152"> </text><text class="foreground" textLength="496" x="160">                                                              </text></g></defs><g display="none"><rect class="foreground" height="17" width="8" x="152" y="391"/><use y="391" xlink:href="#g85"/><animate attributeName="display" begin="33273ms; anim_last.end+33273ms" dur="287ms" from="inline" to="inline"/></g><defs><g id="g86"><text class="foreground" textLength="136" x="0">&#9500;&#9472;&#9472; composer.lock</text></g></defs><defs><g id="g87"><text class="foreground" textLength="80" x="0">&#9492;&#9472;&#9472; vendor</text></g></defs><defs><g id="g88"><text class="foreground" textLength="160" x="0">    &#9500;&#9472;&#9472; autoload.php</text></g></defs><defs><g id="g89"><text class="foreground" textLength="128" x="0">    &#9500;&#9472;&#9472; composer</text></g></defs><defs><g id="g90"><text class="foreground" textLength="216" x="0">    &#9474;&#160;&#160; &#9500;&#9472;&#9472; ClassLoader.php</text></g></defs><defs><g id="g91"><text class="foreground" textLength="152" x="0">    &#9474;&#160;&#160; &#9500;&#9472;&#9472; LICENSE</text></g></defs><defs><g id="g92"><text class="foreground" textLength="264" x="0">    &#9474;&#160;&#160; &#9500;&#9472;&#9472; autoload_classmap.php</text></g></defs><defs><g id="g93"><text class="foreground" textLength="280" x="0">    &#9474;&#160;&#160; &#9500;&#9472;&#9472; autoload_namespaces.php</text></g></defs><defs><g id="g94"><text class="foreground" textLength="232" x="0">    &#9474;&#160;&#160; &#9500;&#9472;&#9472; autoload_psr4.php</text></g></defs><defs><g id="g95"><text class="foreground" textLength="232" x="0">    &#9474;&#160;&#160; &#9500;&#9472;&#9472; autoload_real.php</text></g></defs><defs><g id="g96"><text class="foreground" textLength="248" x="0">    &#9474;&#160;&#160; &#9500;&#9472;&#9472; autoload_static.php</text></g></defs><defs><g id="g97"><text class="foreground" textLength="208" x="0">    &#9474;&#160;&#160; &#9492;&#9472;&#9472; installed.json</text></g></defs><defs><g id="g98"><text class="foreground" textLength="128" x="0">    &#9500;&#9472;&#9472; doctrine</text></g></defs><defs><g id="g99"><text class="foreground" textLength="184" x="0">    &#9474;&#160;&#160; &#9500;&#9472;&#9472; annotations</text></g></defs><defs><g id="g100"><text class="foreground" textLength="136" x="0">    &#9474;&#160;&#160; &#9500;&#9472;&#9472; cache</text></g></defs><defs><g id="g101"><text class="foreground" textLength="184" x="0">    &#9474;&#160;&#160; &#9500;&#9472;&#9472; collections</text></g></defs><defs><g id="g102"><text class="foreground" textLength="144" x="0">    &#9474;&#160;&#160; &#9500;&#9472;&#9472; common</text></g></defs><defs><g id="g103"><text class="foreground" textLength="168" x="0">    &#9474;&#160;&#160; &#9500;&#9472;&#9472; inflector</text></g></defs><defs><g id="g104"><text class="foreground" textLength="136" x="0">    &#9474;&#160;&#160; &#9492;&#9472;&#9472; lexer</text></g></defs><defs><g id="g105"><text class="foreground" textLength="152" x="0">    &#9492;&#9472;&#9472; mercadopago</text></g></defs><defs><g id="g106"><text class="foreground" textLength="144" x="0">        &#9492;&#9472;&#9472; dx-php</text></g></defs><defs><g id="g107"><text class="foreground" textLength="192" x="0">11 directories, 11 files</text></g></defs><g display="none"><use y="0" xlink:href="#g86"/><use y="17" xlink:href="#g87"/><use y="34" xlink:href="#g88"/><use y="51" xlink:href="#g89"/><use y="68" xlink:href="#g90"/><use y="85" xlink:href="#g91"/><use y="102" xlink:href="#g92"/><use y="119" xlink:href="#g93"/><use y="136" xlink:href="#g94"/><use y="153" xlink:href="#g95"/><use y="170" xlink:href="#g96"/><use y="187" xlink:href="#g97"/><use y="204" xlink:href="#g98"/><use y="221" xlink:href="#g99"/><use y="238" xlink:href="#g100"/><use y="255" xlink:href="#g101"/><use y="272" xlink:href="#g102"/><use y="289" xlink:href="#g103"/><use y="306" xlink:href="#g104"/><use y="323" xlink:href="#g105"/><use y="340" xlink:href="#g106"/><use y="374" xlink:href="#g107"/><animate attributeName="display" begin="28171ms; anim_last.end+28171ms" dur="5391ms" from="inline" to="inline"/></g><g display="none"><rect class="foreground" height="17" width="8" x="152" y="391"/><use y="391" xlink:href="#g85"/><animate attributeName="display" begin="33560ms; anim_last.end+33560ms" dur="2ms" from="inline" to="inline"/></g><g display="none"><rect class="foreground" height="17" width="8" x="0" y="391"/><use y="391" xlink:href="#g1"/><animate attributeName="display" begin="33562ms; anim_last.end+33562ms" dur="2ms" from="inline" to="inline"/></g><defs><g id="g108"><text class="color10" font-weight="bold" textLength="24" x="0">&#10140;  </text><text class="color14" font-weight="bold" textLength="64" x="24">demo-php</text><text class="foreground" textLength="568" x="88"> php -a                                                                </text></g></defs><g display="none"><use y="0" xlink:href="#g87"/><use y="17" xlink:href="#g88"/><use y="34" xlink:href="#g89"/><use y="51" xlink:href="#g90"/><use y="68" xlink:href="#g91"/><use y="85" xlink:href="#g92"/><use y="102" xlink:href="#g93"/><use y="119" xlink:href="#g94"/><use y="136" xlink:href="#g95"/><use y="153" xlink:href="#g96"/><use y="170" xlink:href="#g97"/><use y="187" xlink:href="#g98"/><use y="204" xlink:href="#g99"/><use y="221" xlink:href="#g100"/><use y="238" xlink:href="#g101"/><use y="255" xlink:href="#g102"/><use y="272" xlink:href="#g103"/><use y="289" xlink:href="#g104"/><use y="306" xlink:href="#g105"/><use y="323" xlink:href="#g106"/><use y="357" xlink:href="#g107"/><use y="374" xlink:href="#g108"/><animate attributeName="display" begin="33562ms; anim_last.end+33562ms" dur="61ms" from="inline" to="inline"/></g><g display="none"><rect class="foreground" height="17" width="8" x="0" y="391"/><use y="391" xlink:href="#g1"/><animate attributeName="display" begin="33564ms; anim_last.end+33564ms" dur="59ms" from="inline" to="inline"/></g><g display="none"><rect class="foreground" height="17" width="8" x="0" y="391"/><use y="391" xlink:href="#g1"/><animate attributeName="display" begin="33623ms; anim_last.end+33623ms" dur="3ms" from="inline" to="inline"/></g><defs><g id="g109"><text class="foreground" textLength="48" x="0">php &gt; </text><text class="background" textLength="8" x="48"> </text></g></defs><g display="none"><rect class="foreground" height="17" width="8" x="48" y="391"/><use y="391" xlink:href="#g109"/><animate attributeName="display" begin="33626ms; anim_last.end+33626ms" dur="3339ms" from="inline" to="inline"/></g><defs><g id="g110"><text class="foreground" textLength="56" x="0">php &gt; r</text><text class="background" textLength="8" x="56"> </text></g></defs><g display="none"><rect class="foreground" height="17" width="8" x="56" y="391"/><use y="391" xlink:href="#g110"/><animate attributeName="display" begin="36965ms; anim_last.end+36965ms" dur="308ms" from="inline" to="inline"/></g><defs><g id="g111"><text class="foreground" textLength="136" x="0">Interactive shell</text></g></defs><g display="none"><use y="0" xlink:href="#g89"/><use y="17" xlink:href="#g90"/><use y="34" xlink:href="#g91"/><use y="51" xlink:href="#g92"/><use y="68" xlink:href="#g93"/><use y="85" xlink:href="#g94"/><use y="102" xlink:href="#g95"/><use y="119" xlink:href="#g96"/><use y="136" xlink:href="#g97"/><use y="153" xlink:href="#g98"/><use y="170" xlink:href="#g99"/><use y="187" xlink:href="#g100"/><use y="204" xlink:href="#g101"/><use y="221" xlink:href="#g102"/><use y="238" xlink:href="#g103"/><use y="255" xlink:href="#g104"/><use y="272" xlink:href="#g105"/><use y="289" xlink:href="#g106"/><use y="323" xlink:href="#g107"/><use y="340" xlink:href="#g108"/><use y="357" xlink:href="#g111"/><animate attributeName="display" begin="33623ms; anim_last.end+33623ms" dur="4445ms" from="inline" to="inline"/></g><defs><g id="g112"><text class="foreground" textLength="384" x="0">php &gt; require __DIR__  . '/vendor/autoload.php';</text><text class="background" textLength="8" x="384"> </text></g></defs><g display="none"><rect class="foreground" height="17" width="8" x="384" y="391"/><use y="391" xlink:href="#g112"/><animate attributeName="display" begin="37273ms; anim_last.end+37273ms" dur="795ms" from="inline" to="inline"/></g><g display="none"><rect class="foreground" height="17" width="8" x="0" y="391"/><use y="391" xlink:href="#g1"/><animate attributeName="display" begin="38068ms; anim_last.end+38068ms" dur="3ms" from="inline" to="inline"/></g><g display="none"><rect class="foreground" height="17" width="8" x="48" y="391"/><use y="391" xlink:href="#g109"/><animate attributeName="display" begin="38071ms; anim_last.end+38071ms" dur="3976ms" from="inline" to="inline"/></g><defs><g id="g113"><text class="foreground" textLength="384" x="0">php &gt; require __DIR__  . '/vendor/autoload.php';</text></g></defs><g display="none"><use y="0" xlink:href="#g90"/><use y="17" xlink:href="#g91"/><use y="34" xlink:href="#g92"/><use y="51" xlink:href="#g93"/><use y="68" xlink:href="#g94"/><use y="85" xlink:href="#g95"/><use y="102" xlink:href="#g96"/><use y="119" xlink:href="#g97"/><use y="136" xlink:href="#g98"/><use y="153" xlink:href="#g99"/><use y="170" xlink:href="#g100"/><use y="187" xlink:href="#g101"/><use y="204" xlink:href="#g102"/><use y="221" xlink:href="#g103"/><use y="238" xlink:href="#g104"/><use y="255" xlink:href="#g105"/><use y="272" xlink:href="#g106"/><use y="306" xlink:href="#g107"/><use y="323" xlink:href="#g108"/><use y="340" xlink:href="#g111"/><use y="374" xlink:href="#g113"/><animate attributeName="display" begin="38068ms; anim_last.end+38068ms" dur="4358ms" from="inline" to="inline"/></g><defs><g id="g114"><text class="foreground" textLength="480" x="0">php &gt; MercadoPago\SDK::setAccessToken("YOUR_ACCESS_TOKEN"); </text><text class="background" textLength="8" x="480"> </text></g></defs><g display="none"><rect class="foreground" height="17" width="8" x="480" y="391"/><use y="391" xlink:href="#g114"/><animate attributeName="display" begin="42047ms; anim_last.end+42047ms" dur="379ms" from="inline" to="inline"/></g><g display="none"><rect class="foreground" height="17" width="8" x="0" y="391"/><use y="391" xlink:href="#g1"/><animate attributeName="display" begin="42426ms; anim_last.end+42426ms" dur="9ms" from="inline" to="inline"/></g><defs><g id="g115"><text class="foreground" textLength="480" x="0">php &gt; MercadoPago\SDK::setAccessToken("YOUR_ACCESS_TOKEN"); </text></g></defs><g display="none"><use y="0" xlink:href="#g91"/><use y="17" xlink:href="#g92"/><use y="34" xlink:href="#g93"/><use y="51" xlink:href="#g94"/><use y="68" xlink:href="#g95"/><use y="85" xlink:href="#g96"/><use y="102" xlink:href="#g97"/><use y="119" xlink:href="#g98"/><use y="136" xlink:href="#g99"/><use y="153" xlink:href="#g100"/><use y="170" xlink:href="#g101"/><use y="187" xlink:href="#g102"/><use y="204" xlink:href="#g103"/><use y="221" xlink:href="#g104"/><use y="238" xlink:href="#g105"/><use y="255" xlink:href="#g106"/><use y="289" xlink:href="#g107"/><use y="306" xlink:href="#g108"/><use y="323" xlink:href="#g111"/><use y="357" xlink:href="#g113"/><use y="374" xlink:href="#g115"/><animate attributeName="display" begin="42426ms; anim_last.end+42426ms" dur="646ms" from="inline" to="inline"/></g><g display="none"><rect class="foreground" height="17" width="8" x="48" y="391"/><use y="391" xlink:href="#g109"/><animate attributeName="display" begin="42435ms; anim_last.end+42435ms" dur="637ms" from="inline" to="inline"/></g><defs><g id="g116"><text class="foreground" textLength="48" x="0">php &gt; </text></g></defs><g display="none"><use y="0" xlink:href="#g92"/><use y="17" xlink:href="#g93"/><use y="34" xlink:href="#g94"/><use y="51" xlink:href="#g95"/><use y="68" xlink:href="#g96"/><use y="85" xlink:href="#g97"/><use y="102" xlink:href="#g98"/><use y="119" xlink:href="#g99"/><use y="136" xlink:href="#g100"/><use y="153" xlink:href="#g101"/><use y="170" xlink:href="#g102"/><use y="187" xlink:href="#g103"/><use y="204" xlink:href="#g104"/><use y="221" xlink:href="#g105"/><use y="238" xlink:href="#g106"/><use y="272" xlink:href="#g107"/><use y="289" xlink:href="#g108"/><use y="306" xlink:href="#g111"/><use y="340" xlink:href="#g113"/><use y="357" xlink:href="#g115"/><use y="374" xlink:href="#g116"/><rect class="foreground" height="17" width="8" x="48" y="391"/><use y="391" xlink:href="#g109"/><animate attributeName="display" begin="43072ms; anim_last.end+43072ms" dur="1010ms" from="inline" to="inline"/></g><g display="none"><rect class="foreground" height="17" width="8" x="0" y="391"/><use y="391" xlink:href="#g1"/><animate attributeName="display" begin="44082ms; anim_last.end+44082ms" dur="8ms" from="inline" to="inline"/></g><g display="none"><rect class="foreground" height="17" width="8" x="0" y="391"/><use y="391" xlink:href="#g2"/><animate attributeName="display" begin="44090ms; anim_last.end+44090ms" dur="37ms" from="inline" to="inline"/></g><defs><g id="g117"><text class="color9" font-weight="bold" textLength="24" x="0">&#10140;  </text><text class="color14" font-weight="bold" textLength="64" x="24">demo-php</text><text class="foreground" textLength="8" x="88"> </text><text class="background" textLength="8" x="96"> </text><text class="foreground" textLength="552" x="104">                                                                     </text></g></defs><g display="none"><rect class="foreground" height="17" width="8" x="96" y="391"/><use y="391" xlink:href="#g117"/><animate attributeName="display" begin="44127ms; anim_last.end+44127ms" dur="1ms" from="inline" to="inline"/></g><g display="none"><use y="0" xlink:href="#g93"/><use y="17" xlink:href="#g94"/><use y="34" xlink:href="#g95"/><use y="51" xlink:href="#g96"/><use y="68" xlink:href="#g97"/><use y="85" xlink:href="#g98"/><use y="102" xlink:href="#g99"/><use y="119" xlink:href="#g100"/><use y="136" xlink:href="#g101"/><use y="153" xlink:href="#g102"/><use y="170" xlink:href="#g103"/><use y="187" xlink:href="#g104"/><use y="204" xlink:href="#g105"/><use y="221" xlink:href="#g106"/><use y="255" xlink:href="#g107"/><use y="272" xlink:href="#g108"/><use y="289" xlink:href="#g111"/><use y="323" xlink:href="#g113"/><use y="340" xlink:href="#g115"/><use y="357" xlink:href="#g116"/><use y="374" xlink:href="#g116"/><animate attributeName="display" begin="44082ms; anim_last.end+44082ms" dur="1276ms" from="inline" to="inline"/></g><g display="none"><rect class="foreground" height="17" width="8" x="96" y="391"/><use y="391" xlink:href="#g117"/><animate attributeName="display" begin="44128ms; anim_last.end+44128ms" dur="1230ms" from="inline" to="inline"/></g><defs><g id="g118"><text class="color9" font-weight="bold" textLength="24" x="0">&#10140;  </text><text class="color14" font-weight="bold" textLength="64" x="24">demo-php</text><text class="foreground" textLength="568" x="88">                                                                       </text></g></defs><g display="none"><use y="0" xlink:href="#g94"/><use y="17" xlink:href="#g95"/><use y="34" xlink:href="#g96"/><use y="51" xlink:href="#g97"/><use y="68" xlink:href="#g98"/><use y="85" xlink:href="#g99"/><use y="102" xlink:href="#g100"/><use y="119" xlink:href="#g101"/><use y="136" xlink:href="#g102"/><use y="153" xlink:href="#g103"/><use y="170" xlink:href="#g104"/><use y="187" xlink:href="#g105"/><use y="204" xlink:href="#g106"/><use y="238" xlink:href="#g107"/><use y="255" xlink:href="#g108"/><use y="272" xlink:href="#g111"/><use y="306" xlink:href="#g113"/><use y="323" xlink:href="#g115"/><use y="340" xlink:href="#g116"/><use y="357" xlink:href="#g116"/><use y="374" xlink:href="#g118"/><rect class="foreground" height="17" width="8" x="0" y="391"/><use y="391" xlink:href="#g1"/><animate attributeName="display" begin="45358ms; anim_last.end+45358ms" dur="1000ms" from="inline" to="inline" id="anim_last"/></g></svg>
</svg>