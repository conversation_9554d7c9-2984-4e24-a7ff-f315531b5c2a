<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        .demo-card {
            max-width: 800px;
            margin: 20px auto;
        }
        .status-badge {
            font-size: 0.8em;
        }
        .modal-header.bg-primary {
            border-bottom: none;
        }
        .btn-close-white {
            filter: brightness(0) invert(1);
        }
        .form-control:focus, .form-select:focus {
            border-color: #0d6efd;
            box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
        }
        .card.bg-light {
            border: 1px solid #dee2e6;
        }
        .toast-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1070;
        }
        .device-info {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .resumo-proposta {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            border-radius: 10px;
        }
        .resumo-proposta .card-title {
            color: white;
        }
        .resumo-proposta .text-muted {
            color: rgba(255, 255, 255, 0.8) !important;
        }
    </style>
</head>
<body class="bg-light">
    <div class="container-fluid">
        <div class="demo-card">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-tools me-2"></i>
                        Demonstração: Modal de Envio de Proposta
                    </h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-primary">Informações da Solicitação:</h6>
                            <ul class="list-unstyled">
                                <li><strong>ID:</strong> #95</li>
                                <li><strong>Dispositivo:</strong> iPhone 12 Pro</li>
                                <li><strong>Problema:</strong> Tela quebrada</li>
                                <li><strong>Cliente:</strong> João Silva</li>
                                <li><strong>Status:</strong> <span class="badge bg-warning">Pendente</span></li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-primary">Ações Disponíveis:</h6>
                            <button type="button" class="btn btn-primary me-2" onclick="abrirModalProposta()">
                                <i class="fas fa-paper-plane me-2"></i>
                                Enviar Proposta
                            </button>
                            <button type="button" class="btn btn-success" onclick="mostrarToast('success', 'Funcionalidade implementada com sucesso!')">
                                <i class="fas fa-check me-2"></i>
                                Testar Toast
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal para enviar proposta -->
    <div class="modal fade" id="propostaModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title">
                        <i class="fas fa-paper-plane me-2"></i>
                        Enviar Proposta
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <!-- Informações da solicitação -->
                    <div class="device-info">
                        <h6 class="mb-3">
                            <i class="fas fa-mobile-alt me-2"></i>
                            Dispositivo: iPhone 12 Pro
                        </h6>
                        <div class="row g-3">
                            <div class="col-md-6">
                                <small class="opacity-75">Cliente:</small>
                                <p class="mb-1">João Silva</p>
                            </div>
                            <div class="col-md-6">
                                <small class="opacity-75">Problema:</small>
                                <p class="mb-1">Tela quebrada, não responde ao toque...</p>
                            </div>
                        </div>
                    </div>

                    <!-- Formulário da proposta -->
                    <form id="formProposta">
                        <input type="hidden" name="solicitacao_id" value="95">
                        
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label for="preco" class="form-label">
                                    <i class="fas fa-dollar-sign me-1"></i>
                                    Preço do Reparo *
                                </label>
                                <div class="input-group">
                                    <span class="input-group-text">R$</span>
                                    <input type="text" class="form-control" id="preco" name="preco" 
                                           placeholder="0,00" required
                                           oninput="formatarPreco(this)">
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <label for="prazo" class="form-label">
                                    <i class="fas fa-calendar me-1"></i>
                                    Prazo (dias) *
                                </label>
                                <select class="form-select" id="prazo" name="prazo" required>
                                    <option value="">Selecione o prazo</option>
                                    <option value="1">1 dia (Express)</option>
                                    <option value="2">2 dias</option>
                                    <option value="3">3 dias</option>
                                    <option value="5">5 dias</option>
                                    <option value="7">1 semana</option>
                                    <option value="10">10 dias</option>
                                    <option value="15">15 dias</option>
                                    <option value="20">20 dias</option>
                                    <option value="30">30 dias</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="row g-3 mt-2">
                            <div class="col-12">
                                <label for="observacoes" class="form-label">
                                    <i class="fas fa-comment me-1"></i>
                                    Observações e Detalhes da Proposta
                                </label>
                                <textarea class="form-control" id="observacoes" name="observacoes" rows="4"
                                          placeholder="Descreva detalhes sobre o reparo, peças necessárias, garantia, etc."></textarea>
                                <div class="form-text">
                                    Inclua informações importantes como: tipo de peça (original/compatível), garantia oferecida, procedimentos inclusos.
                                </div>
                            </div>
                        </div>
                        
                        <div class="row g-3 mt-2">
                            <div class="col-12">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="retirada_expressa" name="retirada_expressa">
                                    <label class="form-check-label" for="retirada_expressa">
                                        <i class="fas fa-motorcycle me-1"></i>
                                        <strong>Retirada Express</strong>
                                        <small class="text-muted d-block">Buscar e entregar o dispositivo na residência do cliente</small>
                                    </label>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Resumo da proposta -->
                        <div class="card mt-4 resumo-proposta">
                            <div class="card-body">
                                <h6 class="card-title">
                                    <i class="fas fa-calculator me-2"></i>
                                    Resumo da Proposta
                                </h6>
                                <div class="row">
                                    <div class="col-md-4">
                                        <small class="text-muted">Valor do Reparo:</small>
                                        <p class="mb-1 fw-bold" id="resumoPreco">R$ 0,00</p>
                                    </div>
                                    <div class="col-md-4">
                                        <small class="text-muted">Prazo de Entrega:</small>
                                        <p class="mb-1 fw-bold" id="resumoPrazo">-</p>
                                    </div>
                                    <div class="col-md-4">
                                        <small class="text-muted">Taxa da Plataforma (15%):</small>
                                        <p class="mb-1 fw-bold" id="resumoTaxa">R$ 0,00</p>
                                    </div>
                                </div>
                                <hr class="bg-white">
                                <div class="row">
                                    <div class="col-md-6">
                                        <small class="text-muted">Você receberá:</small>
                                        <p class="mb-0 fw-bold fs-5" id="resumoRecebera">R$ 0,00</p>
                                    </div>
                                    <div class="col-md-6">
                                        <small class="text-muted">Cliente pagará:</small>
                                        <p class="mb-0 fw-bold fs-5" id="resumoClientePaga">R$ 0,00</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>
                        Cancelar
                    </button>
                    <button type="button" class="btn btn-primary" onclick="enviarProposta()" id="btnEnviarProposta">
                        <i class="fas fa-paper-plane me-2"></i>
                        Enviar Proposta
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Toast Container -->
    <div class="toast-container">
        <div id="toastNotification" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="toast-header">
                <i class="fas fa-info-circle text-primary me-2"></i>
                <strong class="me-auto">Notificação</strong>
                <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
            </div>
            <div class="toast-body">
                Mensagem de notificação aqui
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function abrirModalProposta() {
            const modal = new bootstrap.Modal(document.getElementById('propostaModal'));
            modal.show();
            
            // Resetar formulário
            document.getElementById('formProposta').reset();
            atualizarResumo();
        }

        function formatarPreco(input) {
            let valor = input.value.replace(/\D/g, '');
            
            if (valor.length === 0) {
                input.value = '';
                atualizarResumo();
                return;
            }
            
            valor = valor.padStart(3, '0');
            valor = valor.slice(0, -2) + ',' + valor.slice(-2);
            valor = valor.replace(/\B(?=(\d{3})+(?!\d))/g, '.');
            
            input.value = valor;
            atualizarResumo();
        }

        function atualizarResumo() {
            const precoInput = document.getElementById('preco').value;
            const prazoSelect = document.getElementById('prazo');
            
            // Converter preço para número
            let preco = 0;
            if (precoInput) {
                preco = parseFloat(precoInput.replace(/\./g, '').replace(',', '.')) || 0;
            }
            
            // Calcular valores
            const taxa = preco * 0.15;
            const recebera = preco - taxa;
            
            // Atualizar resumo
            document.getElementById('resumoPreco').textContent = 'R$ ' + preco.toLocaleString('pt-BR', {minimumFractionDigits: 2});
            document.getElementById('resumoTaxa').textContent = 'R$ ' + taxa.toLocaleString('pt-BR', {minimumFractionDigits: 2});
            document.getElementById('resumoRecebera').textContent = 'R$ ' + recebera.toLocaleString('pt-BR', {minimumFractionDigits: 2});
            document.getElementById('resumoClientePaga').textContent = 'R$ ' + preco.toLocaleString('pt-BR', {minimumFractionDigits: 2});
            
            // Atualizar prazo
            const prazoTexto = prazoSelect.value ? prazoSelect.options[prazoSelect.selectedIndex].text : '-';
            document.getElementById('resumoPrazo').textContent = prazoTexto;
        }

        function enviarProposta() {
            const form = document.getElementById('formProposta');
            const btnEnviar = document.getElementById('btnEnviarProposta');
            
            // Validar campos obrigatórios
            const preco = document.getElementById('preco').value;
            const prazo = document.getElementById('prazo').value;
            
            if (!preco || !prazo) {
                mostrarToast('error', 'Por favor, preencha o preço e o prazo.');
                return;
            }
            
            // Desabilitar botão durante envio
            btnEnviar.disabled = true;
            btnEnviar.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Enviando...';
            
            // Simular envio (substituir por AJAX real)
            setTimeout(() => {
                // Fechar modal
                const modal = bootstrap.Modal.getInstance(document.getElementById('propostaModal'));
                modal.hide();
                
                // Mostrar sucesso
                mostrarToast('success', 'Proposta enviada com sucesso!');
                
                // Reabilitar botão
                btnEnviar.disabled = false;
                btnEnviar.innerHTML = '<i class="fas fa-paper-plane me-2"></i>Enviar Proposta';
                
                // Simular reload da página
                setTimeout(() => {
                    mostrarToast('info', 'Em produção, a página seria recarregada para mostrar a nova proposta.');
                }, 2000);
            }, 2000);
        }

        function mostrarToast(tipo, mensagem) {
            const toastEl = document.getElementById('toastNotification');
            const toastBody = toastEl.querySelector('.toast-body');
            const toastHeader = toastEl.querySelector('.toast-header');
            
            // Configurar ícone e cor baseado no tipo
            let icone = 'fas fa-info-circle text-primary';
            let cor = 'text-primary';
            
            switch (tipo) {
                case 'success':
                    icone = 'fas fa-check-circle text-success';
                    cor = 'text-success';
                    break;
                case 'error':
                    icone = 'fas fa-exclamation-circle text-danger';
                    cor = 'text-danger';
                    break;
                case 'warning':
                    icone = 'fas fa-exclamation-triangle text-warning';
                    cor = 'text-warning';
                    break;
            }
            
            toastHeader.innerHTML = `
                <i class="${icone} me-2"></i>
                <strong class="me-auto ${cor}">Notificação</strong>
                <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
            `;
            
            toastBody.textContent = mensagem;
            
            const toast = new bootstrap.Toast(toastEl);
            toast.show();
        }

        // Event listeners
        document.addEventListener('DOMContentLoaded', function() {
            // Listener para atualizar resumo quando prazo mudar
            document.getElementById('prazo').addEventListener('change', atualizarResumo);
            
            // Listener para format preco em tempo real
            document.getElementById('preco').addEventListener('input', function() {
                formatarPreco(this);
            });
            
            // Inicializar resumo
            atualizarResumo();
        });
    </script>
</body>
</html>
