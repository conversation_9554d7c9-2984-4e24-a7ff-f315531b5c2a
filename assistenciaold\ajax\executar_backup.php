<?php
/**
 * AJAX - Executar Backup Manual
 * FixFácil Assistências - Sistema Novo
 */

header('Content-Type: application/json');

require_once '../config/auth.php';
require_once '../sistema/backup_automatico.php';

// Verificar autenticação e permissão de admin
$auth = getAuth();
try {
    $auth->checkAssistenciaAuth();
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'Não autorizado']);
    exit();
}

$usuario = $auth->getUsuarioLogado();

// Verificar se é admin
if ($usuario['email'] !== '<EMAIL>') {
    echo json_encode(['success' => false, 'message' => 'Acesso negado']);
    exit();
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Método não permitido']);
    exit();
}

try {
    $backup = new BackupAutomatico();
    $resultado = $backup->executarBackup('manual');
    
    if ($resultado['success']) {
        echo json_encode([
            'success' => true,
            'message' => 'Backup criado com sucesso!',
            'arquivo' => $resultado['arquivo'],
            'tamanho' => $resultado['tamanho']
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'Erro ao criar backup: ' . $resultado['erro']
        ]);
    }
    
} catch (Exception $e) {
    error_log("Erro ao executar backup: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Erro interno do servidor']);
}
?>
