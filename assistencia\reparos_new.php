<?php
/**
 * Nova Página de Reparos - Layout Responsivo Mobile-First
 * FixFácil Assistências - Sistema Novo
 */

// Configurar relatório de erros para debug
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Iniciar sessão se não estiver iniciada
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Verificar se o usuário está logado
if (!isset($_SESSION['usuario_id']) || $_SESSION['tipo_usuario'] !== 'assistencia') {
    header('Location: ../login.php');
    exit();
}

// Configuração de banco de dados
$host = "localhost";
$username = "u680766645_fixfacilnew";
$password = "T3cn0l0g1a@";
$database = "u680766645_fixfacilnew";

$mysqli = new mysqli($host, $username, $password, $database);

if ($mysqli->connect_error) {
    die("Erro de conexão: " . $mysqli->connect_error);
}

$mysqli->set_charset("utf8");

// Obter dados do usuário logado
$usuario_id = $_SESSION['usuario_id'];
$usuario = null;

// Buscar dados do usuário de forma simples
$sql = "SELECT u.id, u.nome, u.email, u.telefone, u.plano_id, at.id as assistencia_id 
        FROM usuarios u 
        LEFT JOIN assistencias_tecnicas at ON u.id = at.usuario_id 
        WHERE u.id = $usuario_id";
$result = $mysqli->query($sql);
if ($result && $result->num_rows > 0) {
    $usuario = $result->fetch_assoc();
}

// Dados padrão se não encontrar
if (!$usuario) {
    $usuario = [
        'id' => $usuario_id,
        'nome' => 'Usuário',
        'email' => '',
        'assistencia_id' => 1
    ];
}

// Filtros
$status_filter = $_GET['status'] ?? 'ativos';
$search = $_GET['search'] ?? '';
$page = (int)($_GET['page'] ?? 1);
$limit = 10;
$offset = ($page - 1) * $limit;

// Obter reparos
$reparos = [];
$total_reparos = 0;

if ($usuario && isset($usuario['assistencia_id'])) {
    $where_conditions = ["pa.assistencia_id = " . (int)$usuario['assistencia_id']];
    
    if ($status_filter === 'ativos') {
        $where_conditions[] = "pa.status IN ('aceita', 'Em Andamento')";
    } elseif ($status_filter === 'concluidos') {
        $where_conditions[] = "pa.status = 'Concluída'";
    } elseif ($status_filter === 'aguardando_pagamento') {
        $where_conditions[] = "pa.status = 'Concluída' AND pa.pago = 0";
    }
    
    if (!empty($search)) {
        $search_escaped = $mysqli->real_escape_string($search);
        $where_conditions[] = "(sr.descricao_problema LIKE '%$search_escaped%' OR sr.dispositivo LIKE '%$search_escaped%' OR sr.marca LIKE '%$search_escaped%' OR sr.modelo LIKE '%$search_escaped%' OR u.nome LIKE '%$search_escaped%')";
    }
    
    $where_clause = implode(' AND ', $where_conditions);
    
    // Contar total
    $count_sql = "
        SELECT COUNT(*) as total
        FROM propostas_assistencia pa
        JOIN solicitacoes_reparo sr ON pa.solicitacao_id = sr.id
        JOIN usuarios u ON sr.usuario_id = u.id
        WHERE $where_clause
    ";
    
    $count_result = $mysqli->query($count_sql);
    if ($count_result) {
        $total_reparos = $count_result->fetch_assoc()['total'];
    }
    
    // Buscar reparos
    $sql = "
        SELECT 
            pa.*,
            sr.descricao_problema,
            sr.dispositivo,
            sr.marca,
            sr.modelo,
            sr.memoria,
            sr.metodo_entrega,
            sr.data_solicitacao,
            u.nome as cliente_nome,
            u.telefone as cliente_telefone,
            u.endereco as cliente_endereco,
            u.email as cliente_email
        FROM propostas_assistencia pa
        JOIN solicitacoes_reparo sr ON pa.solicitacao_id = sr.id
        JOIN usuarios u ON sr.usuario_id = u.id
        WHERE $where_clause
        ORDER BY 
            CASE pa.status 
                WHEN 'aceita' THEN 1
                WHEN 'Em Andamento' THEN 2
                WHEN 'Concluída' THEN 3
                ELSE 4
            END,
            pa.data_proposta DESC
        LIMIT $limit OFFSET $offset
    ";
    
    $result = $mysqli->query($sql);
    if ($result) {
        while ($row = $result->fetch_assoc()) {
            $reparos[] = $row;
        }
    }
}

// Estatísticas dos reparos
$stats = [
    'aceitas' => 0, 'em_andamento' => 0, 'concluidas' => 0, 
    'aguardando_pagamento' => 0
];

if ($usuario && isset($usuario['assistencia_id'])) {
    $sql = "
        SELECT 
            COUNT(CASE WHEN status = 'aceita' THEN 1 END) as aceitas,
            COUNT(CASE WHEN status = 'Em Andamento' THEN 1 END) as em_andamento,
            COUNT(CASE WHEN status = 'Concluída' THEN 1 END) as concluidas,
            COUNT(CASE WHEN status = 'Concluída' AND pago = 0 THEN 1 END) as aguardando_pagamento
        FROM propostas_assistencia 
        WHERE assistencia_id = " . (int)$usuario['assistencia_id'] . " AND status IN ('aceita', 'Em Andamento', 'Concluída')
    ";
    
    $result = $mysqli->query($sql);
    if ($result && $result->num_rows > 0) {
        $stats = $result->fetch_assoc();
    }
}

// Função para obter ícone do dispositivo
function getDeviceIcon($dispositivo) {
    $device = strtolower($dispositivo);
    if (strpos($device, 'iphone') !== false) return '📱';
    if (strpos($device, 'samsung') !== false) return '📱';
    if (strpos($device, 'tablet') !== false) return '📲';
    if (strpos($device, 'notebook') !== false) return '💻';
    if (strpos($device, 'smartwatch') !== false) return '⌚';
    return '📱';
}

// Função para obter status badge
function getStatusBadge($status) {
    switch ($status) {
        case 'aceita':
            return ['emoji' => '⏳', 'class' => 'status-waiting', 'text' => 'Na fila'];
        case 'Em Andamento':
            return ['emoji' => '🔧', 'class' => 'status-progress', 'text' => 'Em andamento'];
        case 'Concluída':
            return ['emoji' => '✅', 'class' => 'status-completed', 'text' => 'Concluído'];
        default:
            return ['emoji' => '❓', 'class' => 'status-waiting', 'text' => $status];
    }
}
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reparos - FixFácil Assistências</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            min-height: 100vh;
            color: #1e293b;
            line-height: 1.5;
        }

        .container {
            max-width: 414px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
            position: relative;
            overflow-x: hidden;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            padding-bottom: 100px;
        }

        /* Header */
        .header {
            background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
            color: white;
            padding: 24px 20px 20px 20px;
            position: relative;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 20"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="0.5" fill="white" opacity="0.05"/></pattern></defs><rect width="100" height="20" fill="url(%23grain)"/></svg>');
        }

        .header-content {
            position: relative;
            z-index: 1;
        }

        .header-top {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 20px;
        }

        .header-title {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .back-button {
            background: rgba(255,255,255,0.15);
            border: none;
            border-radius: 12px;
            color: white;
            width: 40px;
            height: 40px;
            font-size: 18px;
            cursor: pointer;
            backdrop-filter: blur(10px);
            transition: all 0.2s ease;
        }

        .back-button:hover {
            background: rgba(255,255,255,0.25);
        }

        .page-title {
            font-size: 20px;
            font-weight: 700;
            letter-spacing: -0.025em;
        }

        .header-actions {
            display: flex;
            gap: 8px;
        }

        .action-btn {
            width: 40px;
            height: 40px;
            background: rgba(255,255,255,0.15);
            border: none;
            border-radius: 12px;
            color: white;
            font-size: 16px;
            cursor: pointer;
            backdrop-filter: blur(10px);
            transition: all 0.2s ease;
        }

        .action-btn:hover {
            background: rgba(255,255,255,0.25);
        }

        .stats-row {
            display: flex;
            gap: 8px;
            margin-bottom: 16px;
        }

        .stat-card {
            flex: 1;
            background: rgba(255,255,255,0.15);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            padding: 12px;
            text-align: center;
            min-width: 0;
        }

        .stat-number {
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 2px;
        }

        .stat-label {
            font-size: 11px;
            opacity: 0.9;
        }

        /* Search */
        .search-bar {
            background: rgba(255,255,255,0.15);
            border: none;
            border-radius: 12px;
            padding: 12px 16px;
            color: white;
            font-size: 14px;
            width: 100%;
            backdrop-filter: blur(10px);
        }

        .search-bar::placeholder {
            color: rgba(255,255,255,0.7);
        }

        /* Content */
        .content {
            padding: 20px;
        }

        .tabs {
            display: flex;
            background: #f1f5f9;
            border-radius: 12px;
            padding: 4px;
            margin-bottom: 20px;
        }

        .tab {
            flex: 1;
            background: none;
            border: none;
            padding: 10px 8px;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            color: #64748b;
            font-size: 13px;
        }

        .tab.active {
            background: white;
            color: #3b82f6;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        /* Repair Cards */
        .repair-card {
            background: white;
            border-radius: 16px;
            border: 1px solid #e2e8f0;
            margin-bottom: 16px;
            cursor: pointer;
            transition: all 0.2s ease;
            overflow: hidden;
        }

        .repair-card:hover {
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }

        .repair-card.priority {
            border-color: #f59e0b;
            background: linear-gradient(135deg, #fef3c7 0%, #ffffff 100%);
        }

        .repair-header {
            padding: 16px 20px 12px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid #f1f5f9;
        }

        .repair-id {
            font-weight: 700;
            color: #1e293b;
            font-size: 16px;
        }

        .repair-status {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
        }

        .status-waiting {
            background: #fef3c7;
            color: #92400e;
        }

        .status-progress {
            background: #dbeafe;
            color: #1e40af;
        }

        .status-completed {
            background: #d1fae5;
            color: #065f46;
        }

        .repair-body {
            padding: 0 20px 20px 20px;
        }

        .repair-main {
            display: flex;
            gap: 16px;
            margin-bottom: 16px;
        }

        .device-icon {
            font-size: 32px;
            width: 48px;
            height: 48px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f8fafc;
            border-radius: 12px;
        }

        .repair-info {
            flex: 1;
            min-width: 0;
        }

        .device-name {
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 4px;
            font-size: 14px;
        }

        .repair-description {
            color: #64748b;
            font-size: 13px;
            margin-bottom: 8px;
        }

        .customer-info {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 12px;
            color: #64748b;
        }

        .customer-avatar {
            width: 20px;
            height: 20px;
            background: #e2e8f0;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
        }

        .repair-meta {
            display: flex;
            justify-content: space-between;
            margin-bottom: 16px;
            padding: 12px;
            background: #f8fafc;
            border-radius: 8px;
        }

        .meta-item {
            text-align: center;
            flex: 1;
        }

        .meta-label {
            font-size: 11px;
            color: #64748b;
            margin-bottom: 2px;
        }

        .meta-value {
            font-weight: 600;
            font-size: 13px;
            color: #1e293b;
        }

        .repair-actions {
            display: flex;
            gap: 8px;
        }

        .btn {
            padding: 12px 16px;
            border: none;
            border-radius: 10px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 14px;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
            color: white;
            flex: 1;
        }

        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }

        .btn-secondary {
            background: #f1f5f9;
            color: #64748b;
            min-width: 44px;
        }

        .btn-secondary:hover {
            background: #e2e8f0;
        }

        .btn-success {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
            flex: 1;
        }

        .btn-success:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
        }

        .btn-sm {
            padding: 8px 12px;
            font-size: 12px;
        }

        /* Empty State */
        .empty-state {
            text-align: center;
            padding: 40px 20px;
            color: #64748b;
        }

        .empty-icon {
            font-size: 48px;
            margin-bottom: 16px;
        }

        .empty-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: #1e293b;
        }

        .empty-description {
            font-size: 14px;
            margin-bottom: 20px;
        }

        /* Pagination */
        .pagination {
            display: flex;
            justify-content: center;
            gap: 8px;
            margin-top: 20px;
        }

        .pagination a, .pagination span {
            padding: 8px 12px;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            text-decoration: none;
            color: #64748b;
            font-size: 14px;
        }

        .pagination a:hover {
            background: #f1f5f9;
        }

        .pagination .current {
            background: #3b82f6;
            color: white;
            border-color: #3b82f6;
        }

        /* Menu Inferior */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            max-width: 414px;
            width: 100%;
            background: white;
            border-top: 1px solid #e2e8f0;
            display: flex;
            justify-content: space-around;
            padding: 8px 0;
            z-index: 1000;
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-decoration: none;
            color: #64748b;
            transition: color 0.2s ease;
            padding: 8px 12px;
            border-radius: 8px;
            min-width: 60px;
        }

        .nav-item:hover, .nav-item.active {
            color: #3b82f6;
        }

        .nav-icon {
            font-size: 20px;
            margin-bottom: 4px;
        }

        .nav-label {
            font-size: 11px;
            font-weight: 500;
        }

        /* Mobile First - Melhorias para dispositivos móveis */
        @media (max-width: 480px) {
            .container {
                padding: 12px;
                padding-bottom: 100px;
            }

            .header {
                padding: 20px 16px;
                margin-bottom: 20px;
            }

            .header h1, .page-title {
                font-size: 1.8rem;
                margin-bottom: 8px;
            }

            .header-subtitle {
                font-size: 0.9rem;
            }

            .stats-row {
                flex-direction: column;
                gap: 12px;
            }

            .stat-card {
                padding: 16px;
                min-width: auto;
            }

            .stat-number {
                font-size: 1.5rem;
            }

            .stat-label {
                font-size: 0.8rem;
            }

            .tabs {
                flex-wrap: wrap;
                gap: 8px;
                margin-bottom: 16px;
            }

            .tab {
                flex: 1;
                min-width: calc(50% - 4px);
                padding: 10px 12px;
                font-size: 0.8rem;
            }

            .reparos-grid {
                gap: 16px;
            }

            .reparo-card {
                padding: 16px;
            }

            .reparo-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 8px;
            }

            .reparo-id {
                font-size: 0.9rem;
            }

            .device-info {
                flex-direction: column;
                gap: 12px;
            }

            .device-icon {
                width: 48px;
                height: 48px;
                font-size: 1.2rem;
            }

            .device-name {
                font-size: 1.1rem;
            }

            .device-specs {
                flex-wrap: wrap;
                gap: 6px;
            }

            .spec-badge {
                font-size: 0.7rem;
                padding: 3px 8px;
            }

            .reparo-actions {
                flex-direction: column;
                gap: 8px;
            }

            .btn {
                width: 100%;
                justify-content: center;
                padding: 12px 16px;
                font-size: 0.8rem;
            }

            .progress-bar {
                height: 6px;
            }
        }

        /* Tablet */
        @media (min-width: 481px) and (max-width: 767px) {
            .container {
                padding: 16px;
                padding-bottom: 100px;
            }

            .stats-row {
                flex-wrap: wrap;
                justify-content: center;
            }

            .stat-card {
                flex: 1;
                min-width: 150px;
            }

            .tabs {
                flex-wrap: wrap;
                gap: 8px;
            }

            .tab {
                flex: 1;
                min-width: 120px;
            }

            .reparos-grid {
                grid-template-columns: 1fr;
            }
        }

        /* Manter sempre formato mobile - menu sempre visível */

        /* Loading */
        .loading {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 40px;
        }

        .spinner {
            width: 32px;
            height: 32px;
            border: 3px solid #f3f4f6;
            border-top: 3px solid #3b82f6;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="header-content">
                <div class="header-top">
                    <div class="header-title">
                        <button class="back-button" onclick="history.back()">
                            <i class="fas fa-arrow-left"></i>
                        </button>
                        <h1 class="page-title">Reparos</h1>
                    </div>
                    <div class="header-actions">
                        <button class="action-btn" onclick="window.location.href='index.php'" title="Área do cliente">
                            <i class="fas fa-user"></i>
                        </button>
                        <button class="action-btn" onclick="window.location.href='logout.php'" title="Sair">
                            <i class="fas fa-sign-out-alt"></i>
                        </button>
                        <button class="action-btn" onclick="refreshPage()" title="Atualizar">
                            <i class="fas fa-sync-alt"></i>
                        </button>
                    </div>
                </div>

                <div class="stats-row">
                    <div class="stat-card">
                        <div class="stat-number"><?php echo $stats['aceitas']; ?></div>
                        <div class="stat-label">Na fila</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number"><?php echo $stats['em_andamento']; ?></div>
                        <div class="stat-label">Em andamento</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number"><?php echo $stats['concluidas']; ?></div>
                        <div class="stat-label">Concluídos</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number"><?php echo $stats['aguardando_pagamento']; ?></div>
                        <div class="stat-label">Aguardando</div>
                    </div>
                </div>

                <input type="text" class="search-bar" placeholder="Buscar reparos..." 
                       id="searchInput" value="<?php echo htmlspecialchars($search); ?>">
            </div>
        </div>

        <!-- Content -->
        <div class="content">
            <!-- Tabs -->
            <div class="tabs">
                <button class="tab <?php echo $status_filter === 'ativos' ? 'active' : ''; ?>" 
                        onclick="switchTab('ativos')">
                    <i class="fas fa-clock"></i> Ativos
                </button>
                <button class="tab <?php echo $status_filter === 'concluidos' ? 'active' : ''; ?>" 
                        onclick="switchTab('concluidos')">
                    <i class="fas fa-check"></i> Concluídos
                </button>
                <button class="tab <?php echo $status_filter === 'aguardando_pagamento' ? 'active' : ''; ?>" 
                        onclick="switchTab('aguardando_pagamento')">
                    <i class="fas fa-credit-card"></i> Aguardando
                </button>
            </div>

            <!-- Reparos -->
            <div id="reparosContainer" class="repair-cards-grid">
                <?php if (empty($reparos)): ?>
                    <div class="empty-state">
                        <div class="empty-icon">
                            <i class="fas fa-tools"></i>
                        </div>
                        <div class="empty-title">Nenhum reparo encontrado</div>
                        <div class="empty-description">
                            <?php if ($status_filter === 'ativos'): ?>
                                Você não tem reparos ativos no momento.
                            <?php else: ?>
                                Não há reparos com o status selecionado.
                            <?php endif; ?>
                        </div>
                        <a href="solicitacoes_new.php" class="btn btn-primary">
                            <i class="fas fa-plus"></i>
                            Ver Solicitações
                        </a>
                    </div>
                <?php else: ?>
                    <?php foreach ($reparos as $reparo): ?>
                        <?php 
                        $statusInfo = getStatusBadge($reparo['status']); 
                        $isPriority = $reparo['retirada_expressa'] || ($reparo['status'] === 'aceita' && strtotime($reparo['data_proposta']) > strtotime('-1 day'));
                        ?>
                        <div class="repair-card <?php echo $isPriority ? 'priority' : ''; ?>" 
                             onclick="viewRepair(<?php echo $reparo['id']; ?>)">
                            <div class="repair-header">
                                <div class="repair-id">#<?php echo str_pad($reparo['id'], 4, '0', STR_PAD_LEFT); ?></div>
                                <div class="repair-status <?php echo $statusInfo['class']; ?>">
                                    <?php echo $statusInfo['emoji']; ?> <?php echo $statusInfo['text']; ?>
                                </div>
                            </div>
                            <div class="repair-body">
                                <div class="repair-main">
                                    <div class="device-icon">
                                        <?php echo getDeviceIcon($reparo['dispositivo']); ?>
                                    </div>
                                    <div class="repair-info">
                                        <div class="device-name">
                                            <?php echo htmlspecialchars($reparo['marca'] . ' ' . $reparo['modelo']); ?>
                                        </div>
                                        <div class="repair-description">
                                            <?php echo htmlspecialchars($reparo['descricao_problema']); ?>
                                        </div>
                                        <div class="customer-info">
                                            <div class="customer-avatar">
                                                <?php echo strtoupper(substr($reparo['cliente_nome'], 0, 1)); ?>
                                            </div>
                                            <span class="customer-name">
                                                <?php echo htmlspecialchars($reparo['cliente_nome']); ?>
                                            </span>
                                        </div>
                                    </div>
                                </div>

                                <div class="repair-meta">
                                    <div class="meta-item">
                                        <div class="meta-label">Valor</div>
                                        <div class="meta-value">R$ <?php echo number_format($reparo['preco'], 2, ',', '.'); ?></div>
                                    </div>
                                    <div class="meta-item">
                                        <div class="meta-label">Prazo</div>
                                        <div class="meta-value"><?php echo $reparo['prazo']; ?> dias</div>
                                    </div>
                                    <div class="meta-item">
                                        <div class="meta-label">Data</div>
                                        <div class="meta-value"><?php echo date('d/m', strtotime($reparo['data_proposta'])); ?></div>
                                    </div>
                                </div>

                                <div class="repair-actions">
                                    <?php if ($reparo['status'] === 'aceita'): ?>
                                        <button class="btn btn-success" onclick="iniciarReparo(<?php echo $reparo['id']; ?>, event)">
                                            <i class="fas fa-play"></i> Iniciar
                                        </button>
                                    <?php elseif ($reparo['status'] === 'Em Andamento'): ?>
                                        <button class="btn btn-primary" onclick="concluirReparo(<?php echo $reparo['id']; ?>, event)">
                                            <i class="fas fa-check"></i> Concluir
                                        </button>
                                    <?php endif; ?>
                                    
                                    <button class="btn btn-secondary" onclick="chatCliente(<?php echo $reparo['id']; ?>, event)" title="Chat">
                                        <i class="fas fa-comments"></i>
                                    </button>
                                    <button class="btn btn-secondary" onclick="verDetalhes(<?php echo $reparo['solicitacao_id']; ?>, event)" title="Detalhes">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>

                    <!-- Pagination -->
                    <?php if ($total_reparos > $limit): ?>
                        <div class="pagination">
                            <?php
                            $total_pages = ceil($total_reparos / $limit);
                            $current_page = $page;
                            
                            // Previous
                            if ($current_page > 1) {
                                $prev_page = $current_page - 1;
                                echo "<a href='?page=$prev_page&status=$status_filter&search=" . urlencode($search) . "'><i class='fas fa-chevron-left'></i></a>";
                            }
                            
                            // Pages
                            for ($i = max(1, $current_page - 2); $i <= min($total_pages, $current_page + 2); $i++) {
                                if ($i == $current_page) {
                                    echo "<span class='current'>$i</span>";
                                } else {
                                    echo "<a href='?page=$i&status=$status_filter&search=" . urlencode($search) . "'>$i</a>";
                                }
                            }
                            
                            // Next
                            if ($current_page < $total_pages) {
                                $next_page = $current_page + 1;
                                echo "<a href='?page=$next_page&status=$status_filter&search=" . urlencode($search) . "'><i class='fas fa-chevron-right'></i></a>";
                            }
                            ?>
                        </div>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Menu Inferior -->
    <div class="bottom-nav">
        <a href="dashboard_mobile_final.php" class="nav-item">
            <div class="nav-icon">🏠</div>
            <div class="nav-label">Início</div>
        </a>
        <a href="solicitacoes.php" class="nav-item">
            <div class="nav-icon">📋</div>
            <div class="nav-label">Solicitações</div>
        </a>
        <a href="reparos_new.php" class="nav-item active">
            <div class="nav-icon">🔧</div>
            <div class="nav-label">Reparos</div>
        </a>
        <a href="propostas.php" class="nav-item">
            <div class="nav-icon">💼</div>
            <div class="nav-label">Propostas</div>
        </a>
        <a href="marketplace.php" class="nav-item">
            <div class="nav-icon">🛒</div>
            <div class="nav-label">Loja</div>
        </a>
        <a href="carteira.php" class="nav-item">
            <div class="nav-icon">💳</div>
            <div class="nav-label">Carteira</div>
        </a>
    </div>

    <script>
        // Tab switching
        function switchTab(status) {
            const currentUrl = new URL(window.location.href);
            currentUrl.searchParams.set('status', status);
            currentUrl.searchParams.set('page', '1');
            window.location.href = currentUrl.toString();
        }

        // Search functionality
        document.getElementById('searchInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                const currentUrl = new URL(window.location.href);
                currentUrl.searchParams.set('search', this.value);
                currentUrl.searchParams.set('page', '1');
                window.location.href = currentUrl.toString();
            }
        });

        // Refresh page
        function refreshPage() {
            window.location.reload();
        }

        // Repair actions
        function viewRepair(reparoId) {
            window.location.href = `detalhes_solicitacao.php?id=${reparoId}`;
        }

        function iniciarReparo(reparoId, event) {
            event.stopPropagation();
            
            if (confirm('Deseja iniciar este reparo?')) {
                fetch('ajax/atualizar_status_reparo.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `proposta_id=${reparoId}&novo_status=Em Andamento`
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        window.location.reload();
                    } else {
                        alert('Erro: ' + (data.message || 'Erro desconhecido'));
                    }
                })
                .catch(error => {
                    console.error('Erro:', error);
                    alert('Erro ao iniciar reparo');
                });
            }
        }

        function concluirReparo(reparoId, event) {
            event.stopPropagation();
            
            if (confirm('Deseja concluir este reparo?')) {
                fetch('ajax/atualizar_status_reparo.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `proposta_id=${reparoId}&novo_status=Concluída`
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        window.location.reload();
                    } else {
                        alert('Erro: ' + (data.message || 'Erro desconhecido'));
                    }
                })
                .catch(error => {
                    console.error('Erro:', error);
                    alert('Erro ao concluir reparo');
                });
            }
        }

        function chatCliente(reparoId, event) {
            event.stopPropagation();
            window.location.href = `chat.php?proposta_id=${reparoId}`;
        }

        function verDetalhes(solicitacaoId, event) {
            event.stopPropagation();
            window.location.href = `detalhes_solicitacao.php?id=${solicitacaoId}`;
        }

        // Auto-refresh stats every 30 seconds
        setInterval(function() {
            fetch('ajax/get_stats_new.php')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        document.querySelector('.stat-card:nth-child(1) .stat-number').textContent = data.stats.aceitas || 0;
                        document.querySelector('.stat-card:nth-child(2) .stat-number').textContent = data.stats.em_andamento || 0;
                        document.querySelector('.stat-card:nth-child(3) .stat-number').textContent = data.stats.concluidas || 0;
                        document.querySelector('.stat-card:nth-child(4) .stat-number').textContent = data.stats.aguardando_pagamento || 0;
                    }
                })
                .catch(error => console.error('Erro ao atualizar stats:', error));
        }, 30000);
    </script>
</body>
</html>
