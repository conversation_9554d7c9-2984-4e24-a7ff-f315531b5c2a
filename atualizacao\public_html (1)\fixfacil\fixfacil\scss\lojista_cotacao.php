<?php
session_start();
require_once 'db.php';

$mensagem = '';

if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['cotar'])) {
    $produto_id = $_POST['produto_id'];
    $quantidade = $_POST['quantidade'];
    $data_programada = $_POST['data_programada'];

    try {
        // Inserção na tabela compras
        $query = "INSERT INTO compras (lojista_id, produto_id, quantidade, status_pedido, data_programada) VALUES (:lojista_id, :produto_id, :quantidade, 'análise', :data_programada)";
        $stmt = $pdo->prepare($query);
        $stmt->bindParam(':lojista_id', $_SESSION['user_id']);
        $stmt->bindParam(':produto_id', $produto_id);
        $stmt->bindParam(':quantidade', $quantidade);
        $stmt->bindParam(':data_programada', $data_programada);
        $stmt->execute();

        // Inserção na tabela leilao
        $queryLeilao = "INSERT INTO leilao (produto_id, status_leilao) VALUES (:produto_id, 'ativo')";
        $stmtLeilao = $pdo->prepare($queryLeilao);
        $stmtLeilao->bindParam(':produto_id', $produto_id);
        $stmtLeilao->execute();

        $mensagem = 'Cotação enviada com sucesso!';
    } catch (PDOException $e) {
        $mensagem = 'Erro ao enviar a cotação: ' . $e->getMessage();
    }
}

$query = "SELECT * FROM produtos";
$stmt = $pdo->prepare($query);
$stmt->execute();
$produtos = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <title>Cotação de Produtos</title>
</head>
<body>
    <h1>Cotação de Produtos</h1>

    <?php if ($mensagem): ?>
        <p><?= $mensagem ?></p>
    <?php endif; ?>

    <form method="post" action="lojista_cotacao.php">
        <label for="produto_id">Selecione o Produto:</label>
        <select name="produto_id" id="produto_id">
            <?php foreach ($produtos as $produto) : ?>
                <option value="<?= $produto['id'] ?>"><?= $produto['nome'] ?></option>
            <?php endforeach; ?>
        </select>
        <br>

        <label for="quantidade">Quantidade:</label>
        <input type="number" name="quantidade" id="quantidade" placeholder="Quantidade" required>
        <br>

        <label for="data_programada">Data Programada:</label>
        <input type="date" name="data_programada" id="data_programada" required>
        <br>

        <button type="submit" name="cotar">Cotar</button>
    </form>
</body>
</html>
