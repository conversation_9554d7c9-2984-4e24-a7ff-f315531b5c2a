<?php
// Incluir bibliotecas da Vonage
require_once 'vonage-php-sdk-core-main/src/autoload.php';

// Configurações da Vonage
$vonageApiKey = 'f60e2d49';
$vonageApiSecret = 'iLgTmr7JJxBFtqd9';

// Inicialização do cliente Vonage
$client = new \Vonage\Client(new \Vonage\Client\Credentials\Basic($vonageApiKey, $vonageApiSecret));

// Envio de mensagem via WhatsApp
$message = [
    'from' => [ 'type' => 'whatsapp', 'number' => 'AVOSBRASIL' ],
    'to' => [ 'type' => 'whatsapp', 'number' => '+5541995577287' ],
    'message' => [
        'content' => [
            'type' => 'text',
            'text' => 'Teste de envio de mensagem via WhatsApp!'
        ]
    ]
];

try {
    $response = $client->sms()->send($message);
    $response_data = $response->getResponseData();
    
    // Verificar se a mensagem foi enviada com sucesso
    if ($response_data['messages'][0]['status'] == 0) {
        $mensagem_whatsapp = "Mensagem via WhatsApp enviada com sucesso!";
    } else {
        $mensagem_whatsapp = "Erro ao enviar mensagem via WhatsApp: " . $response_data['messages'][0]['error-text'];
    }
} catch (\Exception $e) {
    $mensagem_whatsapp = "Erro ao enviar mensagem via WhatsApp: " . $e->getMessage();
}

// Imprimir mensagem na tela
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste Envio WhatsApp</title>
</head>

<body>

    <h1>Teste de Envio de Mensagem via WhatsApp</h1>

    <?php if (isset($mensagem_whatsapp)): ?>
        <p><?php echo $mensagem_whatsapp; ?></p>
    <?php else: ?>
        <p>Nenhuma mensagem para exibir.</p>
    <?php endif; ?>

</body>

</html>
