<?php
/**
 * AJAX - Enviar Proposta
 * FixFácil Assistências - Sistema Novo
 */

header('Content-Type: application/json');

require_once '../config/auth.php';
require_once '../config/database.php';

// Verificar autenticação
$auth = getAuth();
try {
    $auth->checkAssistenciaAuth();
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'Não autorizado']);
    exit();
}

// Obter dados do usuário
$usuario = $auth->getUsuarioLogado();
$db = getDatabase();

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Método não permitido']);
    exit();
}

$solicitacao_id = $_POST['solicitacao_id'] ?? 0;
$preco = $_POST['preco'] ?? '';
$prazo = $_POST['prazo'] ?? '';
$observacoes = $_POST['observacoes'] ?? '';
$retirada_expressa = isset($_POST['retirada_expressa']) ? 1 : 0;

// Validações
if (!$solicitacao_id || !$preco || !$prazo) {
    echo json_encode(['success' => false, 'message' => 'Dados obrigatórios não fornecidos']);
    exit();
}

// Validar formato do preço
$preco = str_replace([','], ['.'], $preco);
$preco = floatval($preco);

if ($preco <= 0) {
    echo json_encode(['success' => false, 'message' => 'Preço deve ser maior que zero']);
    exit();
}

// Validar prazo
$prazo = intval($prazo);
if ($prazo <= 0 || $prazo > 30) {
    echo json_encode(['success' => false, 'message' => 'Prazo deve ser entre 1 e 30 dias']);
    exit();
}

try {
    // Verificar se a solicitação existe e está pendente
    $sql = "SELECT id, status FROM solicitacoes_reparo WHERE id = ? AND status = 'enviado' AND visivel = 1";
    $result = $db->query($sql, [$solicitacao_id]);
    $solicitacao = $result->fetch_assoc();
    
    if (!$solicitacao) {
        echo json_encode(['success' => false, 'message' => 'Solicitação não encontrada ou não está mais disponível']);
        exit();
    }
    
    // Verificar se já enviei proposta para esta solicitação
    $sql = "SELECT id FROM propostas_assistencia WHERE solicitacao_id = ? AND assistencia_id = ?";
    $result = $db->query($sql, [$solicitacao_id, $usuario['assistencia_id']]);
    
    if ($result->fetch_assoc()) {
        echo json_encode(['success' => false, 'message' => 'Você já enviou uma proposta para esta solicitação']);
        exit();
    }
    
    // Inserir nova proposta
    $sql = "
        INSERT INTO propostas_assistencia 
        (solicitacao_id, assistencia_id, preco, prazo, observacoes, retirada_expressa, status, data_proposta)
        VALUES (?, ?, ?, ?, ?, ?, 'enviada', NOW())
    ";
    
    $db->query($sql, [
        $solicitacao_id,
        $usuario['assistencia_id'],
        $preco,
        $prazo,
        $observacoes,
        $retirada_expressa
    ]);
    
    $proposta_id = $db->getConnection()->insert_id;
    
    // Registrar atividade no log (se existir tabela de logs)
    try {
        $sql = "
            INSERT INTO logs_atividades (usuario_id, tipo, descricao, data_atividade)
            VALUES (?, 'proposta_enviada', ?, NOW())
        ";
        $descricao = "Proposta enviada para solicitação #{$solicitacao_id} - R$ " . number_format($preco, 2, ',', '.');
        $db->query($sql, [$usuario['id'], $descricao]);
    } catch (Exception $e) {
        // Log opcional, não interrompe o processo
    }
    
    // Enviar notificação para o cliente (se implementado)
    try {
        enviarNotificacaoCliente($solicitacao_id, $proposta_id, $preco, $prazo, $db);
    } catch (Exception $e) {
        // Notificação opcional, não interrompe o processo
    }
    
    echo json_encode([
        'success' => true, 
        'message' => 'Proposta enviada com sucesso!',
        'proposta_id' => $proposta_id,
        'preco' => number_format($preco, 2, ',', '.'),
        'prazo' => $prazo
    ]);
    
} catch (Exception $e) {
    error_log("Erro ao enviar proposta: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Erro interno do servidor']);
}

/**
 * Função para enviar notificação ao cliente
 */
function enviarNotificacaoCliente($solicitacao_id, $proposta_id, $preco, $prazo, $db) {
    // Obter dados do cliente
    $sql = "
        SELECT 
            u.nome as cliente_nome,
            u.email as cliente_email,
            u.telefone as cliente_telefone,
            sr.dispositivo,
            sr.marca,
            sr.modelo
        FROM solicitacoes_reparo sr
        JOIN usuarios u ON sr.usuario_id = u.id
        WHERE sr.id = ?
    ";
    
    $result = $db->query($sql, [$solicitacao_id]);
    $dados = $result->fetch_assoc();
    
    if (!$dados) return;
    
    // Preparar mensagem
    $dispositivo = $dados['marca'] . ' ' . $dados['modelo'];
    $preco_formatado = 'R$ ' . number_format($preco, 2, ',', '.');
    $prazo_texto = $prazo . ' dia' . ($prazo > 1 ? 's' : '');
    
    $titulo = "Nova Proposta Recebida";
    $mensagem = "Você recebeu uma nova proposta para o reparo do seu {$dispositivo}! Valor: {$preco_formatado}, Prazo: {$prazo_texto}. Acesse o app para visualizar os detalhes.";
    
    // Inserir notificação no banco
    $sql = "
        INSERT INTO notificacoes (usuario_id, tipo, titulo, mensagem, data_criacao)
        SELECT u.id, 'nova_proposta', ?, ?, NOW()
        FROM solicitacoes_reparo sr
        JOIN usuarios u ON sr.usuario_id = u.id
        WHERE sr.id = ?
    ";
    
    $db->query($sql, [$titulo, $mensagem, $solicitacao_id]);
    
    // Aqui você pode adicionar integração com:
    // - WhatsApp API
    // - E-mail
    // - Push notifications
    // - SMS
}
?>
