<?php
session_start();

// Verificar se o administrador está logado
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: ../admin_login.php');
    exit();
}

$admin_nome = $_SESSION['admin_nome'];

// Configurações do banco de dados
$servername = "localhost";
$username_db = "u680766645_fixfacilnew";
$password_db = "T3cn0l0g1a@";
$dbname = "u680766645_fixfacilnew";

$conn = new mysqli($servername, $username_db, $password_db, $dbname);
if ($conn->connect_error) {
    die("Conexão falhou: " . $conn->connect_error);
}

$conn->set_charset("utf8mb4");

// Obter estatísticas gerais
$stmt = $conn->prepare("SELECT COUNT(*) FROM usuarios");
$stmt->execute();
$stmt->bind_result($total_usuarios);
$stmt->fetch();
$stmt->close();

$stmt = $conn->prepare("SELECT COUNT(*) FROM solicitacoes_reparo");
$stmt->execute();
$stmt->bind_result($total_solicitacoes);
$stmt->fetch();
$stmt->close();

$stmt = $conn->prepare("SELECT COUNT(*) FROM propostas_assistencia");
$stmt->execute();
$stmt->bind_result($total_propostas);
$stmt->fetch();
$stmt->close();

$stmt = $conn->prepare("SELECT COUNT(*) FROM agendamentos_retirada WHERE status = 'Pendente'");
$stmt->execute();
$stmt->bind_result($total_agendamentos_pendentes);
$stmt->fetch();
$stmt->close();

// Fechar conexão
$conn->close();
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <title>Dashboard Administrativo - FixFácil</title>
    <!-- Meta Tags para Responsividade -->
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <!-- Incluindo Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome para ícones -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Estilos personalizados -->
    <style>
        body {
            font-family: 'Poppins', sans-serif;
            background-color: #f8f9fa;
            color: #495057;
        }
        /* Navbar */
        .navbar {
            background-color: #343a40;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .navbar-brand {
            font-weight: 600;
            color: #fff;
        }
        .navbar-nav .nav-link {
            color: #fff !important;
            font-weight: 500;
            margin-right: 15px;
        }
        .navbar-nav .nav-link.active {
            color: #17a2b8 !important;
        }
        .navbar-nav .nav-link:hover {
            color: #f8f9fa !important;
        }
        /* Cards de Estatísticas */
        .stats-card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            background-color: #fff;
            margin-bottom: 30px;
            padding: 20px;
            text-align: center;
        }
        .stats-card h3 {
            font-size: 32px;
            font-weight: 700;
            color: #17a2b8;
        }
        .stats-card p {
            font-size: 18px;
            color: #6c757d;
        }
        /* Gráfico */
        .chart-container {
            background-color: #fff;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        /* Footer */
        footer.footer {
            background-color: #343a40;
            padding: 20px 0;
            position: fixed;
            width: 100%;
            bottom: 0;
            box-shadow: 0 -2px 4px rgba(0,0,0,0.1);
        }
        footer.footer span {
            color: #6c757d;
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand" href="dashboard.php">FixFácil - Painel Administrativo</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" 
                    aria-controls="navbarNav" aria-expanded="false" aria-label="Alternar navegação">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse justify-content-end" id="navbarNav">
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link active" href="dashboard.php"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="admin_agendamentos.php"><i class="fas fa-calendar-alt"></i> Agendamentos</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="manage_users.php"><i class="fas fa-users"></i> Usuários</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="reports.php"><i class="fas fa-chart-bar"></i> Relatórios</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="logout.php"><i class="fas fa-sign-out-alt"></i> Sair</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Conteúdo Principal -->
    <div class="container main-content" style="margin-top: 80px;">
        <div class="page-title text-center mb-5">
            <h2>Dashboard - Visão Geral</h2>
            <p class="text-muted">Aqui você pode acompanhar as atividades recentes do sistema.</p>
        </div>

        <!-- Cards de Estatísticas -->
        <div class="row">
            <div class="col-md-3">
                <div class="stats-card">
                    <h3><?php echo $total_usuarios; ?></h3>
                    <p>Usuários Registrados</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card">
                    <h3><?php echo $total_solicitacoes; ?></h3>
                    <p>Solicitações de Reparo</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card">
                    <h3><?php echo $total_propostas; ?></h3>
                    <p>Propostas Enviadas</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card">
                    <h3><?php echo $total_agendamentos_pendentes; ?></h3>
                    <p>Agendamentos Pendentes</p>
                </div>
            </div>
        </div>

        <!-- Gráfico de Atividades Recentes -->
        <div class="chart-container">
            <h4>Atividades Recentes</h4>
            <canvas id="myChart"></canvas>
        </div>
    </div>

    <!-- Rodapé -->
    <footer class="footer">
        <div class="container text-center">
            <span>&copy; <?php echo date("Y"); ?> FixFácil. Todos os direitos reservados.</span>
        </div>
    </footer>

    <!-- Incluindo Bootstrap JS, Chart.js e dependências -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Gráfico de Atividades Recentes
        var ctx = document.getElementById('myChart').getContext('2d');
        var myChart = new Chart(ctx, {
            type: 'pie', // Mudando para gráfico de pizza
            data: {
                labels: ['Usuários', 'Solicitações', 'Propostas', 'Agendamentos Pendentes'],
                datasets: [{
                    label: 'Atividades',
                    data: [<?php echo $total_usuarios; ?>, <?php echo $total_solicitacoes; ?>, <?php echo $total_propostas; ?>, <?php echo $total_agendamentos_pendentes; ?>],
                    backgroundColor: [
                        'rgba(54, 162, 235, 0.6)',
                        'rgba(255, 159, 64, 0.6)',
                        'rgba(75, 192, 192, 0.6)',
                        'rgba(153, 102, 255, 0.6)'
                    ],
                    borderColor: [
                        'rgba(54, 162, 235, 1)',
                        'rgba(255, 159, 64, 1)',
                        'rgba(75, 192, 192, 1)',
                        'rgba(153, 102, 255, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'top',
                    },
                    tooltip: {
                        enabled: true,
                    }
                }
            }
        });
    </script>
</body>
</html>
