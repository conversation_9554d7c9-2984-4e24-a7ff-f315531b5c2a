<?php
// admin/manage_users.php

session_start();

// Verificar se o administrador está logado
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: ../admin_login.php');
    exit();
}

$admin_nome = $_SESSION['admin_nome'];

// Configurações do banco de dados
$servername = "localhost";
$username_db = "u680766645_fixfacilnew";
$password_db = "T3cn0l0g1a@";
$dbname = "u680766645_fixfacilnew";

$conn = new mysqli($servername, $username_db, $password_db, $dbname);
if ($conn->connect_error) {
    die("Conexão falhou: " . $conn->connect_error);
}

$conn->set_charset("utf8mb4");

$mensagem = '';

// Processar ações (Adicionar, Editar, Excluir)
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    if (isset($_POST['action'])) {
        // Adicionar Usuário
        if ($_POST['action'] == 'add_user') {
            $nome = trim($_POST['nome']);
            $email = trim($_POST['email']);
            $senha = password_hash($_POST['senha'], PASSWORD_DEFAULT);
            $tipo_usuario = $_POST['tipo_usuario'];
            $status = $_POST['status'];

            $stmt = $conn->prepare("INSERT INTO usuarios (nome, email, senha, tipo_usuario, status) VALUES (?, ?, ?, ?, ?)");
            $stmt->bind_param("sssss", $nome, $email, $senha, $tipo_usuario, $status);

            if ($stmt->execute()) {
                $mensagem = '<div class="alert alert-success">Usuário adicionado com sucesso!</div>';
            } else {
                $mensagem = '<div class="alert alert-danger">Erro ao adicionar usuário: ' . htmlspecialchars($stmt->error) . '</div>';
            }
            $stmt->close();
        }

        // Editar Usuário
        if ($_POST['action'] == 'edit_user') {
            $usuario_id = intval($_POST['usuario_id']);
            $nome = trim($_POST['nome']);
            $email = trim($_POST['email']);
            $tipo_usuario = $_POST['tipo_usuario'];
            $status = $_POST['status'];

            if (!empty($_POST['senha'])) {
                $senha = password_hash($_POST['senha'], PASSWORD_DEFAULT);
                $stmt = $conn->prepare("UPDATE usuarios SET nome = ?, email = ?, senha = ?, tipo_usuario = ?, status = ? WHERE id = ?");
                $stmt->bind_param("sssssi", $nome, $email, $senha, $tipo_usuario, $status, $usuario_id);
            } else {
                $stmt = $conn->prepare("UPDATE usuarios SET nome = ?, email = ?, tipo_usuario = ?, status = ? WHERE id = ?");
                $stmt->bind_param("ssssi", $nome, $email, $tipo_usuario, $status, $usuario_id);
            }

            if ($stmt->execute()) {
                $mensagem = '<div class="alert alert-success">Usuário atualizado com sucesso!</div>';
            } else {
                $mensagem = '<div class="alert alert-danger">Erro ao atualizar usuário: ' . htmlspecialchars($stmt->error) . '</div>';
            }
            $stmt->close();
        }

        // Excluir Usuário
        if ($_POST['action'] == 'delete_user') {
            $usuario_id = intval($_POST['usuario_id']);

            $stmt = $conn->prepare("DELETE FROM usuarios WHERE id = ?");
            $stmt->bind_param("i", $usuario_id);

            if ($stmt->execute()) {
                $mensagem = '<div class="alert alert-success">Usuário excluído com sucesso!</div>';
            } else {
                $mensagem = '<div class="alert alert-danger">Erro ao excluir usuário: ' . htmlspecialchars($stmt->error) . '</div>';
            }
            $stmt->close();
        }
    }
}

// Obter lista de usuários
$stmt = $conn->prepare("SELECT id, nome, email, tipo_usuario, status, data_criacao FROM usuarios ORDER BY data_criacao DESC");
$stmt->execute();
$result = $stmt->get_result();
$usuarios = $result->fetch_all(MYSQLI_ASSOC);
$stmt->close();

// Fechar conexão
$conn->close();
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <title>Gerenciar Usuários - FixFácil</title>
    <!-- Meta Tags para Responsividade -->
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <!-- Incluindo Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome para ícones -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Estilos personalizados -->
    <style>
        body {
            font-family: 'Poppins', sans-serif;
            background-color: #f7f9fc;
            color: #495057;
            margin-bottom: 60px;
        }
        /* Navbar */
        .navbar {
            background-color: #fff;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .navbar-brand {
            font-weight: 600;
            color: #007BFF;
        }
        .navbar-nav .nav-link {
            color: #495057 !important;
            font-weight: 500;
            margin-right: 15px;
        }
        .navbar-nav .nav-link.active {
            color: #007BFF !important;
        }
        .navbar-nav .nav-link:hover {
            color: #0056b3 !important;
        }
        /* Conteúdo Principal */
        .main-content {
            padding: 80px 20px 20px;
        }
        .page-title {
            margin-bottom: 40px;
        }
        .page-title h2 {
            font-weight: 600;
            color: #343a40;
        }
        /* Footer */
        footer.footer {
            background-color: #fff;
            padding: 20px 0;
            position: fixed;
            width: 100%;
            bottom: 0;
            box-shadow: 0 -2px 4px rgba(0,0,0,0.1);
        }
        footer.footer span {
            color: #6c757d;
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-light fixed-top">
        <div class="container-fluid">
            <!-- Nome da Empresa -->
            <a class="navbar-brand" href="dashboard.php">Painel Administrativo - FixFácil</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" 
                    aria-controls="navbarNav" aria-expanded="false" aria-label="Alternar navegação">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse justify-content-end" id="navbarNav">
                <ul class="navbar-nav">
                    <!-- Links de navegação -->
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="admin_agendamentos.php"><i class="fas fa-calendar-alt"></i> Agendamentos</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="manage_users.php"><i class="fas fa-users"></i> Usuários</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="reports.php"><i class="fas fa-chart-bar"></i> Relatórios</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="logout.php"><i class="fas fa-sign-out-alt"></i> Sair</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Conteúdo Principal -->
    <div class="container main-content">
        <div class="page-title">
            <h2>Gerenciar Usuários</h2>
            <p class="text-muted">Adicione, edite ou exclua usuários do sistema.</p>
        </div>

        <!-- Exibir Mensagens -->
        <?php if ($mensagem) echo $mensagem; ?>

        <!-- Botão para Adicionar Usuário -->
        <div class="mb-4">
            <button class="btn btn-success" data-bs-toggle="modal" data-bs-target="#modalAdicionarUsuario"><i class="fas fa-user-plus"></i> Adicionar Usuário</button>
        </div>

        <!-- Tabela de Usuários -->
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Nome</th>
                        <th>E-mail</th>
                        <th>Tipo</th>
                        <th>Status</th>
                        <th>Data de Criação</th>
                        <th>Ações</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (!empty($usuarios)): ?>
                        <?php foreach ($usuarios as $usuario): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($usuario['id']); ?></td>
                                <td><?php echo htmlspecialchars($usuario['nome']); ?></td>
                                <td><?php echo htmlspecialchars($usuario['email']); ?></td>
                                <td><?php echo ucfirst(htmlspecialchars($usuario['tipo_usuario'])); ?></td>
                                <td><?php echo ucfirst(htmlspecialchars($usuario['status'])); ?></td>
                                <td><?php echo date('d/m/Y H:i', strtotime($usuario['data_criacao'])); ?></td>
                                <td>
                                    <button class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#modalEditarUsuario<?php echo htmlspecialchars($usuario['id']); ?>"><i class="fas fa-edit"></i></button>
                                    <form action="manage_users.php" method="POST" style="display: inline-block;">
                                        <input type="hidden" name="action" value="delete_user">
                                        <input type="hidden" name="usuario_id" value="<?php echo htmlspecialchars($usuario['id']); ?>">
                                        <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('Tem certeza que deseja excluir este usuário?');"><i class="fas fa-trash"></i></button>
                                    </form>
                                </td>
                            </tr>

                            <!-- Modal Editar Usuário -->
                            <div class="modal fade" id="modalEditarUsuario<?php echo htmlspecialchars($usuario['id']); ?>" tabindex="-1" aria-labelledby="modalEditarUsuarioLabel" aria-hidden="true">
                                <div class="modal-dialog">
                                    <div class="modal-content">
                                        <form action="manage_users.php" method="POST">
                                            <div class="modal-header">
                                                <h5 class="modal-title" id="modalEditarUsuarioLabel">Editar Usuário</h5>
                                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Fechar"></button>
                                            </div>
                                            <div class="modal-body">
                                                <input type="hidden" name="action" value="edit_user">
                                                <input type="hidden" name="usuario_id" value="<?php echo htmlspecialchars($usuario['id']); ?>">
                                                <div class="mb-3">
                                                    <label for="nome" class="form-label">Nome:</label>
                                                    <input type="text" name="nome" class="form-control" value="<?php echo htmlspecialchars($usuario['nome']); ?>" required>
                                                </div>
                                                <div class="mb-3">
                                                    <label for="email" class="form-label">E-mail:</label>
                                                    <input type="email" name="email" class="form-control" value="<?php echo htmlspecialchars($usuario['email']); ?>" required>
                                                </div>
                                                <div class="mb-3">
                                                    <label for="senha" class="form-label">Senha (deixe em branco para manter a atual):</label>
                                                    <input type="password" name="senha" class="form-control">
                                                </div>
                                                <div class="mb-3">
                                                    <label for="tipo_usuario" class="form-label">Tipo de Usuário:</label>
                                                    <select name="tipo_usuario" class="form-select" required>
                                                        <option value="cliente" <?php echo $usuario['tipo_usuario'] == 'cliente' ? 'selected' : ''; ?>>Cliente</option>
                                                        <option value="administrador" <?php echo $usuario['tipo_usuario'] == 'administrador' ? 'selected' : ''; ?>>Administrador</option>
                                                    </select>
                                                </div>
                                                <div class="mb-3">
                                                    <label for="status" class="form-label">Status:</label>
                                                    <select name="status" class="form-select" required>
                                                        <option value="ativo" <?php echo $usuario['status'] == 'ativo' ? 'selected' : ''; ?>>Ativo</option>
                                                        <option value="inativo" <?php echo $usuario['status'] == 'inativo' ? 'selected' : ''; ?>>Inativo</option>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                                                <button type="submit" class="btn btn-primary">Salvar Alterações</button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <tr>
                            <td colspan="7" class="text-center">Nenhum usuário encontrado.</td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>

    <!-- Modal Adicionar Usuário -->
    <div class="modal fade" id="modalAdicionarUsuario" tabindex="-1" aria-labelledby="modalAdicionarUsuarioLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <form action="manage_users.php" method="POST">
                    <div class="modal-header">
                        <h5 class="modal-title" id="modalAdicionarUsuarioLabel">Adicionar Usuário</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Fechar"></button>
                    </div>
                    <div class="modal-body">
                        <input type="hidden" name="action" value="add_user">
                        <div class="mb-3">
                            <label for="nome" class="form-label">Nome:</label>
                            <input type="text" name="nome" class="form-control" required>
                        </div>
                        <div class="mb-3">
                            <label for="email" class="form-label">E-mail:</label>
                            <input type="email" name="email" class="form-control" required>
                        </div>
                        <div class="mb-3">
                            <label for="senha" class="form-label">Senha:</label>
                            <input type="password" name="senha" class="form-control" required>
                        </div>
                        <div class="mb-3">
                            <label for="tipo_usuario" class="form-label">Tipo de Usuário:</label>
                            <select name="tipo_usuario" class="form-select" required>
                                <option value="cliente">Cliente</option>
                                <option value="administrador">Administrador</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="status" class="form-label">Status:</label>
                            <select name="status" class="form-select" required>
                                <option value="ativo">Ativo</option>
                                <option value="inativo">Inativo</option>
                            </select>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                        <button type="submit" class="btn btn-success">Adicionar Usuário</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Rodapé -->
    <footer class="footer">
        <div class="container text-center">
            <span>&copy; <?php echo date("Y"); ?> FixFácil. Todos os direitos reservados.</span>
        </div>
    </footer>

    <!-- Incluindo Bootstrap JS e dependências -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
