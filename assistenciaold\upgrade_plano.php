<?php
/**
 * Página de Upgrade de Plano
 * FixFácil Assistências - Sistema Novo
 */

require_once 'config/auth.php';
require_once 'config/database.php';
require_once 'includes/layout.php';

// Verificar autenticação
$auth = getAuth();
$auth->checkAssistenciaAuth();

// Obter dados do usuário
$usuario = $auth->getUsuarioLogado();
$plano_atual = $auth->getPlanoInfo($usuario['id']);
$db = getDatabase();

// Obter funcionalidade específica se fornecida
$feature = $_GET['feature'] ?? null;

// Obter todos os planos disponíveis
$planos = [];
try {
    $sql = "SELECT * FROM planos ORDER BY id ASC";
    $result = $db->query($sql);
    
    while ($row = $result->fetch_assoc()) {
        $planos[] = $row;
    }
} catch (Exception $e) {
    error_log("Erro ao obter planos: " . $e->getMessage());
}

// Inicializar layout
$layout = new Layout();
?>

<?php $layout->renderHead("Upgrade de Plano - FixFácil Assistências"); ?>

<div class="main-wrapper">
    <?php $layout->renderSidebar('perfil'); ?>
    
    <main class="main-content">
        <!-- Header -->
        <div class="content-header">
            <h1 class="page-title">
                <i class="fas fa-arrow-up me-3"></i>
                Upgrade de Plano
            </h1>
            <p class="page-subtitle">
                <?php if ($feature): ?>
                    Para acessar esta funcionalidade, você precisa fazer upgrade do seu plano
                <?php else: ?>
                    Escolha o plano ideal para sua assistência técnica
                <?php endif; ?>
            </p>
        </div>
        
        <?php if ($feature): ?>
        <div class="alert alert-info" role="alert">
            <i class="fas fa-info-circle me-2"></i>
            <strong>Funcionalidade Restrita:</strong> 
            <?php
            $features_names = [
                'marketplace' => 'Marketplace',
                'chat' => 'Chat com Clientes',
                'retirada_presencial' => 'Retirada Presencial',
                'selo_fixfacil' => 'Selo FixFácil',
                'link_personalizado' => 'Link Personalizado',
                'retirada_express_prioritaria' => 'Retirada Express'
            ];
            echo $features_names[$feature] ?? 'Esta funcionalidade';
            ?> está disponível apenas nos planos Premium e Master.
        </div>
        <?php endif; ?>
        
        <!-- Plano Atual -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-user me-2"></i>
                    Seu Plano Atual
                </h5>
            </div>
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <div class="d-flex align-items-center">
                            <div class="plano-badge plano-<?php echo strtolower($plano_atual['nome']); ?> me-3">
                                <?php if ($plano_atual['nome'] === 'Master'): ?>
                                    <i class="fas fa-crown me-1"></i>
                                <?php elseif ($plano_atual['nome'] === 'Premium'): ?>
                                    <i class="fas fa-star me-1"></i>
                                <?php else: ?>
                                    <i class="fas fa-user me-1"></i>
                                <?php endif; ?>
                                Plano <?php echo $plano_atual['nome']; ?>
                            </div>
                            <div>
                                <h5 class="mb-1">R$ <?php echo number_format($plano_atual['preco_mensal'], 2, ',', '.'); ?>/mês</h5>
                                <p class="text-muted mb-0">Taxa de serviço: <?php echo number_format($plano_atual['taxa_servico'], 1); ?>%</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 text-md-end">
                        <span class="badge bg-success fs-6">Plano Ativo</span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Comparação de Planos -->
        <div class="row g-4">
            <?php foreach ($planos as $plano): ?>
            <div class="col-lg-4">
                <div class="card h-100 <?php echo $plano['id'] == $plano_atual['id'] ? 'border-success' : ''; ?>">
                    <?php if ($plano['nome'] === 'Master'): ?>
                    <div class="card-header bg-warning text-dark text-center">
                        <i class="fas fa-crown me-2"></i>
                        <strong>MAIS POPULAR</strong>
                    </div>
                    <?php endif; ?>
                    
                    <div class="card-body text-center">
                        <div class="plano-badge plano-<?php echo strtolower($plano['nome']); ?> mb-3">
                            <?php if ($plano['nome'] === 'Master'): ?>
                                <i class="fas fa-crown me-1"></i>
                            <?php elseif ($plano['nome'] === 'Premium'): ?>
                                <i class="fas fa-star me-1"></i>
                            <?php else: ?>
                                <i class="fas fa-user me-1"></i>
                            <?php endif; ?>
                            Plano <?php echo $plano['nome']; ?>
                        </div>
                        
                        <h2 class="text-primary mb-1">
                            R$ <?php echo number_format($plano['preco_mensal'], 2, ',', '.'); ?>
                        </h2>
                        <p class="text-muted mb-3">por mês</p>
                        
                        <div class="mb-4">
                            <h4 class="text-success"><?php echo number_format($plano['taxa_servico'], 1); ?>%</h4>
                            <small class="text-muted">Taxa de Serviço</small>
                        </div>
                        
                        <hr>
                        
                        <div class="text-start mb-4">
                            <h6 class="mb-3">Recursos Inclusos:</h6>
                            <ul class="list-unstyled">
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    Solicitações Ilimitadas
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    Propostas Ilimitadas
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    Carteira Digital
                                </li>
                                <li class="mb-2">
                                    <?php if ($plano['acesso_chat']): ?>
                                        <i class="fas fa-check text-success me-2"></i>
                                    <?php else: ?>
                                        <i class="fas fa-times text-muted me-2"></i>
                                    <?php endif; ?>
                                    Chat com Clientes
                                </li>
                                <li class="mb-2">
                                    <?php if ($plano['acesso_marketplace']): ?>
                                        <i class="fas fa-check text-success me-2"></i>
                                    <?php else: ?>
                                        <i class="fas fa-times text-muted me-2"></i>
                                    <?php endif; ?>
                                    Marketplace
                                </li>
                                <li class="mb-2">
                                    <?php if ($plano['retirada_presencial']): ?>
                                        <i class="fas fa-check text-success me-2"></i>
                                    <?php else: ?>
                                        <i class="fas fa-times text-muted me-2"></i>
                                    <?php endif; ?>
                                    Retirada Presencial
                                </li>
                                <li class="mb-2">
                                    <?php if ($plano['selo_fixfacil']): ?>
                                        <i class="fas fa-check text-success me-2"></i>
                                    <?php else: ?>
                                        <i class="fas fa-times text-muted me-2"></i>
                                    <?php endif; ?>
                                    Selo FixFácil
                                </li>
                                <li class="mb-2">
                                    <?php if ($plano['link_personalizado']): ?>
                                        <i class="fas fa-check text-success me-2"></i>
                                    <?php else: ?>
                                        <i class="fas fa-times text-muted me-2"></i>
                                    <?php endif; ?>
                                    Link Personalizado
                                </li>
                                <li class="mb-2">
                                    <?php if ($plano['retirada_express_prioritaria']): ?>
                                        <i class="fas fa-check text-success me-2"></i>
                                    <?php else: ?>
                                        <i class="fas fa-times text-muted me-2"></i>
                                    <?php endif; ?>
                                    Retirada Express
                                </li>
                            </ul>
                        </div>
                        
                        <div class="d-grid">
                            <?php if ($plano['id'] == $plano_atual['id']): ?>
                            <button class="btn btn-success" disabled>
                                <i class="fas fa-check me-2"></i>
                                Plano Atual
                            </button>
                            <?php elseif ($plano['id'] < $plano_atual['id']): ?>
                            <button class="btn btn-outline-secondary" disabled>
                                <i class="fas fa-arrow-down me-2"></i>
                                Downgrade
                            </button>
                            <?php else: ?>
                            <button type="button" class="btn btn-primary" 
                                    onclick="selecionarPlano(<?php echo $plano['id']; ?>, '<?php echo $plano['nome']; ?>', <?php echo $plano['preco_mensal']; ?>)">
                                <i class="fas fa-arrow-up me-2"></i>
                                Fazer Upgrade
                            </button>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
        
        <!-- Benefícios do Upgrade -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-star me-2"></i>
                    Por que fazer upgrade?
                </h5>
            </div>
            <div class="card-body">
                <div class="row g-4">
                    <div class="col-md-6">
                        <div class="d-flex align-items-start">
                            <div class="bg-success bg-opacity-10 p-3 rounded-circle me-3">
                                <i class="fas fa-percentage text-success"></i>
                            </div>
                            <div>
                                <h6>Menor Taxa de Serviço</h6>
                                <p class="text-muted mb-0">
                                    Quanto maior o plano, menor a taxa cobrada sobre seus reparos.
                                </p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="d-flex align-items-start">
                            <div class="bg-info bg-opacity-10 p-3 rounded-circle me-3">
                                <i class="fas fa-comments text-info"></i>
                            </div>
                            <div>
                                <h6>Chat Direto com Clientes</h6>
                                <p class="text-muted mb-0">
                                    Converse em tempo real e melhore a experiência do cliente.
                                </p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="d-flex align-items-start">
                            <div class="bg-warning bg-opacity-10 p-3 rounded-circle me-3">
                                <i class="fas fa-store text-warning"></i>
                            </div>
                            <div>
                                <h6>Marketplace de Produtos</h6>
                                <p class="text-muted mb-0">
                                    Venda peças e produtos para outros técnicos e clientes.
                                </p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="d-flex align-items-start">
                            <div class="bg-primary bg-opacity-10 p-3 rounded-circle me-3">
                                <i class="fas fa-crown text-primary"></i>
                            </div>
                            <div>
                                <h6>Recursos Exclusivos</h6>
                                <p class="text-muted mb-0">
                                    Selo FixFácil, link personalizado e retirada express.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>
</div>

<!-- Modal de Confirmação -->
<div class="modal fade" id="upgradeModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirmar Upgrade</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="text-center mb-3">
                    <i class="fas fa-arrow-up fs-1 text-primary mb-3"></i>
                    <h4>Upgrade para Plano <span id="plano-nome"></span></h4>
                    <p class="text-muted">Valor: R$ <span id="plano-preco"></span>/mês</p>
                </div>
                
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    O upgrade será ativado imediatamente e você terá acesso a todos os recursos do novo plano.
                </div>
                
                <p class="text-center">
                    <strong>Deseja confirmar o upgrade?</strong>
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                <button type="button" class="btn btn-primary" onclick="confirmarUpgrade()">
                    <i class="fas fa-check me-2"></i>
                    Confirmar Upgrade
                </button>
            </div>
        </div>
    </div>
</div>

<?php 
$extraJS = "
<script>
let planoSelecionado = null;

function selecionarPlano(planoId, planoNome, planoPreco) {
    planoSelecionado = planoId;
    document.getElementById('plano-nome').textContent = planoNome;
    document.getElementById('plano-preco').textContent = planoPreco.toLocaleString('pt-BR', {minimumFractionDigits: 2});
    
    const modal = new bootstrap.Modal(document.getElementById('upgradeModal'));
    modal.show();
}

function confirmarUpgrade() {
    if (!planoSelecionado) return;
    
    const formData = new FormData();
    formData.append('plano_id', planoSelecionado);
    
    fetch('ajax/upgrade_plano.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Upgrade realizado com sucesso!');
            location.reload();
        } else {
            alert('Erro: ' + data.message);
        }
    })
    .catch(error => {
        alert('Erro ao realizar upgrade');
        console.error(error);
    });
}
</script>
";

$layout->renderFooter($extraJS); 
?>
