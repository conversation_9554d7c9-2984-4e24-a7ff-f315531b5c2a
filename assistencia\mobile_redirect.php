<?php
/**
 * Redirecionamentos Mobile-First
 * FixFácil Assistências - Sistema Atualizado
 */

// Verificar se o usuário está logado
session_start();
if (!isset($_SESSION['usuario_id']) || $_SESSION['tipo_usuario'] !== 'assistencia') {
    header('Location: ../login.php');
    exit();
}

// Determinar qual página redirecionar
$page = $_GET['page'] ?? 'dashboard';

switch ($page) {
    case 'dashboard':
        header('Location: dashboard_mobile_final.php');
        break;
    
    case 'solicitacoes':
        header('Location: solicitacoes_updated.php');
        break;
    
    case 'perfil':
        header('Location: perfil_updated.php');
        break;
    
    case 'reparos':
        header('Location: reparos_updated.php');
        break;
    
    case 'marketplace':
        header('Location: marketplace_mobile.php');
        break;
    
    case 'chat':
        header('Location: chat_mobile.php');
        break;
    
    default:
        header('Location: dashboard_mobile_final.php');
        break;
}
exit();
?>
