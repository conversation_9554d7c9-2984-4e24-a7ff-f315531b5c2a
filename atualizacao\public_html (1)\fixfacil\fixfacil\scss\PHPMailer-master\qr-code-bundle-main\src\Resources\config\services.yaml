services:
    _defaults:
        autowire: true
        autoconfigure: true
        public: false

    Endroid\QrCodeBundle\Controller\:
        resource: '../../Controller'
        tags: ['controller.service_arguments']

    Endroid\QrCode\Writer\BinaryWriter: ~
    Endroid\QrCode\Writer\DebugWriter: ~
    Endroid\QrCode\Writer\EpsWriter: ~
    Endroid\QrCode\Writer\GifWriter: ~
    Endroid\QrCode\Writer\PdfWriter: ~
    Endroid\QrCode\Writer\PngWriter: ~
    Endroid\QrCode\Writer\SvgWriter: ~
    Endroid\QrCode\Writer\WebPWriter: ~

    Endroid\QrCode\Builder\Builder: ~
    Endroid\QrCode\Builder\BuilderInterface: '@Endroid\QrCode\Builder\Builder'

    Endroid\QrCode\Builder\BuilderRegistry: ~
    Endroid\QrCode\Builder\BuilderRegistryInterface: '@Endroid\QrCode\Builder\BuilderRegistry'

    Endroid\QrCodeBundle\Twig\QrCodeExtension: ~
    Endroid\QrCodeBundle\Twig\QrCodeRuntime: ~
