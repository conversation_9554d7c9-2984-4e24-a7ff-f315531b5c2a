<?php
/**
 * <PERSON><PERSON><PERSON>a de Chat
 * FixFácil Assistências - Sistema Novo
 */

require_once 'config/auth.php';
require_once 'config/database.php';
require_once 'includes/layout.php';

// Verificar autenticação
$auth = getAuth();
$auth->checkAssistenciaAuth();

// Verificar acesso ao chat
if (!$auth->hasAccess('chat')) {
    header('Location: upgrade_plano.php?feature=chat');
    exit();
}

// Obter dados do usuário
$usuario = $auth->getUsuarioLogado();
$plano = $auth->getPlanoInfo($usuario['id']);
$db = getDatabase();

// Obter proposta específica se fornecida
$proposta_id = $_GET['proposta_id'] ?? null;
$proposta_selecionada = null;

if ($proposta_id) {
    try {
        $sql = "
            SELECT 
                pa.*,
                sr.descricao_problema,
                sr.dispositivo,
                sr.marca,
                sr.modelo,
                u.nome as cliente_nome,
                u.telefone as cliente_telefone
            FROM propostas_assistencia pa
            JOIN solicitacoes_reparo sr ON pa.solicitacao_id = sr.id
            JOIN usuarios u ON sr.usuario_id = u.id
            WHERE pa.id = ? AND pa.assistencia_id = ?
        ";
        
        $result = $db->query($sql, [$proposta_id, $usuario['assistencia_id']]);
        $proposta_selecionada = $result->fetch_assoc();
        
    } catch (Exception $e) {
        error_log("Erro ao obter proposta: " . $e->getMessage());
    }
}

// Obter conversas ativas (propostas aceitas/em andamento)
$conversas = [];
try {
    $sql = "
        SELECT
            pa.id as proposta_id,
            pa.status,
            sr.dispositivo,
            sr.marca,
            sr.modelo,
            u.nome as cliente_nome,
            u.telefone as cliente_telefone,
            0 as total_mensagens,
            0 as nao_lidas,
            pa.data_proposta as ultima_mensagem
        FROM propostas_assistencia pa
        JOIN solicitacoes_reparo sr ON pa.solicitacao_id = sr.id
        JOIN usuarios u ON sr.usuario_id = u.id
        WHERE pa.assistencia_id = ?
        AND pa.status IN ('aceita', 'Em Andamento', 'Concluída')
        ORDER BY pa.data_proposta DESC
        LIMIT 20
    ";

    $result = $db->query($sql, [$usuario['assistencia_id']]);

    while ($row = $result->fetch_assoc()) {
        $conversas[] = $row;
    }

} catch (Exception $e) {
    error_log("Erro ao obter conversas: " . $e->getMessage());
}

// Obter mensagens da conversa selecionada (simuladas por enquanto)
$mensagens = [];
if ($proposta_selecionada) {
    // Por enquanto, vamos simular algumas mensagens
    $mensagens = [
        [
            'id' => 1,
            'mensagem' => 'Olá! Gostaria de saber o status do meu reparo.',
            'remetente_tipo' => 'usuario',
            'remetente_nome' => $proposta_selecionada['cliente_nome'],
            'data_envio' => date('Y-m-d H:i:s', strtotime('-2 hours'))
        ],
        [
            'id' => 2,
            'mensagem' => 'Olá! Seu reparo está em andamento. Estamos aguardando a chegada da peça.',
            'remetente_tipo' => 'assistencia',
            'remetente_nome' => 'Você',
            'data_envio' => date('Y-m-d H:i:s', strtotime('-1 hour'))
        ]
    ];
}

// Inicializar layout
$layout = new Layout();
?>

<?php $layout->renderHead("Chat - FixFácil Assistências"); ?>

<div class="main-wrapper">
    <?php $layout->renderSidebar('chat'); ?>
    
    <main class="main-content">
        <!-- Header -->
        <div class="content-header">
            <h1 class="page-title">
                <i class="fas fa-comments me-3"></i>
                Chat com Clientes
            </h1>
            <p class="page-subtitle">
                Converse em tempo real com seus clientes durante os reparos
            </p>
        </div>
        
        <div class="row g-4" style="height: calc(100vh - 200px);">
            <!-- Lista de Conversas -->
            <div class="col-lg-4">
                <div class="card h-100">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-list me-2"></i>
                            Conversas Ativas
                        </h5>
                    </div>
                    <div class="card-body p-0" style="overflow-y: auto;">
                        <?php if (empty($conversas)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-comments fs-1 text-muted mb-3"></i>
                            <h6 class="text-muted">Nenhuma conversa ativa</h6>
                            <p class="text-muted small">
                                As conversas aparecerão aqui quando os clientes enviarem mensagens.
                            </p>
                        </div>
                        <?php else: ?>
                        <div class="list-group list-group-flush">
                            <?php foreach ($conversas as $conversa): ?>
                            <a href="?proposta_id=<?php echo $conversa['proposta_id']; ?>" 
                               class="list-group-item list-group-item-action <?php echo $proposta_id == $conversa['proposta_id'] ? 'active' : ''; ?>">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div class="flex-grow-1">
                                        <h6 class="mb-1"><?php echo htmlspecialchars($conversa['cliente_nome']); ?></h6>
                                        <p class="mb-1 small">
                                            <?php echo htmlspecialchars($conversa['marca'] . ' ' . $conversa['modelo']); ?>
                                        </p>
                                        <small class="text-muted">
                                            <?php if ($conversa['ultima_mensagem']): ?>
                                                <?php echo date('d/m H:i', strtotime($conversa['ultima_mensagem'])); ?>
                                            <?php endif; ?>
                                        </small>
                                    </div>
                                    <div class="text-end">
                                        <?php if ($conversa['nao_lidas'] > 0): ?>
                                        <span class="badge bg-danger rounded-pill">
                                            <?php echo $conversa['nao_lidas']; ?>
                                        </span>
                                        <?php endif; ?>
                                        <div class="mt-1">
                                            <?php
                                            $status_class = [
                                                'aceita' => 'success',
                                                'Em Andamento' => 'info',
                                                'Concluída' => 'primary'
                                            ];
                                            ?>
                                            <span class="badge bg-<?php echo $status_class[$conversa['status']] ?? 'secondary'; ?> small">
                                                <?php echo $conversa['status']; ?>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </a>
                            <?php endforeach; ?>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            
            <!-- Área de Chat -->
            <div class="col-lg-8">
                <div class="card h-100 d-flex flex-column">
                    <?php if ($proposta_selecionada): ?>
                    <!-- Header da Conversa -->
                    <div class="card-header">
                        <div class="d-flex align-items-center">
                            <div class="flex-grow-1">
                                <h5 class="mb-0"><?php echo htmlspecialchars($proposta_selecionada['cliente_nome']); ?></h5>
                                <small class="text-muted">
                                    <?php echo htmlspecialchars($proposta_selecionada['marca'] . ' ' . $proposta_selecionada['modelo']); ?>
                                    - <?php echo htmlspecialchars($proposta_selecionada['status']); ?>
                                </small>
                            </div>
                            <div>
                                <a href="tel:<?php echo htmlspecialchars($proposta_selecionada['cliente_telefone']); ?>" 
                                   class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-phone me-1"></i>
                                    Ligar
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Mensagens -->
                    <div class="card-body flex-grow-1 d-flex flex-column p-0">
                        <div class="flex-grow-1 p-3" id="mensagens-container" style="overflow-y: auto; max-height: 400px;">
                            <?php if (empty($mensagens)): ?>
                            <div class="text-center py-4">
                                <i class="fas fa-comment-dots fs-1 text-muted mb-3"></i>
                                <h6 class="text-muted">Nenhuma mensagem ainda</h6>
                                <p class="text-muted small">
                                    Envie a primeira mensagem para iniciar a conversa.
                                </p>
                            </div>
                            <?php else: ?>
                            <?php foreach ($mensagens as $mensagem): ?>
                            <div class="mb-3 <?php echo $mensagem['remetente_tipo'] === 'assistencia' ? 'text-end' : ''; ?>">
                                <div class="d-inline-block max-width-70">
                                    <div class="p-2 rounded <?php echo $mensagem['remetente_tipo'] === 'assistencia' ? 'bg-primary text-white' : 'bg-light'; ?>">
                                        <p class="mb-1"><?php echo nl2br(htmlspecialchars($mensagem['mensagem'])); ?></p>
                                        <small class="<?php echo $mensagem['remetente_tipo'] === 'assistencia' ? 'text-white-50' : 'text-muted'; ?>">
                                            <?php echo date('d/m H:i', strtotime($mensagem['data_envio'])); ?>
                                        </small>
                                    </div>
                                    <small class="text-muted d-block mt-1">
                                        <?php echo htmlspecialchars($mensagem['remetente_nome']); ?>
                                    </small>
                                </div>
                            </div>
                            <?php endforeach; ?>
                            <?php endif; ?>
                        </div>
                        
                        <!-- Formulário de Envio -->
                        <div class="border-top p-3">
                            <form id="form-mensagem" onsubmit="enviarMensagem(event)">
                                <div class="input-group">
                                    <input type="hidden" id="proposta_id" value="<?php echo $proposta_id; ?>">
                                    <textarea class="form-control" id="mensagem" name="mensagem" 
                                              placeholder="Digite sua mensagem..." rows="2" required></textarea>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-paper-plane"></i>
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                    
                    <?php else: ?>
                    <!-- Nenhuma conversa selecionada -->
                    <div class="card-body d-flex align-items-center justify-content-center">
                        <div class="text-center">
                            <i class="fas fa-comments fs-1 text-muted mb-3"></i>
                            <h4 class="text-muted">Selecione uma conversa</h4>
                            <p class="text-muted">
                                Escolha uma conversa na lista ao lado para começar a conversar com o cliente.
                            </p>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </main>
</div>

<?php 
$extraJS = "
<style>
.max-width-70 {
    max-width: 70%;
}

#mensagens-container {
    scroll-behavior: smooth;
}
</style>

<script>
function enviarMensagem(event) {
    event.preventDefault();
    
    const propostaId = document.getElementById('proposta_id').value;
    const mensagem = document.getElementById('mensagem').value.trim();
    
    if (!mensagem) return;
    
    const formData = new FormData();
    formData.append('proposta_id', propostaId);
    formData.append('mensagem', mensagem);
    
    fetch('ajax/enviar_mensagem.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Adicionar mensagem à interface
            adicionarMensagemInterface(mensagem, 'Você', new Date());
            
            // Limpar campo
            document.getElementById('mensagem').value = '';
            
            // Scroll para baixo
            scrollToBottom();
        } else {
            alert('Erro ao enviar mensagem: ' + data.message);
        }
    })
    .catch(error => {
        alert('Erro ao enviar mensagem');
        console.error(error);
    });
}

function adicionarMensagemInterface(mensagem, remetente, data) {
    const container = document.getElementById('mensagens-container');
    const dataFormatada = data.toLocaleString('pt-BR', {
        day: '2-digit',
        month: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
    });
    
    const mensagemHtml = `
        <div class=\"mb-3 text-end\">
            <div class=\"d-inline-block max-width-70\">
                <div class=\"p-2 rounded bg-primary text-white\">
                    <p class=\"mb-1\">${mensagem.replace(/\n/g, '<br>')}</p>
                    <small class=\"text-white-50\">${dataFormatada}</small>
                </div>
                <small class=\"text-muted d-block mt-1\">${remetente}</small>
            </div>
        </div>
    `;
    
    container.insertAdjacentHTML('beforeend', mensagemHtml);
}

function scrollToBottom() {
    const container = document.getElementById('mensagens-container');
    container.scrollTop = container.scrollHeight;
}

// Auto-scroll ao carregar
document.addEventListener('DOMContentLoaded', function() {
    scrollToBottom();
    
    // Auto-refresh das mensagens a cada 10 segundos
    if (document.getElementById('proposta_id').value) {
        setInterval(function() {
            // Aqui você pode implementar um refresh automático das mensagens
            // location.reload(); // Simples mas não ideal
        }, 10000);
    }
});

// Enter para enviar (Shift+Enter para nova linha)
document.getElementById('mensagem').addEventListener('keydown', function(e) {
    if (e.key === 'Enter' && !e.shiftKey) {
        e.preventDefault();
        document.getElementById('form-mensagem').dispatchEvent(new Event('submit'));
    }
});
</script>
";

$layout->renderFooter($extraJS); 
?>
