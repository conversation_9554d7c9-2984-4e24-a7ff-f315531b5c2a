<?php
// admin/send_emails.php

session_start();

// Verificar se o administrador está logado
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: ../admin_login.php');
    exit();
}

// Habilitar a exibição de erros para debugging (REMOVA EM PRODUÇÃO)
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Incluir o autoload do Composer
require 'vendor/autoload.php';

use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\Exception;

// Configurações do banco de dados
$servername = "localhost";
$username_db = "u680766645_fixfacilnew"; // Seu usuário do banco de dados
$password_db = "T3cn0l0g1a@";             // Sua senha do banco de dados
$dbname = "u680766645_fixfacilnew";

$conn = new mysqli($servername, $username_db, $password_db, $dbname);
if ($conn->connect_error) {
    die("Conexão falhou: " . $conn->connect_error);
}

$conn->set_charset("utf8mb4");

// Inicializar variáveis
$mensagem_status = "";

// Processar formulário se for POST
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Obter dados do formulário
    $destinatario_tipo = $_POST['destinatario_tipo'];
    $destinatarios_especificos = isset($_POST['destinatarios_especificos']) ? $_POST['destinatarios_especificos'] : [];
    $assunto = trim($_POST['assunto']);
    $mensagem = trim($_POST['mensagem']);

    // Validar campos
    if (empty($assunto) || empty($mensagem)) {
        $mensagem_status = '<div class="alert alert-danger">Assunto e Mensagem são obrigatórios.</div>';
    } else {
        // Obter lista de e-mails com base na seleção
        $emails = [];

        if ($destinatario_tipo == 'todos_usuarios') {
            $sql = "SELECT email FROM usuarios WHERE tipo_usuario = 'usuario' AND status = 1";
            $result = $conn->query($sql);
            if ($result && $result->num_rows > 0) {
                while ($row = $result->fetch_assoc()) {
                    $emails[] = $row['email'];
                }
            } else {
                error_log("Nenhum usuário encontrado ou erro na consulta de usuários.");
            }
        } elseif ($destinatario_tipo == 'todas_assistencias') {
            // Obter e-mails das assistências através da tabela 'assistencias_tecnicas' ligada à tabela 'usuarios'
            $sql = "SELECT u.email FROM assistencias_tecnicas a
                    JOIN usuarios u ON a.usuario_id = u.id
                    WHERE a.usuario_id IS NOT NULL AND u.email IS NOT NULL AND u.status = 1";
            $result = $conn->query($sql);
            if ($result && $result->num_rows > 0) {
                while ($row = $result->fetch_assoc()) {
                    $emails[] = $row['email'];
                }
            } else {
                error_log("Nenhuma assistência encontrada ou erro na consulta de assistências.");
            }
        } elseif ($destinatario_tipo == 'especificos') {
            // Destinatários específicos: podem ser usuários ou assistências
            if (!empty($destinatarios_especificos)) {
                // Separar IDs de usuários e assistências
                $usuario_ids = [];
                $assistencia_ids = [];

                foreach ($destinatarios_especificos as $dest_id) {
                    if (strpos($dest_id, 'u_') === 0) {
                        $usuario_ids[] = intval(substr($dest_id, 2));
                    } elseif (strpos($dest_id, 'a_') === 0) {
                        $assistencia_ids[] = intval(substr($dest_id, 2));
                    }
                }

                // Obter e-mails de usuários específicos
                if (!empty($usuario_ids)) {
                    $placeholders = implode(',', array_fill(0, count($usuario_ids), '?'));
                    $types = str_repeat('i', count($usuario_ids));
                    $stmt = $conn->prepare("SELECT email FROM usuarios WHERE id IN ($placeholders) AND status = 1");
                    if ($stmt) {
                        $stmt->bind_param($types, ...$usuario_ids);
                        $stmt->execute();
                        $result = $stmt->get_result();
                        if ($result && $result->num_rows > 0) {
                            while ($row = $result->fetch_assoc()) {
                                $emails[] = $row['email'];
                            }
                        }
                        $stmt->close();
                    } else {
                        error_log("Erro na preparação da consulta para usuários específicos.");
                    }
                }

                // Obter e-mails de assistências específicas
                if (!empty($assistencia_ids)) {
                    $placeholders = implode(',', array_fill(0, count($assistencia_ids), '?'));
                    $types = str_repeat('i', count($assistencia_ids));
                    $stmt = $conn->prepare("SELECT u.email FROM assistencias_tecnicas a
                                            JOIN usuarios u ON a.usuario_id = u.id
                                            WHERE a.id IN ($placeholders) AND u.status = 1");
                    if ($stmt) {
                        $stmt->bind_param($types, ...$assistencia_ids);
                        $stmt->execute();
                        $result = $stmt->get_result();
                        if ($result && $result->num_rows > 0) {
                            while ($row = $result->fetch_assoc()) {
                                $emails[] = $row['email'];
                            }
                        }
                        $stmt->close();
                    } else {
                        error_log("Erro na preparação da consulta para assistências específicas.");
                    }
                }
            }
        }

        // Remover e-mails duplicados
        $emails = array_unique($emails);

        // Verificar se há e-mails para enviar
        if (empty($emails)) {
            $mensagem_status = '<div class="alert alert-warning">Nenhum destinatário encontrado para a seleção realizada.</div>';
        } else {
            // Configurações do PHPMailer
            $mail = new PHPMailer(true);

            try {
                // Configurações do servidor de e-mail
                $mail->isSMTP();
                $mail->Host       = 'smtp.hostinger.com.br'; // Substitua pelo seu servidor SMTP
                $mail->SMTPAuth   = true;
                $mail->Username   = '<EMAIL>'; // Substitua pelo seu e-mail
                $mail->Password   = 'T3cn0l0g1a@'; // Substitua pela sua senha
                $mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
                $mail->Port       = 587;

                // Remetente
                $mail->setFrom('<EMAIL>', 'FixFacil');

                // Destinatários
                foreach ($emails as $email) {
                    $mail->addAddress($email);
                }

                // Conteúdo do e-mail
                $mail->isHTML(true);
                $mail->Subject = $assunto;
                $mail->Body    = nl2br(htmlspecialchars($mensagem)); // Converte quebras de linha para <br> e escapa caracteres HTML
                $mail->AltBody = htmlspecialchars($mensagem); // Corpo alternativo para clientes de e-mail que não suportam HTML

                $mail->send();
                $mensagem_status = '<div class="alert alert-success">E-mails enviados com sucesso para ' . count($emails) . ' destinatário(s).</div>';
            } catch (Exception $e) {
                $mensagem_status = '<div class="alert alert-danger">Não foi possível enviar os e-mails. Erro: ' . $mail->ErrorInfo . '</div>';
            }
        }
    }
}

// Obter listas de usuários e assistências para seleção específica (fora do bloco POST)
$usuarios = [];
$assistencias = [];

// Obter usuários do tipo 'usuario'
$sql = "SELECT id, nome, email FROM usuarios WHERE tipo_usuario = 'usuario' AND status = 1";
$result = $conn->query($sql);
if ($result && $result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $usuarios[] = $row;
    }
} else {
    error_log("Nenhum usuário encontrado ou erro na consulta de usuários (GET).");
}

// Obter assistências_tecnicas
$sql = "SELECT a.id, u.nome, u.email FROM assistencias_tecnicas a
        JOIN usuarios u ON a.usuario_id = u.id
        WHERE u.status = 1";
$result = $conn->query($sql);
if ($result && $result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $assistencias[] = $row;
    }
} else {
    error_log("Nenhuma assistência encontrada ou erro na consulta de assistências (GET).");
}

$conn->close();
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <title>Enviar E-mails - FixFácil</title>
    <!-- Meta Tags para Responsividade -->
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <!-- Incluindo Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome para ícones -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Estilos personalizados -->
    <style>
        body {
            font-family: 'Poppins', sans-serif;
            background-color: #f7f9fc;
            color: #495057;
            margin-bottom: 60px;
        }
        /* Navbar */
        .navbar {
            background-color: #fff;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .navbar-brand {
            font-weight: 600;
            color: #007BFF;
        }
        .navbar-nav .nav-link {
            color: #495057 !important;
            font-weight: 500;
            margin-right: 15px;
        }
        .navbar-nav .nav-link.active {
            color: #007BFF !important;
        }
        .navbar-nav .nav-link:hover {
            color: #0056b3 !important;
        }
        /* Conteúdo Principal */
        .main-content {
            padding: 80px 20px 20px;
        }
        .page-title {
            margin-bottom: 40px;
        }
        .page-title h2 {
            font-weight: 600;
            color: #343a40;
        }
        /* Footer */
        footer.footer {
            background-color: #fff;
            padding: 20px 0;
            position: fixed;
            width: 100%;
            bottom: 0;
            box-shadow: 0 -2px 4px rgba(0,0,0,0.1);
        }
        footer.footer span {
            color: #6c757d;
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-light fixed-top">
        <div class="container-fluid">
            <!-- Nome da Empresa -->
            <a class="navbar-brand" href="dashboard.php">Painel Administrativo - FixFácil</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" 
                    aria-controls="navbarNav" aria-expanded="false" aria-label="Alternar navegação">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse justify-content-end" id="navbarNav">
                <ul class="navbar-nav">
                    <!-- Links de navegação -->
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="admin_agendamentos.php"><i class="fas fa-calendar-alt"></i> Agendamentos</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="manage_users.php"><i class="fas fa-users"></i> Usuários</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="reports.php"><i class="fas fa-chart-bar"></i> Relatórios</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="send_emails.php"><i class="fas fa-envelope"></i> Enviar E-mails</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="logout.php"><i class="fas fa-sign-out-alt"></i> Sair</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Conteúdo Principal -->
    <div class="container main-content">
        <div class="page-title text-center">
            <h2>Enviar E-mails</h2>
            <p class="text-muted">Envie mensagens para usuários e assistências.</p>
        </div>

        <!-- Exibir Mensagens -->
        <?php if ($mensagem_status) echo $mensagem_status; ?>

        <!-- Formulário de Envio de E-mails -->
        <div class="card mb-4">
            <div class="card-body">
                <form action="send_emails.php" method="POST">
                    <div class="mb-3">
                        <label class="form-label">Selecionar Destinatários:</label>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="destinatario_tipo" id="todos_usuarios" value="todos_usuarios" required>
                            <label class="form-check-label" for="todos_usuarios">
                                Todos os Usuários
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="destinatario_tipo" id="todas_assistencias" value="todas_assistencias" required>
                            <label class="form-check-label" for="todas_assistencias">
                                Todas as Assistências
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="destinatario_tipo" id="especificos" value="especificos" required>
                            <label class="form-check-label" for="especificos">
                                Destinatários Específicos
                            </label>
                        </div>
                    </div>

                    <!-- Se "Destinatários Específicos" estiver selecionado, exibir seleção de usuários e assistências -->
                    <div id="selecionar_especificos" style="display: none;">
                        <div class="mb-3">
                            <label class="form-label">Selecionar Usuários:</label>
                            <?php if (!empty($usuarios)): ?>
                                <?php foreach ($usuarios as $usuario): ?>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="destinatarios_especificos[]" value="u_<?php echo $usuario['id']; ?>" id="usuario_<?php echo $usuario['id']; ?>">
                                        <label class="form-check-label" for="usuario_<?php echo $usuario['id']; ?>">
                                            <?php echo htmlspecialchars($usuario['nome']) . ' (' . htmlspecialchars($usuario['email']) . ')'; ?>
                                        </label>
                                    </div>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <p class="text-muted">Nenhum usuário encontrado.</p>
                            <?php endif; ?>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Selecionar Assistências:</label>
                            <?php if (!empty($assistencias)): ?>
                                <?php foreach ($assistencias as $assistencia): ?>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="destinatarios_especificos[]" value="a_<?php echo $assistencia['id']; ?>" id="assistencia_<?php echo $assistencia['id']; ?>">
                                        <label class="form-check-label" for="assistencia_<?php echo $assistencia['id']; ?>">
                                            <?php echo htmlspecialchars($assistencia['nome']) . ' (' . htmlspecialchars($assistencia['email']) . ')'; ?>
                                        </label>
                                    </div>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <p class="text-muted">Nenhuma assistência encontrada.</p>
                            <?php endif; ?>
                        </div>
                    </div>

                    <hr>

                    <div class="mb-3">
                        <label for="assunto" class="form-label">Assunto:</label>
                        <input type="text" name="assunto" id="assunto" class="form-control" required>
                    </div>

                    <div class="mb-3">
                        <label for="mensagem" class="form-label">Mensagem:</label>
                        <textarea name="mensagem" id="mensagem" class="form-control" rows="6" required></textarea>
                    </div>

                    <button type="submit" class="btn btn-primary"><i class="fas fa-paper-plane"></i> Enviar E-mails</button>
                </form>
            </div>
        </div>
    </div>

    <!-- Rodapé -->
    <footer class="footer">
        <div class="container text-center">
            <span>&copy; <?php echo date("Y"); ?> FixFácil. Todos os direitos reservados.</span>
        </div>
    </footer>

    <!-- Incluindo Bootstrap JS e dependências -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Script para mostrar/ocultar seleção específica de destinatários -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const destinatarioRadios = document.getElementsByName('destinatario_tipo');
            const selecionarEspecificos = document.getElementById('selecionar_especificos');

            destinatarioRadios.forEach(radio => {
                radio.addEventListener('change', function() {
                    if (this.value === 'especificos') {
                        selecionarEspecificos.style.display = 'block';
                    } else {
                        selecionarEspecificos.style.display = 'none';
                    }
                });
            });
        });
    </script>
</body>
</html>
