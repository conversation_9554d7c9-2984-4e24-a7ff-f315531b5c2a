<?php
session_start();
require_once 'db.php';

if (!isset($_SESSION['user_id']) || $_SESSION['nivel_acesso'] != 'lojista') {
    header("Location: login.php");
    exit();
}

$query = "SELECT p.*, u.nome as fornecedor_nome, pl.valor_proposta, pl.status_proposta 
          FROM propostas_leilao pl
          INNER JOIN usuarios u ON pl.fornecedor_id = u.id
          INNER JOIN produtos p ON pl.produto_id = p.id
          WHERE p.id IN (SELECT produto_id FROM compras WHERE lojista_id = :lojista_id AND status_pedido = 'análise')";
$stmt = $pdo->prepare($query);
$stmt->bindParam(':lojista_id', $_SESSION['user_id']);
$stmt->execute();
$propostasLeilao = $stmt->fetchAll(PDO::FETCH_ASSOC);

if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['confirmar_cotacao'])) {
    $proposta_id = $_POST['proposta_id'];

    try {
        // Atualizar status na tabela compras
        $query = "UPDATE compras SET status_pedido = 'confirmado' WHERE lojista_id = :lojista_id AND produto_id IN (SELECT produto_id FROM propostas_leilao WHERE id = :proposta_id) AND status_pedido = 'análise'";
        $stmt = $pdo->prepare($query);
        $stmt->bindParam(':lojista_id', $_SESSION['user_id']);
        $stmt->bindParam(':proposta_id', $proposta_id);
        $stmt->execute();

        // Atualizar status na tabela propostas_leilao
        $query = "UPDATE propostas_leilao SET status_proposta = 'confirmado' WHERE id = :proposta_id AND status_proposta = 'análise'";
        $stmt = $pdo->prepare($query);
        $stmt->bindParam(':proposta_id', $proposta_id);
        $stmt->execute();

        // Atualizar status na tabela leilao
        $query = "UPDATE leilao SET status_leilao = 'desativado' WHERE produto_id IN (SELECT produto_id FROM propostas_leilao WHERE id = :proposta_id) AND status_leilao = 'ativo'";
        $stmt = $pdo->prepare($query);
        $stmt->bindParam(':proposta_id', $proposta_id);
        $stmt->execute();

        echo '<script>alert("Cotação confirmada com sucesso!");</script>';
    } catch (PDOException $e) {
        echo "Erro ao confirmar a cotação: " . $e->getMessage();
    }
}
?>

<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="utf-8">
    <title>Propostas de Leilão</title>
</head>
<body>
    <h1>Propostas de Leilão</h1>

    <table>
        <thead>
            <tr>
                <th>Produto</th>
                <th>Fornecedor</th>
                <th>Valor Proposta</th>
                <th>Status Proposta</th>
                <th>Ação</th>
            </tr>
        </thead>
        <tbody>
            <?php foreach ($propostasLeilao as $proposta) : ?>
                <tr>
                    <td><?= $proposta['nome'] ?></td>
                    <td><?= $proposta['fornecedor_nome'] ?></td>
                    <td>R$ <?= $proposta['valor_proposta'] ?></td>
                    <td><?= $proposta['status_proposta'] ?></td>
                    <td>
                        <form method="post" action="">
                            <input type="hidden" name="proposta_id" value="<?= $proposta['id'] ?>">
                            <button type="submit" name="confirmar_cotacao">Confirmar Cotação</button>
                        </form>
                    </td>
                </tr>
            <?php endforeach; ?>
        </tbody>
    </table>
</body>
</html>
