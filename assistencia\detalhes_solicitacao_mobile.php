<?php
/**
 * Detalhes da Solicitação - Mobile Layout
 * FixFácil Assistências - Sistema Novo
 */

// Redirecionar para versão principal
header('Location: detalhes_solicitacao.php?' . $_SERVER['QUERY_STRING']);
exit();
?>

if (!$solicitacao_id) {
    header('Location: solicitacoes.php');
    exit();
}

// Obter dados da solicitação
$solicitacao = null;
$propostas = [];

try {
    // Dados da solicitação
    $sql = "
        SELECT 
            sr.*,
            u.nome as cliente_nome,
            u.telefone as cliente_telefone,
            u.endereco as cliente_endereco,
            u.email as cliente_email
        FROM solicitacoes_reparo sr
        JOIN usuarios u ON sr.usuario_id = u.id
        WHERE sr.id = ?
    ";
    
    $result = $db->query($sql, [$solicitacao_id]);
    $solicitacao = $result->fetch_assoc();
    
    if (!$solicitacao) {
        header('Location: solicitacoes.php');
        exit();
    }
    
    // Propostas para esta solicitação
    $sql = "
        SELECT 
            pa.*,
            at.nome_empresa,
            u.nome as assistencia_nome
        FROM propostas_assistencia pa
        JOIN assistencias_tecnicas at ON pa.assistencia_id = at.id
        JOIN usuarios u ON at.usuario_id = u.id
        WHERE pa.solicitacao_id = ?
        ORDER BY pa.data_proposta DESC
    ";
    
    $result = $db->query($sql, [$solicitacao_id]);
    while ($row = $result->fetch_assoc()) {
        $propostas[] = $row;
    }
    
} catch (Exception $e) {
    error_log("Erro ao obter detalhes da solicitação: " . $e->getMessage());
    header('Location: solicitacoes.php');
    exit();
}

// Verificar se já enviei proposta
$minha_proposta = null;
foreach ($propostas as $proposta) {
    if ($proposta['assistencia_id'] == $usuario['assistencia_id']) {
        $minha_proposta = $proposta;
        break;
    }
}

// Dados da empresa
$empresa_nome = $usuario['nome'] ?? 'Assistência Técnica';
$empresa_logo = strtoupper(substr($empresa_nome, 0, 2));
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Detalhes da Solicitação - FixFácil</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            min-height: 100vh;
            color: #1e293b;
        }

        .container {
            max-width: 414px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
            position: relative;
            overflow-x: hidden;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }

        .header {
            background: linear-gradient(135deg, #059669 0%, #065f46 100%);
            color: white;
            padding: 24px 20px 20px 20px;
            position: relative;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 20"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="0.5" fill="white" opacity="0.05"/></pattern></defs><rect width="100" height="20" fill="url(%23grain)"/></svg>');
        }

        .header-content {
            position: relative;
            z-index: 1;
        }

        .header-top {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 16px;
        }

        .back-btn {
            background: rgba(255,255,255,0.15);
            border: none;
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 12px;
            font-size: 18px;
            cursor: pointer;
            backdrop-filter: blur(10px);
            transition: all 0.2s ease;
            text-decoration: none;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .back-btn:hover {
            background: rgba(255,255,255,0.25);
            color: white;
        }

        .header-actions {
            display: flex;
            gap: 8px;
        }

        .action-btn {
            background: rgba(255,255,255,0.15);
            border: none;
            color: white;
            padding: 8px 16px;
            border-radius: 12px;
            font-size: 14px;
            cursor: pointer;
            backdrop-filter: blur(10px);
            transition: all 0.2s ease;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .action-btn:hover {
            background: rgba(255,255,255,0.25);
            color: white;
        }

        .solicitation-info {
            text-align: center;
        }

        .solicitation-title {
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 4px;
        }

        .solicitation-id {
            font-size: 14px;
            opacity: 0.8;
        }

        .content {
            padding: 20px;
            padding-bottom: 100px;
        }

        .card {
            background: white;
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
        }

        .card-title {
            font-size: 18px;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
            margin-bottom: 16px;
        }

        .info-item {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .info-label {
            font-size: 12px;
            color: #64748b;
            font-weight: 500;
        }

        .info-value {
            font-size: 16px;
            font-weight: 600;
            color: #1e293b;
        }

        .description-text {
            font-size: 14px;
            color: #64748b;
            line-height: 1.5;
            margin-bottom: 16px;
        }

        .tags-container {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-bottom: 16px;
        }

        .tag {
            background: #f1f5f9;
            color: #64748b;
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 500;
        }

        .tag.device {
            background: #eff6ff;
            color: #3b82f6;
        }

        .tag.status {
            background: #fef3c7;
            color: #d97706;
        }

        .tag.urgent {
            background: #fef2f2;
            color: #ef4444;
        }

        .proposals-list {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .proposal-item {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            padding: 16px;
            transition: all 0.2s ease;
        }

        .proposal-item.mine {
            background: #f0fdf4;
            border-color: #059669;
        }

        .proposal-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 8px;
        }

        .proposal-company {
            font-size: 16px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 4px;
        }

        .proposal-badge {
            background: #059669;
            color: white;
            padding: 2px 8px;
            border-radius: 6px;
            font-size: 10px;
            font-weight: 600;
            margin-left: 8px;
        }

        .proposal-details {
            display: flex;
            gap: 16px;
            margin-bottom: 8px;
        }

        .proposal-price {
            font-size: 18px;
            font-weight: 700;
            color: #059669;
        }

        .proposal-deadline {
            font-size: 14px;
            color: #64748b;
        }

        .proposal-notes {
            font-size: 14px;
            color: #64748b;
            line-height: 1.4;
            margin-top: 8px;
        }

        .proposal-time {
            font-size: 12px;
            color: #94a3b8;
            margin-top: 8px;
        }

        .client-info {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .client-contact {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 0;
            border-bottom: 1px solid #f1f5f9;
        }

        .client-contact:last-child {
            border-bottom: none;
        }

        .contact-icon {
            width: 32px;
            height: 32px;
            background: #f0fdf4;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #059669;
            font-size: 14px;
        }

        .contact-info {
            flex: 1;
        }

        .contact-label {
            font-size: 12px;
            color: #64748b;
            margin-bottom: 2px;
        }

        .contact-value {
            font-size: 14px;
            font-weight: 600;
            color: #1e293b;
        }

        .contact-link {
            color: #059669;
            text-decoration: none;
        }

        .contact-link:hover {
            text-decoration: underline;
        }

        .video-btn {
            background: #059669;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 12px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .video-btn:hover {
            background: #065f46;
        }

        .fab {
            position: fixed;
            bottom: 90px;
            right: 20px;
            width: 56px;
            height: 56px;
            background: linear-gradient(135deg, #059669, #065f46);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            border: none;
            cursor: pointer;
            box-shadow: 0 4px 16px rgba(5, 150, 105, 0.4);
            transition: all 0.3s ease;
            z-index: 100;
        }

        .fab:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 20px rgba(5, 150, 105, 0.5);
        }

        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 100%;
            max-width: 414px;
            background: white;
            border-top: 1px solid #e2e8f0;
            padding: 12px 20px;
            display: flex;
            justify-content: space-around;
            box-shadow: 0 -4px 20px rgba(0,0,0,0.1);
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
            cursor: pointer;
            padding: 8px;
            border-radius: 12px;
            transition: all 0.2s ease;
            position: relative;
            text-decoration: none;
            color: #64748b;
        }

        .nav-item:hover {
            color: #059669;
            text-decoration: none;
        }

        .nav-icon {
            font-size: 20px;
        }

        .nav-label {
            font-size: 10px;
            font-weight: 500;
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 1000;
        }

        .modal.show {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-content {
            background: white;
            border-radius: 16px;
            width: 90%;
            max-width: 500px;
            max-height: 80vh;
            overflow-y: auto;
            position: relative;
        }

        .modal-header {
            background: #059669;
            color: white;
            padding: 20px;
            border-radius: 16px 16px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-title {
            font-size: 18px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .modal-close {
            background: none;
            border: none;
            color: white;
            font-size: 24px;
            cursor: pointer;
        }

        .modal-body {
            padding: 20px;
        }

        .modal-footer {
            padding: 20px;
            border-top: 1px solid #e2e8f0;
            display: flex;
            gap: 12px;
            justify-content: flex-end;
        }

        .btn {
            padding: 12px 20px;
            border-radius: 12px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            border: none;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: #059669;
            color: white;
        }

        .btn-primary:hover {
            background: #065f46;
        }

        .btn-secondary {
            background: #f1f5f9;
            color: #64748b;
        }

        .btn-secondary:hover {
            background: #e2e8f0;
        }

        .form-group {
            margin-bottom: 16px;
        }

        .form-label {
            font-size: 14px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 8px;
            display: block;
        }

        .form-input {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            font-size: 14px;
            background: white;
            transition: border-color 0.2s ease;
        }

        .form-input:focus {
            outline: none;
            border-color: #059669;
        }

        .form-textarea {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            font-size: 14px;
            background: white;
            min-height: 100px;
            resize: vertical;
            transition: border-color 0.2s ease;
        }

        .form-textarea:focus {
            outline: none;
            border-color: #059669;
        }

        .form-select {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            font-size: 14px;
            background: white;
            transition: border-color 0.2s ease;
        }

        .form-select:focus {
            outline: none;
            border-color: #059669;
        }

        .form-checkbox {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-top: 16px;
        }

        .form-checkbox input {
            width: 20px;
            height: 20px;
        }

        .summary-card {
            background: #f0fdf4;
            border: 1px solid #bbf7d0;
            border-radius: 12px;
            padding: 16px;
            margin-top: 16px;
        }

        .summary-title {
            font-size: 16px;
            font-weight: 600;
            color: #059669;
            margin-bottom: 12px;
        }

        .summary-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
            margin-bottom: 12px;
        }

        .summary-item {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .summary-label {
            font-size: 12px;
            color: #065f46;
        }

        .summary-value {
            font-size: 16px;
            font-weight: 600;
            color: #1e293b;
        }

        .summary-total {
            border-top: 1px solid #bbf7d0;
            padding-top: 12px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .summary-total-label {
            font-size: 14px;
            color: #065f46;
        }

        .summary-total-value {
            font-size: 18px;
            font-weight: 700;
            color: #059669;
        }

        .no-proposals {
            text-align: center;
            padding: 40px 20px;
            color: #64748b;
        }

        .no-proposals-icon {
            font-size: 48px;
            margin-bottom: 16px;
            opacity: 0.5;
        }

        @media (max-width: 480px) {
            .container {
                max-width: 100%;
            }
            
            .header {
                padding: 20px 15px 16px 15px;
            }
            
            .content {
                padding: 15px;
                padding-bottom: 100px;
            }
            
            .card {
                padding: 16px;
            }
            
            .info-grid {
                grid-template-columns: 1fr;
                gap: 12px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="header-content">
                <div class="header-top">
                    <a href="solicitacoes.php" class="back-btn">←</a>
                    <div class="header-actions">
                        <?php if ($solicitacao['status'] === 'enviado' && !$minha_proposta): ?>
                        <button class="action-btn" onclick="abrirModalProposta()">
                            ✈️ Enviar Proposta
                        </button>
                        <?php endif; ?>
                    </div>
                </div>
                <div class="solicitation-info">
                    <div class="solicitation-title"><?php echo htmlspecialchars($solicitacao['marca'] . ' ' . $solicitacao['modelo']); ?></div>
                    <div class="solicitation-id">Solicitação #<?php echo $solicitacao['id']; ?></div>
                </div>
            </div>
        </div>

        <!-- Content -->
        <div class="content">
            <!-- Informações do Dispositivo -->
            <div class="card">
                <div class="card-title">📱 Informações do Dispositivo</div>
                <div class="info-grid">
                    <div class="info-item">
                        <div class="info-label">Dispositivo</div>
                        <div class="info-value"><?php echo htmlspecialchars($solicitacao['dispositivo']); ?></div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Marca</div>
                        <div class="info-value"><?php echo htmlspecialchars($solicitacao['marca']); ?></div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Modelo</div>
                        <div class="info-value"><?php echo htmlspecialchars($solicitacao['modelo']); ?></div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Memória</div>
                        <div class="info-value"><?php echo htmlspecialchars($solicitacao['memoria']); ?></div>
                    </div>
                </div>
            </div>

            <!-- Descrição do Problema -->
            <div class="card">
                <div class="card-title">⚠️ Descrição do Problema</div>
                <div class="description-text">
                    <?php echo nl2br(htmlspecialchars($solicitacao['descricao_problema'])); ?>
                </div>
                
                <?php if (!empty($solicitacao['descricao_detalhada'])): ?>
                <div class="description-text">
                    <strong>Detalhes:</strong><br>
                    <?php echo nl2br(htmlspecialchars($solicitacao['descricao_detalhada'])); ?>
                </div>
                <?php endif; ?>
                
                <?php if (!empty($solicitacao['verificacoes'])): ?>
                <div class="tags-container">
                    <?php 
                    $verificacoes = explode(',', $solicitacao['verificacoes']);
                    $verificacoes_labels = [
                        'conector_carga' => 'Conector de Carga',
                        'entrada_fone' => 'Entrada de Fone',
                        'riscos' => 'Riscos na Tela',
                        'cameras' => 'Câmeras',
                        'wifi' => 'Wi-Fi',
                        'bluetooth' => 'Bluetooth',
                        'nao_possivel_verificar' => 'Não foi possível verificar'
                    ];
                    
                    foreach ($verificacoes as $verificacao) {
                        $verificacao = trim($verificacao);
                        if (isset($verificacoes_labels[$verificacao])) {
                            echo '<span class="tag device">' . $verificacoes_labels[$verificacao] . '</span>';
                        }
                    }
                    ?>
                </div>
                <?php endif; ?>
                
                <?php if (!empty($solicitacao['video'])): ?>
                <button class="video-btn" onclick="verVideo('<?php echo htmlspecialchars($solicitacao['video']); ?>')">
                    🎥 Assistir Vídeo
                </button>
                <?php endif; ?>
            </div>

            <!-- Informações do Cliente -->
            <div class="card">
                <div class="card-title">👤 Informações do Cliente</div>
                <div class="client-info">
                    <div class="client-contact">
                        <div class="contact-icon">👤</div>
                        <div class="contact-info">
                            <div class="contact-label">Nome</div>
                            <div class="contact-value"><?php echo htmlspecialchars($solicitacao['cliente_nome']); ?></div>
                        </div>
                    </div>
                    <div class="client-contact">
                        <div class="contact-icon">📞</div>
                        <div class="contact-info">
                            <div class="contact-label">Telefone</div>
                            <div class="contact-value">
                                <a href="tel:<?php echo htmlspecialchars($solicitacao['cliente_telefone']); ?>" class="contact-link">
                                    <?php echo htmlspecialchars($solicitacao['cliente_telefone']); ?>
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="client-contact">
                        <div class="contact-icon">✉️</div>
                        <div class="contact-info">
                            <div class="contact-label">E-mail</div>
                            <div class="contact-value">
                                <a href="mailto:<?php echo htmlspecialchars($solicitacao['cliente_email']); ?>" class="contact-link">
                                    <?php echo htmlspecialchars($solicitacao['cliente_email']); ?>
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="client-contact">
                        <div class="contact-icon">📍</div>
                        <div class="contact-info">
                            <div class="contact-label">Endereço</div>
                            <div class="contact-value"><?php echo htmlspecialchars($solicitacao['cliente_endereco']); ?></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Propostas -->
            <div class="card">
                <div class="card-title">✈️ Propostas Enviadas (<?php echo count($propostas); ?>)</div>
                
                <?php if (empty($propostas)): ?>
                    <div class="no-proposals">
                        <div class="no-proposals-icon">✈️</div>
                        <p>Nenhuma proposta enviada ainda</p>
                        <p>Seja o primeiro a enviar uma proposta!</p>
                    </div>
                <?php else: ?>
                    <div class="proposals-list">
                        <?php foreach ($propostas as $proposta): ?>
                        <div class="proposal-item <?php echo $proposta['assistencia_id'] == $usuario['assistencia_id'] ? 'mine' : ''; ?>">
                            <div class="proposal-header">
                                <div class="proposal-company">
                                    <?php echo htmlspecialchars($proposta['nome_empresa']); ?>
                                    <?php if ($proposta['assistencia_id'] == $usuario['assistencia_id']): ?>
                                        <span class="proposal-badge">Sua Proposta</span>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <div class="proposal-details">
                                <div class="proposal-price">R$ <?php echo number_format($proposta['preco'], 2, ',', '.'); ?></div>
                                <div class="proposal-deadline"><?php echo $proposta['prazo']; ?> dia(s)</div>
                            </div>
                            <?php if (!empty($proposta['observacoes'])): ?>
                            <div class="proposal-notes">
                                <?php echo nl2br(htmlspecialchars($proposta['observacoes'])); ?>
                            </div>
                            <?php endif; ?>
                            <div class="proposal-time">
                                Enviada em <?php echo date('d/m/Y H:i', strtotime($proposta['data_proposta'])); ?>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Informações da Solicitação -->
            <div class="card">
                <div class="card-title">ℹ️ Informações da Solicitação</div>
                <div class="info-grid">
                    <div class="info-item">
                        <div class="info-label">Status</div>
                        <div class="info-value">
                            <?php
                            $status_class = [
                                'enviado' => 'status',
                                'aceita' => 'device',
                                'rejeitada' => 'urgent',
                                'concluido' => 'device'
                            ];
                            $status_text = [
                                'enviado' => 'Pendente',
                                'aceita' => 'Aceita',
                                'rejeitada' => 'Rejeitada',
                                'concluido' => 'Concluída'
                            ];
                            ?>
                            <span class="tag <?php echo $status_class[$solicitacao['status']]; ?>">
                                <?php echo $status_text[$solicitacao['status']]; ?>
                            </span>
                        </div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Data da Solicitação</div>
                        <div class="info-value"><?php echo date('d/m/Y H:i', strtotime($solicitacao['data_solicitacao'])); ?></div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Método de Entrega</div>
                        <div class="info-value"><?php echo htmlspecialchars($solicitacao['metodo_entrega']); ?></div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Termos</div>
                        <div class="info-value">
                            <?php if ($solicitacao['termos_concordo']): ?>
                                <span class="tag device">✅ Aceitos</span>
                            <?php else: ?>
                                <span class="tag urgent">❌ Não aceitos</span>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Bottom Navigation -->
        <div class="bottom-nav">
            <a href="dashboard.php" class="nav-item">
                <div class="nav-icon">🏠</div>
                <div class="nav-label">Início</div>
            </a>
            <a href="solicitacoes.php" class="nav-item">
                <div class="nav-icon">📋</div>
                <div class="nav-label">Solicitações</div>
            </a>
            <a href="reparos.php" class="nav-item">
                <div class="nav-icon">🔧</div>
                <div class="nav-label">Reparos</div>
            </a>
            <a href="marketplace.php" class="nav-item">
                <div class="nav-icon">🛒</div>
                <div class="nav-label">Loja</div>
            </a>
            <a href="perfil.php" class="nav-item">
                <div class="nav-icon">👤</div>
                <div class="nav-label">Perfil</div>
            </a>
        </div>

        <!-- FAB -->
        <?php if ($solicitacao['status'] === 'enviado' && !$minha_proposta): ?>
        <button class="fab" onclick="abrirModalProposta()">
            ✈️
        </button>
        <?php endif; ?>
    </div>

    <!-- Modal para enviar proposta -->
    <div class="modal" id="propostaModal">
        <div class="modal-content">
            <div class="modal-header">
                <div class="modal-title">
                    ✈️ Enviar Proposta
                </div>
                <button class="modal-close" onclick="fecharModal()">&times;</button>
            </div>
            <div class="modal-body">
                <!-- Informações da solicitação -->
                <div class="card">
                    <div class="card-title">📱 <?php echo htmlspecialchars($solicitacao['marca'] . ' ' . $solicitacao['modelo']); ?></div>
                    <div class="info-grid">
                        <div class="info-item">
                            <div class="info-label">Cliente</div>
                            <div class="info-value"><?php echo htmlspecialchars($solicitacao['cliente_nome']); ?></div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Problema</div>
                            <div class="info-value"><?php echo htmlspecialchars(substr($solicitacao['descricao_problema'], 0, 50)); ?>...</div>
                        </div>
                    </div>
                </div>

                <!-- Formulário da proposta -->
                <form id="formProposta">
                    <input type="hidden" name="solicitacao_id" value="<?php echo $solicitacao['id']; ?>">
                    
                    <div class="form-group">
                        <label class="form-label">💰 Preço do Reparo</label>
                        <input type="text" class="form-input" id="preco" name="preco" 
                               placeholder="R$ 0,00" required
                               oninput="formatarPreco(this)">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">📅 Prazo (dias)</label>
                        <select class="form-select" id="prazo" name="prazo" required>
                            <option value="">Selecione o prazo</option>
                            <option value="1">1 dia (Express)</option>
                            <option value="2">2 dias</option>
                            <option value="3">3 dias</option>
                            <option value="5">5 dias</option>
                            <option value="7">1 semana</option>
                            <option value="10">10 dias</option>
                            <option value="15">15 dias</option>
                            <option value="20">20 dias</option>
                            <option value="30">30 dias</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">💬 Observações</label>
                        <textarea class="form-textarea" id="observacoes" name="observacoes" rows="4"
                                  placeholder="Detalhes sobre o reparo, peças, garantia, etc."></textarea>
                    </div>
                    
                    <div class="form-checkbox">
                        <input type="checkbox" id="retirada_expressa" name="retirada_expressa">
                        <label for="retirada_expressa">🏍️ Retirada Express (buscar e entregar)</label>
                    </div>
                    
                    <!-- Resumo da proposta -->
                    <div class="summary-card">
                        <div class="summary-title">📊 Resumo da Proposta</div>
                        <div class="summary-grid">
                            <div class="summary-item">
                                <div class="summary-label">Valor do Reparo</div>
                                <div class="summary-value" id="resumoPreco">R$ 0,00</div>
                            </div>
                            <div class="summary-item">
                                <div class="summary-label">Prazo</div>
                                <div class="summary-value" id="resumoPrazo">-</div>
                            </div>
                            <div class="summary-item">
                                <div class="summary-label">Taxa (15%)</div>
                                <div class="summary-value" id="resumoTaxa">R$ 0,00</div>
                            </div>
                            <div class="summary-item">
                                <div class="summary-label">Você recebe</div>
                                <div class="summary-value" id="resumoRecebera">R$ 0,00</div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="fecharModal()">
                    ❌ Cancelar
                </button>
                <button class="btn btn-primary" onclick="enviarProposta()" id="btnEnviarProposta">
                    ✈️ Enviar Proposta
                </button>
            </div>
        </div>
    </div>

    <!-- Modal para vídeo -->
    <div class="modal" id="videoModal">
        <div class="modal-content">
            <div class="modal-header">
                <div class="modal-title">🎥 Vídeo do Problema</div>
                <button class="modal-close" onclick="fecharVideoModal()">&times;</button>
            </div>
            <div class="modal-body">
                <video id="videoPlayer" class="w-100" controls style="width: 100%; border-radius: 12px;">
                    Seu navegador não suporta vídeos.
                </video>
            </div>
        </div>
    </div>

    <script>
        function verVideo(videoUrl) {
            const modal = document.getElementById('videoModal');
            const video = document.getElementById('videoPlayer');
            video.src = videoUrl;
            modal.classList.add('show');
        }

        function fecharVideoModal() {
            const modal = document.getElementById('videoModal');
            const video = document.getElementById('videoPlayer');
            modal.classList.remove('show');
            video.pause();
            video.currentTime = 0;
        }

        function abrirModalProposta() {
            const modal = document.getElementById('propostaModal');
            modal.classList.add('show');
            
            // Resetar formulário
            document.getElementById('formProposta').reset();
            atualizarResumo();
        }

        function fecharModal() {
            const modal = document.getElementById('propostaModal');
            modal.classList.remove('show');
        }

        function formatarPreco(input) {
            let valor = input.value.replace(/\D/g, '');
            
            if (valor.length === 0) {
                input.value = '';
                atualizarResumo();
                return;
            }
            
            valor = valor.padStart(3, '0');
            valor = valor.slice(0, -2) + ',' + valor.slice(-2);
            valor = valor.replace(/\B(?=(\d{3})+(?!\d))/g, '.');
            
            input.value = valor;
            atualizarResumo();
        }

        function atualizarResumo() {
            const precoInput = document.getElementById('preco').value;
            const prazoSelect = document.getElementById('prazo');
            
            // Converter preço para número
            let preco = 0;
            if (precoInput) {
                preco = parseFloat(precoInput.replace(/\./g, '').replace(',', '.')) || 0;
            }
            
            // Calcular valores
            const taxa = preco * 0.15;
            const recebera = preco - taxa;
            
            // Atualizar resumo
            document.getElementById('resumoPreco').textContent = 'R$ ' + preco.toLocaleString('pt-BR', {minimumFractionDigits: 2});
            document.getElementById('resumoTaxa').textContent = 'R$ ' + taxa.toLocaleString('pt-BR', {minimumFractionDigits: 2});
            document.getElementById('resumoRecebera').textContent = 'R$ ' + recebera.toLocaleString('pt-BR', {minimumFractionDigits: 2});
            
            // Atualizar prazo
            const prazoTexto = prazoSelect.value ? prazoSelect.options[prazoSelect.selectedIndex].text : '-';
            document.getElementById('resumoPrazo').textContent = prazoTexto;
        }

        function enviarProposta() {
            const form = document.getElementById('formProposta');
            const btnEnviar = document.getElementById('btnEnviarProposta');
            
            // Validar campos obrigatórios
            const preco = document.getElementById('preco').value;
            const prazo = document.getElementById('prazo').value;
            
            if (!preco || !prazo) {
                alert('Por favor, preencha o preço e o prazo.');
                return;
            }
            
            // Desabilitar botão durante envio
            btnEnviar.disabled = true;
            btnEnviar.innerHTML = '⏳ Enviando...';
            
            // Preparar dados do formulário
            const formData = new FormData(form);
            
            // Enviar via AJAX
            fetch('ajax/enviar_proposta.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Fechar modal
                    fecharModal();
                    
                    // Mostrar sucesso
                    alert('✅ Proposta enviada com sucesso!');
                    
                    // Recarregar página para mostrar a nova proposta
                    window.location.reload();
                } else {
                    alert('❌ Erro: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Erro:', error);
                alert('❌ Erro ao enviar proposta. Tente novamente.');
            })
            .finally(() => {
                // Reabilitar botão
                btnEnviar.disabled = false;
                btnEnviar.innerHTML = '✈️ Enviar Proposta';
            });
        }

        // Event listeners
        document.addEventListener('DOMContentLoaded', function() {
            // Listener para atualizar resumo quando prazo mudar
            document.getElementById('prazo').addEventListener('change', atualizarResumo);
            
            // Listener para fechar modal clicando fora
            document.getElementById('propostaModal').addEventListener('click', function(e) {
                if (e.target === this) {
                    fecharModal();
                }
            });
            
            document.getElementById('videoModal').addEventListener('click', function(e) {
                if (e.target === this) {
                    fecharVideoModal();
                }
            });
            
            // Inicializar resumo
            atualizarResumo();
        });
    </script>
</body>
</html>
