<?php
/**
 * AJAX - Limpar Cache do Sistema
 * FixFácil Assistências - Sistema Novo
 */

header('Content-Type: application/json');

require_once '../config/auth.php';

// Verificar autenticação e permissão de admin
$auth = getAuth();
try {
    $auth->checkAssistenciaAuth();
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'Não autorizado']);
    exit();
}

$usuario = $auth->getUsuarioLogado();

// Verificar se é admin
if ($usuario['email'] !== '<EMAIL>') {
    echo json_encode(['success' => false, 'message' => 'Acesso negado']);
    exit();
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Método não permitido']);
    exit();
}

try {
    $arquivos_removidos = 0;
    
    // Limpar cache de sessões PHP
    if (function_exists('opcache_reset')) {
        opcache_reset();
    }
    
    // Limpar arquivos de cache temporários
    $cache_dirs = [
        __DIR__ . '/../cache/',
        __DIR__ . '/../temp/',
        sys_get_temp_dir() . '/fixfacil_cache/'
    ];
    
    foreach ($cache_dirs as $dir) {
        if (is_dir($dir)) {
            $files = glob($dir . '*');
            foreach ($files as $file) {
                if (is_file($file) && filemtime($file) < time() - 3600) { // Arquivos com mais de 1 hora
                    unlink($file);
                    $arquivos_removidos++;
                }
            }
        }
    }
    
    // Limpar cache de imagens (se existir)
    $image_cache = __DIR__ . '/../uploads/cache/';
    if (is_dir($image_cache)) {
        $files = glob($image_cache . '*');
        foreach ($files as $file) {
            if (is_file($file)) {
                unlink($file);
                $arquivos_removidos++;
            }
        }
    }
    
    echo json_encode([
        'success' => true,
        'message' => "Cache limpo! $arquivos_removidos arquivos removidos."
    ]);
    
} catch (Exception $e) {
    error_log("Erro ao limpar cache: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Erro interno do servidor']);
}
?>
