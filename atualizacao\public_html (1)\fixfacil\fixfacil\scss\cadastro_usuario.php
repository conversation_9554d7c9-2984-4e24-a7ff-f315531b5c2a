<?php
session_start();
require_once 'db.php';

use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\Exception;

require 'PHPMailer-master/src/Exception.php';
require 'PHPMailer-master/src/PHPMailer.php';
require 'PHPMailer-master/src/SMTP.php';

ob_start(); // Inicia o buffer de saída

// Inicialização de variáveis
$mensagem = $mensagem_email = '';

// Lista de bairros permitidos
$bairros_permitidos = [
    'Abranches', 'Água Verde', '<PERSON><PERSON>', 'Alto Boqueirão', 'Alto da Glória', 'Alto da XV', 'Atuba', 'Augusta',
    'Bacacheri', 'Bairro Alto', 'Barreirinha', 'Batel', 'Boa Vista', 'Bom Retiro', 'Boqueirão', 'Butiatuvinha',
    '<PERSON>abral', 'Cachoeira', 'Cajuru', 'Campina do Siqueira', 'Campo Comprido', 'Campo de Santana', '<PERSON><PERSON> da Imbuia',
    'Capão Raso', 'Cascatinha', 'Centro', 'Centro Histórico', 'Caximba', 'Centro Cívico', 'Champagnat',
    'Cidade Industrial', 'Cristo Rei', 'Fanny', 'Fazendinha', 'Ganchinho', 'Guabirotuba', 'Guaíra', 'Hauer',
    'Hugo Lange', 'Jardim Botânico', 'Jardim Social', 'Jardim das Américas', 'Juvevê', 'Lamenha Pequena', 'Lindóia',
    'Mercês', 'Mossunguê (Ecoville)', 'Novo Mundo', 'Orleans', 'Parolin', 'Pilarzinho', 'Pinheirinho', 'Portão',
    'Prado Velho', 'Rebouças', 'Riviera', 'Santa Cândida', 'Santa Felicidade', 'Santa Quitéria', 'Santo Inácio',
    'São Braz', 'São Francisco', 'São João', 'São Lourenço', 'São Miguel', 'Seminário', 'Sítio Cercado',
    'Taboão', 'Tarumã', 'Tatuquara', 'Tingui', 'Uberaba', 'Umbará', 'Vila Izabel', 'Vista Alegre', 'Xaxim'
];

// Processamento do formulário
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    if(isset($_POST['nome'], $_POST['email'], $_POST['senha'], $_POST['nivel_acesso'], $_POST['bairro'])) {
        $nome = $_POST['nome'];
        $email = $_POST['email'];
        $senha = $_POST['senha'];
        $nivel_acesso = $_POST['nivel_acesso'];
        $bairro = $_POST['bairro'];

        if (!in_array($bairro, $bairros_permitidos)) {
            $mensagem = "Bairro selecionado não é válido.";
        } else {
            try {
                // Inserção no banco de dados
                $query = "INSERT INTO usuarios (nome, email, senha, nivel_acesso, bairro) VALUES (:nome, :email, :senha, :nivel_acesso, :bairro)";
                $stmt = $pdo->prepare($query);
                $stmt->bindParam(':nome', $nome);
                $stmt->bindParam(':email', $email);
                $stmt->bindParam(':senha', $senha);
                $stmt->bindParam(':nivel_acesso', $nivel_acesso);
                $stmt->bindParam(':bairro', $bairro);
                $stmt->execute();
                $mensagem = "Usuário cadastrado com sucesso!";

                // Envio de email
                $mail = new PHPMailer(true);
                $mail->isSMTP();
                $mail->Host = 'email-ssl.com.br';
                $mail->SMTPAuth = true;
                $mail->Username = '<EMAIL>';
                $mail->Password = 'Sup@202323';
                $mail->SMTPSecure = 'ssl';
                $mail->Port = 465;

                $mail->setFrom('<EMAIL>', 'Seu Nome');
                $mail->addAddress('<EMAIL>');

                $mail->isHTML(true);
                $mail->Subject = 'Novo usuário cadastrado';
                $mail->Body = 'Novo usuário cadastrado: <br> Nome: ' . $nome . '<br> Email: ' . $email . '<br> Nível de Acesso: ' . $nivel_acesso . '<br> Bairro: ' . $bairro;

                $mail->send();
                $mensagem_email = "Bem-vindo à AVOS BRASIL!";
                
                header("Location: https://mpago.la/25oXn5n");
                exit;
                
            } catch (PDOException $e) {
                $mensagem = "Erro ao cadastrar usuário: " . $e->getMessage();
            } catch (Exception $e) {
                $mensagem_email = "Erro ao enviar e-mail: " . $e->getMessage();
            }
        }
    } else {
        $mensagem = "Por favor, preencha todos os campos obrigatórios.";
    }
}
ob_end_flush(); // Libera o buffer de saída
?>

<!DOCTYPE html>
<html lang="en">

<head>

    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="description" content="">
    <meta name="author" content="">

    <title>Avos Brasil - Register</title>

    <!-- Custom fonts for this template-->
    <link href="vendor/fontawesome-free/css/all.min.css" rel="stylesheet" type="text/css">
    <link
        href="https://fonts.googleapis.com/css?family=Nunito:200,200i,300,300i,400,400i,600,600i,700,700i,800,800i,900,900i"
        rel="stylesheet">

    <!-- Custom styles for this template-->
    <link href="css/sb-admin-2.min.css" rel="stylesheet">

</head>

<body class="bg-gradient-primary">

    <div class="container">

        <div class="card o-hidden border-0 shadow-lg my-5">
            <div class="card-body p-0">
                <!-- Nested Row within Card Body -->
                <div class="row">
                    <div class="col-lg-5 d-none d-lg-block bg-register-image"></div>
                    <div class="col-lg-7">
                        <div class="p-5">
                            <div class="text-center">
                                <h1 class="h4 text-gray-900 mb-4">Create an Account!</h1>
                            </div>
                            <div class="container">
    <h2 class="mt-5">Cadastro de Usuário</h2>
    <form method="post" action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>" class="mt-4">
        <div class="form-group">
            <label for="nome">Nome:</label>
            <input type="text" name="nome" class="form-control" required>
        </div>
        <div class="form-group">
            <label for="email">Email:</label>
            <input type="email" name="email" class="form-control" required>
        </div>
        <div class="form-group">
            <label for="senha">Senha:</label>
            <input type="password" name="senha" class="form-control" required>
        </div>
        <div class="form-group">
            <label for="nivel_acesso">Nível de Acesso:</label>
            <select name="nivel_acesso" class="form-control" required>
                <option value="fornecedor">Distribuidor</option>
                <option value="lojista">Lojista</option>
            </select>
        </div>
        <div class="form-group">
            <label for="bairro">Bairro:</label>
            <select name="bairro" class="form-control" required>
                <?php foreach ($bairros_permitidos as $bairro_option): ?>
                    <option value="<?php echo $bairro_option; ?>"><?php echo $bairro_option; ?></option>
                <?php endforeach; ?>
            </select>
        </div>
        <div class="form-group">
            <label for="cnpj">CNPJ:</label>
            <input type="text" name="cnpj" class="form-control" required>
        </div>
        <div class="form-group">
            <label for="endereco">Endereço:</label>
            <input type="text" name="endereco" class="form-control" required>
        </div>
        <div class="form-group">
            <label for="telefone">Telefone:</label>
            <input type="tel" name="telefone" class="form-control" required>
        </div>
        <div class="form-group">
            <label for="ramo_atuacao">Ramo de Atuação:</label>
            <input type="text" name="ramo_atuacao" class="form-control" required>
        </div>
        <button type="submit" class="btn btn-primary">Cadastrar</button>
    </form>
</div>

                            <hr>
                            <div class="text-center">
                                <a class="small" href="">Forgot Password?</a>
                            </div>
                            <div class="text-center">
                                <a class="small" href="login.php">Already have an account? Login!</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>

    <!-- Bootstrap core JavaScript-->
    <script src="vendor/jquery/jquery.min.js"></script>
    <script src="vendor/bootstrap/js/bootstrap.bundle.min.js"></script>

    <!-- Core plugin JavaScript-->
    <script src="vendor/jquery-easing/jquery.easing.min.js"></script>

    <!-- Custom scripts for all pages-->
    <script src="js/sb-admin-2.min.js"></script>

</body>

</html>
