<?php
/**
 * AJAX - Enviar Mensagem no Chat
 * FixFácil Assistências - Sistema Novo
 */

header('Content-Type: application/json');

require_once '../config/auth.php';
require_once '../config/database.php';

// Verificar autenticação
$auth = getAuth();
try {
    $auth->checkAssistenciaAuth();
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'Não autorizado']);
    exit();
}

// Verificar acesso ao chat
if (!$auth->hasAccess('chat')) {
    echo json_encode(['success' => false, 'message' => 'Acesso negado ao chat']);
    exit();
}

// Obter dados do usuário
$usuario = $auth->getUsuarioLogado();
$db = getDatabase();

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Método não permitido']);
    exit();
}

$proposta_id = $_POST['proposta_id'] ?? 0;
$mensagem = trim($_POST['mensagem'] ?? '');

// Validações
if (!$proposta_id || empty($mensagem)) {
    echo json_encode(['success' => false, 'message' => 'Dados obrigatórios não fornecidos']);
    exit();
}

if (strlen($mensagem) > 1000) {
    echo json_encode(['success' => false, 'message' => 'Mensagem muito longa (máximo 1000 caracteres)']);
    exit();
}

try {
    // Verificar se a proposta pertence à assistência e está ativa
    $sql = "
        SELECT pa.id, pa.status, sr.usuario_id as cliente_id
        FROM propostas_assistencia pa
        JOIN solicitacoes_reparo sr ON pa.solicitacao_id = sr.id
        WHERE pa.id = ? AND pa.assistencia_id = ?
        AND pa.status IN ('aceita', 'Em Andamento', 'Concluída')
    ";
    
    $result = $db->query($sql, [$proposta_id, $usuario['assistencia_id']]);
    $proposta = $result->fetch_assoc();
    
    if (!$proposta) {
        echo json_encode(['success' => false, 'message' => 'Proposta não encontrada ou inativa']);
        exit();
    }
    
    // Inserir mensagem
    $sql = "
        INSERT INTO mensagens_chat 
        (proposta_id, remetente_id, remetente_tipo, mensagem, data_envio, lida)
        VALUES (?, ?, 'assistencia', ?, NOW(), 1)
    ";
    
    $db->query($sql, [$proposta_id, $usuario['assistencia_id'], $mensagem]);
    $mensagem_id = $db->lastInsertId();
    
    // Criar notificação para o cliente
    try {
        $sql = "
            INSERT INTO notificacoes (usuario_id, tipo, titulo, mensagem, data_criacao)
            VALUES (?, 'nova_mensagem', 'Nova mensagem', 'Você recebeu uma nova mensagem da assistência técnica', NOW())
        ";
        $db->query($sql, [$proposta['cliente_id']]);
    } catch (Exception $e) {
        // Notificação opcional
    }
    
    // Registrar atividade
    try {
        $sql = "
            INSERT INTO logs_atividades (usuario_id, tipo, descricao, data_atividade)
            VALUES (?, 'chat_mensagem', ?, NOW())
        ";
        $descricao = "Mensagem enviada no chat da proposta #{$proposta_id}";
        $db->query($sql, [$usuario['id'], $descricao]);
    } catch (Exception $e) {
        // Log opcional
    }
    
    echo json_encode([
        'success' => true,
        'message' => 'Mensagem enviada com sucesso',
        'mensagem_id' => $mensagem_id,
        'data_envio' => date('Y-m-d H:i:s')
    ]);
    
} catch (Exception $e) {
    error_log("Erro ao enviar mensagem: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Erro interno do servidor']);
}
?>
