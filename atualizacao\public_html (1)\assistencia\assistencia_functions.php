<?php
// scripts/assistencia_functions.php

// Função para obter todas as solicitações pendentes
function get_solicitacoes_pendentes($conn) {
    $sql = "SELECT * FROM solicitacoes_reparo WHERE status = 'pendente' ORDER BY data_solicitacao DESC";
    $result = $conn->query($sql);
    return $result;
}

// Função para atualizar o status da solicitação
function atualizar_status_solicitacao($conn, $solicitacao_id, $novo_status) {
    $stmt = $conn->prepare("UPDATE solicitacoes_reparo SET status = ? WHERE id = ?");
    $stmt->bind_param("si", $novo_status, $solicitacao_id);
    return $stmt->execute();
}

// Função para criar uma proposta
function criar_proposta($conn, $solicitacao_id, $assistencia_id, $valor, $tempo_estimado, $detalhes) {
    $stmt = $conn->prepare("INSERT INTO propostas (solicitacao_id, assistencia_id, valor, tempo_estimado, detalhes) VALUES (?, ?, ?, ?, ?)");
    $stmt->bind_param("iisss", $solicitacao_id, $assistencia_id, $valor, $tempo_estimado, $detalhes);
    return $stmt->execute();
}

// Função para obter propostas de uma solicitação
function get_propostas_por_solicitacao($conn, $solicitacao_id) {
    $stmt = $conn->prepare("SELECT p.*, a.nome AS assistencia_nome FROM propostas p JOIN assistencias a ON p.assistencia_id = a.id WHERE p.solicitacao_id = ?");
    $stmt->bind_param("i", $solicitacao_id);
    $stmt->execute();
    return $stmt->get_result();
}
?>
