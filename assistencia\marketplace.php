<?php
/**
 * Marketplace Principal - Mobile First
 * FixFácil Assistências - Padrão Mobile
 */

// Configurar relatório de erros para debug
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Iniciar sessão se não estiver iniciada
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Verificar se o usuário está logado
if (!isset($_SESSION['usuario_id']) || $_SESSION['tipo_usuario'] !== 'assistencia') {
    header('Location: ../login.php');
    exit();
}

// Configuração de banco de dados
$host = "localhost";
$username = "u680766645_fixfacilnew";
$password = "T3cn0l0g1a@";
$database = "u680766645_fixfacilnew";

$mysqli = new mysqli($host, $username, $password, $database);

if ($mysqli->connect_error) {
    die("Erro de conexão: " . $mysqli->connect_error);
}

$mysqli->set_charset("utf8");

// Obter dados do usuário logado
$usuario_id = $_SESSION['usuario_id'];
$usuario = null;

// Buscar dados do usuário de forma simples
$sql = "SELECT u.id, u.nome, u.email, u.telefone, u.plano_id, at.id as assistencia_id
        FROM usuarios u
        LEFT JOIN assistencias_tecnicas at ON u.id = at.usuario_id
        WHERE u.id = $usuario_id";
$result = $mysqli->query($sql);
if ($result && $result->num_rows > 0) {
    $usuario = $result->fetch_assoc();
}

// Dados padrão se não encontrar
if (!$usuario) {
    $usuario = [
        'id' => $usuario_id,
        'nome' => 'Usuário',
        'email' => '',
        'assistencia_id' => 1
    ];
}

// Filtros
$categoria_filter = $_GET['categoria'] ?? 'todos';
$status_filter = $_GET['status'] ?? 'ativo';
$search = $_GET['search'] ?? '';

// Obter produtos do marketplace
$produtos = [];
if ($usuario && isset($usuario['assistencia_id'])) {
    $where_conditions = ["p.assistencia_id = " . (int)$usuario['assistencia_id']];
    
    if ($status_filter !== 'todos') {
        $where_conditions[] = "p.status = '" . $mysqli->real_escape_string($status_filter) . "'";
    }
    
    if ($categoria_filter !== 'todos') {
        $where_conditions[] = "p.categoria = '" . $mysqli->real_escape_string($categoria_filter) . "'";
    }
    
    if (!empty($search)) {
        $search_escaped = $mysqli->real_escape_string($search);
        $where_conditions[] = "(p.nome LIKE '%$search_escaped%' OR p.descricao LIKE '%$search_escaped%' OR p.marca LIKE '%$search_escaped%')";
    }
    
    $where_clause = implode(' AND ', $where_conditions);
    
    $sql = "
        SELECT 
            p.*,
            COUNT(v.id) as total_vendas,
            COALESCE(SUM(v.quantidade), 0) as quantidade_vendida
        FROM produtos_marketplace p
        LEFT JOIN vendas_marketplace v ON p.id = v.produto_id AND v.status = 'concluida'
        WHERE $where_clause
        GROUP BY p.id
        ORDER BY p.data_criacao DESC
        LIMIT 20
    ";
    
    $result = $mysqli->query($sql);
    if ($result) {
        while ($row = $result->fetch_assoc()) {
            $produtos[] = $row;
        }
    }
}

// Estatísticas do marketplace
$stats = [
    'total_produtos' => 0,
    'produtos_ativos' => 0,
    'produtos_pausados' => 0,
    'total_vendas' => 0,
    'receita_total' => 0,
    'itens_vendidos' => 0
];

if ($usuario && isset($usuario['assistencia_id'])) {
    $sql = "
        SELECT 
            COUNT(p.id) as total_produtos,
            COUNT(CASE WHEN p.status = 'ativo' THEN 1 END) as produtos_ativos,
            COUNT(CASE WHEN p.status = 'pausado' THEN 1 END) as produtos_pausados,
            COUNT(v.id) as total_vendas,
            COALESCE(SUM(v.valor_total), 0) as receita_total,
            COALESCE(SUM(v.quantidade), 0) as itens_vendidos
        FROM produtos_marketplace p
        LEFT JOIN vendas_marketplace v ON p.id = v.produto_id AND v.status = 'concluida'
        WHERE p.assistencia_id = " . (int)$usuario['assistencia_id'];
    
    $result = $mysqli->query($sql);
    if ($result && $result->num_rows > 0) {
        $stats = $result->fetch_assoc();
    }
}

// Estatísticas para o header
$stats = [
    'total_solicitacoes' => 0,
    'aguardando_resposta' => 0,
    'em_andamento' => 0
];

?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Marketplace - FixFácil Assistências</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            background: #f8fafc;
            min-height: 100vh;
            color: #1e293b;
        }

        .container {
            max-width: 414px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
            position: relative;
            overflow-x: hidden;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }

        .header {
            background: linear-gradient(135deg, #059669 0%, #065f46 100%);
            color: white;
            padding: 24px 20px 20px 20px;
            position: relative;
        }

        .header-content {
            position: relative;
            z-index: 1;
        }

        .header-top {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 20px;
        }

        .company-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .company-logo {
            width: 48px;
            height: 48px;
            background: rgba(255,255,255,0.2);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            font-weight: 700;
            backdrop-filter: blur(10px);
        }

        .company-details h1 {
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 2px;
        }

        .company-status {
            font-size: 12px;
            opacity: 0.9;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .status-indicator {
            width: 8px;
            height: 8px;
            background: #10b981;
            border-radius: 50%;
        }

        .header-actions {
            display: flex;
            gap: 8px;
        }

        .action-btn {
            width: 40px;
            height: 40px;
            background: rgba(255,255,255,0.15);
            border: none;
            border-radius: 12px;
            color: white;
            font-size: 18px;
            cursor: pointer;
            backdrop-filter: blur(10px);
            transition: all 0.2s ease;
            position: relative;
            text-decoration: none;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 12px;
        }

        .stat-card {
            background: rgba(255,255,255,0.15);
            border-radius: 12px;
            padding: 16px 12px;
            text-align: center;
            backdrop-filter: blur(10px);
        }

        .stat-number {
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 4px;
        }

        .stat-label {
            font-size: 11px;
            opacity: 0.8;
        }

        .content {
            padding: 20px;
            padding-bottom: 100px;
        }

        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 100%;
            max-width: 414px;
            background: white;
            border-top: 1px solid #e2e8f0;
            padding: 12px 20px;
            display: flex;
            justify-content: space-around;
            box-shadow: 0 -4px 20px rgba(0,0,0,0.1);
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
            cursor: pointer;
            padding: 8px;
            border-radius: 12px;
            transition: all 0.2s ease;
            position: relative;
            text-decoration: none;
            color: #64748b;
        }

        .nav-item.active {
            background: #f0fdf4;
            color: #059669;
        }

        .nav-icon {
            font-size: 20px;
        }

        .nav-label {
            font-size: 10px;
            font-weight: 500;
        }

        .floating-action {
            position: fixed;
            bottom: 80px;
            right: 20px;
            width: 56px;
            height: 56px;
            background: linear-gradient(135deg, #059669, #065f46);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            cursor: pointer;
            box-shadow: 0 4px 16px rgba(5, 150, 105, 0.4);
            transition: all 0.3s ease;
            z-index: 100;
            text-decoration: none;
        }

        /* Manter sempre formato mobile */

        /* Estilos específicos para marketplace */
        .category-tabs {
            display: flex;
            background: white;
            border-radius: 12px;
            padding: 4px;
            margin-bottom: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            overflow-x: auto;
        }

        .category-tab {
            flex: 1;
            min-width: 80px;
            padding: 12px 16px;
            text-align: center;
            border-radius: 8px;
            font-size: 12px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            text-decoration: none;
            color: #64748b;
            white-space: nowrap;
        }

        .category-tab.active {
            background: #059669;
            color: white;
        }

        .products-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
        }

        .product-card {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: transform 0.2s ease;
        }

        .product-card:hover {
            transform: translateY(-2px);
        }

        .product-image {
            width: 100%;
            height: 120px;
            background: #f8fafc;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 32px;
            color: #059669;
        }

        .product-info {
            padding: 12px;
        }

        .product-name {
            font-size: 14px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 4px;
            line-height: 1.3;
        }

        .product-price {
            font-size: 16px;
            font-weight: 700;
            color: #059669;
            margin-bottom: 8px;
        }

        .product-stock {
            font-size: 11px;
            color: #64748b;
            margin-bottom: 8px;
        }

        .btn-add-cart {
            width: 100%;
            background: linear-gradient(135deg, #059669, #065f46);
            color: white;
            border: none;
            border-radius: 6px;
            padding: 8px 12px;
            font-size: 12px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .btn-add-cart:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(5, 150, 105, 0.3);
        }

        .cart-summary {
            position: fixed;
            bottom: 80px;
            left: 50%;
            transform: translateX(-50%);
            width: calc(100% - 40px);
            max-width: 374px;
            background: white;
            border-radius: 12px;
            padding: 16px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
            display: none;
        }

        .cart-summary.show {
            display: block;
        }

        .cart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }

        .cart-title {
            font-size: 16px;
            font-weight: 700;
            color: #1e293b;
        }

        .cart-total {
            font-size: 18px;
            font-weight: 700;
            color: #059669;
        }

        .btn-checkout {
            width: 100%;
            background: linear-gradient(135deg, #059669, #065f46);
            color: white;
            border: none;
            border-radius: 8px;
            padding: 12px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .btn-checkout:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(5, 150, 105, 0.3);
        }

        /* Grid sempre mobile */
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="header-content">
                <div class="header-top">
                    <div class="company-info">
                        <div class="company-logo">FF</div>
                        <div class="company-details">
                            <h1><?php echo htmlspecialchars(substr($usuario['nome'], 0, 20)); ?></h1>
                            <div class="company-status">
                                <div class="status-indicator"></div>
                                <span>Online • Verificado</span>
                            </div>
                        </div>
                    </div>
                    <div class="header-actions">
                        <a href="../" class="action-btn" title="Acessar área do cliente">👤</a>
                        <a href="logout.php" class="action-btn" title="Sair">🚪</a>
                        <button class="action-btn">🔔</button>
                    </div>
                </div>

                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number">0</div>
                        <div class="stat-label">Carrinho</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">6</div>
                        <div class="stat-label">Produtos</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">0</div>
                        <div class="stat-label">Pedidos</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="content">
            <!-- Categorias -->
            <div class="category-tabs">
                <a href="?categoria=pecas" class="category-tab <?php echo ($_GET['categoria'] ?? 'pecas') === 'pecas' ? 'active' : ''; ?>">
                    🔧 Peças
                </a>
                <a href="?categoria=ferramentas" class="category-tab <?php echo ($_GET['categoria'] ?? '') === 'ferramentas' ? 'active' : ''; ?>">
                    🛠️ Ferramentas
                </a>
                <a href="?categoria=acessorios" class="category-tab <?php echo ($_GET['categoria'] ?? '') === 'acessorios' ? 'active' : ''; ?>">
                    📱 Acessórios
                </a>
                <a href="?categoria=todos" class="category-tab <?php echo ($_GET['categoria'] ?? '') === 'todos' ? 'active' : ''; ?>">
                    📦 Todos
                </a>
            </div>

            <!-- Aviso sobre Plano Master -->
            <div style="background: linear-gradient(135deg, #fbbf24, #f59e0b); border-radius: 16px; padding: 20px; margin-bottom: 20px; color: white; text-align: center;">
                <div style="font-size: 24px; margin-bottom: 8px;">⭐</div>
                <h3 style="font-size: 16px; font-weight: 700; margin-bottom: 8px;">Plano Master em Breve!</h3>
                <p style="font-size: 14px; opacity: 0.9; line-height: 1.4;">
                    Em breve, você que tem nosso <strong>Plano Master</strong> poderá comprar peças para sua manutenção diretamente aqui!
                </p>
                <div style="background: rgba(255,255,255,0.2); border-radius: 12px; padding: 12px; margin-top: 12px;">
                    <p style="font-size: 13px; font-weight: 600;">
                        🚧 No momento não está disponível
                    </p>
                </div>
            </div>

            <!-- Produtos -->
            <div class="products-grid">
                <!-- Produto 1 -->
                <div class="product-card">
                    <div class="product-image">📱</div>
                    <div class="product-info">
                        <div class="product-name">Tela iPhone 12</div>
                        <div class="product-price">R$ 299,90</div>
                        <div class="product-stock">5 em estoque</div>
                        <button class="btn-add-cart" onclick="addToCart(1, 'Tela iPhone 12', 299.90)">
                            Adicionar
                        </button>
                    </div>
                </div>

                <!-- Produto 2 -->
                <div class="product-card">
                    <div class="product-image">🔋</div>
                    <div class="product-info">
                        <div class="product-name">Bateria Samsung A51</div>
                        <div class="product-price">R$ 89,90</div>
                        <div class="product-stock">12 em estoque</div>
                        <button class="btn-add-cart" onclick="addToCart(2, 'Bateria Samsung A51', 89.90)">
                            Adicionar
                        </button>
                    </div>
                </div>

                <!-- Produto 3 -->
                <div class="product-card">
                    <div class="product-image">🛠️</div>
                    <div class="product-info">
                        <div class="product-name">Kit Chaves Precisão</div>
                        <div class="product-price">R$ 45,90</div>
                        <div class="product-stock">8 em estoque</div>
                        <button class="btn-add-cart" onclick="addToCart(3, 'Kit Chaves Precisão', 45.90)">
                            Adicionar
                        </button>
                    </div>
                </div>

                <!-- Produto 4 -->
                <div class="product-card">
                    <div class="product-image">🔌</div>
                    <div class="product-info">
                        <div class="product-name">Cabo USB-C</div>
                        <div class="product-price">R$ 19,90</div>
                        <div class="product-stock">25 em estoque</div>
                        <button class="btn-add-cart" onclick="addToCart(4, 'Cabo USB-C', 19.90)">
                            Adicionar
                        </button>
                    </div>
                </div>

                <!-- Produto 5 -->
                <div class="product-card">
                    <div class="product-image">🎧</div>
                    <div class="product-info">
                        <div class="product-name">Fone Bluetooth</div>
                        <div class="product-price">R$ 129,90</div>
                        <div class="product-stock">3 em estoque</div>
                        <button class="btn-add-cart" onclick="addToCart(5, 'Fone Bluetooth', 129.90)">
                            Adicionar
                        </button>
                    </div>
                </div>

                <!-- Produto 6 -->
                <div class="product-card">
                    <div class="product-image">🔧</div>
                    <div class="product-info">
                        <div class="product-name">Chave Pentalobe</div>
                        <div class="product-price">R$ 15,90</div>
                        <div class="product-stock">15 em estoque</div>
                        <button class="btn-add-cart" onclick="addToCart(6, 'Chave Pentalobe', 15.90)">
                            Adicionar
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Carrinho -->
        <div class="cart-summary" id="cartSummary">
            <div class="cart-header">
                <div class="cart-title">🛒 Carrinho</div>
                <div class="cart-total" id="cartTotal">R$ 0,00</div>
            </div>
            <button class="btn-checkout" onclick="checkout()">
                Finalizar Pedido
            </button>
        </div>

        <!-- Floating Action Button -->
        <a href="marketplace.php" class="floating-action" title="Carrinho">
            <span>🛒</span>
        </a>

        <!-- Menu de Navegação -->
        <div class="bottom-nav">
            <a href="dashboard_mobile_final.php" class="nav-item">
                <div class="nav-icon">🏠</div>
                <div class="nav-label">Início</div>
            </a>
            <a href="solicitacoes.php" class="nav-item">
                <div class="nav-icon">📋</div>
                <div class="nav-label">Solicitações</div>
            </a>
            <a href="reparos_new.php" class="nav-item">
                <div class="nav-icon">🔧</div>
                <div class="nav-label">Reparos</div>
            </a>
            <a href="propostas.php" class="nav-item">
                <div class="nav-icon">💼</div>
                <div class="nav-label">Propostas</div>
            </a>
            <a href="marketplace.php" class="nav-item active">
                <div class="nav-icon">🛒</div>
                <div class="nav-label">Loja</div>
            </a>
            <a href="carteira.php" class="nav-item">
                <div class="nav-icon">💳</div>
                <div class="nav-label">Carteira</div>
            </a>
        </div>
    </div>

    <script>
        let cart = [];
        let cartTotal = 0;

        function addToCart(id, name, price) {
            cart.push({ id, name, price });
            cartTotal += price;
            updateCartDisplay();
            showNotification(`${name} adicionado ao carrinho!`);
        }

        function updateCartDisplay() {
            const cartSummary = document.getElementById('cartSummary');
            const cartTotalElement = document.getElementById('cartTotal');

            if (cart.length > 0) {
                cartSummary.classList.add('show');
                cartTotalElement.textContent = `R$ ${cartTotal.toFixed(2).replace('.', ',')}`;
            } else {
                cartSummary.classList.remove('show');
            }
        }

        function checkout() {
            if (cart.length === 0) {
                showNotification('Carrinho vazio!', 'error');
                return;
            }

            showNotification('Redirecionando para pagamento...');
            setTimeout(() => {
                // Simular redirecionamento para checkout
                window.location.href = '#checkout';
            }, 1000);
        }

        function showNotification(message, type = 'success') {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                left: 50%;
                transform: translateX(-50%);
                background: ${type === 'success' ? '#10b981' : '#ef4444'};
                color: white;
                padding: 12px 20px;
                border-radius: 8px;
                font-size: 14px;
                font-weight: 600;
                z-index: 2000;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                animation: slideDown 0.3s ease;
            `;
            notification.textContent = message;

            document.body.appendChild(notification);

            setTimeout(() => {
                notification.remove();
            }, 3000);
        }

        // CSS para animação
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideDown {
                from {
                    opacity: 0;
                    transform: translateX(-50%) translateY(-20px);
                }
                to {
                    opacity: 1;
                    transform: translateX(-50%) translateY(0);
                }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
