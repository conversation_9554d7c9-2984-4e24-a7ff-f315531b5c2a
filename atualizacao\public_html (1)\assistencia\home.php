<?php
// Iniciar sessão
session_start();

// Verificar se o usuário está logado e se é do tipo 'assistencia'
if (!isset($_SESSION['usuario_id']) || $_SESSION['tipo_usuario'] != 'assistencia') {
    header('Location: ../login.php');
    exit();
}

// Obter informações do usuário
$usuario_id = $_SESSION['usuario_id'];
$nome_usuario = $_SESSION['nome'];

// Conexão com o banco de dados
$servername = "localhost";
$username_db = "u680766645_fixfacilnew";
$password_db = "T3cn0l0g1a@";
$dbname = "u680766645_fixfacilnew";

$conn = new mysqli($servername, $username_db, $password_db, $dbname);

// Verificar conexão
if ($conn->connect_error) {
    die("Falha na conexão: " . $conn->connect_error);
}

// Definir charset
$conn->set_charset("utf8");

// Obter estatísticas para exibir no painel
// Solicitações pendentes
$sql_pendentes = "SELECT COUNT(*) as total FROM solicitacoes_reparo WHERE status = 'enviado'";
$result_pendentes = $conn->query($sql_pendentes);
$total_pendentes = ($result_pendentes && $result_pendentes->num_rows > 0) ? $result_pendentes->fetch_assoc()['total'] : 0;

// Obter o assistencia_id correspondente ao usuario_id
$sql_assistencia = "SELECT id FROM assistencias_tecnicas WHERE usuario_id = ?";
$stmt_assistencia = $conn->prepare($sql_assistencia);
$stmt_assistencia->bind_param("i", $usuario_id);
$stmt_assistencia->execute();
$result_assistencia = $stmt_assistencia->get_result();
$assistencia_id = 0;
if ($row_assistencia = $result_assistencia->fetch_assoc()) {
    $assistencia_id = $row_assistencia['id'];
}
$stmt_assistencia->close();

// Propostas enviadas
$sql_propostas = "SELECT COUNT(*) as total FROM propostas_assistencia WHERE assistencia_id = ?";
$stmt_propostas = $conn->prepare($sql_propostas);
$stmt_propostas->bind_param("i", $assistencia_id);
$stmt_propostas->execute();
$result_propostas = $stmt_propostas->get_result();
$total_propostas = ($result_propostas && $result_propostas->num_rows > 0) ? $result_propostas->fetch_assoc()['total'] : 0;
$stmt_propostas->close();

// Reparos em andamento
$sql_reparos = "SELECT COUNT(*) as total FROM solicitacoes_reparo 
                WHERE id IN (SELECT solicitacao_id FROM propostas_assistencia 
                           WHERE assistencia_id = ? AND status = 'aceita')
                AND status = 'em_andamento'";
$stmt_reparos = $conn->prepare($sql_reparos);
$stmt_reparos->bind_param("i", $assistencia_id);
$stmt_reparos->execute();
$result_reparos = $stmt_reparos->get_result();
$total_reparos = ($result_reparos && $result_reparos->num_rows > 0) ? $result_reparos->fetch_assoc()['total'] : 0;
$stmt_reparos->close();

// Fechar conexão
$conn->close();
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <title>Painel de Assistência - FixFácil</title>
    <!-- Meta Tags para Responsividade -->
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <!-- Incluindo Bootstrap CSS (Versão 5.3) -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome para ícones -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Estilos personalizados -->
    <style>
        :root {
            --primary-color: #2563eb;
            --primary-dark: #1d4ed8;
            --secondary-color: #475569;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --light-bg: #f8fafc;
            --card-bg: #ffffff;
            --text-color: #1e293b;
            --text-muted: #64748b;
            --border-radius: 12px;
            --shadow-sm: 0 1px 3px rgba(0,0,0,0.1);
            --shadow-md: 0 4px 6px -1px rgba(0,0,0,0.1), 0 2px 4px -1px rgba(0,0,0,0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0,0,0,0.1), 0 4px 6px -2px rgba(0,0,0,0.05);
        }
        
        body {
            font-family: 'Inter', sans-serif;
            background-color: var(--light-bg);
            color: var(--text-color);
            margin-bottom: 80px;
            padding-top: 70px;
        }
        
        /* Navbar */
        .navbar {
            background-color: var(--card-bg);
            box-shadow: var(--shadow-sm);
            padding: 12px 0;
        }
        
        .navbar-brand {
            font-weight: 700;
            color: var(--primary-color) !important;
        }
        
        .navbar .nav-link {
            color: var(--text-color) !important;
            font-weight: 500;
            padding: 8px 16px;
            border-radius: var(--border-radius);
            transition: all 0.3s ease;
        }
        
        .navbar .nav-link:hover {
            background-color: rgba(37, 99, 235, 0.1);
            color: var(--primary-color) !important;
        }
        
        .navbar .nav-link.active {
            background-color: var(--primary-color);
            color: white !important;
        }
        
        /* Conteúdo Principal */
        .main-content {
            padding: 20px 12px;
        }
        
        .welcome {
            margin-bottom: 30px;
        }
        
        .welcome h2 {
            font-weight: 700;
            font-size: 1.5rem;
            margin-bottom: 8px;
        }
        
        @media (min-width: 768px) {
            .welcome h2 {
                font-size: 2rem;
            }
        }
        
        .welcome p {
            color: var(--text-muted);
            font-size: 1rem;
        }
        
        /* Dashboard Stats */
        .stats-container {
            margin-bottom: 20px;
        }
        
        .stat-card {
            background: var(--card-bg);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-md);
            padding: 16px;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            height: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-lg);
        }
        
        .stat-card .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 16px;
        }
        
        .stat-card .stat-icon i {
            font-size: 24px;
            color: white;
        }
        
        .stat-card .stat-number {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 8px;
        }
        
        .stat-card .stat-label {
            color: var(--text-muted);
            font-weight: 500;
            margin-bottom: 16px;
            text-align: center;
        }
        
        .stat-card .stat-btn {
            margin-top: auto;
            width: 100%;
            padding: 10px;
            border-radius: var(--border-radius);
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        /* Cards com ícones coloridos */
        .bg-primary-soft {
            background-color: rgba(37, 99, 235, 0.15);
        }
        
        .bg-success-soft {
            background-color: rgba(16, 185, 129, 0.15);
        }
        
        .bg-warning-soft {
            background-color: rgba(245, 158, 11, 0.15);
        }
        
        .icon-primary {
            background-color: var(--primary-color);
        }
        
        .icon-success {
            background-color: var(--success-color);
        }
        
        .icon-warning {
            background-color: var(--warning-color);
        }
        
        /* Seção de Ações Rápidas */
        .quick-actions {
            margin-top: 30px;
        }
        
        .quick-actions h3 {
            font-weight: 600;
            font-size: 1.25rem;
            margin-bottom: 20px;
            position: relative;
            padding-left: 15px;
        }
        
        .quick-actions h3::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 4px;
            height: 20px;
            background-color: var(--primary-color);
            border-radius: 2px;
        }
        
        .action-card {
            background: var(--card-bg);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-md);
            padding: 16px;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            transition: transform 0.3s ease;
        }
        
        .action-card:hover {
            transform: translateX(5px);
        }
        
        .action-card .action-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            background-color: rgba(37, 99, 235, 0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 16px;
        }
        
        .action-card .action-icon i {
            font-size: 24px;
            color: var(--primary-color);
        }
        
        .action-card .action-content {
            flex: 1;
        }
        
        .action-card .action-title {
            font-weight: 600;
            margin-bottom: 4px;
        }
        
        .action-card .action-desc {
            color: var(--text-muted);
            font-size: 0.875rem;
        }
        
        .action-card .action-arrow {
            color: var(--primary-color);
        }
        
        /* Footer */
        .footer {
            background-color: var(--card-bg);
            padding: 16px 0;
            position: fixed;
            width: 100%;
            bottom: 0;
            box-shadow: var(--shadow-sm);
            z-index: 999;
        }
        
        .footer span {
            color: var(--text-muted);
            font-size: 0.875rem;
        }
        
        /* Mobile Menu */
        .mobile-menu {
            position: fixed;
            bottom: 0;
            width: 100%;
            background-color: var(--card-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            z-index: 1000;
            padding: 8px 0;
            display: flex;
            justify-content: space-around;
        }
        
        .mobile-menu .menu-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 8px 0;
            width: 20%;
        }
        
        .mobile-menu .menu-item i {
            font-size: 20px;
            margin-bottom: 4px;
            color: var(--text-muted);
            transition: color 0.3s ease;
        }
        
        .mobile-menu .menu-item span {
            font-size: 12px;
            color: var(--text-muted);
            transition: color 0.3s ease;
        }
        
        .mobile-menu .menu-item.active i,
        .mobile-menu .menu-item.active span {
            color: var(--primary-color);
            font-weight: 600;
        }
        
        .mobile-menu .menu-item:hover i,
        .mobile-menu .menu-item:hover span {
            color: var(--primary-dark);
        }
        
        /* Esconder menu mobile em desktop */
        @media (min-width: 992px) {
            .mobile-menu {
                display: none;
            }
            body {
                margin-bottom: 60px;
            }
        }
        
        /* Ajustes para mobile */
        @media (max-width: 576px) {
            .main-content {
                padding: 15px 10px;
            }
            .stat-card {
                padding: 12px;
                margin-bottom: 16px;
            }
            .stat-card .stat-number {
                font-size: 1.75rem;
            }
            .stat-card .stat-icon {
                width: 50px;
                height: 50px;
            }
            .welcome h2 {
                font-size: 1.25rem;
            }
            .action-card {
                padding: 12px;
            }
            .action-card .action-icon {
                width: 40px;
                height: 40px;
            }
            .action-card .action-title {
                font-size: 0.95rem;
            }
        }
    </style>
</head>
<body>
    <!-- Cabeçalho (Navbar) -->
    <nav class="navbar navbar-expand-lg fixed-top navbar-light">
        <div class="container">
            <a class="navbar-brand" href="#">FixFácil</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" 
                    aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="home.php">Painel</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="solicitacoes.php">Solicitações</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="propostas_enviadas.php">Propostas</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="reparos_em_andamento.php">Reparos</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="marketplace.php">Marketplace</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="solicitar_pecas.php">Peças</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="carteira.php">Carteira</a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fa-solid fa-user-circle"></i>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="perfil.php">Meu Perfil</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../logout.php">Sair</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Conteúdo Principal -->
    <div class="container main-content">
        <div class="welcome">
            <h2>Olá, <?php echo htmlspecialchars($nome_usuario); ?>! 👋</h2>
            <p>Bem-vindo ao seu painel de assistência técnica.</p>
        </div>

        <!-- Estatísticas/Cards -->
        <div class="stats-container">
            <div class="row g-3">
                <!-- Card 1: Solicitações Pendentes -->
                <div class="col-12 col-md-4">
                    <div class="stat-card bg-primary-soft">
                        <div class="stat-icon icon-primary">
                            <i class="fas fa-inbox"></i>
                        </div>
                        <div class="stat-number"><?php echo $total_pendentes; ?></div>
                        <div class="stat-label">Solicitações Pendentes</div>
                        <a href="solicitacoes.php" class="btn btn-primary stat-btn">
                            Ver Solicitações
                        </a>
                    </div>
                </div>
                
                <!-- Card 2: Propostas Enviadas -->
                <div class="col-12 col-md-4">
                    <div class="stat-card bg-success-soft">
                        <div class="stat-icon icon-success">
                            <i class="fas fa-paper-plane"></i>
                        </div>
                        <div class="stat-number"><?php echo $total_propostas; ?></div>
                        <div class="stat-label">Propostas Enviadas</div>
                        <a href="propostas_enviadas.php" class="btn btn-success stat-btn">
                            Ver Propostas
                        </a>
                    </div>
                </div>
                
                <!-- Card 3: Reparos em Andamento -->
                <div class="col-12 col-md-4">
                    <div class="stat-card bg-warning-soft">
                        <div class="stat-icon icon-warning">
                            <i class="fas fa-tools"></i>
                        </div>
                        <div class="stat-number"><?php echo $total_reparos; ?></div>
                        <div class="stat-label">Reparos em Andamento</div>
                        <a href="reparos_em_andamento.php" class="btn btn-warning stat-btn">
                            Ver Reparos
                        </a>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Ações Rápidas -->
        <div class="quick-actions">
            <h3>Ações Rápidas</h3>
            
            <!-- Linha 1 -->
            <div class="row g-3">
                <div class="col-12 col-md-6">
                    <a href="solicitacoes.php" class="text-decoration-none">
                        <div class="action-card">
                            <div class="action-icon">
                                <i class="fas fa-search"></i>
                            </div>
                            <div class="action-content">
                                <div class="action-title">Ver novas solicitações</div>
                                <div class="action-desc">Verifique e envie propostas para reparos</div>
                            </div>
                            <div class="action-arrow">
                                <i class="fas fa-chevron-right"></i>
                            </div>
                        </div>
                    </a>
                </div>
                
                <div class="col-12 col-md-6">
                    <a href="reparos_em_andamento.php" class="text-decoration-none">
                        <div class="action-card">
                            <div class="action-icon">
                                <i class="fas fa-clipboard-list"></i>
                            </div>
                            <div class="action-content">
                                <div class="action-title">Atualizar status de reparos</div>
                                <div class="action-desc">Mantenha seus clientes informados</div>
                            </div>
                            <div class="action-arrow">
                                <i class="fas fa-chevron-right"></i>
                            </div>
                        </div>
                    </a>
                </div>
            </div>
            
            <!-- Linha 2 -->
            <div class="row g-3 mt-1">
                <div class="col-12 col-md-6">
                    <a href="solicitar_pecas.php" class="text-decoration-none">
                        <div class="action-card">
                            <div class="action-icon">
                                <i class="fas fa-cog"></i>
                            </div>
                            <div class="action-content">
                                <div class="action-title">Solicitar peças</div>
                                <div class="action-desc">Encontre as peças necessárias para reparos</div>
                            </div>
                            <div class="action-arrow">
                                <i class="fas fa-chevron-right"></i>
                            </div>
                        </div>
                    </a>
                </div>
                
                <div class="col-12 col-md-6">
                    <a href="marketplace.php" class="text-decoration-none">
                        <div class="action-card">
                            <div class="action-icon">
                                <i class="fas fa-store"></i>
                            </div>
                            <div class="action-content">
                                <div class="action-title">Gerenciar Marketplace</div>
                                <div class="action-desc">Atualize seus serviços e ofertas</div>
                            </div>
                            <div class="action-arrow">
                                <i class="fas fa-chevron-right"></i>
                            </div>
                        </div>
                    </a>
                </div>
            </div>
            
            <!-- Linha 3 (Visível apenas em desktop para manter o layout mais limpo em mobile) -->
            <div class="row g-3 mt-1 d-none d-md-flex">
                <div class="col-12 col-md-6">
                    <a href="carteira.php" class="text-decoration-none">
                        <div class="action-card">
                            <div class="action-icon">
                                <i class="fas fa-wallet"></i>
                            </div>
                            <div class="action-content">
                                <div class="action-title">Ver carteira</div>
                                <div class="action-desc">Gerencie seus ganhos e pagamentos</div>
                            </div>
                            <div class="action-arrow">
                                <i class="fas fa-chevron-right"></i>
                            </div>
                        </div>
                    </a>
                </div>
                
                <div class="col-12 col-md-6">
                    <a href="perfil.php" class="text-decoration-none">
                        <div class="action-card">
                            <div class="action-icon">
                                <i class="fas fa-user-cog"></i>
                            </div>
                            <div class="action-content">
                                <div class="action-title">Atualizar perfil</div>
                                <div class="action-desc">Mantenha suas informações atualizadas</div>
                            </div>
                            <div class="action-arrow">
                                <i class="fas fa-chevron-right"></i>
                            </div>
                        </div>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Menu Mobile (fixo na parte inferior) -->
    <div class="mobile-menu d-lg-none">
        <a href="home.php" class="menu-item active">
            <i class="fas fa-home"></i>
            <span>Painel</span>
        </a>
        <a href="solicitacoes.php" class="menu-item">
            <i class="fas fa-inbox"></i>
            <span>Solicitações</span>
        </a>
        <a href="reparos_em_andamento.php" class="menu-item">
            <i class="fas fa-tools"></i>
            <span>Reparos</span>
        </a>
        <a href="marketplace.php" class="menu-item">
            <i class="fas fa-store"></i>
            <span>Marketplace</span>
        </a>
        <a href="perfil.php" class="menu-item">
            <i class="fas fa-user"></i>
            <span>Perfil</span>
        </a>
    </div>

    <!-- Rodapé (visível apenas em desktop) -->
    <footer class="footer d-none d-lg-block">
        <div class="container text-center">
            <span>&copy; <?php echo date("Y"); ?> FixFácil - Todos os direitos reservados</span>
        </div>
    </footer>

    <!-- Scripts Bootstrap -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>