<?php
/**
 * Página de Assistência Virtual Personalizada
 * FixFácil Assistências - Sistema Novo
 * Funcionalidade exclusiva do Plano Master
 */

require_once 'config/auth.php';
require_once 'config/database.php';
require_once 'includes/layout.php';

// Verificar autenticação
$auth = getAuth();
$auth->checkAssistenciaAuth();

// Verificar acesso à assistência virtual (plano Master)
$usuario = $auth->getUsuarioLogado();

if (!$auth->hasAccess('assistencia_virtual')) {
    header('Location: upgrade_plano.php?feature=assistencia_virtual');
    exit();
}

$plano = $auth->getPlanoInfo($usuario['id']);

$db = getDatabase();

// Obter dados da assistência virtual
$assistencia_virtual = null;
try {
    $sql = "
        SELECT av.*, at.nome_empresa, at.endereco, at.telefone, at.email, at.site
        FROM assistencias_virtuais av
        JOIN assistencias_tecnicas at ON av.assistencia_id = at.id
        WHERE av.assistencia_id = ?
    ";
    $result = $db->query($sql, [$usuario['assistencia_id']]);
    $assistencia_virtual = $result->fetch_assoc();
} catch (Exception $e) {
    error_log("Erro ao obter assistência virtual: " . $e->getMessage());
}

// Processar formulário
$erro = '';
$sucesso = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $acao = $_POST['acao'] ?? '';

    if ($acao === 'configurar') {
        $slug_personalizado = trim($_POST['slug_personalizado'] ?? '');
        $titulo_pagina = trim($_POST['titulo_pagina'] ?? '');
        $descricao = trim($_POST['descricao'] ?? '');
        $cor_primaria = $_POST['cor_primaria'] ?? '#667eea';
        $cor_secundaria = $_POST['cor_secundaria'] ?? '#764ba2';
        $logo_url = trim($_POST['logo_url'] ?? '');
        $ativo = isset($_POST['ativo']) ? 1 : 0;

        // Novos campos
        $banner_principal = trim($_POST['banner_principal'] ?? '');
        $texto_banner = trim($_POST['texto_banner'] ?? '');
        $botao_cta_texto = trim($_POST['botao_cta_texto'] ?? 'Solicitar Reparo');
        $botao_cta_cor = $_POST['botao_cta_cor'] ?? '#ffffff';
        $whatsapp = trim($_POST['whatsapp'] ?? '');
        $instagram = trim($_POST['instagram'] ?? '');
        $facebook = trim($_POST['facebook'] ?? '');
        $endereco_completo = trim($_POST['endereco_completo'] ?? '');
        $horario_funcionamento = trim($_POST['horario_funcionamento'] ?? '');
        $sobre_empresa = trim($_POST['sobre_empresa'] ?? '');
        $servicos_oferecidos = trim($_POST['servicos_oferecidos'] ?? '');
        $chat_habilitado = isset($_POST['chat_habilitado']) ? 1 : 0;
        $marketplace_habilitado = isset($_POST['marketplace_habilitado']) ? 1 : 0;
        $retirada_express_habilitada = isset($_POST['retirada_express_habilitada']) ? 1 : 0;
        $tema_personalizado = $_POST['tema_personalizado'] ?? 'moderno';
        $fonte_personalizada = $_POST['fonte_personalizada'] ?? 'Inter';
        
        // Validações
        if (empty($slug_personalizado)) {
            $erro = 'Slug personalizado é obrigatório.';
        } elseif (!preg_match('/^[a-z0-9-]+$/', $slug_personalizado)) {
            $erro = 'Slug deve conter apenas letras minúsculas, números e hífens.';
        } elseif (empty($titulo_pagina)) {
            $erro = 'Título da página é obrigatório.';
        } else {
            try {
                // Verificar se slug já existe (para outras assistências)
                $sql = "SELECT id FROM assistencias_virtuais WHERE slug_personalizado = ? AND assistencia_id != ?";
                $result = $db->query($sql, [$slug_personalizado, $usuario['assistencia_id']]);
                
                if ($result->fetch_assoc()) {
                    $erro = 'Este slug já está sendo usado por outra assistência.';
                } else {
                    if ($assistencia_virtual) {
                        // Atualizar
                        $sql = "
                            UPDATE assistencias_virtuais
                            SET slug_personalizado = ?, titulo_pagina = ?, descricao = ?,
                                cor_primaria = ?, cor_secundaria = ?, logo_url = ?, ativo = ?,
                                banner_principal = ?, texto_banner = ?, botao_cta_texto = ?, botao_cta_cor = ?,
                                whatsapp = ?, instagram = ?, facebook = ?, endereco_completo = ?,
                                horario_funcionamento = ?, sobre_empresa = ?, servicos_oferecidos = ?,
                                chat_habilitado = ?, marketplace_habilitado = ?, retirada_express_habilitada = ?,
                                tema_personalizado = ?, fonte_personalizada = ?
                            WHERE assistencia_id = ?
                        ";
                        $db->query($sql, [
                            $slug_personalizado, $titulo_pagina, $descricao,
                            $cor_primaria, $cor_secundaria, $logo_url, $ativo,
                            $banner_principal, $texto_banner, $botao_cta_texto, $botao_cta_cor,
                            $whatsapp, $instagram, $facebook, $endereco_completo,
                            $horario_funcionamento, $sobre_empresa, $servicos_oferecidos,
                            $chat_habilitado, $marketplace_habilitado, $retirada_express_habilitada,
                            $tema_personalizado, $fonte_personalizada,
                            $usuario['assistencia_id']
                        ]);
                    } else {
                        // Criar
                        $sql = "
                            INSERT INTO assistencias_virtuais
                            (assistencia_id, slug_personalizado, titulo_pagina, descricao,
                             cor_primaria, cor_secundaria, logo_url, ativo, data_criacao,
                             banner_principal, texto_banner, botao_cta_texto, botao_cta_cor,
                             whatsapp, instagram, facebook, endereco_completo,
                             horario_funcionamento, sobre_empresa, servicos_oferecidos,
                             chat_habilitado, marketplace_habilitado, retirada_express_habilitada,
                             tema_personalizado, fonte_personalizada)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW(), ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                        ";
                        $db->query($sql, [
                            $usuario['assistencia_id'], $slug_personalizado, $titulo_pagina, $descricao,
                            $cor_primaria, $cor_secundaria, $logo_url, $ativo,
                            $banner_principal, $texto_banner, $botao_cta_texto, $botao_cta_cor,
                            $whatsapp, $instagram, $facebook, $endereco_completo,
                            $horario_funcionamento, $sobre_empresa, $servicos_oferecidos,
                            $chat_habilitado, $marketplace_habilitado, $retirada_express_habilitada,
                            $tema_personalizado, $fonte_personalizada
                        ]);
                    }
                    
                    $sucesso = 'Assistência virtual configurada com sucesso!';

                    // Criar arquivo físico para a assistência virtual
                    try {
                        $arquivo_virtual = __DIR__ . '/../virtual/' . $slug_personalizado . '.php';
                        $conteudo_arquivo = "<?php\n/**\n * Página da Assistência Virtual - " . $titulo_pagina . "\n * FixFácil Assistências - Sistema Novo\n */\n\n// Definir o slug diretamente\n\$_GET['slug'] = '" . $slug_personalizado . "';\n\n// Incluir a página principal\nrequire_once 'index.php';\n?>";

                        file_put_contents($arquivo_virtual, $conteudo_arquivo);
                        $sucesso .= ' Arquivo criado: ' . $slug_personalizado . '.php';
                    } catch (Exception $e) {
                        // Não é crítico se falhar
                    }

                    // Recarregar dados
                    $result = $db->query("SELECT * FROM assistencias_virtuais WHERE assistencia_id = ?", [$usuario['assistencia_id']]);
                    $assistencia_virtual = $result->fetch_assoc();
                }
            } catch (Exception $e) {
                error_log("Erro ao configurar assistência virtual: " . $e->getMessage());
                $erro = 'Erro interno. Tente novamente.';
            }
        }
    }

    if ($acao === 'gerenciar_marketplace') {
        $marketplace_acao = $_POST['marketplace_acao'] ?? '';

        if ($marketplace_acao === 'adicionar_produto') {
            $nome = trim($_POST['produto_nome'] ?? '');
            $categoria = $_POST['produto_categoria'] ?? '';
            $preco = (float)($_POST['produto_preco'] ?? 0);
            $quantidade_estoque = (int)($_POST['produto_estoque'] ?? 0);
            $descricao = trim($_POST['produto_descricao'] ?? '');
            $imagem = trim($_POST['produto_imagem'] ?? '');
            $marca = trim($_POST['produto_marca'] ?? '');
            $tipo_peca = $_POST['produto_tipo'] ?? 'compativel';

            if (empty($nome) || empty($categoria) || $preco <= 0) {
                $erro = 'Preencha todos os campos obrigatórios do produto.';
            } else {
                try {
                    $sql = "
                        INSERT INTO produtos_marketplace
                        (assistencia_id, nome, categoria, preco, quantidade_estoque, descricao,
                         imagem, marca, tipo_peca, status, data_criacao)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 'ativo', NOW())
                    ";
                    $db->query($sql, [
                        $usuario['assistencia_id'], $nome, $categoria, $preco, $quantidade_estoque,
                        $descricao, $imagem, $marca, $tipo_peca
                    ]);

                    $sucesso = 'Produto adicionado ao marketplace com sucesso!';
                } catch (Exception $e) {
                    error_log("Erro ao adicionar produto: " . $e->getMessage());
                    $erro = 'Erro ao adicionar produto. Tente novamente.';
                }
            }
        }

        if ($marketplace_acao === 'atualizar_produto') {
            $produto_id = (int)($_POST['produto_id'] ?? 0);
            $nome = trim($_POST['produto_nome'] ?? '');
            $categoria = $_POST['produto_categoria'] ?? '';
            $preco = (float)($_POST['produto_preco'] ?? 0);
            $quantidade_estoque = (int)($_POST['produto_estoque'] ?? 0);
            $descricao = trim($_POST['produto_descricao'] ?? '');
            $imagem = trim($_POST['produto_imagem'] ?? '');
            $marca = trim($_POST['produto_marca'] ?? '');
            $tipo_peca = $_POST['produto_tipo'] ?? 'compativel';
            $status = $_POST['produto_status'] ?? 'ativo';

            if ($produto_id > 0 && !empty($nome) && !empty($categoria) && $preco > 0) {
                try {
                    $sql = "
                        UPDATE produtos_marketplace
                        SET nome = ?, categoria = ?, preco = ?, quantidade_estoque = ?,
                            descricao = ?, imagem = ?, marca = ?, tipo_peca = ?, status = ?
                        WHERE id = ? AND assistencia_id = ?
                    ";
                    $db->query($sql, [
                        $nome, $categoria, $preco, $quantidade_estoque, $descricao,
                        $imagem, $marca, $tipo_peca, $status, $produto_id, $usuario['assistencia_id']
                    ]);

                    $sucesso = 'Produto atualizado com sucesso!';
                } catch (Exception $e) {
                    error_log("Erro ao atualizar produto: " . $e->getMessage());
                    $erro = 'Erro ao atualizar produto. Tente novamente.';
                }
            } else {
                $erro = 'Dados do produto inválidos.';
            }
        }

        if ($marketplace_acao === 'remover_produto') {
            $produto_id = (int)($_POST['produto_id'] ?? 0);

            if ($produto_id > 0) {
                try {
                    $sql = "UPDATE produtos_marketplace SET status = 'inativo' WHERE id = ? AND assistencia_id = ?";
                    $db->query($sql, [$produto_id, $usuario['assistencia_id']]);

                    $sucesso = 'Produto removido do marketplace.';
                } catch (Exception $e) {
                    error_log("Erro ao remover produto: " . $e->getMessage());
                    $erro = 'Erro ao remover produto. Tente novamente.';
                }
            }
        }
    }
}

// Estatísticas da assistência virtual
$stats = [];
if ($assistencia_virtual) {
    try {
        $sql = "
            SELECT 
                COUNT(*) as total_acessos,
                COUNT(CASE WHEN DATE(data_acesso) = CURDATE() THEN 1 END) as acessos_hoje,
                COUNT(CASE WHEN data_acesso >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 END) as acessos_semana
            FROM acessos_assistencia_virtual 
            WHERE assistencia_id = ?
        ";
        $result = $db->query($sql, [$usuario['assistencia_id']]);
        $stats = $result->fetch_assoc();
    } catch (Exception $e) {
        $stats = ['total_acessos' => 0, 'acessos_hoje' => 0, 'acessos_semana' => 0];
    }
}

// Inicializar layout
$layout = new Layout();
?>

<?php $layout->renderHead("Assistência Virtual - FixFácil Assistências"); ?>

<div class="main-wrapper">
    <?php $layout->renderSidebar('assistencia_virtual'); ?>
    
    <main class="main-content">
        <!-- Header -->
        <div class="content-header">
            <h1 class="page-title">
                <i class="fas fa-crown me-3"></i>
                Assistência Virtual Personalizada
            </h1>
            <p class="page-subtitle">
                Crie sua própria página de assistência técnica com link personalizado
            </p>
        </div>
        
        <?php if ($erro): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <?php echo htmlspecialchars($erro); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>
        
        <?php if ($sucesso): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?php echo htmlspecialchars($sucesso); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>
        
        <div class="row g-4">
            <!-- Configuração -->
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-cog me-2"></i>
                            Configuração da Assistência Virtual
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <input type="hidden" name="acao" value="configurar">
                            
                            <div class="row g-3">
                                <div class="col-12">
                                    <label for="slug_personalizado" class="form-label">Link Personalizado *</label>
                                    <div class="input-group">
                                        <span class="input-group-text">fixfacilassistencia.com.br/virtual/</span>
                                        <input type="text" class="form-control" id="slug_personalizado" name="slug_personalizado" 
                                               value="<?php echo htmlspecialchars($assistencia_virtual['slug_personalizado'] ?? ''); ?>" 
                                               placeholder="minha-assistencia" pattern="[a-z0-9-]+" required>
                                    </div>
                                    <div class="form-text">Apenas letras minúsculas, números e hífens. Ex: minha-assistencia</div>
                                </div>
                                
                                <div class="col-md-6">
                                    <label for="titulo_pagina" class="form-label">Título da Página *</label>
                                    <input type="text" class="form-control" id="titulo_pagina" name="titulo_pagina" 
                                           value="<?php echo htmlspecialchars($assistencia_virtual['titulo_pagina'] ?? $usuario['nome_empresa'] ?? ''); ?>" 
                                           placeholder="Nome da sua assistência" required>
                                </div>
                                
                                <div class="col-md-6">
                                    <label for="logo_url" class="form-label">URL do Logo</label>
                                    <input type="url" class="form-control" id="logo_url" name="logo_url" 
                                           value="<?php echo htmlspecialchars($assistencia_virtual['logo_url'] ?? ''); ?>" 
                                           placeholder="https://exemplo.com/logo.png">
                                </div>
                                
                                <div class="col-12">
                                    <label for="descricao" class="form-label">Descrição</label>
                                    <textarea class="form-control" id="descricao" name="descricao" rows="3" 
                                              placeholder="Descreva sua assistência técnica..."><?php echo htmlspecialchars($assistencia_virtual['descricao'] ?? ''); ?></textarea>
                                </div>
                                
                                <div class="col-md-6">
                                    <label for="cor_primaria" class="form-label">Cor Primária</label>
                                    <input type="color" class="form-control form-control-color" id="cor_primaria" name="cor_primaria" 
                                           value="<?php echo $assistencia_virtual['cor_primaria'] ?? '#667eea'; ?>">
                                </div>
                                
                                <div class="col-md-6">
                                    <label for="cor_secundaria" class="form-label">Cor Secundária</label>
                                    <input type="color" class="form-control form-control-color" id="cor_secundaria" name="cor_secundaria"
                                           value="<?php echo $assistencia_virtual['cor_secundaria'] ?? '#764ba2'; ?>">
                                </div>

                                <div class="col-md-6">
                                    <label for="banner_principal" class="form-label">Banner Principal (URL)</label>
                                    <input type="url" class="form-control" id="banner_principal" name="banner_principal"
                                           value="<?php echo htmlspecialchars($assistencia_virtual['banner_principal'] ?? ''); ?>"
                                           placeholder="https://exemplo.com/banner.jpg">
                                </div>

                                <div class="col-md-6">
                                    <label for="whatsapp" class="form-label">WhatsApp</label>
                                    <input type="text" class="form-control" id="whatsapp" name="whatsapp"
                                           value="<?php echo htmlspecialchars($assistencia_virtual['whatsapp'] ?? ''); ?>"
                                           placeholder="41999999999">
                                </div>

                                <div class="col-12">
                                    <label for="texto_banner" class="form-label">Texto do Banner</label>
                                    <textarea class="form-control" id="texto_banner" name="texto_banner" rows="2"
                                              placeholder="Texto que aparece no banner principal..."><?php echo htmlspecialchars($assistencia_virtual['texto_banner'] ?? ''); ?></textarea>
                                </div>

                                <div class="col-md-6">
                                    <label for="botao_cta_texto" class="form-label">Texto do Botão Principal</label>
                                    <input type="text" class="form-control" id="botao_cta_texto" name="botao_cta_texto"
                                           value="<?php echo htmlspecialchars($assistencia_virtual['botao_cta_texto'] ?? 'Solicitar Reparo'); ?>"
                                           placeholder="Solicitar Reparo">
                                </div>

                                <div class="col-md-6">
                                    <label for="botao_cta_cor" class="form-label">Cor do Botão Principal</label>
                                    <input type="color" class="form-control form-control-color" id="botao_cta_cor" name="botao_cta_cor"
                                           value="<?php echo $assistencia_virtual['botao_cta_cor'] ?? '#ffffff'; ?>">
                                </div>

                                <div class="col-md-6">
                                    <label for="instagram" class="form-label">Instagram</label>
                                    <input type="text" class="form-control" id="instagram" name="instagram"
                                           value="<?php echo htmlspecialchars($assistencia_virtual['instagram'] ?? ''); ?>"
                                           placeholder="@minha_assistencia">
                                </div>

                                <div class="col-md-6">
                                    <label for="facebook" class="form-label">Facebook</label>
                                    <input type="text" class="form-control" id="facebook" name="facebook"
                                           value="<?php echo htmlspecialchars($assistencia_virtual['facebook'] ?? ''); ?>"
                                           placeholder="facebook.com/minhaassistencia">
                                </div>

                                <div class="col-12">
                                    <label for="endereco_completo" class="form-label">Endereço Completo</label>
                                    <textarea class="form-control" id="endereco_completo" name="endereco_completo" rows="2"
                                              placeholder="Rua, número, bairro, cidade, CEP..."><?php echo htmlspecialchars($assistencia_virtual['endereco_completo'] ?? ''); ?></textarea>
                                </div>

                                <div class="col-md-6">
                                    <label for="horario_funcionamento" class="form-label">Horário de Funcionamento</label>
                                    <textarea class="form-control" id="horario_funcionamento" name="horario_funcionamento" rows="3"
                                              placeholder="Segunda a Sexta: 8h às 18h..."><?php echo htmlspecialchars($assistencia_virtual['horario_funcionamento'] ?? ''); ?></textarea>
                                </div>

                                <div class="col-md-6">
                                    <label for="servicos_oferecidos" class="form-label">Serviços Oferecidos</label>
                                    <textarea class="form-control" id="servicos_oferecidos" name="servicos_oferecidos" rows="3"
                                              placeholder="Conserto de telas, troca de baterias..."><?php echo htmlspecialchars($assistencia_virtual['servicos_oferecidos'] ?? ''); ?></textarea>
                                </div>

                                <div class="col-12">
                                    <label for="sobre_empresa" class="form-label">Sobre a Empresa</label>
                                    <textarea class="form-control" id="sobre_empresa" name="sobre_empresa" rows="4"
                                              placeholder="Conte sobre sua empresa, experiência, diferenciais..."><?php echo htmlspecialchars($assistencia_virtual['sobre_empresa'] ?? ''); ?></textarea>
                                </div>

                                <div class="col-md-4">
                                    <label for="tema_personalizado" class="form-label">Tema</label>
                                    <select class="form-select" id="tema_personalizado" name="tema_personalizado">
                                        <option value="moderno" <?php echo ($assistencia_virtual['tema_personalizado'] ?? 'moderno') === 'moderno' ? 'selected' : ''; ?>>Moderno</option>
                                        <option value="minimalista" <?php echo ($assistencia_virtual['tema_personalizado'] ?? '') === 'minimalista' ? 'selected' : ''; ?>>Minimalista</option>
                                        <option value="profissional" <?php echo ($assistencia_virtual['tema_personalizado'] ?? '') === 'profissional' ? 'selected' : ''; ?>>Profissional</option>
                                        <option value="padrao" <?php echo ($assistencia_virtual['tema_personalizado'] ?? '') === 'padrao' ? 'selected' : ''; ?>>Padrão</option>
                                    </select>
                                </div>

                                <div class="col-md-4">
                                    <label for="fonte_personalizada" class="form-label">Fonte</label>
                                    <select class="form-select" id="fonte_personalizada" name="fonte_personalizada">
                                        <option value="Inter" <?php echo ($assistencia_virtual['fonte_personalizada'] ?? 'Inter') === 'Inter' ? 'selected' : ''; ?>>Inter</option>
                                        <option value="Roboto" <?php echo ($assistencia_virtual['fonte_personalizada'] ?? '') === 'Roboto' ? 'selected' : ''; ?>>Roboto</option>
                                        <option value="Open Sans" <?php echo ($assistencia_virtual['fonte_personalizada'] ?? '') === 'Open Sans' ? 'selected' : ''; ?>>Open Sans</option>
                                        <option value="Poppins" <?php echo ($assistencia_virtual['fonte_personalizada'] ?? '') === 'Poppins' ? 'selected' : ''; ?>>Poppins</option>
                                    </select>
                                </div>

                                <div class="col-md-4">
                                    <label class="form-label">Funcionalidades</label>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="chat_habilitado" name="chat_habilitado"
                                               <?php echo ($assistencia_virtual['chat_habilitado'] ?? 1) ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="chat_habilitado">Chat</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="marketplace_habilitado" name="marketplace_habilitado"
                                               <?php echo ($assistencia_virtual['marketplace_habilitado'] ?? 1) ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="marketplace_habilitado">Marketplace</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="retirada_express_habilitada" name="retirada_express_habilitada"
                                               <?php echo ($assistencia_virtual['retirada_express_habilitada'] ?? 1) ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="retirada_express_habilitada">Retirada Express</label>
                                    </div>
                                </div>

                                <div class="col-12">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="ativo" name="ativo"
                                               <?php echo ($assistencia_virtual['ativo'] ?? 1) ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="ativo">
                                            Página ativa (visível para o público)
                                        </label>
                                    </div>
                                </div>
                                
                                <div class="col-12">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>
                                        Salvar Configurações
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
                
                <?php if ($assistencia_virtual): ?>
                <!-- Gerenciar Marketplace -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-store me-2"></i>
                            Gerenciar Marketplace
                        </h5>
                    </div>
                    <div class="card-body">
                        <!-- Adicionar Produto -->
                        <div class="mb-4">
                            <h6>Adicionar Novo Produto</h6>
                            <form method="POST" class="row g-3">
                                <input type="hidden" name="acao" value="gerenciar_marketplace">
                                <input type="hidden" name="marketplace_acao" value="adicionar_produto">

                                <div class="col-md-6">
                                    <label class="form-label">Nome do Produto *</label>
                                    <input type="text" class="form-control" name="produto_nome" required>
                                </div>

                                <div class="col-md-6">
                                    <label class="form-label">Categoria *</label>
                                    <select class="form-select" name="produto_categoria" required>
                                        <option value="">Selecione...</option>
                                        <option value="telas">Telas</option>
                                        <option value="baterias">Baterias</option>
                                        <option value="capas">Capas e Proteções</option>
                                        <option value="carregadores">Carregadores</option>
                                        <option value="fones">Fones de Ouvido</option>
                                        <option value="cabos">Cabos</option>
                                        <option value="suportes">Suportes</option>
                                        <option value="outros">Outros</option>
                                    </select>
                                </div>

                                <div class="col-md-4">
                                    <label class="form-label">Preço (R$) *</label>
                                    <input type="number" class="form-control" name="produto_preco" step="0.01" min="0" required>
                                </div>

                                <div class="col-md-4">
                                    <label class="form-label">Estoque *</label>
                                    <input type="number" class="form-control" name="produto_estoque" min="0" required>
                                </div>

                                <div class="col-md-4">
                                    <label class="form-label">Tipo</label>
                                    <select class="form-select" name="produto_tipo">
                                        <option value="compativel">Compatível</option>
                                        <option value="original">Original</option>
                                    </select>
                                </div>

                                <div class="col-md-6">
                                    <label class="form-label">Marca</label>
                                    <input type="text" class="form-control" name="produto_marca">
                                </div>

                                <div class="col-md-6">
                                    <label class="form-label">URL da Imagem</label>
                                    <input type="url" class="form-control" name="produto_imagem">
                                </div>

                                <div class="col-12">
                                    <label class="form-label">Descrição</label>
                                    <textarea class="form-control" name="produto_descricao" rows="3"></textarea>
                                </div>

                                <div class="col-12">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-plus me-2"></i>
                                        Adicionar Produto
                                    </button>
                                </div>
                            </form>
                        </div>

                        <!-- Lista de Produtos -->
                        <div>
                            <h6>Produtos Cadastrados</h6>
                            <?php
                            // Buscar produtos do marketplace
                            $sql = "
                                SELECT pm.*,
                                       COUNT(cm.id) as itens_carrinho,
                                       SUM(cm.quantidade) as total_carrinho
                                FROM produtos_marketplace pm
                                LEFT JOIN carrinho_marketplace cm ON pm.id = cm.produto_id
                                WHERE pm.assistencia_id = ? AND pm.status = 'ativo'
                                GROUP BY pm.id
                                ORDER BY pm.data_criacao DESC
                            ";
                            $result = $db->query($sql, [$usuario['assistencia_id']]);
                            $produtos_marketplace = [];
                            while ($row = $result->fetch_assoc()) {
                                $produtos_marketplace[] = $row;
                            }
                            ?>

                            <?php if (empty($produtos_marketplace)): ?>
                                <div class="text-center py-4">
                                    <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
                                    <p class="text-muted">Nenhum produto cadastrado ainda.</p>
                                </div>
                            <?php else: ?>
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>Produto</th>
                                                <th>Categoria</th>
                                                <th>Preço</th>
                                                <th>Estoque</th>
                                                <th>Carrinho</th>
                                                <th>Ações</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($produtos_marketplace as $produto): ?>
                                            <tr>
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <?php if ($produto['imagem']): ?>
                                                            <img src="<?php echo htmlspecialchars($produto['imagem']); ?>"
                                                                 alt="<?php echo htmlspecialchars($produto['nome']); ?>"
                                                                 style="width: 40px; height: 40px; object-fit: cover;"
                                                                 class="rounded me-2">
                                                        <?php else: ?>
                                                            <div class="bg-light rounded me-2 d-flex align-items-center justify-content-center"
                                                                 style="width: 40px; height: 40px;">
                                                                <i class="fas fa-image text-muted"></i>
                                                            </div>
                                                        <?php endif; ?>
                                                        <div>
                                                            <div class="fw-medium"><?php echo htmlspecialchars($produto['nome']); ?></div>
                                                            <?php if ($produto['marca']): ?>
                                                                <small class="text-muted"><?php echo htmlspecialchars($produto['marca']); ?></small>
                                                            <?php endif; ?>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td>
                                                    <span class="badge bg-secondary"><?php echo ucfirst($produto['categoria']); ?></span>
                                                    <?php if ($produto['tipo_peca'] === 'original'): ?>
                                                        <span class="badge bg-success ms-1">Original</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>R$ <?php echo number_format($produto['preco'], 2, ',', '.'); ?></td>
                                                <td>
                                                    <span class="badge <?php echo $produto['quantidade_estoque'] > 0 ? 'bg-success' : 'bg-danger'; ?>">
                                                        <?php echo $produto['quantidade_estoque']; ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <?php if ($produto['total_carrinho'] > 0): ?>
                                                        <span class="badge bg-primary"><?php echo $produto['total_carrinho']; ?> itens</span>
                                                    <?php else: ?>
                                                        <span class="text-muted">-</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <div class="btn-group btn-group-sm">
                                                        <button type="button" class="btn btn-outline-primary"
                                                                onclick="editarProduto(<?php echo $produto['id']; ?>)">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                        <form method="POST" style="display: inline;"
                                                              onsubmit="return confirm('Deseja remover este produto?')">
                                                            <input type="hidden" name="acao" value="gerenciar_marketplace">
                                                            <input type="hidden" name="marketplace_acao" value="remover_produto">
                                                            <input type="hidden" name="produto_id" value="<?php echo $produto['id']; ?>">
                                                            <button type="submit" class="btn btn-outline-danger">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        </form>
                                                    </div>
                                                </td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Preview -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-eye me-2"></i>
                            Preview da Página
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="border rounded p-4" style="background: linear-gradient(135deg, <?php echo $assistencia_virtual['cor_primaria']; ?>, <?php echo $assistencia_virtual['cor_secundaria']; ?>);">
                            <div class="text-white text-center">
                                <?php if ($assistencia_virtual['logo_url']): ?>
                                <img src="<?php echo htmlspecialchars($assistencia_virtual['logo_url']); ?>" 
                                     alt="Logo" style="max-height: 60px;" class="mb-3">
                                <?php endif; ?>
                                <h2><?php echo htmlspecialchars($assistencia_virtual['titulo_pagina']); ?></h2>
                                <?php if ($assistencia_virtual['descricao']): ?>
                                <p class="opacity-75"><?php echo htmlspecialchars($assistencia_virtual['descricao']); ?></p>
                                <?php endif; ?>
                                <button class="btn btn-light">Solicitar Reparo</button>
                            </div>
                        </div>
                        
                        <div class="mt-3 text-center">
                            <div class="btn-group" role="group">
                                <a href="https://fixfacilassistencia.com.br/virtual/<?php echo htmlspecialchars($assistencia_virtual['slug_personalizado']); ?>.php"
                                   target="_blank" class="btn btn-outline-primary">
                                    <i class="fas fa-external-link-alt me-2"></i>
                                    Ver Página
                                </a>
                                <a href="https://fixfacilassistencia.com.br/virtual/index.php?slug=<?php echo htmlspecialchars($assistencia_virtual['slug_personalizado']); ?>"
                                   target="_blank" class="btn btn-outline-secondary">
                                    <i class="fas fa-link me-2"></i>
                                    URL Alternativa
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            </div>
            
            <!-- Sidebar -->
            <div class="col-lg-4">
                <?php if ($assistencia_virtual): ?>
                <!-- Estatísticas -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-chart-bar me-2"></i>
                            Estatísticas
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-3 text-center">
                            <div class="col-4">
                                <h4 class="text-primary"><?php echo number_format($stats['total_acessos']); ?></h4>
                                <small class="text-muted">Total</small>
                            </div>
                            <div class="col-4">
                                <h4 class="text-success"><?php echo number_format($stats['acessos_hoje']); ?></h4>
                                <small class="text-muted">Hoje</small>
                            </div>
                            <div class="col-4">
                                <h4 class="text-info"><?php echo number_format($stats['acessos_semana']); ?></h4>
                                <small class="text-muted">7 dias</small>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Link de Compartilhamento -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-share-alt me-2"></i>
                            Compartilhar
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label">Seu Link Personalizado:</label>
                            <div class="input-group">
                                <input type="text" class="form-control"
                                       value="https://fixfacilassistencia.com.br/virtual/<?php echo htmlspecialchars($assistencia_virtual['slug_personalizado']); ?>.php"
                                       id="linkCompartilhar" readonly>
                                <button class="btn btn-outline-secondary" onclick="copiarLink()">
                                    <i class="fas fa-copy"></i>
                                </button>
                            </div>
                        </div>
                        
                        <div class="d-grid gap-2">
                            <button class="btn btn-success btn-sm" onclick="compartilharWhatsApp()">
                                <i class="fab fa-whatsapp me-2"></i>
                                WhatsApp
                            </button>
                            <button class="btn btn-primary btn-sm" onclick="compartilharFacebook()">
                                <i class="fab fa-facebook me-2"></i>
                                Facebook
                            </button>
                            <button class="btn btn-info btn-sm" onclick="compartilharTwitter()">
                                <i class="fab fa-twitter me-2"></i>
                                Twitter
                            </button>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
                
                <!-- Benefícios Master -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-crown me-2"></i>
                            Benefícios Master
                        </h5>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled">
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                Link personalizado
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                Página customizável
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                Cores personalizadas
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                Logo próprio
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                Estatísticas detalhadas
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                SEO otimizado
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </main>
</div>

<?php 
$extraJS = "
<script>
function copiarLink() {
    const input = document.getElementById('linkCompartilhar');
    input.select();
    document.execCommand('copy');
    
    // Feedback visual
    const btn = event.target.closest('button');
    const originalHTML = btn.innerHTML;
    btn.innerHTML = '<i class=\"fas fa-check\"></i>';
    btn.classList.add('btn-success');
    btn.classList.remove('btn-outline-secondary');
    
    setTimeout(() => {
        btn.innerHTML = originalHTML;
        btn.classList.remove('btn-success');
        btn.classList.add('btn-outline-secondary');
    }, 2000);
}

function compartilharWhatsApp() {
    const link = document.getElementById('linkCompartilhar').value;
    const texto = 'Confira minha assistência técnica: ' + link;
    window.open('https://wa.me/?text=' + encodeURIComponent(texto));
}

function compartilharFacebook() {
    const link = document.getElementById('linkCompartilhar').value;
    window.open('https://www.facebook.com/sharer/sharer.php?u=' + encodeURIComponent(link));
}

function compartilharTwitter() {
    const link = document.getElementById('linkCompartilhar').value;
    const texto = 'Confira minha assistência técnica';
    window.open('https://twitter.com/intent/tweet?text=' + encodeURIComponent(texto) + '&url=' + encodeURIComponent(link));
}

// Validação do slug em tempo real
document.getElementById('slug_personalizado').addEventListener('input', function(e) {
    let value = e.target.value.toLowerCase();
    value = value.replace(/[^a-z0-9-]/g, '');
    e.target.value = value;
});
</script>
";

$layout->renderFooter($extraJS); 
?>
