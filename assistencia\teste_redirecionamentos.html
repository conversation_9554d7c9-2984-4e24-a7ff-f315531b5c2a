<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste de Redirecionamentos - FixFácil</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            padding: 20px;
            color: #1e293b;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            padding: 30px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }

        h1 {
            color: #059669;
            margin-bottom: 20px;
            text-align: center;
        }

        .test-section {
            background: #f8fafc;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .test-section h2 {
            color: #1e293b;
            margin-bottom: 15px;
            font-size: 18px;
        }

        .test-links {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 12px;
        }

        .test-link {
            background: #059669;
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.2s ease;
            text-align: center;
        }

        .test-link:hover {
            background: #065f46;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(5, 150, 105, 0.3);
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-ok {
            background: #059669;
        }

        .status-redirect {
            background: #f59e0b;
        }

        .description {
            background: #e0f2fe;
            border-left: 4px solid #0284c7;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 0 8px 8px 0;
        }

        .redirect-info {
            background: #fef3c7;
            border-left: 4px solid #f59e0b;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 0 8px 8px 0;
        }

        .redirect-info h3 {
            color: #d97706;
            margin-bottom: 8px;
        }

        .redirect-list {
            list-style: none;
            padding-left: 0;
        }

        .redirect-list li {
            padding: 8px 0;
            border-bottom: 1px solid #e2e8f0;
        }

        .redirect-list li:last-child {
            border-bottom: none;
        }

        .arrow {
            color: #059669;
            font-weight: bold;
            margin: 0 10px;
        }

        @media (max-width: 600px) {
            .container {
                padding: 20px;
            }
            
            .test-links {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Teste de Redirecionamentos - Sistema Mobile-First</h1>
        
        <div class="description">
            <p><strong>Objetivo:</strong> Verificar se todos os redirecionamentos para as versões mobile-first estão funcionando corretamente.</p>
        </div>

        <div class="redirect-info">
            <h3>📋 Redirecionamentos Configurados</h3>
            <ul class="redirect-list">
                <li>
                    <span class="status-indicator status-redirect"></span>
                    <code>solicitacoes.php</code>
                    <span class="arrow">→</span>
                    <code>solicitacoes_mobile.php</code>
                </li>
                <li>
                    <span class="status-indicator status-redirect"></span>
                    <code>reparos.php</code>
                    <span class="arrow">→</span>
                    <code>reparos_new.php</code>
                </li>
                <li>
                    <span class="status-indicator status-redirect"></span>
                    <code>perfil.php</code>
                    <span class="arrow">→</span>
                    <code>perfil_new.php</code>
                </li>
                <li>
                    <span class="status-indicator status-redirect"></span>
                    <code>detalhes_solicitacao.php</code>
                    <span class="arrow">→</span>
                    <code>detalhes_solicitacao_new.php</code>
                </li>
                <li>
                    <span class="status-indicator status-ok"></span>
                    <code>dashboard.php</code>
                    <span class="arrow">→</span>
                    <code>Já responsivo</code>
                </li>
            </ul>
        </div>

        <div class="test-section">
            <h2>🏠 Página Principal</h2>
            <div class="test-links">
                <a href="index.php" class="test-link">
                    <span class="status-indicator status-ok"></span>
                    Index (Entry Point)
                </a>
                <a href="dashboard.php" class="test-link">
                    <span class="status-indicator status-ok"></span>
                    Dashboard
                </a>
            </div>
        </div>

        <div class="test-section">
            <h2>📋 Páginas com Redirecionamento</h2>
            <div class="test-links">
                <a href="solicitacoes.php" class="test-link">
                    <span class="status-indicator status-redirect"></span>
                    Solicitações (Redirect)
                </a>
                <a href="reparos.php" class="test-link">
                    <span class="status-indicator status-redirect"></span>
                    Reparos (Redirect)
                </a>
                <a href="perfil.php" class="test-link">
                    <span class="status-indicator status-redirect"></span>
                    Perfil (Redirect)
                </a>
                <a href="detalhes_solicitacao.php?id=1" class="test-link">
                    <span class="status-indicator status-redirect"></span>
                    Detalhes Solicitação (Redirect)
                </a>
            </div>
        </div>

        <div class="test-section">
            <h2>🎯 Páginas Mobile-First (Diretas)</h2>
            <div class="test-links">
                <a href="solicitacoes_mobile.php" class="test-link">
                    <span class="status-indicator status-ok"></span>
                    Solicitações Mobile
                </a>
                <a href="reparos_new.php" class="test-link">
                    <span class="status-indicator status-ok"></span>
                    Reparos New
                </a>
                <a href="perfil_new.php" class="test-link">
                    <span class="status-indicator status-ok"></span>
                    Perfil New
                </a>
                <a href="detalhes_solicitacao_new.php?id=1" class="test-link">
                    <span class="status-indicator status-ok"></span>
                    Detalhes New
                </a>
            </div>
        </div>

        <div class="test-section">
            <h2>🔧 Páginas de Apoio</h2>
            <div class="test-links">
                <a href="marketplace.php" class="test-link">
                    <span class="status-indicator status-ok"></span>
                    Marketplace
                </a>
                <a href="chat.php" class="test-link">
                    <span class="status-indicator status-ok"></span>
                    Chat
                </a>
                <a href="logout.php" class="test-link">
                    <span class="status-indicator status-ok"></span>
                    Logout
                </a>
            </div>
        </div>

        <div class="test-section">
            <h2>🧪 Testes Especiais</h2>
            <div class="test-links">
                <a href="teste_modal_proposta.html" class="test-link">
                    <span class="status-indicator status-ok"></span>
                    Teste Modal Proposta
                </a>
                <a href="ajax/enviar_proposta.php" class="test-link">
                    <span class="status-indicator status-ok"></span>
                    AJAX Endpoint
                </a>
            </div>
        </div>

        <div class="description">
            <p><strong>Instruções:</strong></p>
            <ul style="margin-left: 20px; margin-top: 10px;">
                <li>🟢 <strong>Status OK:</strong> Página funcional ou já responsiva</li>
                <li>🟡 <strong>Status Redirect:</strong> Página redireciona para versão mobile-first</li>
                <li>Teste cada link para verificar se os redirecionamentos estão funcionando</li>
                <li>Verifique se o layout está responsivo em diferentes tamanhos de tela</li>
            </ul>
        </div>
    </div>

    <script>
        // Adicionar indicadores visuais aos links
        document.addEventListener('DOMContentLoaded', function() {
            const links = document.querySelectorAll('.test-link');
            
            links.forEach(link => {
                link.addEventListener('click', function(e) {
                    const originalText = this.textContent;
                    this.textContent = '🔄 Testando...';
                    
                    setTimeout(() => {
                        this.textContent = originalText;
                    }, 1000);
                });
            });
        });
    </script>
</body>
</html>
