<?php
session_start();
require_once 'db.php';

if (!isset($_SESSION['user_id']) || $_SESSION['nivel_acesso'] != 'administrador') {
    header("Location: login.php");
    exit();
}

if(isset($_GET['logout']) && $_GET['logout'] == 'true') {
    session_destroy();
    header("Location: login.php");
    exit();
}

// Verificar se o pedido foi confirmado
if ($_SERVER["REQUEST_METHOD"] == "GET" && isset($_GET['confirmar_compra'])) {
    $compra_id = $_GET['confirmar_compra'];
    $query = "UPDATE compras SET status_pedido = 'confirmado' WHERE id = :compra_id AND status_pedido = 'pendente'";
    $stmt = $pdo->prepare($query);
    $stmt->bindParam(':compra_id', $compra_id);
    $stmt->execute();
}

// Excluir pedido
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['excluir_pedido'])) {
    $compra_id = $_POST['compra_id'];
    $query = "DELETE FROM compras WHERE id = :compra_id";
    $stmt = $pdo->prepare($query);
    $stmt->bindParam(':compra_id', $compra_id);
    $stmt->execute();
}

// Recuperar todos os pedidos
$query = "SELECT compras.*, produtos.nome AS produto_nome, produtos.preco AS produto_preco, usuarios.nome AS fornecedor_nome
          FROM compras
          JOIN produtos ON compras.produto_id = produtos.id
          JOIN usuarios ON compras.fornecedor_id = usuarios.id";
$stmt = $pdo->query($query);
$compras = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <title>Administrador - Pedidos</title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap4.min.css">
</head>
<style>
        body {
            background-image: url('https://img.freepik.com/vetores-gratis/vetor-de-fundo-de-padrao-geometrico-branco-e-cinza_53876-136510.jpg?size=626&ext=jpg&ga=GA1.1.735520172.1712102400&semt=sph');
            background-size: cover;
            background-position: center;
            background-attachment: fixed;
            background-repeat: no-repeat;
            opacity: 0.9; /* Ajuste a opacidade conforme necessário */
            filter: alpha(opacity=80); /* Para navegadores antigos */
        }
    </style>

<body>
<nav class="navbar navbar-expand-lg navbar-light bg-light">
        <a class="navbar-brand" href="#">Administrador</a>
        <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarSupportedContent" aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>

        <div class="collapse navbar-collapse" id="navbarSupportedContent">
            <ul class="navbar-nav mr-auto">
                <li class="nav-item active">
                    <a class="nav-link" href="admin_dashboard.php">Home <span class="sr-only">(current)</span></a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="admin_produto.php">Produtos</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="admin_pedidos.php">Pedidos</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="admin_usuario.php">Administrar Usuários</a>
                </li>
                <li class="nav-item">
                <a class="nav-link" href="admin_acesso.php">Acessos</a>
            </li>
            
            </ul>
            <form class="form-inline my-2 my-lg-0">
                <input class="form-control mr-sm-2" type="search" placeholder="Search" aria-label="Search">
                <button class="btn btn-outline-success my-2 my-sm-0" type="submit">Search</button>
            </form>
            <ul class="navbar-nav ml-auto">
                <li class="nav-item">
                    <a class="nav-link" href="?logout=true">Logout</a>
                </li>
            </ul>
        </div>
    </nav>

    <div class="container mt-5">
        <h2>Pedidos</h2>
        <table id="pedidos-table" class="table table-striped table-bordered" style="width:100%">
            <thead>
                <tr>
                    <th>#</th>
                    <th>Produto</th>
                    <th>Fornecedor</th>
                    <th>Quantidade</th>
                    <th>Preço Unitário</th>
                    <th>Total</th>
                    <th>Observação</th>
                    <th>Status</th>
                    <th>Ações</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($compras as $key => $compra): ?>
                    <tr>
                        <td><?php echo $compra['id']; ?></td>
                        <td><?php echo $compra['produto_nome']; ?></td>
                        <td><?php echo $compra['fornecedor_nome']; ?></td>
                        <td><?php echo $compra['quantidade']; ?></td>
                        <td><?php echo 'R$ ' . number_format($compra['produto_preco'], 2, ',', '.'); ?></td>
                        <td><?php echo 'R$ ' . number_format($compra['quantidade'] * $compra['produto_preco'], 2, ',', '.'); ?></td>
                        <td><?php echo $compra['observacao']; ?></td>
                        <td><?php echo $compra['status_pedido']; ?></td>
                        <td>
                            <?php if ($compra['status_pedido'] == 'pendente'): ?>
                                <a href="?confirmar_compra=<?php echo $compra['id']; ?>" class="btn btn-sm btn-success">Confirmar</a>
                                <!-- Formulário para excluir pedido -->
                                <form method="post" action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>" class="d-inline">
                                    <input type="hidden" name="compra_id" value="<?php echo $compra['id']; ?>">
                                    <button type="submit" class="btn btn-sm btn-danger" name="excluir_pedido">Excluir</button>
                                </form>
                            <?php endif; ?>
                        </td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap4.min.js"></script>
    <script>
        $(document).ready(function() {
            $('#pedidos-table').DataTable();
        });
    </script>
</body>
</html>
