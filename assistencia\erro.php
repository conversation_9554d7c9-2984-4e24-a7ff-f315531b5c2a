<?php
/**
 * Página de Erro - Sistema de Assistência
 * FixFácil Assistências
 */

// Verificar se há uma mensagem de erro
$erro = $_GET['erro'] ?? 'Erro desconhecido';
$codigo = $_GET['codigo'] ?? '500';

// Limpar output buffer se necessário
if (ob_get_level()) {
    ob_end_clean();
}

// Definir header apropriado
http_response_code(intval($codigo));
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Erro - FixFácil Assistências</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
        }

        .error-container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            max-width: 500px;
            width: 90%;
            text-align: center;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }

        .error-icon {
            font-size: 64px;
            margin-bottom: 20px;
        }

        .error-title {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 12px;
            color: #1e293b;
        }

        .error-message {
            font-size: 16px;
            color: #64748b;
            margin-bottom: 30px;
            line-height: 1.6;
        }

        .error-code {
            background: #f1f5f9;
            color: #475569;
            padding: 8px 16px;
            border-radius: 8px;
            font-family: monospace;
            font-size: 14px;
            margin-bottom: 30px;
            display: inline-block;
        }

        .btn {
            background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            transition: all 0.2s ease;
            margin: 0 8px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
        }

        .btn-secondary {
            background: #f1f5f9;
            color: #475569;
        }

        .btn-secondary:hover {
            background: #e2e8f0;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }

        .suggestions {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e2e8f0;
        }

        .suggestions h3 {
            font-size: 18px;
            margin-bottom: 15px;
            color: #1e293b;
        }

        .suggestions ul {
            list-style: none;
            text-align: left;
        }

        .suggestions li {
            padding: 8px 0;
            color: #64748b;
            font-size: 14px;
        }

        .suggestions li:before {
            content: "💡";
            margin-right: 8px;
        }

        @media (max-width: 768px) {
            .error-container {
                padding: 30px 20px;
            }
            
            .error-title {
                font-size: 24px;
            }
            
            .btn {
                display: block;
                margin: 8px 0;
            }
        }
    </style>
</head>
<body>
    <div class="error-container">
        <div class="error-icon">⚠️</div>
        <h1 class="error-title">Ops! Algo deu errado</h1>
        <p class="error-message">
            <?php echo htmlspecialchars($erro); ?>
        </p>
        <div class="error-code">
            Código: <?php echo htmlspecialchars($codigo); ?>
        </div>
        
        <div>
            <a href="dashboard_new.php" class="btn">
                🏠 Voltar ao Dashboard
            </a>
            <a href="javascript:history.back()" class="btn btn-secondary">
                ← Página Anterior
            </a>
        </div>
        
        <div class="suggestions">
            <h3>Sugestões:</h3>
            <ul>
                <li>Verifique sua conexão com a internet</li>
                <li>Tente atualizar a página (F5)</li>
                <li>Limpe o cache do navegador</li>
                <li>Se o problema persistir, contate o suporte</li>
            </ul>
        </div>
    </div>

    <script>
        // Auto-redirect após 10 segundos se for erro de timeout
        <?php if ($codigo === '500' || $codigo === '503'): ?>
        setTimeout(function() {
            window.location.href = 'dashboard_new.php';
        }, 10000);
        <?php endif; ?>
    </script>
</body>
</html>
