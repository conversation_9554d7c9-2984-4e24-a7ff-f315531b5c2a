<?php
/**
 * Dashboard Administrativo
 * FixFácil Assistências - Sistema Novo
 */

require_once '../config/auth.php';
require_once '../config/database.php';
require_once '../sistema/monitor_sistema.php';
require_once '../sistema/backup_automatico.php';

// Verificar autenticação e permissão de admin
$auth = getAuth();
$auth->checkAssistenciaAuth();

$usuario = $auth->getUsuarioLogado();

// Verificar se é admin (você pode implementar um sistema de roles)
if ($usuario['email'] !== '<EMAIL>') {
    header('Location: ../dashboard.php');
    exit();
}

$db = getDatabase();
$monitor = new MonitorSistema();
$backup = new BackupAutomatico();

// Obter estatísticas do sistema
$periodo = $_GET['periodo'] ?? '24h';
$stats = $monitor->obterEstatisticas($periodo);
$saude = $monitor->verificarSaudeSystem();
$backups = $backup->listarBackups();

// Estatísticas gerais
$stats_gerais = [];
try {
    // Total de assistências
    $result = $db->query("SELECT COUNT(*) as total FROM assistencias_tecnicas");
    $stats_gerais['total_assistencias'] = $result->fetch_assoc()['total'];
    
    // Total de usuários
    $result = $db->query("SELECT COUNT(*) as total FROM usuarios WHERE tipo_usuario = 'assistencia'");
    $stats_gerais['total_usuarios'] = $result->fetch_assoc()['total'];
    
    // Solicitações hoje
    $result = $db->query("SELECT COUNT(*) as total FROM solicitacoes_reparo WHERE DATE(data_solicitacao) = CURDATE()");
    $stats_gerais['solicitacoes_hoje'] = $result->fetch_assoc()['total'];
    
    // Propostas hoje
    $result = $db->query("SELECT COUNT(*) as total FROM propostas_assistencia WHERE DATE(data_proposta) = CURDATE()");
    $stats_gerais['propostas_hoje'] = $result->fetch_assoc()['total'];
    
    // Receita total do mês
    $result = $db->query("
        SELECT COALESCE(SUM(preco * (taxa_servico/100)), 0) as receita_fixfacil
        FROM propostas_assistencia pa
        JOIN assinaturas_assistencias aa ON pa.assistencia_id = aa.assistencia_id
        JOIN planos p ON aa.plano_id = p.id
        WHERE pa.status = 'Concluída' AND pa.pago = 1
        AND MONTH(pa.data_conclusao) = MONTH(CURDATE())
    ");
    $stats_gerais['receita_mes'] = $result->fetch_assoc()['receita_fixfacil'];
    
} catch (Exception $e) {
    $stats_gerais = [
        'total_assistencias' => 0,
        'total_usuarios' => 0,
        'solicitacoes_hoje' => 0,
        'propostas_hoje' => 0,
        'receita_mes' => 0
    ];
}

require_once '../includes/layout.php';
$layout = new Layout();
?>

<?php $layout->renderHead("Admin Dashboard - FixFácil Assistências", "
<style>
.admin-header {
    background: linear-gradient(135deg, #dc3545, #fd7e14);
    color: white;
    padding: 1rem;
    border-radius: 1rem;
    margin-bottom: 2rem;
}

.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 8px;
}

.status-ok { background: #28a745; }
.status-alerta { background: #ffc107; }
.status-erro { background: #dc3545; }

.metric-card {
    background: white;
    border-radius: 1rem;
    padding: 1.5rem;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    transition: transform 0.2s;
}

.metric-card:hover {
    transform: translateY(-2px);
}

.log-table {
    font-size: 0.9rem;
}

.admin-nav {
    background: rgba(255,255,255,0.1);
    backdrop-filter: blur(10px);
    border-radius: 1rem;
    padding: 1rem;
    margin-bottom: 2rem;
}

.admin-nav .nav-link {
    color: white;
    border-radius: 0.5rem;
    margin: 0 0.25rem;
}

.admin-nav .nav-link.active {
    background: rgba(255,255,255,0.2);
}
</style>
"); ?>

<div class="main-wrapper">
    <?php $layout->renderSidebar('admin'); ?>
    
    <main class="main-content">
        <!-- Header Admin -->
        <div class="admin-header">
            <div class="d-flex align-items-center justify-content-between">
                <div>
                    <h1 class="mb-1">
                        <i class="fas fa-shield-alt me-3"></i>
                        Painel Administrativo
                    </h1>
                    <p class="mb-0 opacity-75">Monitoramento e gestão do sistema FixFácil</p>
                </div>
                <div class="text-end">
                    <div class="d-flex align-items-center">
                        <span class="status-indicator status-<?php echo $saude['status'] === 'ok' ? 'ok' : ($saude['status'] === 'alerta' ? 'alerta' : 'erro'); ?>"></span>
                        <span>Sistema <?php echo ucfirst($saude['status']); ?></span>
                    </div>
                    <small class="opacity-75">Última verificação: <?php echo date('H:i:s'); ?></small>
                </div>
            </div>
        </div>
        
        <!-- Navegação Admin -->
        <div class="admin-nav">
            <ul class="nav nav-pills">
                <li class="nav-item">
                    <a class="nav-link active" href="#overview" data-bs-toggle="pill">
                        <i class="fas fa-chart-line me-2"></i>Visão Geral
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#logs" data-bs-toggle="pill">
                        <i class="fas fa-list-alt me-2"></i>Logs
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#backups" data-bs-toggle="pill">
                        <i class="fas fa-database me-2"></i>Backups
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#sistema" data-bs-toggle="pill">
                        <i class="fas fa-cog me-2"></i>Sistema
                    </a>
                </li>
            </ul>
        </div>
        
        <div class="tab-content">
            <!-- Visão Geral -->
            <div class="tab-pane fade show active" id="overview">
                <!-- Estatísticas Gerais -->
                <div class="row g-4 mb-4">
                    <div class="col-lg-3 col-md-6">
                        <div class="metric-card text-center">
                            <div class="bg-primary bg-opacity-10 p-3 rounded-circle d-inline-flex mb-3">
                                <i class="fas fa-users text-primary fs-3"></i>
                            </div>
                            <h3 class="text-primary"><?php echo number_format($stats_gerais['total_assistencias']); ?></h3>
                            <p class="text-muted mb-0">Assistências Ativas</p>
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-6">
                        <div class="metric-card text-center">
                            <div class="bg-success bg-opacity-10 p-3 rounded-circle d-inline-flex mb-3">
                                <i class="fas fa-inbox text-success fs-3"></i>
                            </div>
                            <h3 class="text-success"><?php echo number_format($stats_gerais['solicitacoes_hoje']); ?></h3>
                            <p class="text-muted mb-0">Solicitações Hoje</p>
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-6">
                        <div class="metric-card text-center">
                            <div class="bg-info bg-opacity-10 p-3 rounded-circle d-inline-flex mb-3">
                                <i class="fas fa-paper-plane text-info fs-3"></i>
                            </div>
                            <h3 class="text-info"><?php echo number_format($stats_gerais['propostas_hoje']); ?></h3>
                            <p class="text-muted mb-0">Propostas Hoje</p>
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-6">
                        <div class="metric-card text-center">
                            <div class="bg-warning bg-opacity-10 p-3 rounded-circle d-inline-flex mb-3">
                                <i class="fas fa-dollar-sign text-warning fs-3"></i>
                            </div>
                            <h3 class="text-warning">R$ <?php echo number_format($stats_gerais['receita_mes'], 0, ',', '.'); ?></h3>
                            <p class="text-muted mb-0">Receita FixFácil (Mês)</p>
                        </div>
                    </div>
                </div>
                
                <!-- Saúde do Sistema -->
                <div class="row g-4 mb-4">
                    <div class="col-lg-8">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-heartbeat me-2"></i>
                                    Saúde do Sistema
                                </h5>
                            </div>
                            <div class="card-body">
                                <?php if (!empty($saude['problemas'])): ?>
                                <div class="alert alert-warning">
                                    <h6><i class="fas fa-exclamation-triangle me-2"></i>Problemas Detectados:</h6>
                                    <ul class="mb-0">
                                        <?php foreach ($saude['problemas'] as $problema): ?>
                                        <li><?php echo htmlspecialchars($problema); ?></li>
                                        <?php endforeach; ?>
                                    </ul>
                                </div>
                                <?php endif; ?>
                                
                                <div class="row g-3">
                                    <div class="col-md-3">
                                        <div class="text-center">
                                            <div class="status-indicator status-<?php echo $saude['metricas']['banco'] === 'ok' ? 'ok' : 'erro'; ?> mx-auto mb-2"></div>
                                            <h6>Banco de Dados</h6>
                                            <small class="text-muted"><?php echo ucfirst($saude['metricas']['banco']); ?></small>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-3">
                                        <div class="text-center">
                                            <div class="status-indicator status-<?php echo $saude['metricas']['disco']['percentual_usado'] > 90 ? 'erro' : ($saude['metricas']['disco']['percentual_usado'] > 80 ? 'alerta' : 'ok'); ?> mx-auto mb-2"></div>
                                            <h6>Espaço em Disco</h6>
                                            <small class="text-muted"><?php echo $saude['metricas']['disco']['percentual_usado']; ?>% usado</small>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-3">
                                        <div class="text-center">
                                            <div class="status-indicator status-<?php echo $saude['metricas']['memoria']['percentual'] > 80 ? 'alerta' : 'ok'; ?> mx-auto mb-2"></div>
                                            <h6>Memória</h6>
                                            <small class="text-muted"><?php echo $saude['metricas']['memoria']['percentual']; ?>% usada</small>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-3">
                                        <div class="text-center">
                                            <div class="status-indicator status-<?php echo $saude['metricas']['performance']['tempo_medio'] > 2 ? 'alerta' : 'ok'; ?> mx-auto mb-2"></div>
                                            <h6>Performance</h6>
                                            <small class="text-muted"><?php echo $saude['metricas']['performance']['tempo_medio']; ?>s médio</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-chart-pie me-2"></i>
                                    Atividades (<?php echo $periodo; ?>)
                                </h5>
                            </div>
                            <div class="card-body">
                                <?php if (!empty($stats['atividades'])): ?>
                                <?php foreach (array_slice($stats['atividades'], 0, 5) as $atividade): ?>
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <span><?php echo ucfirst(str_replace('_', ' ', $atividade['tipo'])); ?></span>
                                    <span class="badge bg-primary"><?php echo $atividade['quantidade']; ?></span>
                                </div>
                                <?php endforeach; ?>
                                <?php else: ?>
                                <p class="text-muted text-center">Nenhuma atividade registrada</p>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Logs -->
            <div class="tab-pane fade" id="logs">
                <div class="row g-4">
                    <div class="col-lg-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    Erros Recentes
                                </h5>
                            </div>
                            <div class="card-body">
                                <?php if (!empty($stats['erros'])): ?>
                                <div class="table-responsive">
                                    <table class="table table-sm log-table">
                                        <thead>
                                            <tr>
                                                <th>Tipo</th>
                                                <th>Quantidade</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($stats['erros'] as $erro): ?>
                                            <tr>
                                                <td><?php echo htmlspecialchars($erro['tipo']); ?></td>
                                                <td><span class="badge bg-danger"><?php echo $erro['quantidade']; ?></span></td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                                <?php else: ?>
                                <p class="text-muted text-center">Nenhum erro registrado</p>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-users me-2"></i>
                                    Usuários Mais Ativos
                                </h5>
                            </div>
                            <div class="card-body">
                                <?php if (!empty($stats['usuarios_ativos'])): ?>
                                <div class="table-responsive">
                                    <table class="table table-sm log-table">
                                        <thead>
                                            <tr>
                                                <th>Usuário</th>
                                                <th>Atividades</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($stats['usuarios_ativos'] as $usuario_ativo): ?>
                                            <tr>
                                                <td><?php echo htmlspecialchars($usuario_ativo['nome']); ?></td>
                                                <td><span class="badge bg-info"><?php echo $usuario_ativo['atividades']; ?></span></td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                                <?php else: ?>
                                <p class="text-muted text-center">Nenhuma atividade registrada</p>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Backups -->
            <div class="tab-pane fade" id="backups">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-database me-2"></i>
                            Backups do Sistema
                        </h5>
                        <button type="button" class="btn btn-primary" onclick="executarBackup()">
                            <i class="fas fa-plus me-2"></i>
                            Criar Backup
                        </button>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($backups)): ?>
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>Arquivo</th>
                                        <th>Tamanho</th>
                                        <th>Tipo</th>
                                        <th>Data</th>
                                        <th>Status</th>
                                        <th>Ações</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($backups as $backup_item): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($backup_item['nome_arquivo']); ?></td>
                                        <td><?php echo $backup_item['tamanho_formatado']; ?></td>
                                        <td>
                                            <span class="badge bg-<?php echo $backup_item['tipo'] === 'automatico' ? 'info' : 'warning'; ?>">
                                                <?php echo ucfirst($backup_item['tipo']); ?>
                                            </span>
                                        </td>
                                        <td><?php echo date('d/m/Y H:i', strtotime($backup_item['data_criacao'])); ?></td>
                                        <td>
                                            <span class="badge bg-<?php echo $backup_item['status'] === 'concluido' ? 'success' : 'danger'; ?>">
                                                <?php echo ucfirst($backup_item['status']); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <button type="button" class="btn btn-sm btn-outline-primary" 
                                                    onclick="downloadBackup('<?php echo $backup_item['nome_arquivo']; ?>')">
                                                <i class="fas fa-download"></i>
                                            </button>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        <?php else: ?>
                        <p class="text-muted text-center">Nenhum backup encontrado</p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            
            <!-- Sistema -->
            <div class="tab-pane fade" id="sistema">
                <div class="row g-4">
                    <div class="col-lg-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-broom me-2"></i>
                                    Manutenção
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="d-grid gap-2">
                                    <button type="button" class="btn btn-outline-warning" onclick="limparLogs()">
                                        <i class="fas fa-trash me-2"></i>
                                        Limpar Logs Antigos
                                    </button>
                                    <button type="button" class="btn btn-outline-info" onclick="otimizarBanco()">
                                        <i class="fas fa-database me-2"></i>
                                        Otimizar Banco de Dados
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary" onclick="limparCache()">
                                        <i class="fas fa-sync me-2"></i>
                                        Limpar Cache
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-info-circle me-2"></i>
                                    Informações do Sistema
                                </h5>
                            </div>
                            <div class="card-body">
                                <table class="table table-sm">
                                    <tr>
                                        <td><strong>Versão PHP:</strong></td>
                                        <td><?php echo PHP_VERSION; ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Versão MySQL:</strong></td>
                                        <td><?php echo $db->getConnection()->server_info; ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Limite de Memória:</strong></td>
                                        <td><?php echo ini_get('memory_limit'); ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Tempo Máximo:</strong></td>
                                        <td><?php echo ini_get('max_execution_time'); ?>s</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Upload Máximo:</strong></td>
                                        <td><?php echo ini_get('upload_max_filesize'); ?></td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>
</div>

<?php 
$extraJS = "
<script>
function executarBackup() {
    if (confirm('Deseja criar um novo backup? Este processo pode demorar alguns minutos.')) {
        fetch('../ajax/executar_backup.php', {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Backup criado com sucesso!');
                location.reload();
            } else {
                alert('Erro ao criar backup: ' + data.message);
            }
        });
    }
}

function downloadBackup(arquivo) {
    window.open('../sistema/download_backup.php?arquivo=' + encodeURIComponent(arquivo));
}

function limparLogs() {
    if (confirm('Deseja limpar logs antigos (mais de 30 dias)?')) {
        fetch('../ajax/limpar_logs.php', {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            alert(data.message);
            if (data.success) location.reload();
        });
    }
}

function otimizarBanco() {
    if (confirm('Deseja otimizar o banco de dados?')) {
        fetch('../ajax/otimizar_banco.php', {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            alert(data.message);
        });
    }
}

function limparCache() {
    if (confirm('Deseja limpar o cache do sistema?')) {
        fetch('../ajax/limpar_cache.php', {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            alert(data.message);
        });
    }
}

// Auto-refresh a cada 30 segundos
setInterval(function() {
    location.reload();
}, 30000);
</script>
";

$layout->renderFooter($extraJS); 
?>
