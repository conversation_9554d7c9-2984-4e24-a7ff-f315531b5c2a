<?php
/**
 * Verificação rápida do sistema
 */

require_once 'config.php';

echo "<h1>Status do Sistema</h1>";

// Verificar se o usuário está logado
if (isset($_SESSION['usuario_id'])) {
    echo "<p>✓ Usuário logado: " . $_SESSION['nome'] . " (ID: " . $_SESSION['usuario_id'] . ")</p>";
    echo "<p>✓ Tipo: " . $_SESSION['tipo_usuario'] . "</p>";
    
    if (isset($_SESSION['assistencia_id'])) {
        echo "<p>✓ Assistência ID: " . $_SESSION['assistencia_id'] . "</p>";
    } else {
        echo "<p>❌ Assistência ID não definido</p>";
    }
    
    echo "<p><a href='propostas.php'>Ir para Propostas</a></p>";
} else {
    echo "<p>❌ Usuário não logado</p>";
    echo "<p><a href='login_assistencia.php'><PERSON><PERSON><PERSON></a></p>";
}

echo "<p><a href='teste_propostas.php'>Teste Completo</a></p>";
?>
