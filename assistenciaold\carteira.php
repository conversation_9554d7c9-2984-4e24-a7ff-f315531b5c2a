<?php
/**
 * <PERSON><PERSON><PERSON><PERSON> de Carteira
 * FixFácil Assistências - Sistema Novo
 */

require_once 'config/auth.php';
require_once 'config/database.php';
require_once 'includes/layout.php';

// Verificar autenticação
$auth = getAuth();
$auth->checkAssistenciaAuth();

// Obter dados do usuário
$usuario = $auth->getUsuarioLogado();
$plano = $auth->getPlanoInfo($usuario['id']);
$db = getDatabase();

// Filtros
$periodo = $_GET['periodo'] ?? 'mes_atual';
$ano = $_GET['ano'] ?? date('Y');
$mes = $_GET['mes'] ?? date('m');

// Calcular datas baseado no período
$data_inicio = '';
$data_fim = '';

switch ($periodo) {
    case 'mes_atual':
        $data_inicio = date('Y-m-01');
        $data_fim = date('Y-m-t');
        break;
    case 'mes_anterior':
        $data_inicio = date('Y-m-01', strtotime('first day of last month'));
        $data_fim = date('Y-m-t', strtotime('last day of last month'));
        break;
    case 'ano_atual':
        $data_inicio = date('Y-01-01');
        $data_fim = date('Y-12-31');
        break;
    case 'personalizado':
        $data_inicio = "$ano-$mes-01";
        $data_fim = date('Y-m-t', strtotime($data_inicio));
        break;
}

// Obter estatísticas financeiras
$financeiro = [];
try {
    // Receita total e líquida
    $sql = "
        SELECT 
            COUNT(*) as total_reparos,
            COALESCE(SUM(preco), 0) as receita_bruta,
            COALESCE(SUM(preco * (1 - ?/100)), 0) as receita_liquida,
            COALESCE(SUM(preco * (?/100)), 0) as taxa_fixfacil,
            COALESCE(AVG(preco), 0) as ticket_medio
        FROM propostas_assistencia 
        WHERE assistencia_id = ? 
        AND status = 'Concluída' 
        AND pago = 1
        AND DATE(data_conclusao) BETWEEN ? AND ?
    ";
    
    $result = $db->query($sql, [
        $plano['taxa_servico'], 
        $plano['taxa_servico'], 
        $usuario['assistencia_id'], 
        $data_inicio, 
        $data_fim
    ]);
    $financeiro = $result->fetch_assoc();
    
    // Reparos por status
    $sql = "
        SELECT 
            COUNT(CASE WHEN status = 'Concluída' AND pago = 1 THEN 1 END) as pagos,
            COUNT(CASE WHEN status = 'Concluída' AND pago = 0 THEN 1 END) as aguardando_pagamento,
            COUNT(CASE WHEN status = 'Em Andamento' THEN 1 END) as em_andamento
        FROM propostas_assistencia 
        WHERE assistencia_id = ?
        AND DATE(data_proposta) BETWEEN ? AND ?
    ";
    
    $result = $db->query($sql, [$usuario['assistencia_id'], $data_inicio, $data_fim]);
    $status_reparos = $result->fetch_assoc();
    
    $financeiro = array_merge($financeiro, $status_reparos);
    
} catch (Exception $e) {
    error_log("Erro ao obter dados financeiros: " . $e->getMessage());
    $financeiro = [
        'total_reparos' => 0, 'receita_bruta' => 0, 'receita_liquida' => 0,
        'taxa_fixfacil' => 0, 'ticket_medio' => 0, 'pagos' => 0,
        'aguardando_pagamento' => 0, 'em_andamento' => 0
    ];
}

// Obter histórico de pagamentos
$pagamentos = [];
try {
    $sql = "
        SELECT 
            pa.*,
            sr.dispositivo,
            sr.marca,
            sr.modelo,
            u.nome as cliente_nome,
            (pa.preco * (1 - ?/100)) as valor_recebido
        FROM propostas_assistencia pa
        JOIN solicitacoes_reparo sr ON pa.solicitacao_id = sr.id
        JOIN usuarios u ON sr.usuario_id = u.id
        WHERE pa.assistencia_id = ? 
        AND pa.status = 'Concluída'
        AND DATE(pa.data_conclusao) BETWEEN ? AND ?
        ORDER BY pa.data_conclusao DESC
        LIMIT 20
    ";
    
    $result = $db->query($sql, [
        $plano['taxa_servico'], 
        $usuario['assistencia_id'], 
        $data_inicio, 
        $data_fim
    ]);
    
    while ($row = $result->fetch_assoc()) {
        $pagamentos[] = $row;
    }
    
} catch (Exception $e) {
    error_log("Erro ao obter pagamentos: " . $e->getMessage());
}

// Obter dados para gráfico mensal (últimos 6 meses)
$grafico_dados = [];
try {
    for ($i = 5; $i >= 0; $i--) {
        $mes_grafico = date('Y-m', strtotime("-$i months"));
        $inicio_mes = $mes_grafico . '-01';
        $fim_mes = date('Y-m-t', strtotime($inicio_mes));
        
        $sql = "
            SELECT 
                COALESCE(SUM(preco * (1 - ?/100)), 0) as receita
            FROM propostas_assistencia 
            WHERE assistencia_id = ? 
            AND status = 'Concluída' 
            AND pago = 1
            AND DATE(data_conclusao) BETWEEN ? AND ?
        ";
        
        $result = $db->query($sql, [
            $plano['taxa_servico'], 
            $usuario['assistencia_id'], 
            $inicio_mes, 
            $fim_mes
        ]);
        
        $dados = $result->fetch_assoc();
        $grafico_dados[] = [
            'mes' => date('M/Y', strtotime($inicio_mes)),
            'receita' => $dados['receita']
        ];
    }
} catch (Exception $e) {
    error_log("Erro ao obter dados do gráfico: " . $e->getMessage());
}

// Inicializar layout
$layout = new Layout();
?>

<?php $layout->renderHead("Carteira - FixFácil Assistências"); ?>

<div class="main-wrapper">
    <?php $layout->renderSidebar('carteira'); ?>
    
    <main class="main-content">
        <!-- Header -->
        <div class="content-header">
            <h1 class="page-title">
                <i class="fas fa-wallet me-3"></i>
                Carteira Digital
            </h1>
            <p class="page-subtitle">
                Acompanhe seus ganhos, estatísticas financeiras e histórico de pagamentos
            </p>
        </div>
        
        <!-- Filtros -->
        <div class="card mb-4">
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-4">
                        <label class="form-label">Período</label>
                        <select name="periodo" class="form-select" onchange="toggleCustomPeriod()">
                            <option value="mes_atual" <?php echo $periodo === 'mes_atual' ? 'selected' : ''; ?>>
                                Mês Atual
                            </option>
                            <option value="mes_anterior" <?php echo $periodo === 'mes_anterior' ? 'selected' : ''; ?>>
                                Mês Anterior
                            </option>
                            <option value="ano_atual" <?php echo $periodo === 'ano_atual' ? 'selected' : ''; ?>>
                                Ano Atual
                            </option>
                            <option value="personalizado" <?php echo $periodo === 'personalizado' ? 'selected' : ''; ?>>
                                Personalizado
                            </option>
                        </select>
                    </div>
                    <div class="col-md-3" id="ano_select" style="display: <?php echo $periodo === 'personalizado' ? 'block' : 'none'; ?>;">
                        <label class="form-label">Ano</label>
                        <select name="ano" class="form-select">
                            <?php for ($y = date('Y'); $y >= date('Y') - 3; $y--): ?>
                            <option value="<?php echo $y; ?>" <?php echo $ano == $y ? 'selected' : ''; ?>>
                                <?php echo $y; ?>
                            </option>
                            <?php endfor; ?>
                        </select>
                    </div>
                    <div class="col-md-3" id="mes_select" style="display: <?php echo $periodo === 'personalizado' ? 'block' : 'none'; ?>;">
                        <label class="form-label">Mês</label>
                        <select name="mes" class="form-select">
                            <?php 
                            $meses = [
                                '01' => 'Janeiro', '02' => 'Fevereiro', '03' => 'Março',
                                '04' => 'Abril', '05' => 'Maio', '06' => 'Junho',
                                '07' => 'Julho', '08' => 'Agosto', '09' => 'Setembro',
                                '10' => 'Outubro', '11' => 'Novembro', '12' => 'Dezembro'
                            ];
                            foreach ($meses as $num => $nome): ?>
                            <option value="<?php echo $num; ?>" <?php echo $mes == $num ? 'selected' : ''; ?>>
                                <?php echo $nome; ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search me-2"></i>
                                Filtrar
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- Resumo Financeiro -->
        <div class="row g-4 mb-4">
            <div class="col-lg-3 col-md-6">
                <div class="card text-center">
                    <div class="card-body">
                        <div class="bg-success bg-opacity-10 p-3 rounded-circle d-inline-flex mb-3">
                            <i class="fas fa-dollar-sign text-success fs-3"></i>
                        </div>
                        <h3 class="text-success mb-1">R$ <?php echo number_format($financeiro['receita_liquida'], 2, ',', '.'); ?></h3>
                        <p class="text-muted mb-0">Receita Líquida</p>
                        <small class="text-muted">Após taxa de <?php echo number_format($plano['taxa_servico'], 1); ?>%</small>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6">
                <div class="card text-center">
                    <div class="card-body">
                        <div class="bg-primary bg-opacity-10 p-3 rounded-circle d-inline-flex mb-3">
                            <i class="fas fa-chart-line text-primary fs-3"></i>
                        </div>
                        <h3 class="text-primary mb-1">R$ <?php echo number_format($financeiro['receita_bruta'], 2, ',', '.'); ?></h3>
                        <p class="text-muted mb-0">Receita Bruta</p>
                        <small class="text-muted"><?php echo $financeiro['total_reparos']; ?> reparos</small>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6">
                <div class="card text-center">
                    <div class="card-body">
                        <div class="bg-info bg-opacity-10 p-3 rounded-circle d-inline-flex mb-3">
                            <i class="fas fa-receipt text-info fs-3"></i>
                        </div>
                        <h3 class="text-info mb-1">R$ <?php echo number_format($financeiro['ticket_medio'], 2, ',', '.'); ?></h3>
                        <p class="text-muted mb-0">Ticket Médio</p>
                        <small class="text-muted">Por reparo</small>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6">
                <div class="card text-center">
                    <div class="card-body">
                        <div class="bg-warning bg-opacity-10 p-3 rounded-circle d-inline-flex mb-3">
                            <i class="fas fa-percentage text-warning fs-3"></i>
                        </div>
                        <h3 class="text-warning mb-1">R$ <?php echo number_format($financeiro['taxa_fixfacil'], 2, ',', '.'); ?></h3>
                        <p class="text-muted mb-0">Taxa FixFácil</p>
                        <small class="text-muted"><?php echo number_format($plano['taxa_servico'], 1); ?>% do total</small>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Status dos Reparos -->
        <div class="row g-4 mb-4">
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-body text-center">
                        <div class="bg-success bg-opacity-10 p-3 rounded-circle d-inline-flex mb-3">
                            <i class="fas fa-check-circle text-success fs-4"></i>
                        </div>
                        <h4 class="text-success mb-1"><?php echo $financeiro['pagos']; ?></h4>
                        <p class="text-muted mb-0">Reparos Pagos</p>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-body text-center">
                        <div class="bg-warning bg-opacity-10 p-3 rounded-circle d-inline-flex mb-3">
                            <i class="fas fa-clock text-warning fs-4"></i>
                        </div>
                        <h4 class="text-warning mb-1"><?php echo $financeiro['aguardando_pagamento']; ?></h4>
                        <p class="text-muted mb-0">Aguardando Pagamento</p>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-body text-center">
                        <div class="bg-info bg-opacity-10 p-3 rounded-circle d-inline-flex mb-3">
                            <i class="fas fa-cog fa-spin text-info fs-4"></i>
                        </div>
                        <h4 class="text-info mb-1"><?php echo $financeiro['em_andamento']; ?></h4>
                        <p class="text-muted mb-0">Em Andamento</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Gráfico de Receita -->
        <div class="row g-4 mb-4">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-chart-area me-2"></i>
                            Evolução da Receita (Últimos 6 Meses)
                        </h5>
                    </div>
                    <div class="card-body">
                        <canvas id="receitaChart" height="100"></canvas>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-info-circle me-2"></i>
                            Informações do Plano
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="text-center mb-3">
                            <div class="plano-badge plano-<?php echo strtolower($plano['nome']); ?>">
                                <?php if ($plano['nome'] === 'Master'): ?>
                                    <i class="fas fa-crown me-1"></i>
                                <?php elseif ($plano['nome'] === 'Premium'): ?>
                                    <i class="fas fa-star me-1"></i>
                                <?php else: ?>
                                    <i class="fas fa-user me-1"></i>
                                <?php endif; ?>
                                Plano <?php echo $plano['nome']; ?>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <small class="text-muted d-block">Taxa de Serviço</small>
                            <strong class="fs-5"><?php echo number_format($plano['taxa_servico'], 1); ?>%</strong>
                        </div>
                        
                        <div class="mb-3">
                            <small class="text-muted d-block">Preço Mensal</small>
                            <strong class="fs-5">R$ <?php echo number_format($plano['preco_mensal'], 2, ',', '.'); ?></strong>
                        </div>
                        
                        <hr>
                        
                        <div class="d-grid">
                            <a href="upgrade_plano.php" class="btn btn-outline-primary">
                                <i class="fas fa-arrow-up me-2"></i>
                                Upgrade de Plano
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Histórico de Pagamentos -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-history me-2"></i>
                    Histórico de Pagamentos
                </h5>
            </div>
            <div class="card-body">
                <?php if (empty($pagamentos)): ?>
                <div class="text-center py-4">
                    <i class="fas fa-receipt fs-1 text-muted mb-3"></i>
                    <h6 class="text-muted">Nenhum pagamento no período</h6>
                    <p class="text-muted">Não há reparos concluídos no período selecionado.</p>
                </div>
                <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Data</th>
                                <th>Dispositivo</th>
                                <th>Cliente</th>
                                <th>Valor Bruto</th>
                                <th>Taxa FixFácil</th>
                                <th>Valor Recebido</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($pagamentos as $pagamento): ?>
                            <tr>
                                <td><?php echo date('d/m/Y', strtotime($pagamento['data_conclusao'])); ?></td>
                                <td>
                                    <strong><?php echo htmlspecialchars($pagamento['marca'] . ' ' . $pagamento['modelo']); ?></strong>
                                    <br>
                                    <small class="text-muted"><?php echo htmlspecialchars($pagamento['dispositivo']); ?></small>
                                </td>
                                <td><?php echo htmlspecialchars($pagamento['cliente_nome']); ?></td>
                                <td>R$ <?php echo number_format($pagamento['preco'], 2, ',', '.'); ?></td>
                                <td>R$ <?php echo number_format($pagamento['preco'] * ($plano['taxa_servico']/100), 2, ',', '.'); ?></td>
                                <td>
                                    <strong class="text-success">
                                        R$ <?php echo number_format($pagamento['valor_recebido'], 2, ',', '.'); ?>
                                    </strong>
                                </td>
                                <td>
                                    <?php if ($pagamento['pago']): ?>
                                        <span class="badge bg-success">Pago</span>
                                    <?php else: ?>
                                        <span class="badge bg-warning">Aguardando</span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </main>
</div>

<?php 
$extraJS = "
<script src='https://cdn.jsdelivr.net/npm/chart.js'></script>
<script>
function toggleCustomPeriod() {
    const periodo = document.querySelector('select[name=\"periodo\"]').value;
    const anoSelect = document.getElementById('ano_select');
    const mesSelect = document.getElementById('mes_select');
    
    if (periodo === 'personalizado') {
        anoSelect.style.display = 'block';
        mesSelect.style.display = 'block';
    } else {
        anoSelect.style.display = 'none';
        mesSelect.style.display = 'none';
    }
}

// Gráfico de receita
const ctx = document.getElementById('receitaChart').getContext('2d');
const receitaChart = new Chart(ctx, {
    type: 'line',
    data: {
        labels: " . json_encode(array_column($grafico_dados, 'mes')) . ",
        datasets: [{
            label: 'Receita Líquida (R$)',
            data: " . json_encode(array_column($grafico_dados, 'receita')) . ",
            borderColor: 'rgb(102, 126, 234)',
            backgroundColor: 'rgba(102, 126, 234, 0.1)',
            tension: 0.4,
            fill: true
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    callback: function(value) {
                        return 'R$ ' + value.toLocaleString('pt-BR');
                    }
                }
            }
        }
    }
});
</script>
";

$layout->renderFooter($extraJS); 
?>
