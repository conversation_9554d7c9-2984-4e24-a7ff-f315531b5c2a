<?php
/**
 * Página de Marketplace
 * FixFácil Assistências - Sistema Novo
 */

require_once 'config/auth.php';
require_once 'config/database.php';
require_once 'includes/layout.php';

// Verificar autenticação
$auth = getAuth();
$auth->checkAssistenciaAuth();

// Verificar acesso ao marketplace
if (!$auth->hasAccess('marketplace')) {
    header('Location: upgrade_plano.php?feature=marketplace');
    exit();
}

// Obter dados do usuário
$usuario = $auth->getUsuarioLogado();
$plano = $auth->getPlanoInfo($usuario['id']);
$db = getDatabase();

// Filtros
$categoria_filter = $_GET['categoria'] ?? 'todos';
$status_filter = $_GET['status'] ?? 'ativo';
$search = $_GET['search'] ?? '';

// Obter produtos
$produtos = [];
try {
    $where_conditions = ["p.assistencia_id = ?"];
    $params = [$usuario['assistencia_id']];
    
    if ($status_filter !== 'todos') {
        $where_conditions[] = "p.status = ?";
        $params[] = $status_filter;
    }
    
    if ($categoria_filter !== 'todos') {
        $where_conditions[] = "p.categoria = ?";
        $params[] = $categoria_filter;
    }
    
    if (!empty($search)) {
        $where_conditions[] = "(p.nome LIKE ? OR p.descricao LIKE ? OR p.marca LIKE ?)";
        $search_param = "%$search%";
        $params = array_merge($params, [$search_param, $search_param, $search_param]);
    }
    
    $where_clause = implode(' AND ', $where_conditions);
    
    $sql = "
        SELECT 
            p.*,
            COUNT(v.id) as total_vendas,
            COALESCE(SUM(v.quantidade), 0) as quantidade_vendida
        FROM produtos_marketplace p
        LEFT JOIN vendas_marketplace v ON p.id = v.produto_id AND v.status = 'concluida'
        WHERE $where_clause
        GROUP BY p.id
        ORDER BY p.data_criacao DESC
        LIMIT 50
    ";
    
    $result = $db->query($sql, $params);
    
    while ($row = $result->fetch_assoc()) {
        $produtos[] = $row;
    }
    
} catch (Exception $e) {
    error_log("Erro ao obter produtos: " . $e->getMessage());
}

// Estatísticas do marketplace
$stats = [];
try {
    $sql = "
        SELECT 
            COUNT(p.id) as total_produtos,
            COUNT(CASE WHEN p.status = 'ativo' THEN 1 END) as produtos_ativos,
            COUNT(CASE WHEN p.status = 'pausado' THEN 1 END) as produtos_pausados,
            COUNT(v.id) as total_vendas,
            COALESCE(SUM(v.valor_total), 0) as receita_total,
            COALESCE(SUM(v.quantidade), 0) as itens_vendidos
        FROM produtos_marketplace p
        LEFT JOIN vendas_marketplace v ON p.id = v.produto_id AND v.status = 'concluida'
        WHERE p.assistencia_id = ?
    ";
    
    $result = $db->query($sql, [$usuario['assistencia_id']]);
    $stats = $result->fetch_assoc();
    
} catch (Exception $e) {
    error_log("Erro ao obter estatísticas: " . $e->getMessage());
    $stats = [
        'total_produtos' => 0, 'produtos_ativos' => 0, 'produtos_pausados' => 0,
        'total_vendas' => 0, 'receita_total' => 0, 'itens_vendidos' => 0
    ];
}

// Inicializar layout
$layout = new Layout();
?>

<?php $layout->renderHead("Marketplace - FixFácil Assistências"); ?>

<div class="main-wrapper">
    <?php $layout->renderSidebar('marketplace'); ?>
    
    <main class="main-content">
        <!-- Header -->
        <div class="content-header">
            <div class="d-flex align-items-center justify-content-between">
                <div>
                    <h1 class="page-title">
                        <i class="fas fa-store me-3"></i>
                        Marketplace
                    </h1>
                    <p class="page-subtitle">
                        Venda produtos e peças para outros técnicos e clientes
                    </p>
                </div>
                <div>
                    <a href="adicionar_produto.php" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>
                        Adicionar Produto
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Estatísticas -->
        <div class="row g-4 mb-4">
            <div class="col-lg-2 col-md-4 col-sm-6">
                <div class="card text-center">
                    <div class="card-body py-3">
                        <h4 class="text-primary mb-1"><?php echo $stats['total_produtos']; ?></h4>
                        <small class="text-muted">Total Produtos</small>
                    </div>
                </div>
            </div>
            <div class="col-lg-2 col-md-4 col-sm-6">
                <div class="card text-center">
                    <div class="card-body py-3">
                        <h4 class="text-success mb-1"><?php echo $stats['produtos_ativos']; ?></h4>
                        <small class="text-muted">Ativos</small>
                    </div>
                </div>
            </div>
            <div class="col-lg-2 col-md-4 col-sm-6">
                <div class="card text-center">
                    <div class="card-body py-3">
                        <h4 class="text-warning mb-1"><?php echo $stats['produtos_pausados']; ?></h4>
                        <small class="text-muted">Pausados</small>
                    </div>
                </div>
            </div>
            <div class="col-lg-2 col-md-4 col-sm-6">
                <div class="card text-center">
                    <div class="card-body py-3">
                        <h4 class="text-info mb-1"><?php echo $stats['total_vendas']; ?></h4>
                        <small class="text-muted">Vendas</small>
                    </div>
                </div>
            </div>
            <div class="col-lg-2 col-md-4 col-sm-6">
                <div class="card text-center">
                    <div class="card-body py-3">
                        <h4 class="text-primary mb-1"><?php echo $stats['itens_vendidos']; ?></h4>
                        <small class="text-muted">Itens Vendidos</small>
                    </div>
                </div>
            </div>
            <div class="col-lg-2 col-md-4 col-sm-6">
                <div class="card text-center">
                    <div class="card-body py-3">
                        <h4 class="text-success mb-1">R$ <?php echo number_format($stats['receita_total'], 0); ?></h4>
                        <small class="text-muted">Receita</small>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Filtros -->
        <div class="card mb-4">
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-3">
                        <label class="form-label">Categoria</label>
                        <select name="categoria" class="form-select">
                            <option value="todos" <?php echo $categoria_filter === 'todos' ? 'selected' : ''; ?>>
                                Todas as Categorias
                            </option>
                            <option value="telas" <?php echo $categoria_filter === 'telas' ? 'selected' : ''; ?>>
                                Telas
                            </option>
                            <option value="baterias" <?php echo $categoria_filter === 'baterias' ? 'selected' : ''; ?>>
                                Baterias
                            </option>
                            <option value="cameras" <?php echo $categoria_filter === 'cameras' ? 'selected' : ''; ?>>
                                Câmeras
                            </option>
                            <option value="conectores" <?php echo $categoria_filter === 'conectores' ? 'selected' : ''; ?>>
                                Conectores
                            </option>
                            <option value="ferramentas" <?php echo $categoria_filter === 'ferramentas' ? 'selected' : ''; ?>>
                                Ferramentas
                            </option>
                            <option value="outros" <?php echo $categoria_filter === 'outros' ? 'selected' : ''; ?>>
                                Outros
                            </option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">Status</label>
                        <select name="status" class="form-select">
                            <option value="todos" <?php echo $status_filter === 'todos' ? 'selected' : ''; ?>>
                                Todos
                            </option>
                            <option value="ativo" <?php echo $status_filter === 'ativo' ? 'selected' : ''; ?>>
                                Ativo
                            </option>
                            <option value="pausado" <?php echo $status_filter === 'pausado' ? 'selected' : ''; ?>>
                                Pausado
                            </option>
                            <option value="esgotado" <?php echo $status_filter === 'esgotado' ? 'selected' : ''; ?>>
                                Esgotado
                            </option>
                        </select>
                    </div>
                    <div class="col-md-5">
                        <label class="form-label">Buscar</label>
                        <input type="text" name="search" class="form-control" 
                               placeholder="Buscar por nome, descrição ou marca..."
                               value="<?php echo htmlspecialchars($search); ?>">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search me-2"></i>
                                Filtrar
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- Lista de Produtos -->
        <?php if (empty($produtos)): ?>
        <div class="card">
            <div class="card-body text-center py-5">
                <i class="fas fa-store fs-1 text-muted mb-3"></i>
                <h4 class="text-muted">Nenhum produto encontrado</h4>
                <p class="text-muted">
                    <?php if ($categoria_filter === 'todos' && $status_filter === 'todos' && empty($search)): ?>
                        Você ainda não adicionou nenhum produto ao marketplace.
                    <?php else: ?>
                        Não há produtos que correspondam aos filtros selecionados.
                    <?php endif; ?>
                </p>
                <a href="adicionar_produto.php" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>
                    Adicionar Primeiro Produto
                </a>
            </div>
        </div>
        <?php else: ?>
        <div class="row g-4">
            <?php foreach ($produtos as $produto): ?>
            <div class="col-lg-4 col-md-6">
                <div class="card h-100">
                    <?php if (!empty($produto['imagem'])): ?>
                    <img src="<?php echo htmlspecialchars($produto['imagem']); ?>" 
                         class="card-img-top" style="height: 200px; object-fit: cover;"
                         alt="<?php echo htmlspecialchars($produto['nome']); ?>">
                    <?php else: ?>
                    <div class="card-img-top bg-light d-flex align-items-center justify-content-center" 
                         style="height: 200px;">
                        <i class="fas fa-image text-muted fs-1"></i>
                    </div>
                    <?php endif; ?>
                    
                    <div class="card-body d-flex flex-column">
                        <div class="d-flex justify-content-between align-items-start mb-2">
                            <h6 class="card-title mb-0"><?php echo htmlspecialchars($produto['nome']); ?></h6>
                            <?php
                            $status_class = [
                                'ativo' => 'success',
                                'pausado' => 'warning',
                                'esgotado' => 'danger'
                            ];
                            ?>
                            <span class="badge bg-<?php echo $status_class[$produto['status']] ?? 'secondary'; ?>">
                                <?php echo ucfirst($produto['status']); ?>
                            </span>
                        </div>
                        
                        <p class="card-text text-muted small mb-2">
                            <?php echo htmlspecialchars(substr($produto['descricao'], 0, 100)); ?>
                            <?php if (strlen($produto['descricao']) > 100): ?>...<?php endif; ?>
                        </p>
                        
                        <div class="mb-2">
                            <span class="badge bg-info"><?php echo ucfirst($produto['categoria']); ?></span>
                            <?php if (!empty($produto['marca'])): ?>
                            <span class="badge bg-secondary"><?php echo htmlspecialchars($produto['marca']); ?></span>
                            <?php endif; ?>
                        </div>
                        
                        <div class="row g-2 mb-3 text-center">
                            <div class="col-6">
                                <small class="text-muted d-block">Preço</small>
                                <strong class="text-primary">R$ <?php echo number_format($produto['preco'], 2, ',', '.'); ?></strong>
                            </div>
                            <div class="col-6">
                                <small class="text-muted d-block">Estoque</small>
                                <strong><?php echo $produto['quantidade_estoque']; ?></strong>
                            </div>
                        </div>
                        
                        <?php if ($produto['total_vendas'] > 0): ?>
                        <div class="mb-3">
                            <small class="text-success">
                                <i class="fas fa-chart-line me-1"></i>
                                <?php echo $produto['total_vendas']; ?> venda(s) - 
                                <?php echo $produto['quantidade_vendida']; ?> item(s)
                            </small>
                        </div>
                        <?php endif; ?>
                        
                        <div class="mt-auto">
                            <div class="d-grid gap-2">
                                <a href="editar_produto.php?id=<?php echo $produto['id']; ?>" 
                                   class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-edit me-2"></i>
                                    Editar
                                </a>
                                
                                <?php if ($produto['status'] === 'ativo'): ?>
                                <button type="button" class="btn btn-outline-warning btn-sm" 
                                        onclick="pausarProduto(<?php echo $produto['id']; ?>)">
                                    <i class="fas fa-pause me-2"></i>
                                    Pausar
                                </button>
                                <?php elseif ($produto['status'] === 'pausado'): ?>
                                <button type="button" class="btn btn-outline-success btn-sm" 
                                        onclick="ativarProduto(<?php echo $produto['id']; ?>)">
                                    <i class="fas fa-play me-2"></i>
                                    Ativar
                                </button>
                                <?php endif; ?>
                                
                                <button type="button" class="btn btn-outline-danger btn-sm" 
                                        onclick="excluirProduto(<?php echo $produto['id']; ?>)">
                                    <i class="fas fa-trash me-2"></i>
                                    Excluir
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
        <?php endif; ?>
    </main>
</div>

<?php 
$extraJS = "
<script>
function pausarProduto(produtoId) {
    if (confirm('Deseja pausar este produto? Ele não aparecerá mais nas buscas.')) {
        atualizarStatusProduto(produtoId, 'pausado');
    }
}

function ativarProduto(produtoId) {
    atualizarStatusProduto(produtoId, 'ativo');
}

function excluirProduto(produtoId) {
    if (confirm('Deseja excluir este produto? Esta ação não pode ser desfeita.')) {
        atualizarStatusProduto(produtoId, 'excluido');
    }
}

function atualizarStatusProduto(produtoId, novoStatus) {
    const formData = new FormData();
    formData.append('produto_id', produtoId);
    formData.append('status', novoStatus);
    
    fetch('ajax/atualizar_produto.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Erro: ' + data.message);
        }
    })
    .catch(error => {
        alert('Erro ao atualizar produto');
        console.error(error);
    });
}
</script>
";

$layout->renderFooter($extraJS); 
?>
