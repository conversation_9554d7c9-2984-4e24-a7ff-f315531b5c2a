<?php
/**
 * Visualizador de Mídia - Imagens e Vídeos
 * FixFácil Assistências
 */

// Iniciar sessão se não estiver iniciada
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Verificar se o usuário está logado
if (!isset($_SESSION['usuario_id']) || $_SESSION['tipo_usuario'] !== 'assistencia') {
    header('HTTP/1.0 403 Forbidden');
    exit('Acesso negado');
}

// Obter o arquivo solicitado
$file = $_GET['file'] ?? '';

if (empty($file)) {
    header('HTTP/1.0 404 Not Found');
    exit('Arquivo não especificado');
}

// Sanitizar o nome do arquivo
$file = basename($file);

// Possíveis localizações dos arquivos
$possible_paths = [
    __DIR__ . '/uploads/videos/' . $file,
    __DIR__ . '/uploads/produtos/' . $file,
    __DIR__ . '/../usuario/uploads/solicitacoes/' . $file,
    __DIR__ . '/../uploads/solicitacoes/' . $file,
    __DIR__ . '/../uploads/videos/' . $file,
    __DIR__ . '/../uploads/produtos/' . $file
];

$file_path = null;
$mime_type = null;

// Procurar o arquivo em todas as localizações possíveis
foreach ($possible_paths as $path) {
    if (file_exists($path) && is_readable($path)) {
        $file_path = $path;
        break;
    }
}

if (!$file_path) {
    header('HTTP/1.0 404 Not Found');
    exit('Arquivo não encontrado');
}

// Determinar o tipo MIME
$extension = strtolower(pathinfo($file_path, PATHINFO_EXTENSION));

switch ($extension) {
    case 'jpg':
    case 'jpeg':
        $mime_type = 'image/jpeg';
        break;
    case 'png':
        $mime_type = 'image/png';
        break;
    case 'gif':
        $mime_type = 'image/gif';
        break;
    case 'webp':
        $mime_type = 'image/webp';
        break;
    case 'mp4':
        $mime_type = 'video/mp4';
        break;
    case 'mov':
        $mime_type = 'video/quicktime';
        break;
    case 'avi':
        $mime_type = 'video/x-msvideo';
        break;
    case 'webm':
        $mime_type = 'video/webm';
        break;
    default:
        $mime_type = 'application/octet-stream';
}

// Definir headers apropriados
header('Content-Type: ' . $mime_type);
header('Content-Length: ' . filesize($file_path));
header('Cache-Control: public, max-age=3600');
header('Expires: ' . gmdate('D, d M Y H:i:s', time() + 3600) . ' GMT');

// Para vídeos, suportar range requests
if (strpos($mime_type, 'video/') === 0) {
    $size = filesize($file_path);
    $start = 0;
    $end = $size - 1;
    
    if (isset($_SERVER['HTTP_RANGE'])) {
        $range = $_SERVER['HTTP_RANGE'];
        if (preg_match('/bytes=(\d+)-(\d*)/', $range, $matches)) {
            $start = intval($matches[1]);
            if (!empty($matches[2])) {
                $end = intval($matches[2]);
            }
        }
        
        header('HTTP/1.1 206 Partial Content');
        header('Accept-Ranges: bytes');
        header("Content-Range: bytes $start-$end/$size");
        header('Content-Length: ' . ($end - $start + 1));
    }
    
    // Abrir arquivo e enviar conteúdo
    $fp = fopen($file_path, 'rb');
    fseek($fp, $start);
    
    $buffer = 1024 * 8;
    $remaining = $end - $start + 1;
    
    while ($remaining > 0 && !feof($fp)) {
        $read = min($buffer, $remaining);
        echo fread($fp, $read);
        $remaining -= $read;
        flush();
    }
    
    fclose($fp);
} else {
    // Para imagens, enviar diretamente
    readfile($file_path);
}

exit();
?>
