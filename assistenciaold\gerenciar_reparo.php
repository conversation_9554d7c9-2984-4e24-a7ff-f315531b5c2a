<?php
/**
 * <PERSON><PERSON><PERSON>a de Gerenciar Reparo
 * FixFácil Assistências - Sistema Novo
 */

require_once 'config/auth.php';
require_once 'config/database.php';
require_once 'includes/layout.php';

// Verificar autenticação
$auth = getAuth();
$auth->checkAssistenciaAuth();

// Obter dados do usuário
$usuario = $auth->getUsuarioLogado();
$plano = $auth->getPlanoInfo($usuario['id']);
$db = getDatabase();

// Obter ID da proposta
$proposta_id = $_GET['proposta_id'] ?? 0;

if (!$proposta_id) {
    header('Location: reparos.php');
    exit();
}

// Obter dados da proposta
$proposta = null;
try {
    $sql = "
        SELECT 
            pa.*,
            sr.descricao_problema,
            sr.dispositivo,
            sr.marca,
            sr.modelo,
            sr.memoria,
            sr.metodo_entrega,
            sr.data_solicitacao,
            sr.video_problema,
            u.nome as cliente_nome,
            u.telefone as cliente_telefone,
            u.endereco as cliente_endereco,
            u.email as cliente_email
        FROM propostas_assistencia pa
        JOIN solicitacoes_reparo sr ON pa.solicitacao_id = sr.id
        JOIN usuarios u ON sr.usuario_id = u.id
        WHERE pa.id = ? AND pa.assistencia_id = ?
    ";
    
    $result = $db->query($sql, [$proposta_id, $usuario['assistencia_id']]);
    $proposta = $result->fetch_assoc();
    
    if (!$proposta) {
        header('Location: reparos.php');
        exit();
    }
    
} catch (Exception $e) {
    error_log("Erro ao obter proposta: " . $e->getMessage());
    header('Location: reparos.php');
    exit();
}

// Processar atualizações
$erro = '';
$sucesso = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $acao = $_POST['acao'] ?? '';
    
    if ($acao === 'atualizar_status') {
        $novo_status = $_POST['status'] ?? '';
        $observacoes = $_POST['observacoes'] ?? '';
        
        $status_permitidos = ['aceita', 'Em Andamento', 'Concluída'];
        if (in_array($novo_status, $status_permitidos)) {
            try {
                $campos_update = ['status = ?'];
                $params = [$novo_status];
                
                if ($novo_status === 'Em Andamento' && $proposta['status'] === 'aceita') {
                    $campos_update[] = 'data_inicio = NOW()';
                } elseif ($novo_status === 'Concluída') {
                    $campos_update[] = 'data_conclusao = NOW()';
                }
                
                if (!empty($observacoes)) {
                    $obs_atuais = $proposta['observacoes'] ?? '';
                    $novas_obs = $obs_atuais . "\n\n[" . date('d/m/Y H:i') . "] " . $observacoes;
                    $campos_update[] = 'observacoes = ?';
                    $params[] = $novas_obs;
                }
                
                $params[] = $proposta_id;
                
                $sql = "UPDATE propostas_assistencia SET " . implode(', ', $campos_update) . " WHERE id = ?";
                $db->query($sql, $params);
                
                $sucesso = 'Status atualizado com sucesso!';
                
                // Recarregar dados
                $result = $db->query("SELECT * FROM propostas_assistencia WHERE id = ?", [$proposta_id]);
                $proposta = array_merge($proposta, $result->fetch_assoc());
                
            } catch (Exception $e) {
                $erro = 'Erro ao atualizar status: ' . $e->getMessage();
            }
        } else {
            $erro = 'Status inválido.';
        }
    }
}

// Inicializar layout
$layout = new Layout();
?>

<?php $layout->renderHead("Gerenciar Reparo - FixFácil Assistências"); ?>

<div class="main-wrapper">
    <?php $layout->renderSidebar('reparos'); ?>
    
    <main class="main-content">
        <!-- Header -->
        <div class="content-header">
            <div class="d-flex align-items-center justify-content-between">
                <div>
                    <h1 class="page-title">
                        <i class="fas fa-tools me-3"></i>
                        Gerenciar Reparo #<?php echo $proposta['id']; ?>
                    </h1>
                    <p class="page-subtitle">
                        <?php echo htmlspecialchars($proposta['marca'] . ' ' . $proposta['modelo']); ?>
                    </p>
                </div>
                <div>
                    <a href="reparos.php" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>
                        Voltar
                    </a>
                </div>
            </div>
        </div>
        
        <?php if ($erro): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <?php echo htmlspecialchars($erro); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>
        
        <?php if ($sucesso): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?php echo htmlspecialchars($sucesso); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>
        
        <div class="row g-4">
            <!-- Informações do Reparo -->
            <div class="col-lg-8">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-info-circle me-2"></i>
                            Informações do Reparo
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label class="form-label"><strong>Dispositivo:</strong></label>
                                <p><?php echo htmlspecialchars($proposta['dispositivo']); ?></p>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label"><strong>Marca/Modelo:</strong></label>
                                <p><?php echo htmlspecialchars($proposta['marca'] . ' ' . $proposta['modelo']); ?></p>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label"><strong>Memória:</strong></label>
                                <p><?php echo htmlspecialchars($proposta['memoria']); ?></p>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label"><strong>Método de Entrega:</strong></label>
                                <p><?php echo htmlspecialchars($proposta['metodo_entrega']); ?></p>
                            </div>
                            <div class="col-12">
                                <label class="form-label"><strong>Problema Relatado:</strong></label>
                                <p><?php echo nl2br(htmlspecialchars($proposta['descricao_problema'])); ?></p>
                            </div>
                            <?php if (!empty($proposta['video_problema'])): ?>
                            <div class="col-12">
                                <label class="form-label"><strong>Vídeo do Problema:</strong></label>
                                <div>
                                    <video controls style="max-width: 100%; height: 300px;">
                                        <source src="<?php echo htmlspecialchars($proposta['video_problema']); ?>" type="video/mp4">
                                        Seu navegador não suporta vídeos.
                                    </video>
                                </div>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                
                <!-- Dados do Cliente -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-user me-2"></i>
                            Dados do Cliente
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label class="form-label"><strong>Nome:</strong></label>
                                <p><?php echo htmlspecialchars($proposta['cliente_nome']); ?></p>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label"><strong>Telefone:</strong></label>
                                <p>
                                    <a href="tel:<?php echo htmlspecialchars($proposta['cliente_telefone']); ?>" class="text-decoration-none">
                                        <?php echo htmlspecialchars($proposta['cliente_telefone']); ?>
                                    </a>
                                </p>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label"><strong>E-mail:</strong></label>
                                <p>
                                    <a href="mailto:<?php echo htmlspecialchars($proposta['cliente_email']); ?>" class="text-decoration-none">
                                        <?php echo htmlspecialchars($proposta['cliente_email']); ?>
                                    </a>
                                </p>
                            </div>
                            <div class="col-12">
                                <label class="form-label"><strong>Endereço:</strong></label>
                                <p><?php echo nl2br(htmlspecialchars($proposta['cliente_endereco'])); ?></p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Observações -->
                <?php if (!empty($proposta['observacoes'])): ?>
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-sticky-note me-2"></i>
                            Observações
                        </h5>
                    </div>
                    <div class="card-body">
                        <div style="white-space: pre-line;"><?php echo htmlspecialchars($proposta['observacoes']); ?></div>
                    </div>
                </div>
                <?php endif; ?>
            </div>
            
            <!-- Painel de Controle -->
            <div class="col-lg-4">
                <!-- Status Atual -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-flag me-2"></i>
                            Status Atual
                        </h5>
                    </div>
                    <div class="card-body text-center">
                        <?php
                        $status_class = [
                            'aceita' => 'success',
                            'Em Andamento' => 'info',
                            'Concluída' => 'primary'
                        ];
                        ?>
                        <div class="mb-3">
                            <span class="badge bg-<?php echo $status_class[$proposta['status']] ?? 'secondary'; ?> fs-5">
                                <?php echo htmlspecialchars($proposta['status']); ?>
                            </span>
                        </div>
                        
                        <div class="row g-2 text-center">
                            <div class="col-6">
                                <small class="text-muted d-block">Valor</small>
                                <strong class="text-primary">R$ <?php echo number_format($proposta['preco'], 2, ',', '.'); ?></strong>
                            </div>
                            <div class="col-6">
                                <small class="text-muted d-block">Prazo</small>
                                <strong><?php echo $proposta['prazo']; ?> dia(s)</strong>
                            </div>
                        </div>
                        
                        <?php if ($proposta['status'] === 'Concluída' && $proposta['pago']): ?>
                        <div class="mt-3">
                            <small class="text-success d-block">Você recebeu</small>
                            <strong class="text-success">
                                R$ <?php echo number_format($proposta['preco'] * (1 - $plano['taxa_servico']/100), 2, ',', '.'); ?>
                            </strong>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
                
                <!-- Atualizar Status -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-edit me-2"></i>
                            Atualizar Status
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <input type="hidden" name="acao" value="atualizar_status">
                            
                            <div class="mb-3">
                                <label for="status" class="form-label">Novo Status</label>
                                <select class="form-select" id="status" name="status" required>
                                    <option value="">Selecione...</option>
                                    <?php if ($proposta['status'] === 'aceita'): ?>
                                    <option value="Em Andamento">Iniciar Reparo</option>
                                    <?php elseif ($proposta['status'] === 'Em Andamento'): ?>
                                    <option value="Concluída">Concluir Reparo</option>
                                    <?php endif; ?>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <label for="observacoes" class="form-label">Observações (opcional)</label>
                                <textarea class="form-control" id="observacoes" name="observacoes" rows="3" 
                                          placeholder="Adicione observações sobre esta atualização..."></textarea>
                            </div>
                            
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>
                                    Atualizar Status
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
                
                <!-- Ações Rápidas -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-bolt me-2"></i>
                            Ações Rápidas
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <?php if ($auth->hasAccess('chat')): ?>
                            <a href="chat.php?proposta_id=<?php echo $proposta['id']; ?>" class="btn btn-outline-info">
                                <i class="fas fa-comments me-2"></i>
                                Chat com Cliente
                            </a>
                            <?php endif; ?>
                            
                            <a href="detalhes_solicitacao.php?id=<?php echo $proposta['solicitacao_id']; ?>" class="btn btn-outline-secondary">
                                <i class="fas fa-eye me-2"></i>
                                Ver Solicitação
                            </a>
                            
                            <a href="tel:<?php echo htmlspecialchars($proposta['cliente_telefone']); ?>" class="btn btn-outline-success">
                                <i class="fas fa-phone me-2"></i>
                                Ligar para Cliente
                            </a>
                            
                            <a href="mailto:<?php echo htmlspecialchars($proposta['cliente_email']); ?>" class="btn btn-outline-warning">
                                <i class="fas fa-envelope me-2"></i>
                                Enviar E-mail
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>
</div>

<?php $layout->renderFooter(); ?>
