{"name": "endroid/qr-code-bundle", "description": "Endroid QR Code Bundle", "keywords": ["endroid", "qr", "code", "symfony", "bundle", "php"], "homepage": "https://github.com/endroid/qr-code-bundle", "type": "symfony-bundle", "license": "MIT", "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "require": {"php": "^8.1", "endroid/installer": "^1.2.2", "endroid/qr-code": "^5.0", "symfony/framework-bundle": "^5.4||^6.4||^7.0", "symfony/twig-bundle": "^5.4||^6.4||^7.0", "symfony/yaml": "^5.4||^6.4||^7.0"}, "require-dev": {"endroid/quality": "dev-main"}, "suggest": {"roave/security-advisories": "Avoids installation of package versions with vulnerabilities"}, "autoload": {"psr-4": {"Endroid\\QrCodeBundle\\": "src/"}}, "autoload-dev": {"psr-4": {"Endroid\\QrCodeBundle\\Tests\\": "tests/"}}, "extra": {"branch-alias": {"dev-main": "4.x-dev"}}, "config": {"sort-packages": true, "allow-plugins": {"endroid/installer": true}}}