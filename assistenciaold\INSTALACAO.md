# 🚀 G<PERSON><PERSON> de Instalação - FixFácil Assistências

## 📋 Pré-requisitos

- **PHP 7.4+** com extensões:
  - mysqli
  - json
  - session
  - gd (para manipulação de imagens)
  - zip (para backups)
- **MySQL 5.7+** ou **MariaDB 10.2+**
- **Apache** com mod_rewrite habilitado
- **HTTPS** configurado (recomendado)

## 🔧 Passos de Instalação

### 1. Configurar Banco de Dados

Execute o SQL fornecido (`u680766645_fixfacilnew (2).sql`) no seu banco de dados MySQL.

Em seguida, execute as tabelas adicionais:
```sql
-- Execute o arquivo: assistencia/sql/tabelas_adicionais.sql
```

### 2. Configurar Conexão com Banco

Edite o arquivo `assistencia/config/database.php` com suas credenciais:

```php
private $host = 'localhost';
private $username = 'seu_usuario';
private $password = 'sua_senha';
private $database = 'seu_banco';
```

### 3. Configurar Permissões de Diretórios

```bash
chmod 755 assistencia/
chmod 755 virtual/
chmod 777 assistencia/uploads/ (se existir)
chmod 777 assistencia/backups/ (será criado automaticamente)
chmod 777 assistencia/logs/ (será criado automaticamente)
```

### 4. Configurar Apache

Certifique-se de que o mod_rewrite está habilitado:
```bash
a2enmod rewrite
systemctl restart apache2
```

### 5. Configurar HTTPS (Recomendado)

Configure SSL/TLS no seu servidor Apache ou use um proxy reverso como Cloudflare.

### 6. Testar Instalação

1. Acesse: `https://seudominio.com.br/assistencia/`
2. Faça login com uma conta de assistência técnica
3. Teste as funcionalidades básicas

## 🎯 Funcionalidades por Plano

### 📱 Plano Free (Taxa 25%)
- ✅ Dashboard básico
- ✅ Solicitações e propostas
- ✅ Gerenciamento de reparos
- ✅ Carteira digital

### ⭐ Plano Premium (R$ 89,90/mês - Taxa 20%)
- ✅ Todas as funcionalidades do Free
- ✅ Chat com clientes
- ✅ Marketplace de produtos
- ✅ Retirada presencial
- ✅ Selo FixFácil

### 👑 Plano Master (R$ 159,90/mês - Taxa 10%)
- ✅ Todas as funcionalidades do Premium
- ✅ **Assistência Virtual Personalizada**
- ✅ Link personalizado (fixfacil.com.br/sua-assistencia)
- ✅ Página customizável com cores e logo
- ✅ Retirada express prioritária

## 🔗 URLs Importantes

### Sistema Principal
- Dashboard: `/assistencia/dashboard.php`
- Login: `/assistencia/login.php`
- Solicitações: `/assistencia/solicitacoes.php`
- Chat: `/assistencia/chat.php` (Premium/Master)
- Marketplace: `/assistencia/marketplace.php` (Premium/Master)
- Assistência Virtual: `/assistencia/assistencia_virtual.php` (Master)

### Assistência Virtual (Master)
- Configuração: `/assistencia/assistencia_virtual.php`
- Página pública: `/virtual/sua-assistencia` (configurável)
- Exemplo: `https://fixfacil.com.br/minha-assistencia`

### Admin (Apenas para admins)
- Dashboard Admin: `/assistencia/admin/dashboard_admin.php`

## 🛠️ Configurações Avançadas

### Backup Automático
Configure um cron job para backup automático:
```bash
# Backup diário às 2h da manhã
0 2 * * * /usr/bin/php /caminho/para/assistencia/sistema/backup_automatico.php
```

### Limpeza de Logs
Configure limpeza automática de logs:
```bash
# Limpeza semanal aos domingos às 3h
0 3 * * 0 /usr/bin/php -r "require '/caminho/para/assistencia/sistema/monitor_sistema.php'; (new MonitorSistema())->limparLogsAntigos(30);"
```

### Upload de Arquivos
Configure limites no PHP:
```ini
upload_max_filesize = 10M
post_max_size = 10M
max_execution_time = 300
memory_limit = 256M
```

## 🔐 Segurança

### Headers de Segurança
O sistema já inclui headers de segurança básicos. Para maior proteção, configure no Apache:

```apache
Header always set X-Content-Type-Options nosniff
Header always set X-Frame-Options DENY
Header always set X-XSS-Protection "1; mode=block"
Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains"
```

### Firewall
Configure firewall para permitir apenas portas necessárias:
- 80 (HTTP)
- 443 (HTTPS)
- 22 (SSH - apenas para administração)

## 📊 Monitoramento

### Logs do Sistema
- Atividades: Tabela `logs_atividades`
- Erros: Tabela `logs_erros`
- Performance: Tabela `logs_performance`

### Métricas Importantes
- Tempo de resposta das páginas
- Uso de memória
- Erros por hora
- Usuários ativos

## 🆘 Solução de Problemas

### Erro 500 - Internal Server Error
1. Verifique logs do Apache: `/var/log/apache2/error.log`
2. Verifique permissões dos arquivos
3. Verifique configuração do banco de dados

### Chat não funciona
1. Verifique se o plano tem acesso ao chat
2. Verifique se as tabelas `mensagens_chat` existem
3. Teste a conexão AJAX

### Assistência Virtual não carrega
1. Verifique se o mod_rewrite está habilitado
2. Verifique o arquivo `.htaccess` em `/virtual/`
3. Verifique se a tabela `assistencias_virtuais` existe

### Backup falha
1. Verifique permissões do diretório `/backups/`
2. Verifique espaço em disco
3. Verifique logs em `logs_erros`

## 📞 Suporte

Para suporte técnico:
- 📧 Email: <EMAIL>
- 📱 WhatsApp: (11) 99999-9999
- 🌐 Site: https://fixfacil.com.br

## 🔄 Atualizações

Para atualizar o sistema:
1. Faça backup completo
2. Baixe a nova versão
3. Execute scripts SQL de atualização
4. Teste todas as funcionalidades

---

**✅ Sistema FixFácil Assistências - Versão 2.0**  
**🚀 Pronto para produção com todas as funcionalidades avançadas!**
