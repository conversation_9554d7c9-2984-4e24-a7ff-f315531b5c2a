<?php
session_start();
require_once 'db.php';

if (!isset($_SESSION['user_id']) || $_SESSION['nivel_acesso'] != 'fornecedor') {
    header("Location: login.php");
    exit();
}

// Verificar se o formulário foi enviado
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['enviar_valor'])) {
    $produto_id = $_POST['produto_id'];
    $fornecedor_id = $_SESSION['user_id'];
    $valor_proposta = $_POST['valor_proposta'];

    try {
        $query = "INSERT INTO propostas_leilao (produto_id, fornecedor_id, valor_proposta) VALUES (:produto_id, :fornecedor_id, :valor_proposta)";
        $stmt = $pdo->prepare($query);
        $stmt->bindParam(':produto_id', $produto_id, PDO::PARAM_INT);
        $stmt->bindParam(':fornecedor_id', $fornecedor_id, PDO::PARAM_INT);
        $stmt->bindParam(':valor_proposta', $valor_proposta, PDO::PARAM_STR);
        $stmt->execute();

        echo '<script>alert("Proposta enviada com sucesso!");</script>';
    } catch (PDOException $e) {
        echo "Erro ao enviar a proposta: " . $e->getMessage();
    }
}

// Buscar todos os produtos da tabela propostas_leilao
$query = "SELECT p.id, p.nome, p.preco, p.marca, p.validade, p.tipo_embalagem, p.peso_kg, p.descricao, u.nome as fornecedor_nome
          FROM propostas_leilao pl
          JOIN produtos p ON pl.produto_id = p.id
          JOIN usuarios u ON p.fornecedor_id = u.id
          ORDER BY p.id DESC";
$stmt = $pdo->prepare($query);
$stmt->execute();
$produtos = $stmt->fetchAll(PDO::FETCH_ASSOC);

?>

<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="utf-8">
    <title>Fornecedor - Leilão</title>
    <!-- Adicionar seus links para CSS e JavaScript aqui -->
</head>
<body>
    <h1>Produtos Disponíveis para Leilão</h1>

    <table border="1">
        <thead>
            <tr>
                <th>ID</th>
                <th>Nome</th>
                <th>Preço</th>
                <th>Marca</th>
                <th>Validade</th>
                <th>Tipo Embalagem</th>
                <th>Peso (kg)</th>
                <th>Descrição</th>
                <th>Fornecedor</th>
                <th>Ação</th>
            </tr>
        </thead>
        <tbody>
            <?php foreach ($produtos as $produto): ?>
                <tr>
                    <td><?php echo $produto['id']; ?></td>
                    <td><?php echo $produto['nome']; ?></td>
                    <td>R$ <?php echo number_format($produto['preco'], 2, ',', '.'); ?></td>
                    <td><?php echo $produto['marca']; ?></td>
                    <td><?php echo $produto['validade']; ?></td>
                    <td><?php echo $produto['tipo_embalagem']; ?></td>
                    <td><?php echo $produto['peso_kg']; ?></td>
                    <td><?php echo $produto['descricao']; ?></td>
                    <td><?php echo $produto['fornecedor_nome']; ?></td>
                    <td>
                        <form action="" method="post">
                            <input type="hidden" name="produto_id" value="<?php echo $produto['id']; ?>">
                            <input type="text" name="valor_proposta" placeholder="Valor da Proposta">
                            <input type="submit" name="enviar_valor" value="Enviar Proposta">
                        </form>
                    </td>
                </tr>
            <?php endforeach; ?>
        </tbody>
    </table>

    <!-- Adicionar seu código HTML/JavaScript abaixo -->
</body>
</html>
