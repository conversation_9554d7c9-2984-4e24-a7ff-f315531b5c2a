<?php
/**
 * Dashboard Simples para Teste
 * Versão simplificada para identificar problemas
 */

// Configurar relatório de erros
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Iniciar sessão
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Verificar se usuário está logado
if (!isset($_SESSION['usuario_id']) || $_SESSION['tipo_usuario'] !== 'assistencia') {
    header('Location: ../login.php');
    exit();
}

// Configuração do banco de dados
$servername = "localhost";
$username_db = "u680766645_fixfacilnew";
$password_db = "T3cn0l0g1a@";
$dbname = "u680766645_fixfacilnew";

try {
    $conn = new mysqli($servername, $username_db, $password_db, $dbname);
    $conn->set_charset("utf8mb4");
    
    if ($conn->connect_error) {
        throw new Exception("Conexão falhou: " . $conn->connect_error);
    }
    
    // Obter dados do usuário
    $usuario_id = $_SESSION['usuario_id'];
    $sql = "SELECT u.*, at.id as assistencia_id, at.nome_empresa 
            FROM usuarios u 
            LEFT JOIN assistencias_tecnicas at ON u.id = at.usuario_id 
            WHERE u.id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $usuario_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $usuario = $result->fetch_assoc();
    
    if (!$usuario) {
        throw new Exception("Usuário não encontrado");
    }
    
    // Obter estatísticas básicas
    $stats = [];
    
    // Solicitações pendentes
    $result = $conn->query("SELECT COUNT(*) as count FROM solicitacoes_reparo WHERE status = 'enviado'");
    $row = $result->fetch_assoc();
    $stats['solicitacoes_pendentes'] = $row['count'];
    
    // Propostas enviadas
    if ($usuario['assistencia_id']) {
        $stmt = $conn->prepare("SELECT COUNT(*) as count FROM propostas_assistencia WHERE assistencia_id = ?");
        $stmt->bind_param("i", $usuario['assistencia_id']);
        $stmt->execute();
        $result = $stmt->get_result();
        $row = $result->fetch_assoc();
        $stats['propostas_enviadas'] = $row['count'];
    } else {
        $stats['propostas_enviadas'] = 0;
    }
    
} catch (Exception $e) {
    die("Erro: " . $e->getMessage());
}
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - FixFácil Assistências</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: #f8fafc;
            font-family: 'Inter', sans-serif;
        }
        .sidebar {
            width: 250px;
            height: 100vh;
            background: white;
            position: fixed;
            border-right: 1px solid #e5e7eb;
            box-shadow: 0 0 15px rgba(0,0,0,0.05);
        }
        .main-content {
            margin-left: 250px;
            padding: 2rem;
        }
        .nav-link {
            color: #374151;
            padding: 0.75rem 1rem;
            border-radius: 0.375rem;
            margin: 0.25rem 1rem;
        }
        .nav-link:hover {
            background: #f3f4f6;
            color: #2563eb;
        }
        .nav-link.active {
            background: #2563eb;
            color: white;
        }
        .card {
            border: none;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .stat-card {
            background: white;
            border-radius: 0.5rem;
            padding: 1.5rem;
            text-align: center;
        }
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #2563eb;
        }
        .stat-label {
            color: #6b7280;
            font-size: 0.875rem;
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <nav class="sidebar">
        <div class="p-3 border-bottom">
            <h5 class="text-primary mb-0">
                <i class="fas fa-wrench me-2"></i>FixFácil
            </h5>
            <small class="text-muted"><?php echo htmlspecialchars($usuario['nome']); ?></small>
        </div>
        
        <div class="py-3">
            <a href="dashboard.php" class="nav-link active">
                <i class="fas fa-home me-2"></i>Dashboard
            </a>
            <a href="solicitacoes.php" class="nav-link">
                <i class="fas fa-inbox me-2"></i>Solicitações
            </a>
            <a href="propostas.php" class="nav-link">
                <i class="fas fa-paper-plane me-2"></i>Propostas
            </a>
            <a href="reparos.php" class="nav-link">
                <i class="fas fa-tools me-2"></i>Reparos
            </a>
            <a href="carteira.php" class="nav-link">
                <i class="fas fa-wallet me-2"></i>Carteira
            </a>
            <a href="perfil.php" class="nav-link">
                <i class="fas fa-user me-2"></i>Perfil
            </a>
            <hr class="mx-3">
            <a href="logout.php" class="nav-link text-danger">
                <i class="fas fa-sign-out-alt me-2"></i>Sair
            </a>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="h3 mb-0">Dashboard</h1>
                <p class="text-muted">Bem-vindo de volta, <?php echo htmlspecialchars($usuario['nome']); ?>!</p>
            </div>
            <div class="badge bg-primary">
                <i class="fas fa-user me-1"></i>Assistência
            </div>
        </div>

        <!-- Estatísticas -->
        <div class="row g-4 mb-4">
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="stat-number"><?php echo $stats['solicitacoes_pendentes']; ?></div>
                    <div class="stat-label">Solicitações Pendentes</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="stat-number"><?php echo $stats['propostas_enviadas']; ?></div>
                    <div class="stat-label">Propostas Enviadas</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="stat-number">0</div>
                    <div class="stat-label">Reparos Ativos</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="stat-number">R$ 0,00</div>
                    <div class="stat-label">Receita do Mês</div>
                </div>
            </div>
        </div>

        <!-- Ações Rápidas -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-bolt me-2"></i>Ações Rápidas
                </h5>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-md-3">
                        <a href="solicitacoes.php" class="btn btn-outline-primary w-100">
                            <i class="fas fa-inbox me-2"></i>Ver Solicitações
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="propostas.php" class="btn btn-outline-info w-100">
                            <i class="fas fa-paper-plane me-2"></i>Minhas Propostas
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="reparos.php" class="btn btn-outline-success w-100">
                            <i class="fas fa-tools me-2"></i>Reparos Ativos
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="carteira.php" class="btn btn-outline-warning w-100">
                            <i class="fas fa-wallet me-2"></i>Ver Carteira
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Informações do Sistema -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>Informações do Sistema
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>Usuário ID:</strong> <?php echo $usuario['id']; ?></p>
                        <p><strong>Email:</strong> <?php echo htmlspecialchars($usuario['email']); ?></p>
                        <p><strong>Empresa:</strong> <?php echo htmlspecialchars($usuario['nome_empresa'] ?? 'Não informado'); ?></p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>Assistência ID:</strong> <?php echo $usuario['assistencia_id'] ?? 'Não encontrado'; ?></p>
                        <p><strong>Tipo:</strong> <?php echo $usuario['tipo_usuario']; ?></p>
                        <p><strong>Status:</strong> <span class="badge bg-success">Ativo</span></p>
                    </div>
                </div>
                
                <div class="mt-3">
                    <a href="teste_debug.php" class="btn btn-sm btn-outline-secondary">
                        <i class="fas fa-bug me-1"></i>Executar Teste de Debug
                    </a>
                    <a href="dashboard.php" class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-sync me-1"></i>Tentar Dashboard Completo
                    </a>
                </div>
            </div>
        </div>
    </main>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

<?php $conn->close(); ?>
