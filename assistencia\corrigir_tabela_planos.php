<?php
/**
 * Co<PERSON><PERSON>r Tabela de Planos - Adicionar coluna acesso_assistencia_virtual
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔧 Correção da Tabela Planos</h1>";
echo "<style>body { font-family: Arial, sans-serif; margin: 20px; } table { border-collapse: collapse; width: 100%; } th, td { border: 1px solid #ddd; padding: 8px; text-align: left; } th { background-color: #f2f2f2; }</style>";

try {
    require_once 'config/database.php';
    $db = getDatabase();
    
    echo "<p>✅ Conexão estabelecida</p>";
    
    // Verificar estrutura atual da tabela planos
    echo "<h3>📋 Estrutura Atual da Tabela Planos:</h3>";
    $result = $db->query("DESCRIBE planos");
    
    echo "<table>";
    echo "<tr><th>Campo</th><th>Tipo</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    
    $colunas_existentes = [];
    while ($row = $result->fetch_assoc()) {
        $colunas_existentes[] = $row['Field'];
        echo "<tr>";
        echo "<td><strong>{$row['Field']}</strong></td>";
        echo "<td>{$row['Type']}</td>";
        echo "<td>{$row['Null']}</td>";
        echo "<td>{$row['Key']}</td>";
        echo "<td>{$row['Default']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Verificar se a coluna acesso_assistencia_virtual existe
    if (in_array('acesso_assistencia_virtual', $colunas_existentes)) {
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
        echo "<h4>✅ Coluna 'acesso_assistencia_virtual' já existe!</h4>";
        echo "<p>A tabela já está atualizada.</p>";
        echo "</div>";
    } else {
        echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
        echo "<h4>⚠️ Coluna 'acesso_assistencia_virtual' não existe!</h4>";
        echo "<p>É necessário adicionar esta coluna para o sistema de planos funcionar corretamente.</p>";
        echo "</div>";
        
        if (isset($_GET['adicionar_coluna']) && $_GET['adicionar_coluna'] === 'sim') {
            try {
                $sql = "ALTER TABLE planos ADD COLUMN acesso_assistencia_virtual TINYINT(1) NOT NULL DEFAULT 0 AFTER retirada_express_prioritaria";
                $db->query($sql);

                echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
                echo "<h4>✅ Coluna adicionada com sucesso!</h4>";
                echo "<p>A coluna 'acesso_assistencia_virtual' foi adicionada à tabela planos.</p>";
                echo "</div>";

                // Atualizar planos existentes
                echo "<h4>🔄 Atualizando planos existentes...</h4>";

                // Plano Master deve ter acesso à assistência virtual
                $sql = "UPDATE planos SET acesso_assistencia_virtual = 1 WHERE nome = 'Master'";
                $db->query($sql);
                echo "<p>✅ Plano Master atualizado com acesso à assistência virtual</p>";

                echo "<p><a href='?'>Recarregar página</a></p>";

            } catch (Exception $e) {
                echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
                echo "<h4>❌ Erro ao adicionar coluna!</h4>";
                echo "<p>" . $e->getMessage() . "</p>";
                echo "</div>";
            }
        } else {
            echo "<p><a href='?adicionar_coluna=sim' style='background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🔧 Adicionar Coluna</a></p>";
        }
    }
    
    // Mostrar planos atuais
    echo "<h3>📊 Planos Cadastrados:</h3>";
    $result = $db->query("SELECT * FROM planos ORDER BY id");
    
    if ($result->num_rows > 0) {
        echo "<table>";
        echo "<tr><th>ID</th><th>Nome</th><th>Preço</th><th>Taxa</th><th>Chat</th><th>Marketplace</th><th>Assistência Virtual</th></tr>";
        
        while ($row = $result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>{$row['id']}</td>";
            echo "<td><strong>{$row['nome']}</strong></td>";
            echo "<td>R$ " . number_format($row['preco_mensal'], 2, ',', '.') . "</td>";
            echo "<td>{$row['taxa_servico']}%</td>";
            echo "<td>" . ($row['acesso_chat'] ? '✅ SIM' : '❌ NÃO') . "</td>";
            echo "<td>" . ($row['acesso_marketplace'] ? '✅ SIM' : '❌ NÃO') . "</td>";
            echo "<td>" . (isset($row['acesso_assistencia_virtual']) ? ($row['acesso_assistencia_virtual'] ? '✅ SIM' : '❌ NÃO') : '❓ N/A') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>⚠️ Nenhum plano cadastrado</p>";
        
        if (isset($_GET['criar_planos']) && $_GET['criar_planos'] === 'sim') {
            echo "<h4>🛠️ Criando planos padrão...</h4>";
            
            $planos = [
                [
                    'nome' => 'Free',
                    'descricao' => 'Plano gratuito com funcionalidades básicas',
                    'preco_mensal' => 0.00,
                    'taxa_servico' => 25.00,
                    'acesso_chat' => 0,
                    'acesso_marketplace' => 0,
                    'acesso_assistencia_virtual' => 0
                ],
                [
                    'nome' => 'Premium',
                    'descricao' => 'Plano premium com chat e marketplace',
                    'preco_mensal' => 89.90,
                    'taxa_servico' => 20.00,
                    'acesso_chat' => 1,
                    'acesso_marketplace' => 1,
                    'acesso_assistencia_virtual' => 0
                ],
                [
                    'nome' => 'Master',
                    'descricao' => 'Plano completo com assistência virtual',
                    'preco_mensal' => 159.90,
                    'taxa_servico' => 10.00,
                    'acesso_chat' => 1,
                    'acesso_marketplace' => 1,
                    'acesso_assistencia_virtual' => 1
                ]
            ];
            
            foreach ($planos as $plano) {
                try {
                    $sql = "
                        INSERT INTO planos
                        (nome, descricao, preco_mensal, taxa_servico, acesso_chat, acesso_marketplace, acesso_assistencia_virtual)
                        VALUES (?, ?, ?, ?, ?, ?, ?)
                    ";
                    $db->query($sql, [
                        $plano['nome'],
                        $plano['descricao'],
                        $plano['preco_mensal'],
                        $plano['taxa_servico'],
                        $plano['acesso_chat'],
                        $plano['acesso_marketplace'],
                        $plano['acesso_assistencia_virtual']
                    ]);
                    echo "<p>✅ Plano {$plano['nome']} criado</p>";
                } catch (Exception $e) {
                    echo "<p>❌ Erro ao criar plano {$plano['nome']}: " . $e->getMessage() . "</p>";
                }
            }
            
            echo "<p><a href='?'>Recarregar página</a></p>";
        } else {
            echo "<p><a href='?criar_planos=sim' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🛠️ Criar Planos Padrão</a></p>";
        }
    }
    
    // Verificar assinaturas
    echo "<h3>📝 Assinaturas Ativas:</h3>";
    $sql = "
        SELECT 
            aa.id,
            at.nome_empresa,
            p.nome as plano_nome,
            p.acesso_assistencia_virtual,
            aa.status
        FROM assinaturas_assistencias aa
        JOIN assistencias_tecnicas at ON aa.assistencia_id = at.id
        JOIN planos p ON aa.plano_id = p.id
        WHERE aa.status = 'ativa'
        ORDER BY aa.id DESC
    ";
    
    $result = $db->query($sql);
    if ($result->num_rows > 0) {
        echo "<table>";
        echo "<tr><th>ID</th><th>Empresa</th><th>Plano</th><th>Assistência Virtual</th><th>Status</th></tr>";
        while ($row = $result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>{$row['id']}</td>";
            echo "<td>" . htmlspecialchars($row['nome_empresa']) . "</td>";
            echo "<td><strong>{$row['plano_nome']}</strong></td>";
            echo "<td>" . (isset($row['acesso_assistencia_virtual']) ? ($row['acesso_assistencia_virtual'] ? '✅ SIM' : '❌ NÃO') : '❓ N/A') . "</td>";
            echo "<td>{$row['status']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>⚠️ Nenhuma assinatura ativa encontrada</p>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ Erro: " . $e->getMessage() . "</p>";
}

?>

<hr>

<h3>🔗 Próximos Passos:</h3>
<div style="background: #e7f3ff; padding: 15px; border-radius: 5px;">
    <h4>Após corrigir a tabela:</h4>
    <ol>
        <li><strong>Teste o sistema:</strong> <a href="verificar_planos.php">verificar_planos.php</a></li>
        <li><strong>Acesse assistência virtual:</strong> <a href="assistencia_virtual.php">assistencia_virtual.php</a></li>
        <li><strong>Verifique o menu:</strong> <a href="dashboard.php">dashboard.php</a></li>
    </ol>
</div>

<h3>🔗 Links Úteis:</h3>
<ul>
    <li><a href="verificar_planos.php">Verificar Sistema de Planos</a></li>
    <li><a href="dashboard.php">Dashboard</a></li>
    <li><a href="assistencia_virtual.php">Assistência Virtual</a></li>
    <li><a href="../login.php">Login</a></li>
</ul>

<p><small>Correção executada em <?php echo date('d/m/Y H:i:s'); ?></small></p>
