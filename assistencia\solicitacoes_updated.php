<?php
/**
 * Solicitações - Mobile First
 * FixFácil Assistências - Design Moderno
 */

// Redirecionar para versão mobile final
header('Location: solicitacoes.php?' . $_SERVER['QUERY_STRING']);
exit();
?>

// Configuração de banco de dados
$host = "localhost";
$username = "u680766645_fixfacilnew";
$password = "T3cn0l0g1a@";
$database = "u680766645_fixfacilnew";

$mysqli = new mysqli($host, $username, $password, $database);

if ($mysqli->connect_error) {
    die("Erro de conexão: " . $mysqli->connect_error);
}

// Obter dados do usuário logado
$usuario_id = $_SESSION['usuario_id'];
$usuario = null;

try {
    // Buscar dados do usuário
    $sql = "SELECT u.id, u.nome, u.email, u.telefone, u.plano_id, at.id as assistencia_id 
            FROM usuarios u 
            LEFT JOIN assistencias_tecnicas at ON u.id = at.usuario_id 
            WHERE u.id = ?";
    $stmt = $mysqli->prepare($sql);
    $stmt->bind_param("i", $usuario_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $usuario = $result->fetch_assoc();
    
} catch (Exception $e) {
    error_log("Erro ao buscar dados do usuário: " . $e->getMessage());
    $usuario = ['nome' => 'Usuário', 'email' => '', 'assistencia_id' => null];
}

// Filtros
$status_filter = $_GET['status'] ?? 'aguardando_resposta';
$search = $_GET['search'] ?? '';

// Obter estatísticas das solicitações
$stats = [
    'total' => 0,
    'aguardando_resposta' => 0,
    'em_andamento' => 0,
    'concluidas' => 0
];

try {
    if ($usuario && isset($usuario['assistencia_id'])) {
        $assistencia_id = $usuario['assistencia_id'];
        
        // Buscar estatísticas
        $sql_stats = "
            SELECT 
                COUNT(*) as total,
                SUM(CASE WHEN status = 'aguardando_resposta' THEN 1 ELSE 0 END) as aguardando_resposta,
                SUM(CASE WHEN status = 'em_andamento' THEN 1 ELSE 0 END) as em_andamento,
                SUM(CASE WHEN status = 'concluido' THEN 1 ELSE 0 END) as concluidas
            FROM solicitacoes_reparo 
            WHERE assistencia_id = ?
        ";
        
        $stmt = $mysqli->prepare($sql_stats);
        $stmt->bind_param("i", $assistencia_id);
        $stmt->execute();
        $result_stats = $stmt->get_result();
        $stats = $result_stats->fetch_assoc();
    }
} catch (Exception $e) {
    error_log("Erro ao buscar estatísticas: " . $e->getMessage());
}

// Obter solicitações
$solicitacoes = [];
try {
    if ($usuario && isset($usuario['assistencia_id'])) {
        $assistencia_id = $usuario['assistencia_id'];
        
        $where_conditions = ["sr.assistencia_id = ?", "sr.status = ?"];
        $params = [$assistencia_id, $status_filter];
        
        if (!empty($search)) {
            $where_conditions[] = "(sr.descricao_problema LIKE ? OR sr.dispositivo LIKE ? OR u.nome LIKE ?)";
            $search_param = "%$search%";
            $params = array_merge($params, [$search_param, $search_param, $search_param]);
        }
        
        $where_clause = implode(' AND ', $where_conditions);
        
        $sql = "
            SELECT
                sr.id,
                sr.cliente_id,
                sr.dispositivo,
                sr.marca,
                sr.modelo,
                sr.descricao_problema,
                sr.status,
                sr.urgencia,
                sr.orcamento_maximo,
                sr.data_criacao,
                sr.data_atualizacao,
                u.nome as cliente_nome,
                u.telefone as cliente_telefone,
                u.email as cliente_email
            FROM solicitacoes_reparo sr
            LEFT JOIN usuarios u ON sr.cliente_id = u.id
            WHERE $where_clause
            ORDER BY sr.data_criacao DESC
            LIMIT 20
        ";
        
        $stmt = $mysqli->prepare($sql);
        $stmt->bind_param(str_repeat('s', count($params)), ...$params);
        $stmt->execute();
        $result = $stmt->get_result();
        
        while ($row = $result->fetch_assoc()) {
            $solicitacoes[] = $row;
        }
    }
} catch (Exception $e) {
    error_log("Erro ao buscar solicitações: " . $e->getMessage());
}

function getStatusBadge($status) {
    switch ($status) {
        case 'aguardando_resposta':
            return '<div class="status-badge pending">Aguardando</div>';
        case 'em_andamento':
            return '<div class="status-badge active">Em Andamento</div>';
        case 'concluido':
            return '<div class="status-badge completed">Concluído</div>';
        case 'rejeitado':
            return '<div class="status-badge rejected">Rejeitado</div>';
        default:
            return '<div class="status-badge">Desconhecido</div>';
    }
}

function getUrgenciaClass($urgencia) {
    switch ($urgencia) {
        case 'alta':
            return 'urgent';
        case 'media':
            return 'premium';
        default:
            return '';
    }
}

function timeAgo($datetime) {
    $time = time() - strtotime($datetime);
    
    if ($time < 60) return 'agora';
    if ($time < 3600) return round($time/60) . 'min';
    if ($time < 86400) return round($time/3600) . 'h';
    if ($time < 2592000) return round($time/86400) . 'd';
    if ($time < 31536000) return round($time/2592000) . 'mês';
    return round($time/31536000) . 'ano';
}
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Solicitações - FixFacil</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            min-height: 100vh;
            color: #1e293b;
        }

        .container {
            max-width: 414px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
            position: relative;
            overflow-x: hidden;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }

        .header {
            background: linear-gradient(135deg, #059669 0%, #065f46 100%);
            color: white;
            padding: 24px 20px 20px 20px;
            position: relative;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 20"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="0.5" fill="white" opacity="0.05"/></pattern></defs><rect width="100" height="20" fill="url(%23grain)"/></svg>');
        }

        .header-content {
            position: relative;
            z-index: 1;
        }

        .header-top {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 20px;
        }

        .back-btn {
            width: 40px;
            height: 40px;
            background: rgba(255,255,255,0.15);
            border: none;
            border-radius: 12px;
            color: white;
            font-size: 18px;
            cursor: pointer;
            backdrop-filter: blur(10px);
            transition: all 0.2s ease;
            text-decoration: none;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .back-btn:hover {
            background: rgba(255,255,255,0.25);
            color: white;
            text-decoration: none;
        }

        .page-title {
            font-size: 20px;
            font-weight: 700;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .header-actions {
            display: flex;
            gap: 8px;
        }

        .action-btn {
            width: 40px;
            height: 40px;
            background: rgba(255,255,255,0.15);
            border: none;
            border-radius: 12px;
            color: white;
            font-size: 18px;
            cursor: pointer;
            backdrop-filter: blur(10px);
            transition: all 0.2s ease;
            position: relative;
        }

        .action-btn:hover {
            background: rgba(255,255,255,0.25);
        }

        .notification-badge {
            position: absolute;
            top: -2px;
            right: -2px;
            background: #ef4444;
            color: white;
            border-radius: 50%;
            width: 16px;
            height: 16px;
            font-size: 10px;
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr 1fr;
            gap: 8px;
        }

        .stat-card {
            background: rgba(255,255,255,0.15);
            border-radius: 12px;
            padding: 12px 8px;
            text-align: center;
            backdrop-filter: blur(10px);
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .stat-card:hover {
            background: rgba(255,255,255,0.25);
        }

        .stat-card.active {
            background: rgba(255,255,255,0.3);
        }

        .stat-number {
            font-size: 18px;
            font-weight: 700;
            margin-bottom: 4px;
        }

        .stat-label {
            font-size: 10px;
            opacity: 0.9;
        }

        .content {
            padding: 20px;
            padding-bottom: 100px;
        }

        .search-section {
            background: white;
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
        }

        .search-input {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            font-size: 16px;
            transition: all 0.2s ease;
        }

        .search-input:focus {
            outline: none;
            border-color: #059669;
            box-shadow: 0 0 0 3px rgba(5, 150, 105, 0.1);
        }

        .solicitacoes-list {
            background: white;
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
        }

        .solicitacao-item {
            padding: 16px 20px;
            border-bottom: 1px solid #f1f5f9;
            display: flex;
            gap: 12px;
            cursor: pointer;
            transition: background 0.2s ease;
            text-decoration: none;
            color: inherit;
        }

        .solicitacao-item:last-child {
            border-bottom: none;
        }

        .solicitacao-item:hover {
            background: #f8fafc;
            color: inherit;
            text-decoration: none;
        }

        .solicitacao-icon {
            width: 48px;
            height: 48px;
            background: #f0fdf4;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            color: #059669;
            flex-shrink: 0;
        }

        .solicitacao-icon.urgent {
            background: #fef2f2;
            color: #ef4444;
        }

        .solicitacao-icon.premium {
            background: #fef3c7;
            color: #f59e0b;
        }

        .solicitacao-info {
            flex: 1;
        }

        .solicitacao-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 4px;
        }

        .solicitacao-title {
            font-size: 16px;
            font-weight: 600;
            color: #1e293b;
        }

        .solicitacao-time {
            font-size: 12px;
            color: #64748b;
        }

        .solicitacao-description {
            font-size: 14px;
            color: #64748b;
            margin-bottom: 8px;
            line-height: 1.3;
        }

        .solicitacao-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .solicitacao-tags {
            display: flex;
            gap: 6px;
            align-items: center;
        }

        .solicitacao-tag {
            background: #f1f5f9;
            color: #64748b;
            border-radius: 6px;
            padding: 2px 6px;
            font-size: 10px;
            font-weight: 500;
        }

        .solicitacao-tag.device {
            background: #eff6ff;
            color: #3b82f6;
        }

        .solicitacao-tag.location {
            background: #ecfdf5;
            color: #059669;
        }

        .solicitacao-price {
            background: #059669;
            color: white;
            border-radius: 6px;
            padding: 4px 8px;
            font-size: 11px;
            font-weight: 600;
        }

        .status-badge {
            background: #f1f5f9;
            color: #64748b;
            border-radius: 6px;
            padding: 4px 8px;
            font-size: 11px;
            font-weight: 600;
        }

        .status-badge.pending {
            background: #fef3c7;
            color: #f59e0b;
        }

        .status-badge.active {
            background: #dbeafe;
            color: #3b82f6;
        }

        .status-badge.completed {
            background: #dcfce7;
            color: #16a34a;
        }

        .status-badge.rejected {
            background: #fecaca;
            color: #dc2626;
        }

        .empty-state {
            text-align: center;
            padding: 40px 20px;
            color: #64748b;
        }

        .empty-state-icon {
            font-size: 48px;
            margin-bottom: 16px;
        }

        .empty-state-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 8px;
            color: #1e293b;
        }

        .empty-state-description {
            font-size: 14px;
            line-height: 1.5;
        }

        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 100%;
            max-width: 414px;
            background: white;
            border-top: 1px solid #e2e8f0;
            padding: 12px 20px;
            display: flex;
            justify-content: space-around;
            box-shadow: 0 -4px 20px rgba(0,0,0,0.1);
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
            cursor: pointer;
            padding: 8px;
            border-radius: 12px;
            transition: all 0.2s ease;
            position: relative;
            text-decoration: none;
            color: inherit;
        }

        .nav-item.active {
            background: #f0fdf4;
            color: #059669;
        }

        .nav-item:hover {
            color: #059669;
            text-decoration: none;
        }

        .nav-icon {
            font-size: 20px;
        }

        .nav-label {
            font-size: 10px;
            font-weight: 500;
        }

        .nav-badge {
            position: absolute;
            top: 4px;
            right: 4px;
            background: #ef4444;
            color: white;
            border-radius: 50%;
            width: 16px;
            height: 16px;
            font-size: 10px;
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .floating-action {
            position: fixed;
            bottom: 80px;
            right: 20px;
            width: 56px;
            height: 56px;
            background: linear-gradient(135deg, #059669, #065f46);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            cursor: pointer;
            box-shadow: 0 4px 16px rgba(5, 150, 105, 0.4);
            transition: all 0.3s ease;
            z-index: 100;
            text-decoration: none;
        }

        .floating-action:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 20px rgba(5, 150, 105, 0.5);
            color: white;
            text-decoration: none;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .content > * {
            animation: fadeIn 0.6s ease;
        }

        @media (max-width: 480px) {
            .container {
                max-width: 100vw;
                box-shadow: none;
            }
            
            .header {
                padding: 20px 16px 16px 16px;
            }
            
            .content {
                padding: 16px;
                padding-bottom: 100px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="header-content">
                <div class="header-top">
                    <a href="dashboard_mobile.php" class="back-btn">←</a>
                    <div class="page-title">
                        📋 Solicitações
                    </div>
                    <div class="header-actions">
                        <button class="action-btn" onclick="showNotification('🔔 <?php echo $stats['aguardando_resposta']; ?> solicitações pendentes')">
                            🔔
                            <?php if ($stats['aguardando_resposta'] > 0): ?>
                                <div class="notification-badge"><?php echo $stats['aguardando_resposta']; ?></div>
                            <?php endif; ?>
                        </button>
                        <button class="action-btn" onclick="location.reload()">🔄</button>
                    </div>
                </div>

                <div class="stats-grid">
                    <a href="?status=aguardando_resposta" class="stat-card <?php echo $status_filter === 'aguardando_resposta' ? 'active' : ''; ?>">
                        <div class="stat-number"><?php echo $stats['aguardando_resposta']; ?></div>
                        <div class="stat-label">Pendentes</div>
                    </a>
                    <a href="?status=em_andamento" class="stat-card <?php echo $status_filter === 'em_andamento' ? 'active' : ''; ?>">
                        <div class="stat-number"><?php echo $stats['em_andamento']; ?></div>
                        <div class="stat-label">Andamento</div>
                    </a>
                    <a href="?status=concluido" class="stat-card <?php echo $status_filter === 'concluido' ? 'active' : ''; ?>">
                        <div class="stat-number"><?php echo $stats['concluidas']; ?></div>
                        <div class="stat-label">Concluídas</div>
                    </a>
                    <a href="?" class="stat-card <?php echo empty($status_filter) ? 'active' : ''; ?>">
                        <div class="stat-number"><?php echo $stats['total']; ?></div>
                        <div class="stat-label">Total</div>
                    </a>
                </div>
            </div>
        </div>

        <!-- Content -->
        <div class="content">
            <!-- Search Section -->
            <div class="search-section">
                <form method="GET" action="">
                    <input type="hidden" name="status" value="<?php echo htmlspecialchars($status_filter); ?>">
                    <input type="text" name="search" class="search-input" placeholder="🔍 Buscar por dispositivo, problema ou cliente..." value="<?php echo htmlspecialchars($search); ?>">
                </form>
            </div>

            <!-- Solicitações List -->
            <?php if (!empty($solicitacoes)): ?>
            <div class="solicitacoes-list">
                <?php foreach ($solicitacoes as $solicitacao): ?>
                <a href="detalhes_solicitacao.php?id=<?php echo $solicitacao['id']; ?>" class="solicitacao-item">
                    <div class="solicitacao-icon <?php echo getUrgenciaClass($solicitacao['urgencia']); ?>">
                        <?php
                        if ($solicitacao['urgencia'] === 'alta') echo '🚨';
                        elseif ($solicitacao['urgencia'] === 'media') echo '👑';
                        else echo '📱';
                        ?>
                    </div>
                    <div class="solicitacao-info">
                        <div class="solicitacao-header">
                            <div class="solicitacao-title">
                                <?php echo htmlspecialchars($solicitacao['dispositivo'] ?? 'Dispositivo'); ?>
                                <?php if ($solicitacao['marca']): ?>
                                    - <?php echo htmlspecialchars($solicitacao['marca']); ?>
                                <?php endif; ?>
                            </div>
                            <div class="solicitacao-time">
                                <?php echo timeAgo($solicitacao['data_criacao']); ?>
                            </div>
                        </div>
                        <div class="solicitacao-description">
                            <?php echo htmlspecialchars(substr($solicitacao['descricao_problema'], 0, 80) . '...'); ?>
                        </div>
                        <div class="solicitacao-meta">
                            <div class="solicitacao-tags">
                                <?php if ($solicitacao['marca']): ?>
                                    <div class="solicitacao-tag device"><?php echo htmlspecialchars($solicitacao['marca']); ?></div>
                                <?php endif; ?>
                                <div class="solicitacao-tag location"><?php echo htmlspecialchars($solicitacao['cliente_nome'] ?? 'Cliente'); ?></div>
                                <?php if ($solicitacao['orcamento_maximo']): ?>
                                    <div class="solicitacao-price">Até R$ <?php echo number_format($solicitacao['orcamento_maximo'], 0, ',', '.'); ?></div>
                                <?php endif; ?>
                            </div>
                            <?php echo getStatusBadge($solicitacao['status']); ?>
                        </div>
                    </div>
                </a>
                <?php endforeach; ?>
            </div>
            <?php else: ?>
            <div class="solicitacoes-list">
                <div class="empty-state">
                    <div class="empty-state-icon">📋</div>
                    <div class="empty-state-title">Nenhuma solicitação encontrada</div>
                    <div class="empty-state-description">
                        <?php if ($status_filter === 'aguardando_resposta'): ?>
                            Não há solicitações aguardando resposta no momento.
                        <?php elseif ($status_filter === 'em_andamento'): ?>
                            Não há reparos em andamento no momento.
                        <?php elseif ($status_filter === 'concluido'): ?>
                            Não há solicitações concluídas ainda.
                        <?php else: ?>
                            Nenhuma solicitação foi encontrada com os filtros aplicados.
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            <?php endif; ?>
        </div>

        <!-- Bottom Navigation -->
        <div class="bottom-nav">
            <a href="dashboard_mobile.php" class="nav-item">
                <div class="nav-icon">🏠</div>
                <div class="nav-label">Início</div>
            </a>
            <a href="solicitacoes_updated.php" class="nav-item active">
                <div class="nav-icon">📋</div>
                <div class="nav-label">Solicitações</div>
                <?php if ($stats['aguardando_resposta'] > 0): ?>
                    <div class="nav-badge"><?php echo $stats['aguardando_resposta']; ?></div>
                <?php endif; ?>
            </a>
            <a href="reparos.php" class="nav-item">
                <div class="nav-icon">🔧</div>
                <div class="nav-label">Reparos</div>
                <?php if ($stats['em_andamento'] > 0): ?>
                    <div class="nav-badge"><?php echo $stats['em_andamento']; ?></div>
                <?php endif; ?>
            </a>
            <a href="marketplace.php" class="nav-item">
                <div class="nav-icon">🛒</div>
                <div class="nav-label">Loja</div>
            </a>
            <a href="carteira.php" class="nav-item">
                <div class="nav-icon">💳</div>
                <div class="nav-label">Carteira</div>
            </a>
            <a href="perfil.php" class="nav-item">
                <div class="nav-icon">👤</div>
                <div class="nav-label">Perfil</div>
            </a>
        </div>

        <!-- Floating Action Button -->
        <a href="assistencia_virtual.php" class="floating-action" title="Assistência Virtual">
            🤖
        </a>
    </div>

    <script>
        // Função para mostrar notificações
        function showNotification(message) {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                left: 50%;
                transform: translateX(-50%);
                background: #059669;
                color: white;
                padding: 12px 20px;
                border-radius: 8px;
                font-size: 14px;
                font-weight: 600;
                z-index: 2000;
                box-shadow: 0 4px 12px rgba(5, 150, 105, 0.3);
                max-width: 90%;
                text-align: center;
                animation: slideDown 0.3s ease-out;
            `;
            notification.textContent = message;
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.style.opacity = '0';
                setTimeout(() => notification.remove(), 300);
            }, 4000);
        }

        // Auto-refresh a cada 30 segundos
        setInterval(() => {
            console.log('Verificando novas solicitações...');
            // Aqui você pode adicionar código para verificar novas solicitações via AJAX
        }, 30000);

        // Adicionar estilo para animação
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideDown {
                from {
                    opacity: 0;
                    transform: translateX(-50%) translateY(-20px);
                }
                to {
                    opacity: 1;
                    transform: translateX(-50%) translateY(0);
                }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
