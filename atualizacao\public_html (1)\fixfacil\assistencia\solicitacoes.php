<?php
session_start();
if (!isset($_SESSION['usuario_id']) || $_SESSION['tipo_usuario'] != 'assistencia') {
    header('Location: ../login.php');
    exit();
}

$usuario_id = $_SESSION['usuario_id'];
$nome_usuario = $_SESSION['nome'];

// Conexão com o banco de dados
$servername = "localhost";
$username_db = "u682219090_fixfacilnew";
$password_db = "T3cn0l0g1a@";
$dbname = "u682219090_fixfacilnew";

$conn = new mysqli($servername, $username_db, $password_db, $dbname);

if ($conn->connect_error) {
    die("Falha na conexão: " . $conn->connect_error);
}

$conn->set_charset("utf8");

// Obter o assistencia_id correspondente ao usuario_id
$sql_assistencia = "SELECT id FROM assistencias_tecnicas WHERE usuario_id = ?";
$stmt_assistencia = $conn->prepare($sql_assistencia);
$stmt_assistencia->bind_param("i", $usuario_id);
$stmt_assistencia->execute();
$result_assistencia = $stmt_assistencia->get_result();
if ($row_assistencia = $result_assistencia->fetch_assoc()) {
    $assistencia_id = $row_assistencia['id'];
} else {
    // Assistência técnica não encontrada
    die("Assistência técnica não encontrada.");
}
$stmt_assistencia->close();

// Inicializar variáveis para mensagens
$mensagem = "";
$tipo_alerta = "";

// Processar o formulário de envio de proposta quando enviado
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['enviar_proposta'])) {
    // Obter dados do formulário e sanitizar
    $solicitacao_id = intval($_POST['solicitacao_id']);
    $preco = floatval(str_replace(',', '.', $_POST['preco']));
    $prazo = intval($_POST['prazo']);
    $observacoes = $conn->real_escape_string(trim($_POST['observacoes']));

    // Inserir proposta no banco de dados
    $sql_insert = "INSERT INTO propostas_assistencia (assistencia_id, solicitacao_id, preco, prazo, observacoes, status, data_proposta)
                   VALUES (?, ?, ?, ?, ?, 'enviada', NOW())";
    $stmt = $conn->prepare($sql_insert);
    $stmt->bind_param("iidis", $assistencia_id, $solicitacao_id, $preco, $prazo, $observacoes);

    if ($stmt->execute()) {
        $mensagem = "Proposta enviada com sucesso!";
        $tipo_alerta = "success";
    } else {
        $mensagem = "Erro ao enviar a proposta: " . $stmt->error;
        $tipo_alerta = "danger";
    }

    $stmt->close();
}

// Obter as solicitações de reparo
$sql = "SELECT sr.id, sr.descricao_problema, sr.dispositivo, sr.marca, sr.modelo, u.nome AS nome_usuario
        FROM solicitacoes_reparo sr
        INNER JOIN usuarios u ON sr.usuario_id = u.id
        WHERE sr.status = 'enviado'";
$result = $conn->query($sql);
?>
<!-- O restante do código HTML permanece o mesmo -->

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <title>Solicitações de Reparo - FixFácil</title>
    <!-- Meta Tags para Responsividade -->
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <!-- Incluindo Bootstrap CSS (Versão 4.5.2) -->
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <!-- Font Awesome para ícones -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.1/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css?family=Poppins:300,400,500,600,700&display=swap" rel="stylesheet">
    <!-- Estilos personalizados -->
    <style>
        body {
            font-family: 'Poppins', sans-serif;
            background-color: #f7f9fc;
            margin-bottom: 60px; /* Espaço para a navbar inferior */
            color: #495057;
        }
        /* Navbar */
        .navbar {
            background-color: #fff;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .navbar-brand img {
            width: 150px;
        }
        .navbar-nav .nav-link {
            color: #495057 !important;
            font-weight: 500;
            margin-right: 15px;
        }
        .navbar-nav .nav-link.active {
            color: #007BFF !important;
        }
        .navbar-nav .nav-link:hover {
            color: #0056b3 !important;
        }
        /* Conteúdo Principal */
        .main-content {
            padding: 80px 20px 20px; /* Ajuste o padding-top para evitar sobreposição com a navbar fixa */
        }
        .welcome {
            margin-bottom: 40px;
        }
        .welcome h2 {
            font-weight: 600;
            color: #343a40;
        }
        .card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.05);
            background-color: #fff;
            padding: 30px;
            margin-bottom: 20px;
        }
        /* Tabela */
        .table thead th {
            border-bottom: none;
            font-weight: 600;
            color: #343a40;
        }
        .table tbody td {
            vertical-align: middle;
        }
        /* Footer */
        footer.footer {
            background-color: #fff;
            padding: 20px 0;
            position: fixed;
            width: 100%;
            bottom: 0;
            box-shadow: 0 -2px 4px rgba(0,0,0,0.1);
        }
        footer.footer span {
            color: #6c757d;
        }
        /* Navbar Inferior (Mobile) */
        .footer-nav {
            position: fixed;
            bottom: 0;
            width: 100%;
            background-color: #fff;
            box-shadow: 0 -2px 5px rgba(0,0,0,0.1);
            z-index: 1000;
        }
        .footer-nav .nav-link {
            color: #6c757d;
            text-align: center;
            padding: 10px 0;
            font-size: 12px;
        }
        .footer-nav .nav-link.active {
            color: #007BFF;
        }
        .footer-nav .nav-link i {
            font-size: 20px;
        }
        @media (min-width: 768px) {
            .footer-nav {
                display: none;
            }
        }
    </style>
</head>
<body>
    <!-- Cabeçalho -->
     <nav class="navbar navbar-expand-lg navbar-light fixed-top">
        
        <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarNavAssistencia" 
                aria-controls="navbarNavAssistencia" aria-expanded="false" aria-label="Alternar navegação">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse justify-content-end" id="navbarNavAssistencia">
           <ul class="navbar-nav">
                <li class="nav-item">
                    <a class="nav-link " href="home.php"><i class="fas fa-home"></i> Painel</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link active" href="solicitacoes.php"><i class="fas fa-envelope"></i> Solicitações</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="meumarktplace.php"><i class="fas fa-store"></i> Marketplace</a> <!-- Corrigido o link e ícone -->
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="solicitar_pecas.php"><i class="fas fa-plus"></i> Solicitação Peças</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="propostas_enviadas.php"><i class="fas fa-paper-plane"></i> Propostas Enviadas</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="reparos_em_andamento.php"><i class="fas fa-tools"></i> Reparos em Andamento</a>
                </li>
                 <li class="nav-item">
                    <a class="nav-link " href="carteira.php"><i class="fas fa-wallet"></i> Carteira</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="perfil.php"><i class="fas fa-user-circle"></i> Perfil</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="../logout.php"><i class="fas fa-sign-out-alt"></i> Sair</a>
                </li>
            </ul>
        </div>
    </nav>

    <!-- Conteúdo Principal -->
    <div class="container main-content">
        <div class="welcome text-center">
            <h2>Solicitações de Reparo</h2>
            <p class="text-muted">Gerencie as solicitações recebidas dos clientes.</p>
        </div>

        <!-- Exibir Mensagens -->
        <?php if (!empty($mensagem)): ?>
            <div class="alert alert-<?php echo $tipo_alerta; ?>" role="alert">
                <?php echo htmlspecialchars($mensagem); ?>
            </div>
        <?php endif; ?>

        <!-- Lista de Solicitações -->
        <div class="card">
            <div class="table-responsive">
                <table class="table table-striped" id="tabela-solicitacoes">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Cliente</th>
                            <th>Dispositivo</th>
                            <th>Marca</th>
                            <th>Modelo</th>
                            <th>Descrição do Problema</th>
                            <th>Ações</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if ($result->num_rows > 0): ?>
                            <?php while ($solicitacao = $result->fetch_assoc()): ?>
                                <tr>
                                    <td><?php echo $solicitacao['id']; ?></td>
                                    <td><?php echo htmlspecialchars($solicitacao['nome_usuario']); ?></td>
                                    <td><?php echo htmlspecialchars($solicitacao['dispositivo']); ?></td>
                                    <td><?php echo htmlspecialchars($solicitacao['marca']); ?></td>
                                    <td><?php echo htmlspecialchars($solicitacao['modelo']); ?></td>
                                    <td><?php echo htmlspecialchars($solicitacao['descricao_problema']); ?></td>
                                    <td>
                                        <button class="btn btn-primary btn-sm btn-enviar-proposta" data-toggle="modal" data-target="#modalProposta" data-id="<?php echo $solicitacao['id']; ?>">
                                            Enviar Proposta
                                        </button>
                                    </td>
                                </tr>
                            <?php endwhile; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="7" class="text-center">Nenhuma solicitação pendente.</td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Modal para Enviar Proposta -->
    <div class="modal fade" id="modalProposta" tabindex="-1" aria-labelledby="modalPropostaLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <form action="solicitacoes.php" method="POST">
                    <div class="modal-header">
                        <h5 class="modal-title" id="modalPropostaLabel">Enviar Proposta</h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Fechar">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                            <input type="hidden" name="solicitacao_id" id="solicitacao_id_modal" value="">
                            <div class="form-group">
                                <label for="preco">Preço (R$)</label>
                                <input type="text" class="form-control" id="preco" name="preco" required>
                            </div>
                            <div class="form-group">
                                <label for="prazo">Prazo (em dias)</label>
                                <input type="number" class="form-control" id="prazo" name="prazo" required>
                            </div>
                            <div class="form-group">
                                <label for="observacoes">Observações</label>
                                <textarea class="form-control" id="observacoes" name="observacoes" rows="4"></textarea>
                            </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancelar</button>
                        <button type="submit" name="enviar_proposta" class="btn btn-primary">Enviar Proposta</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Barra de Navegação Inferior (Mobile) -->
    <nav class="footer-nav d-md-none">
        <ul class="nav justify-content-around">
            <li class="nav-item">
                <a class="nav-link" href="home.php">
                    <i class="fas fa-home"></i><br>Painel
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link active" href="solicitacoes.php">
                    <i class="fas fa-envelope"></i><br>Solicitações
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="propostas_enviadas.php">
                    <i class="fas fa-paper-plane"></i><br>Propostas
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="reparos_em_andamento.php">
                    <i class="fas fa-tools"></i><br>Reparos
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="perfil.php">
                    <i class="fas fa-user-circle"></i><br>Perfil
                </a>
            </li>
        </ul>
    </nav>

    <!-- Rodapé -->
    <footer class="footer d-none d-md-block">
        <div class="container text-center">
            <span>&copy; <?php echo date("Y"); ?> FixFácil. Todos os direitos reservados.</span>
        </div>
    </footer>

    <!-- Incluindo Bootstrap JS e dependências -->
    <script src="https://code.jquery.com/jquery-3.5.1.min.js" ></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.5.2/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Script para preencher o ID da solicitação no modal -->
    <script>
        $(document).ready(function() {
            $('.btn-enviar-proposta').on('click', function() {
                var solicitacaoId = $(this).data('id');
                $('#solicitacao_id_modal').val(solicitacaoId);
            });
        });
    </script>
</body>
</html>
