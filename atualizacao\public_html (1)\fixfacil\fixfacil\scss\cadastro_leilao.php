<?php
session_start();
require_once 'db.php';

$mensagem = '';

if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['cadastrar_leilao'])) {
    $produto_id = $_POST['produto_id'];
    $data_inicio = $_POST['data_inicio'];
    $data_termino = $_POST['data_termino'];

    try {
        $query = "INSERT INTO leilao (produto_id, lojista_id, status_leilao, data_inicio, data_termino) VALUES (:produto_id, :lojista_id, 'ativo', :data_inicio, :data_termino)";
        $stmt = $pdo->prepare($query);
        $stmt->bindParam(':produto_id', $produto_id);
        $stmt->bindParam(':lojista_id', $_SESSION['user_id']);
        $stmt->bindParam(':data_inicio', $data_inicio);
        $stmt->bindParam(':data_termino', $data_termino);
        $stmt->execute();

        $mensagem = 'Leilão cadastrado com sucesso!';
    } catch (PDOException $e) {
        $mensagem = 'Erro ao cadastrar leilão: ' . $e->getMessage();
    }
}

$query = "SELECT * FROM produtos";
$stmt = $pdo->prepare($query);
$stmt->execute();
$produtos = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="utf-8">
    <title>Cadastro de Leilão</title>
</head>
<body>
    <?php if ($mensagem): ?>
        <div><?= $mensagem ?></div>
    <?php endif; ?>

    <h1>Cadastrar Leilão</h1>

    <form method="post" action="cadastro_leilao.php">
        <select name="produto_id">
            <?php foreach ($produtos as $produto) : ?>
                <option value="<?= $produto['id'] ?>"><?= $produto['nome'] ?></option>
            <?php endforeach; ?>
        </select>
        <input type="date" name="data_inicio" placeholder="Data Início">
        <input type="date" name="data_termino" placeholder="Data Término">
        <button type="submit" name="cadastrar_leilao">Cadastrar Leilão</button>
    </form>
</body>
</html>
