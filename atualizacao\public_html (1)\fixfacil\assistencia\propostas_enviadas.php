<?php
session_start();
if (!isset($_SESSION['usuario_id']) || $_SESSION['tipo_usuario'] != 'assistencia') {
    header('Location: ../login.php');
    exit();
}

$usuario_id = $_SESSION['usuario_id'];
$nome_usuario = $_SESSION['nome'];

// Conexão com o banco de dados
$servername = "localhost";
$username_db = "u682219090_fixfacilnew";
$password_db = "T3cn0l0g1a@";
$dbname = "u682219090_fixfacilnew";

$conn = new mysqli($servername, $username_db, $password_db, $dbname);

if ($conn->connect_error) {
    die("Falha na conexão: " . $conn->connect_error);
}

$conn->set_charset("utf8");

// Inicializar variáveis para mensagens
$mensagem = "";
$tipo_alerta = "";

// Obter o assistencia_id correspondente ao usuario_id
$sql_assistencia = "SELECT id FROM assistencias_tecnicas WHERE usuario_id = ?";
$stmt_assistencia = $conn->prepare($sql_assistencia);
$stmt_assistencia->bind_param("i", $usuario_id);
$stmt_assistencia->execute();
$result_assistencia = $stmt_assistencia->get_result();
if ($row_assistencia = $result_assistencia->fetch_assoc()) {
    $assistencia_id = $row_assistencia['id'];
} else {
    // Assistência técnica não encontrada
    die("Assistência técnica não encontrada.");
}
$stmt_assistencia->close();

// Processar ações de mudança de status ou exclusão
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    if (isset($_POST['alterar_status'])) {
        $proposta_id = intval($_POST['proposta_id']);
        $novo_status = $_POST['novo_status'];

        // Atualizar o status da proposta
        $sql_update_status = "UPDATE propostas_assistencia SET status = ? WHERE id = ? AND assistencia_id = ?";
        $stmt_update = $conn->prepare($sql_update_status);
        $stmt_update->bind_param("sii", $novo_status, $proposta_id, $assistencia_id);
        if ($stmt_update->execute()) {
            $mensagem = "Status da proposta atualizado com sucesso.";
            $tipo_alerta = "success";
        } else {
            $mensagem = "Erro ao atualizar o status da proposta.";
            $tipo_alerta = "danger";
        }
        $stmt_update->close();
    } elseif (isset($_POST['excluir_proposta'])) {
        $proposta_id = intval($_POST['proposta_id']);

        // Excluir a proposta
        $sql_delete_proposta = "DELETE FROM propostas_assistencia WHERE id = ? AND assistencia_id = ? AND status = 'enviada'";
        $stmt_delete = $conn->prepare($sql_delete_proposta);
        $stmt_delete->bind_param("ii", $proposta_id, $assistencia_id);
        if ($stmt_delete->execute()) {
            if ($stmt_delete->affected_rows > 0) {
                $mensagem = "Proposta excluída com sucesso.";
                $tipo_alerta = "success";
            } else {
                $mensagem = "Não é possível excluir esta proposta.";
                $tipo_alerta = "danger";
            }
        } else {
            $mensagem = "Erro ao excluir a proposta.";
            $tipo_alerta = "danger";
        }
        $stmt_delete->close();
    }
}

// Obter as propostas enviadas pela assistência técnica, ordenando as aceitas no topo
$sql = "SELECT pa.id AS proposta_id, pa.preco, pa.prazo, pa.observacoes, pa.status, pa.data_proposta,
        sr.descricao_problema, sr.dispositivo, sr.marca, sr.modelo, u.nome AS nome_cliente
        FROM propostas_assistencia pa
        INNER JOIN solicitacoes_reparo sr ON pa.solicitacao_id = sr.id
        INNER JOIN usuarios u ON sr.usuario_id = u.id
        WHERE pa.assistencia_id = ?
        ORDER BY (CASE WHEN pa.status = 'aceita' THEN 0 ELSE 1 END), pa.data_proposta DESC";
$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $assistencia_id);
$stmt->execute();
$result = $stmt->get_result();
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <title>Propostas Enviadas - FixFácil</title>
    <!-- Meta Tags para Responsividade -->
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <!-- Incluindo Bootstrap CSS (Versão 4.5.2) -->
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <!-- Font Awesome para ícones -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.1/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css?family=Poppins:300,400,500,600,700&display=swap" rel="stylesheet">
    <!-- Estilos personalizados -->
    <style>
        body {
            font-family: 'Poppins', sans-serif;
            background-color: #f7f9fc;
            margin-bottom: 60px; /* Espaço para a navbar inferior */
            color: #495057;
        }
        /* Navbar */
        .navbar {
            background-color: #fff;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .navbar-brand img {
            width: 150px;
        }
        .navbar-nav .nav-link {
            color: #495057 !important;
            font-weight: 500;
            margin-right: 15px;
        }
        .navbar-nav .nav-link.active {
            color: #007BFF !important;
        }
        .navbar-nav .nav-link:hover {
            color: #0056b3 !important;
        }
        /* Conteúdo Principal */
        .main-content {
            padding: 80px 20px 20px; /* Ajuste o padding-top para evitar sobreposição com a navbar fixa */
        }
        .welcome {
            margin-bottom: 40px;
        }
        .welcome h2 {
            font-weight: 600;
            color: #343a40;
        }
        .card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.05);
            background-color: #fff;
            padding: 30px;
            margin-bottom: 20px;
        }
        /* Tabela */
        .table thead th {
            border-bottom: none;
            font-weight: 600;
            color: #343a40;
        }
        .table tbody td {
            vertical-align: middle;
        }
        /* Footer */
        footer.footer {
            background-color: #fff;
            padding: 20px 0;
            position: fixed;
            width: 100%;
            bottom: 0;
            box-shadow: 0 -2px 4px rgba(0,0,0,0.1);
        }
        footer.footer span {
            color: #6c757d;
        }
        /* Navbar Inferior (Mobile) */
        .footer-nav {
            position: fixed;
            bottom: 0;
            width: 100%;
            background-color: #fff;
            box-shadow: 0 -2px 5px rgba(0,0,0,0.1);
            z-index: 1000;
        }
        .footer-nav .nav-link {
            color: #6c757d;
            text-align: center;
            padding: 10px 0;
            font-size: 12px;
        }
        .footer-nav .nav-link.active {
            color: #007BFF;
        }
        .footer-nav .nav-link i {
            font-size: 20px;
        }
        @media (min-width: 768px) {
            .footer-nav {
                display: none;
            }
        }
    </style>
</head>
<body>
    <!-- Cabeçalho -->
   <nav class="navbar navbar-expand-lg navbar-light fixed-top">
       
        <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarNavAssistencia" 
                aria-controls="navbarNavAssistencia" aria-expanded="false" aria-label="Alternar navegação">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse justify-content-end" id="navbarNavAssistencia">
            <ul class="navbar-nav">
                <li class="nav-item">
                    <a class="nav-link " href="home.php"><i class="fas fa-home"></i> Painel</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link " href="solicitacoes.php"><i class="fas fa-envelope"></i> Solicitações</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link " href="meumarktplace.php"><i class="fas fa-store"></i> Marketplace</a> <!-- Corrigido o link e ícone -->
                </li>
                <li class="nav-item">
                    <a class="nav-link " href="solicitar_pecas.php"><i class="fas fa-plus"></i> Solicitação Peças</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link active" href="propostas_enviadas.php"><i class="fas fa-paper-plane"></i> Propostas Enviadas</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="reparos_em_andamento.php"><i class="fas fa-tools"></i> Reparos em Andamento</a>
                </li>
                 <li class="nav-item">
                    <a class="nav-link " href="carteira.php"><i class="fas fa-wallet"></i> Carteira</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="perfil.php"><i class="fas fa-user-circle"></i> Perfil</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="../logout.php"><i class="fas fa-sign-out-alt"></i> Sair</a>
                </li>
                </ul>
        </div>
    </nav>

    <!-- Conteúdo Principal -->
    <div class="container main-content">
        <div class="welcome text-center">
            <h2>Propostas Enviadas</h2>
            <p class="text-muted">Acompanhe as propostas enviadas aos clientes.</p>
        </div>

        <!-- Exibir Mensagens -->
        <?php if (!empty($mensagem)): ?>
            <div class="alert alert-<?php echo $tipo_alerta; ?>" role="alert">
                <?php echo htmlspecialchars($mensagem); ?>
            </div>
        <?php endif; ?>

        <!-- Lista de Propostas -->
        <div class="card">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Cliente</th>
                            <th>Dispositivo</th>
                            <th>Marca</th>
                            <th>Modelo</th>
                            <th>Preço</th>
                            <th>Prazo (dias)</th>
                            <th>Status</th>
                            <th>Data Envio</th>
                            <th>Ações</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if ($result->num_rows > 0): ?>
                            <?php while ($proposta = $result->fetch_assoc()): ?>
                                <tr>
                                    <td><?php echo $proposta['proposta_id']; ?></td>
                                    <td><?php echo htmlspecialchars($proposta['nome_cliente']); ?></td>
                                    <td><?php echo htmlspecialchars($proposta['dispositivo']); ?></td>
                                    <td><?php echo htmlspecialchars($proposta['marca']); ?></td>
                                    <td><?php echo htmlspecialchars($proposta['modelo']); ?></td>
                                    <td>R$ <?php echo number_format($proposta['preco'], 2, ',', '.'); ?></td>
                                    <td><?php echo $proposta['prazo']; ?></td>
                                    <td>
                                        <?php
                                        $status = $proposta['status'];
                                        if ($status == 'enviada') {
                                            echo '<span class="badge badge-warning">Enviada</span>';
                                        } elseif ($status == 'aceita') {
                                            echo '<span class="badge badge-success">Aceita</span>';
                                        } elseif ($status == 'rejeitada') {
                                            echo '<span class="badge badge-danger">Rejeitada</span>';
                                        }
                                        ?>
                                    </td>
                                    <td><?php echo date('d/m/Y H:i', strtotime($proposta['data_proposta'])); ?></td>
                                    <td>
                                        <?php if ($status == 'aceita'): ?>
                                            <!-- Formulário para alterar o status da proposta aceita -->
                                            <?php if ($status == 'aceita' || $status == 'em andamento'): ?>
    <!-- Formulário para alterar o status da proposta -->
    <form method="POST" style="display:inline;">
        <input type="hidden" name="proposta_id" value="<?php echo $proposta['proposta_id']; ?>">
        <select name="novo_status" class="form-control form-control-sm mb-1">
            <option value="aceita" <?php if ($status == 'aceita') echo 'selected'; ?>>Aceita</option>
            <option value="em andamento" <?php if ($status == 'em andamento') echo 'selected'; ?>>Em Andamento</option>
            <option value="concluída" <?php if ($status == 'concluída') echo 'selected'; ?>>Concluída</option>
            <option value="rejeitada">Rejeitada</option>
        </select>
        <button type="submit" name="alterar_status" class="btn btn-primary btn-sm btn-block">Atualizar</button>
    </form>
<?php endif; ?>

                                        <?php elseif ($status == 'enviada'): ?>
                                            <!-- Botão para excluir a proposta enviada -->
                                            <form method="POST" style="display:inline;">
                                                <input type="hidden" name="proposta_id" value="<?php echo $proposta['proposta_id']; ?>">
                                                <button type="submit" name="excluir_proposta" class="btn btn-danger btn-sm" onclick="return confirm('Tem certeza que deseja excluir esta proposta?');"><i class="fas fa-trash-alt"></i> Excluir</button>
                                            </form>
                                        <?php else: ?>
                                            <!-- Nenhuma ação disponível -->
                                            <span>-</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endwhile; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="10" class="text-center">Nenhuma proposta enviada.</td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <!-- Barra de Navegação Inferior (Mobile) -->
    <nav class="footer-nav d-md-none">
        <ul class="nav justify-content-around">
            <li class="nav-item">
                <a class="nav-link" href="home.php">
                    <i class="fas fa-home"></i><br>Painel
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="solicitacoes.php">
                    <i class="fas fa-envelope"></i><br>Solicitações
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link active" href="propostas_enviadas.php">
                    <i class="fas fa-paper-plane"></i><br>Propostas
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="reparos_em_andamento.php">
                    <i class="fas fa-tools"></i><br>Reparos
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="perfil.php">
                    <i class="fas fa-user-circle"></i><br>Perfil
                </a>
            </li>
        </ul>
    </nav>

    <!-- Rodapé -->
    <footer class="footer d-none d-md-block">
        <div class="container text-center">
            <span>&copy; <?php echo date("Y"); ?> FixFácil. Todos os direitos reservados.</span>
        </div>
    </footer>

    <!-- Incluindo Bootstrap JS e dependências -->
    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js" ></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.5.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
