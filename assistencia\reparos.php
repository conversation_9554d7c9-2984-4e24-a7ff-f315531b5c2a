<?php
/**
 * Página de Reparos - Mobile First
 * FixFácil Assistências - Sistema Novo
 */

// Redirecionar para versão mobile-first
header("Location: reparos_new.php?" . $_SERVER['QUERY_STRING']);
exit();

// Código mantido para referência
require_once 'config/auth.php';
require_once 'config/database.php';
require_once 'includes/layout.php';

// Verificar autenticação
$auth = getAuth();
$auth->checkAssistenciaAuth();

// Obter dados do usuário
$usuario = $auth->getUsuarioLogado();
$plano = $auth->getPlanoInfo($usuario['id']);
$db = getDatabase();

// Filtros
$status_filter = $_GET['status'] ?? 'ativos';
$search = $_GET['search'] ?? '';

// Obter reparos
$reparos = [];
try {
    $where_conditions = ["pa.assistencia_id = ?"];
    $params = [$usuario['assistencia_id']];
    
    if ($status_filter === 'ativos') {
        $where_conditions[] = "pa.status IN ('aceita', 'Em Andamento')";
    } elseif ($status_filter === 'concluidos') {
        $where_conditions[] = "pa.status = 'Concluída'";
    } elseif ($status_filter === 'aguardando_pagamento') {
        $where_conditions[] = "pa.status = 'Concluída' AND pa.pago = 0";
    }
    
    if (!empty($search)) {
        $where_conditions[] = "(sr.descricao_problema LIKE ? OR sr.dispositivo LIKE ? OR sr.marca LIKE ? OR sr.modelo LIKE ? OR u.nome LIKE ?)";
        $search_param = "%$search%";
        $params = array_merge($params, [$search_param, $search_param, $search_param, $search_param, $search_param]);
    }
    
    $where_clause = implode(' AND ', $where_conditions);
    
    $sql = "
        SELECT 
            pa.*,
            sr.descricao_problema,
            sr.dispositivo,
            sr.marca,
            sr.modelo,
            sr.memoria,
            sr.metodo_entrega,
            sr.data_solicitacao,
            u.nome as cliente_nome,
            u.telefone as cliente_telefone,
            u.endereco as cliente_endereco,
            u.email as cliente_email
        FROM propostas_assistencia pa
        JOIN solicitacoes_reparo sr ON pa.solicitacao_id = sr.id
        JOIN usuarios u ON sr.usuario_id = u.id
        WHERE $where_clause
        ORDER BY 
            CASE pa.status 
                WHEN 'aceita' THEN 1
                WHEN 'Em Andamento' THEN 2
                WHEN 'Concluída' THEN 3
                ELSE 4
            END,
            pa.data_proposta DESC
        LIMIT 50
    ";
    
    $result = $db->query($sql, $params);
    
    while ($row = $result->fetch_assoc()) {
        $reparos[] = $row;
    }
    
} catch (Exception $e) {
    error_log("Erro ao obter reparos: " . $e->getMessage());
}

// Estatísticas dos reparos
$stats = [];
try {
    $sql = "
        SELECT 
            COUNT(CASE WHEN status = 'aceita' THEN 1 END) as aceitas,
            COUNT(CASE WHEN status = 'Em Andamento' THEN 1 END) as em_andamento,
            COUNT(CASE WHEN status = 'Concluída' THEN 1 END) as concluidas,
            COUNT(CASE WHEN status = 'Concluída' AND pago = 0 THEN 1 END) as aguardando_pagamento,
            COALESCE(AVG(CASE WHEN status = 'Concluída' THEN DATEDIFF(data_conclusao, data_proposta) END), 0) as tempo_medio
        FROM propostas_assistencia 
        WHERE assistencia_id = ? AND status IN ('aceita', 'Em Andamento', 'Concluída')
    ";
    
    $result = $db->query($sql, [$usuario['assistencia_id']]);
    $stats = $result->fetch_assoc();
    
} catch (Exception $e) {
    error_log("Erro ao obter estatísticas: " . $e->getMessage());
    $stats = [
        'aceitas' => 0, 'em_andamento' => 0, 'concluidas' => 0, 
        'aguardando_pagamento' => 0, 'tempo_medio' => 0
    ];
}

// Inicializar layout
$layout = new Layout();
?>

<?php $layout->renderHead("Reparos - FixFácil Assistências"); ?>

<div class="main-wrapper">
    <?php $layout->renderSidebar('reparos'); ?>
    
    <main class="main-content">
        <!-- Header -->
        <div class="content-header">
            <h1 class="page-title">
                <i class="fas fa-tools me-3"></i>
                Gerenciar Reparos
            </h1>
            <p class="page-subtitle">
                Acompanhe e gerencie todos os reparos em andamento
            </p>
        </div>
        
        <!-- Estatísticas -->
        <div class="row g-4 mb-4">
            <div class="col-lg-3 col-md-6">
                <div class="card text-center">
                    <div class="card-body">
                        <div class="bg-success bg-opacity-10 p-3 rounded-circle d-inline-flex mb-2">
                            <i class="fas fa-check-circle text-success fs-4"></i>
                        </div>
                        <h4 class="text-success mb-1"><?php echo $stats['aceitas']; ?></h4>
                        <small class="text-muted">Aceitas</small>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="card text-center">
                    <div class="card-body">
                        <div class="bg-info bg-opacity-10 p-3 rounded-circle d-inline-flex mb-2">
                            <i class="fas fa-cog fa-spin text-info fs-4"></i>
                        </div>
                        <h4 class="text-info mb-1"><?php echo $stats['em_andamento']; ?></h4>
                        <small class="text-muted">Em Andamento</small>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="card text-center">
                    <div class="card-body">
                        <div class="bg-primary bg-opacity-10 p-3 rounded-circle d-inline-flex mb-2">
                            <i class="fas fa-flag-checkered text-primary fs-4"></i>
                        </div>
                        <h4 class="text-primary mb-1"><?php echo $stats['concluidas']; ?></h4>
                        <small class="text-muted">Concluídas</small>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="card text-center">
                    <div class="card-body">
                        <div class="bg-warning bg-opacity-10 p-3 rounded-circle d-inline-flex mb-2">
                            <i class="fas fa-clock text-warning fs-4"></i>
                        </div>
                        <h4 class="text-warning mb-1"><?php echo round($stats['tempo_medio']); ?></h4>
                        <small class="text-muted">Dias Médios</small>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Filtros -->
        <div class="card mb-4">
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-3">
                        <label class="form-label">Status</label>
                        <select name="status" class="form-select">
                            <option value="ativos" <?php echo $status_filter === 'ativos' ? 'selected' : ''; ?>>
                                Reparos Ativos
                            </option>
                            <option value="concluidos" <?php echo $status_filter === 'concluidos' ? 'selected' : ''; ?>>
                                Concluídos
                            </option>
                            <option value="aguardando_pagamento" <?php echo $status_filter === 'aguardando_pagamento' ? 'selected' : ''; ?>>
                                Aguardando Pagamento
                            </option>
                        </select>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">Buscar</label>
                        <input type="text" name="search" class="form-control" 
                               placeholder="Buscar por dispositivo, marca, modelo ou cliente..."
                               value="<?php echo htmlspecialchars($search); ?>">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search me-2"></i>
                                Filtrar
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- Lista de Reparos -->
        <?php if (empty($reparos)): ?>
        <div class="card">
            <div class="card-body text-center py-5">
                <i class="fas fa-tools fs-1 text-muted mb-3"></i>
                <h4 class="text-muted">Nenhum reparo encontrado</h4>
                <p class="text-muted">
                    <?php if ($status_filter === 'ativos'): ?>
                        Você não tem reparos ativos no momento.
                    <?php else: ?>
                        Não há reparos com o status selecionado.
                    <?php endif; ?>
                </p>
                <a href="solicitacoes.php" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>
                    Ver Solicitações
                </a>
            </div>
        </div>
        <?php else: ?>
        <div class="row g-4">
            <?php foreach ($reparos as $reparo): ?>
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-lg-8">
                                <div class="d-flex align-items-start mb-3">
                                    <div class="flex-shrink-0">
                                        <?php
                                        $icon_class = [
                                            'aceita' => 'fas fa-check-circle text-success',
                                            'Em Andamento' => 'fas fa-cog fa-spin text-info',
                                            'Concluída' => 'fas fa-flag-checkered text-primary'
                                        ];
                                        ?>
                                        <div class="bg-light p-3 rounded-circle">
                                            <i class="<?php echo $icon_class[$reparo['status']] ?? 'fas fa-mobile-alt text-primary'; ?> fs-5"></i>
                                        </div>
                                    </div>
                                    <div class="flex-grow-1 ms-3">
                                        <h5 class="mb-1">
                                            <?php echo htmlspecialchars($reparo['marca'] . ' ' . $reparo['modelo']); ?>
                                            <span class="badge bg-secondary ms-2"><?php echo htmlspecialchars($reparo['memoria']); ?></span>
                                        </h5>
                                        <p class="text-muted mb-2">
                                            <i class="fas fa-user me-1"></i>
                                            <?php echo htmlspecialchars($reparo['cliente_nome']); ?>
                                            <i class="fas fa-phone ms-3 me-1"></i>
                                            <a href="tel:<?php echo htmlspecialchars($reparo['cliente_telefone']); ?>" class="text-decoration-none">
                                                <?php echo htmlspecialchars($reparo['cliente_telefone']); ?>
                                            </a>
                                        </p>
                                        <p class="mb-2">
                                            <strong>Problema:</strong> 
                                            <?php echo htmlspecialchars($reparo['descricao_problema']); ?>
                                        </p>
                                        <?php if (!empty($reparo['observacoes'])): ?>
                                        <p class="mb-2">
                                            <strong>Observações:</strong> 
                                            <?php echo nl2br(htmlspecialchars($reparo['observacoes'])); ?>
                                        </p>
                                        <?php endif; ?>
                                        
                                        <!-- Timeline do reparo -->
                                        <div class="mt-3">
                                            <small class="text-muted d-block mb-2">Timeline do Reparo:</small>
                                            <div class="d-flex align-items-center text-muted small">
                                                <i class="fas fa-calendar me-1"></i>
                                                Aceita em <?php echo date('d/m/Y', strtotime($reparo['data_proposta'])); ?>
                                                
                                                <?php if ($reparo['status'] === 'Em Andamento' && $reparo['data_inicio']): ?>
                                                <i class="fas fa-play ms-3 me-1"></i>
                                                Iniciado em <?php echo date('d/m/Y', strtotime($reparo['data_inicio'])); ?>
                                                <?php endif; ?>
                                                
                                                <?php if ($reparo['status'] === 'Concluída' && $reparo['data_conclusao']): ?>
                                                <i class="fas fa-flag-checkered ms-3 me-1"></i>
                                                Concluído em <?php echo date('d/m/Y', strtotime($reparo['data_conclusao'])); ?>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-4">
                                <div class="text-lg-end">
                                    <div class="mb-3">
                                        <?php
                                        $status_class = [
                                            'aceita' => 'success',
                                            'Em Andamento' => 'info',
                                            'Concluída' => 'primary'
                                        ];
                                        ?>
                                        <span class="badge bg-<?php echo $status_class[$reparo['status']] ?? 'secondary'; ?> fs-6">
                                            <?php echo htmlspecialchars($reparo['status']); ?>
                                        </span>
                                        
                                        <?php if ($reparo['retirada_expressa']): ?>
                                        <div class="mt-1">
                                            <span class="badge bg-warning">
                                                <i class="fas fa-motorcycle me-1"></i>
                                                Express
                                            </span>
                                        </div>
                                        <?php endif; ?>
                                        
                                        <?php if ($reparo['status'] === 'Concluída' && !$reparo['pago']): ?>
                                        <div class="mt-1">
                                            <span class="badge bg-danger">
                                                <i class="fas fa-exclamation-triangle me-1"></i>
                                                Aguardando Pagamento
                                            </span>
                                        </div>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <div class="row g-2 text-center">
                                            <div class="col-6">
                                                <small class="text-muted d-block">Valor</small>
                                                <strong class="text-primary">R$ <?php echo number_format($reparo['preco'], 2, ',', '.'); ?></strong>
                                            </div>
                                            <div class="col-6">
                                                <small class="text-muted d-block">Prazo</small>
                                                <strong><?php echo $reparo['prazo']; ?> dia(s)</strong>
                                            </div>
                                        </div>
                                        
                                        <?php if ($reparo['status'] === 'Concluída' && $reparo['pago']): ?>
                                        <div class="mt-2">
                                            <small class="text-success d-block">Você recebeu</small>
                                            <strong class="text-success">
                                                R$ <?php echo number_format($reparo['preco'] * (1 - $plano['taxa_servico']/100), 2, ',', '.'); ?>
                                            </strong>
                                        </div>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <div class="d-grid gap-2">
                                        <?php if ($reparo['status'] === 'aceita'): ?>
                                        <button type="button" class="btn btn-success btn-sm" 
                                                onclick="iniciarReparo(<?php echo $reparo['id']; ?>)">
                                            <i class="fas fa-play me-2"></i>
                                            Iniciar Reparo
                                        </button>
                                        <?php elseif ($reparo['status'] === 'Em Andamento'): ?>
                                        <button type="button" class="btn btn-primary btn-sm" 
                                                onclick="concluirReparo(<?php echo $reparo['id']; ?>)">
                                            <i class="fas fa-check me-2"></i>
                                            Concluir Reparo
                                        </button>
                                        <?php endif; ?>
                                        
                                        <?php if ($auth->hasAccess('chat')): ?>
                                        <a href="chat.php?proposta_id=<?php echo $reparo['id']; ?>" 
                                           class="btn btn-outline-info btn-sm">
                                            <i class="fas fa-comments me-2"></i>
                                            Chat com Cliente
                                        </a>
                                        <?php endif; ?>
                                        
                                        <a href="detalhes_solicitacao.php?id=<?php echo $reparo['solicitacao_id']; ?>" 
                                           class="btn btn-outline-secondary btn-sm">
                                            <i class="fas fa-eye me-2"></i>
                                            Ver Detalhes
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
        <?php endif; ?>
    </main>
</div>

<!-- Modal para atualizar status -->
<div class="modal fade" id="statusModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="statusModalTitle">Atualizar Status</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="statusForm">
                    <input type="hidden" id="proposta_id" name="proposta_id">
                    <input type="hidden" id="novo_status" name="novo_status">
                    
                    <div class="mb-3">
                        <label for="observacoes_status" class="form-label">Observações (opcional)</label>
                        <textarea class="form-control" id="observacoes_status" name="observacoes_status" 
                                  rows="3" placeholder="Adicione observações sobre esta atualização..."></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                <button type="button" class="btn btn-primary" onclick="confirmarStatus()">Confirmar</button>
            </div>
        </div>
    </div>
</div>

<?php 
$extraJS = "
<script>
function iniciarReparo(propostaId) {
    document.getElementById('proposta_id').value = propostaId;
    document.getElementById('novo_status').value = 'Em Andamento';
    document.getElementById('statusModalTitle').textContent = 'Iniciar Reparo';
    
    const modal = new bootstrap.Modal(document.getElementById('statusModal'));
    modal.show();
}

function concluirReparo(propostaId) {
    document.getElementById('proposta_id').value = propostaId;
    document.getElementById('novo_status').value = 'Concluída';
    document.getElementById('statusModalTitle').textContent = 'Concluir Reparo';
    
    const modal = new bootstrap.Modal(document.getElementById('statusModal'));
    modal.show();
}

function confirmarStatus() {
    const form = document.getElementById('statusForm');
    const formData = new FormData(form);
    
    fetch('ajax/atualizar_status_reparo.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Erro: ' + data.message);
        }
    })
    .catch(error => {
        alert('Erro ao atualizar status');
        console.error(error);
    });
}
</script>
";

$layout->renderFooter($extraJS); 
?>
