<?php
/**
 * Detalhes da Solicitação
 * FixFácil Assistências - Sistema Novo
 */

require_once 'config/auth.php';
require_once 'config/database.php';
require_once 'includes/layout.php';

// Verificar autenticação
$auth = getAuth();
$auth->checkAssistenciaAuth();

// Obter dados do usuário
$usuario = $auth->getUsuarioLogado();
$plano = $auth->getPlanoInfo($usuario['id']);
$db = getDatabase();

// Obter ID da solicitação
$solicitacao_id = $_GET['id'] ?? 0;

if (!$solicitacao_id) {
    header('Location: solicitacoes.php');
    exit();
}

// Obter dados da solicitação
$solicitacao = null;
$propostas = [];

try {
    // Dados da solicitação
    $sql = "
        SELECT 
            sr.*,
            u.nome as cliente_nome,
            u.telefone as cliente_telefone,
            u.endereco as cliente_endereco,
            u.email as cliente_email
        FROM solicitacoes_reparo sr
        JOIN usuarios u ON sr.usuario_id = u.id
        WHERE sr.id = ?
    ";
    
    $result = $db->query($sql, [$solicitacao_id]);
    $solicitacao = $result->fetch_assoc();
    
    if (!$solicitacao) {
        header('Location: solicitacoes.php');
        exit();
    }
    
    // Propostas para esta solicitação
    $sql = "
        SELECT 
            pa.*,
            at.nome_empresa,
            u.nome as assistencia_nome
        FROM propostas_assistencia pa
        JOIN assistencias_tecnicas at ON pa.assistencia_id = at.id
        JOIN usuarios u ON at.usuario_id = u.id
        WHERE pa.solicitacao_id = ?
        ORDER BY pa.data_proposta DESC
    ";
    
    $result = $db->query($sql, [$solicitacao_id]);
    while ($row = $result->fetch_assoc()) {
        $propostas[] = $row;
    }
    
} catch (Exception $e) {
    error_log("Erro ao obter detalhes da solicitação: " . $e->getMessage());
    header('Location: solicitacoes.php');
    exit();
}

// Verificar se já enviei proposta
$minha_proposta = null;
foreach ($propostas as $proposta) {
    if ($proposta['assistencia_id'] == $usuario['assistencia_id']) {
        $minha_proposta = $proposta;
        break;
    }
}

// Inicializar layout
$layout = new Layout();
?>

<?php $layout->renderHead("Detalhes da Solicitação - FixFácil Assistências"); ?>

<div class="main-wrapper">
    <?php $layout->renderSidebar('solicitacoes'); ?>
    
    <main class="main-content">
        <!-- Header -->
        <div class="content-header">
            <div class="d-flex align-items-center justify-content-between">
                <div>
                    <h1 class="page-title">
                        <i class="fas fa-file-alt me-3"></i>
                        Detalhes da Solicitação
                    </h1>
                    <p class="page-subtitle">
                        Solicitação #<?php echo $solicitacao['id']; ?> - 
                        <?php echo htmlspecialchars($solicitacao['marca'] . ' ' . $solicitacao['modelo']); ?>
                    </p>
                </div>
                <div>
                    <a href="solicitacoes.php" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>
                        Voltar
                    </a>
                    <?php if ($solicitacao['status'] === 'enviado' && !$minha_proposta): ?>
                    <a href="enviar_proposta.php?solicitacao_id=<?php echo $solicitacao['id']; ?>" 
                       class="btn btn-primary ms-2">
                        <i class="fas fa-paper-plane me-2"></i>
                        Enviar Proposta
                    </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <div class="row g-4">
            <!-- Informações do Dispositivo -->
            <div class="col-lg-8">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-mobile-alt me-2"></i>
                            Informações do Dispositivo
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label class="form-label text-muted">Dispositivo</label>
                                <p class="fw-bold"><?php echo htmlspecialchars($solicitacao['dispositivo']); ?></p>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label text-muted">Marca</label>
                                <p class="fw-bold"><?php echo htmlspecialchars($solicitacao['marca']); ?></p>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label text-muted">Modelo</label>
                                <p class="fw-bold"><?php echo htmlspecialchars($solicitacao['modelo']); ?></p>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label text-muted">Memória</label>
                                <p class="fw-bold"><?php echo htmlspecialchars($solicitacao['memoria']); ?></p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Descrição do Problema -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            Descrição do Problema
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label text-muted">Problema Relatado</label>
                            <p><?php echo nl2br(htmlspecialchars($solicitacao['descricao_problema'])); ?></p>
                        </div>
                        
                        <?php if (!empty($solicitacao['descricao_detalhada'])): ?>
                        <div class="mb-3">
                            <label class="form-label text-muted">Descrição Detalhada</label>
                            <p><?php echo nl2br(htmlspecialchars($solicitacao['descricao_detalhada'])); ?></p>
                        </div>
                        <?php endif; ?>
                        
                        <?php if (!empty($solicitacao['verificacoes'])): ?>
                        <div class="mb-3">
                            <label class="form-label text-muted">Verificações Realizadas</label>
                            <div class="d-flex flex-wrap gap-2">
                                <?php 
                                $verificacoes = explode(',', $solicitacao['verificacoes']);
                                $verificacoes_labels = [
                                    'conector_carga' => 'Conector de Carga',
                                    'entrada_fone' => 'Entrada de Fone',
                                    'riscos' => 'Riscos na Tela',
                                    'cameras' => 'Câmeras',
                                    'wifi' => 'Wi-Fi',
                                    'bluetooth' => 'Bluetooth',
                                    'nao_possivel_verificar' => 'Não foi possível verificar'
                                ];
                                
                                foreach ($verificacoes as $verificacao) {
                                    $verificacao = trim($verificacao);
                                    if (isset($verificacoes_labels[$verificacao])) {
                                        echo '<span class="badge bg-info">' . $verificacoes_labels[$verificacao] . '</span>';
                                    }
                                }
                                ?>
                            </div>
                        </div>
                        <?php endif; ?>
                        
                        <?php if (!empty($solicitacao['video'])): ?>
                        <div class="mb-3">
                            <label class="form-label text-muted">Vídeo do Problema</label>
                            <div>
                                <button type="button" class="btn btn-outline-primary" 
                                        onclick="verVideo('<?php echo htmlspecialchars($solicitacao['video']); ?>')">
                                    <i class="fas fa-play me-2"></i>
                                    Assistir Vídeo
                                </button>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
                
                <!-- Propostas -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-paper-plane me-2"></i>
                            Propostas Enviadas (<?php echo count($propostas); ?>)
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($propostas)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-paper-plane fs-1 text-muted mb-3"></i>
                            <h6 class="text-muted">Nenhuma proposta enviada ainda</h6>
                            <p class="text-muted">Seja o primeiro a enviar uma proposta para esta solicitação!</p>
                        </div>
                        <?php else: ?>
                        <div class="list-group list-group-flush">
                            <?php foreach ($propostas as $proposta): ?>
                            <div class="list-group-item border-0 px-0 <?php echo $proposta['assistencia_id'] == $usuario['assistencia_id'] ? 'bg-light' : ''; ?>">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div class="flex-grow-1">
                                        <h6 class="mb-1">
                                            <?php echo htmlspecialchars($proposta['nome_empresa']); ?>
                                            <?php if ($proposta['assistencia_id'] == $usuario['assistencia_id']): ?>
                                                <span class="badge bg-primary ms-2">Sua Proposta</span>
                                            <?php endif; ?>
                                        </h6>
                                        <p class="mb-1">
                                            <strong>Preço:</strong> R$ <?php echo number_format($proposta['preco'], 2, ',', '.'); ?>
                                            <strong class="ms-3">Prazo:</strong> <?php echo $proposta['prazo']; ?> dia(s)
                                        </p>
                                        <?php if (!empty($proposta['observacoes'])): ?>
                                        <p class="mb-1 text-muted">
                                            <?php echo nl2br(htmlspecialchars($proposta['observacoes'])); ?>
                                        </p>
                                        <?php endif; ?>
                                        <small class="text-muted">
                                            Enviada em <?php echo date('d/m/Y H:i', strtotime($proposta['data_proposta'])); ?>
                                        </small>
                                    </div>
                                    <div class="text-end">
                                        <?php
                                        $status_class = [
                                            'enviada' => 'warning',
                                            'aceita' => 'success',
                                            'rejeitada' => 'danger',
                                            'Em Andamento' => 'info',
                                            'Concluída' => 'primary',
                                            'pagamento' => 'success'
                                        ];
                                        ?>
                                        <span class="badge bg-<?php echo $status_class[$proposta['status']] ?? 'secondary'; ?>">
                                            <?php echo htmlspecialchars($proposta['status']); ?>
                                        </span>
                                        <?php if ($proposta['retirada_expressa']): ?>
                                        <div class="mt-1">
                                            <span class="badge bg-info">
                                                <i class="fas fa-motorcycle me-1"></i>
                                                Retirada Express
                                            </span>
                                        </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            
            <!-- Informações do Cliente -->
            <div class="col-lg-4">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-user me-2"></i>
                            Informações do Cliente
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label text-muted">Nome</label>
                            <p class="fw-bold"><?php echo htmlspecialchars($solicitacao['cliente_nome']); ?></p>
                        </div>
                        <div class="mb-3">
                            <label class="form-label text-muted">Telefone</label>
                            <p>
                                <a href="tel:<?php echo htmlspecialchars($solicitacao['cliente_telefone']); ?>" 
                                   class="text-decoration-none">
                                    <i class="fas fa-phone me-1"></i>
                                    <?php echo htmlspecialchars($solicitacao['cliente_telefone']); ?>
                                </a>
                            </p>
                        </div>
                        <div class="mb-3">
                            <label class="form-label text-muted">E-mail</label>
                            <p>
                                <a href="mailto:<?php echo htmlspecialchars($solicitacao['cliente_email']); ?>" 
                                   class="text-decoration-none">
                                    <i class="fas fa-envelope me-1"></i>
                                    <?php echo htmlspecialchars($solicitacao['cliente_email']); ?>
                                </a>
                            </p>
                        </div>
                        <div class="mb-3">
                            <label class="form-label text-muted">Endereço</label>
                            <p><?php echo htmlspecialchars($solicitacao['cliente_endereco']); ?></p>
                        </div>
                    </div>
                </div>
                
                <!-- Informações da Solicitação -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-info-circle me-2"></i>
                            Informações da Solicitação
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label text-muted">Status</label>
                            <?php
                            $status_class = [
                                'enviado' => 'warning',
                                'aceita' => 'success',
                                'rejeitada' => 'danger',
                                'concluido' => 'primary'
                            ];
                            $status_text = [
                                'enviado' => 'Pendente',
                                'aceita' => 'Aceita',
                                'rejeitada' => 'Rejeitada',
                                'concluido' => 'Concluída'
                            ];
                            ?>
                            <p>
                                <span class="badge bg-<?php echo $status_class[$solicitacao['status']]; ?> fs-6">
                                    <?php echo $status_text[$solicitacao['status']]; ?>
                                </span>
                            </p>
                        </div>
                        <div class="mb-3">
                            <label class="form-label text-muted">Data da Solicitação</label>
                            <p><?php echo date('d/m/Y H:i', strtotime($solicitacao['data_solicitacao'])); ?></p>
                        </div>
                        <div class="mb-3">
                            <label class="form-label text-muted">Método de Entrega</label>
                            <p>
                                <i class="fas fa-truck me-1"></i>
                                <?php echo htmlspecialchars($solicitacao['metodo_entrega']); ?>
                            </p>
                        </div>
                        <?php if ($solicitacao['termos_concordo']): ?>
                        <div class="mb-3">
                            <span class="badge bg-success">
                                <i class="fas fa-check me-1"></i>
                                Termos Aceitos
                            </span>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </main>
</div>

<!-- Modal para vídeo -->
<div class="modal fade" id="videoModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Vídeo do Problema</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <video id="videoPlayer" class="w-100" controls>
                    Seu navegador não suporta vídeos.
                </video>
            </div>
        </div>
    </div>
</div>

<?php 
$extraJS = "
<script>
function verVideo(videoUrl) {
    const modal = new bootstrap.Modal(document.getElementById('videoModal'));
    const video = document.getElementById('videoPlayer');
    video.src = videoUrl;
    modal.show();
    
    // Pausar vídeo quando modal fechar
    document.getElementById('videoModal').addEventListener('hidden.bs.modal', function() {
        video.pause();
        video.currentTime = 0;
    });
}
</script>
";

$layout->renderFooter($extraJS); 
?>
