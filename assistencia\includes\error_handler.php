<?php
/**
 * Sistema de Tratamento de Erros
 * FixFácil Assistências
 */

class ErrorHandler {
    
    public static function handleError($errno, $errstr, $errfile, $errline) {
        // Log do erro
        error_log("PHP Error: [$errno] $errstr in $errfile:$errline");
        
        // Se for um erro crítico, redirecionar
        if ($errno === E_ERROR || $errno === E_PARSE || $errno === E_CORE_ERROR) {
            self::redirectToError("Erro crítico do sistema", "500");
        }
        
        return false; // Permite que o PHP continue o tratamento normal
    }
    
    public static function handleException($exception) {
        // Log da exceção
        error_log("Exception: " . $exception->getMessage() . " in " . $exception->getFile() . ":" . $exception->getLine());
        
        // Verificar se é erro de banco
        if (strpos($exception->getMessage(), 'database') !== false || 
            strpos($exception->getMessage(), 'mysql') !== false ||
            strpos($exception->getMessage(), 'connection') !== false) {
            self::redirectToError("Erro de conexão com banco de dados", "503");
        }
        
        // Verificar se é erro de layout
        if (strpos($exception->getMessage(), 'Layout') !== false) {
            // Redirecionar para versão móvel
            header('Location: dashboard_new.php');
            exit();
        }
        
        // Erro genérico
        self::redirectToError("Erro interno do servidor", "500");
    }
    
    public static function redirectToError($message, $code = "500") {
        // Limpar output buffer
        if (ob_get_level()) {
            ob_end_clean();
        }
        
        // Verificar se estamos em AJAX
        if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && 
            strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest') {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'error' => $message]);
            exit();
        }
        
        // Redirecionar para página de erro
        header("Location: erro.php?erro=" . urlencode($message) . "&codigo=" . urlencode($code));
        exit();
    }
    
    public static function setup() {
        // Configurar handlers
        set_error_handler([self::class, 'handleError']);
        set_exception_handler([self::class, 'handleException']);
        
        // Configurar relatórios de erro
        if (defined('ENVIRONMENT') && constant('ENVIRONMENT') === 'development') {
            error_reporting(E_ALL);
            ini_set('display_errors', 1);
        } else {
            error_reporting(E_ALL & ~E_NOTICE & ~E_DEPRECATED & ~E_STRICT);
            ini_set('display_errors', 0);
        }
        
        // Configurar log
        ini_set('log_errors', 1);
        ini_set('error_log', __DIR__ . '/../logs/error.log');
    }
    
    public static function checkDependencies() {
        $required_files = [
            'config/database.php',
            'config/auth.php',
            'includes/layout.php'
        ];
        
        foreach ($required_files as $file) {
            if (!file_exists(__DIR__ . '/../' . $file)) {
                throw new Exception("Arquivo necessário não encontrado: $file");
            }
        }
    }
    
    public static function validateDatabase() {
        try {
            require_once __DIR__ . '/../config/database.php';
            $db = getDatabase();
            
            // Test query
            $result = $db->query("SELECT 1");
            if (!$result) {
                throw new Exception("Não foi possível conectar ao banco de dados");
            }
            
            return true;
        } catch (Exception $e) {
            error_log("Erro de validação do banco: " . $e->getMessage());
            return false;
        }
    }
}

// Função helper para uso global
function handleCriticalError($message, $code = "500") {
    ErrorHandler::redirectToError($message, $code);
}

// Função para verificar se o sistema está funcionando
function checkSystemHealth() {
    try {
        ErrorHandler::checkDependencies();
        
        if (!ErrorHandler::validateDatabase()) {
            throw new Exception("Banco de dados indisponível");
        }
        
        return true;
    } catch (Exception $e) {
        error_log("Health check falhou: " . $e->getMessage());
        return false;
    }
}
?>
