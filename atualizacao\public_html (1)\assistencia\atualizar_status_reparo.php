<?php
session_start();
if (!isset($_SESSION['usuario_id']) || $_SESSION['tipo_usuario'] != 'assistencia') {
    header('Location: ../login.php');
    exit();
}

$usuario_id = $_SESSION['usuario_id'];

// Verificar se o formulário foi submetido
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['alterar_status'])) {
    $proposta_id = intval($_POST['proposta_id']);
    $novo_status = $_POST['novo_status'];

    // Conexão com o banco de dados
    $servername = "localhost";
    $username_db = "u680766645_fixfacilnew";
    $password_db = "T3cn0l0g1a@";
    $dbname = "u680766645_fixfacilnew";
    

    $conn = new mysqli($servername, $username_db, $password_db, $dbname);

    if ($conn->connect_error) {
        die("Falha na conexão: " . $conn->connect_error);
    }

    $conn->set_charset("utf8");

    // Obter o assistencia_id correspondente ao usuario_id
    $sql_assistencia = "SELECT id FROM assistencias_tecnicas WHERE usuario_id = ?";
    $stmt_assistencia = $conn->prepare($sql_assistencia);
    $stmt_assistencia->bind_param("i", $usuario_id);
    $stmt_assistencia->execute();
    $result_assistencia = $stmt_assistencia->get_result();
    if ($row_assistencia = $result_assistencia->fetch_assoc()) {
        $assistencia_id = $row_assistencia['id'];
    } else {
        die("Assistência técnica não encontrada.");
    }
    $stmt_assistencia->close();

    // Atualizar o status da proposta
    $sql_update_status = "UPDATE propostas_assistencia SET status = ? WHERE id = ? AND assistencia_id = ?";
    $stmt_update = $conn->prepare($sql_update_status);
    $stmt_update->bind_param("sii", $novo_status, $proposta_id, $assistencia_id);
    if ($stmt_update->execute()) {
        $mensagem = "Status do reparo atualizado com sucesso.";
        $tipo_alerta = "success";
    } else {
        $mensagem = "Erro ao atualizar o status do reparo.";
        $tipo_alerta = "danger";
    }
    $stmt_update->close();

    $conn->close();

    // Redirecionar de volta para a página de reparos em andamento
    header("Location: reparos_em_andamento.php?mensagem=" . urlencode($mensagem) . "&tipo_alerta=" . $tipo_alerta);
    exit();
} else {
    // Se acesso direto, redirecionar
    header("Location: reparos_em_andamento.php");
    exit();
}
?>
