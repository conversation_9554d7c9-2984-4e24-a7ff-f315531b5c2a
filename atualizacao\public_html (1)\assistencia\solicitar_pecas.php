<?php
// **Ativar exibição de erros (apenas para desenvolvimento)**
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

session_start();

// Verificar se o usuário está logado e é uma assistência técnica
if (!isset($_SESSION['usuario_id']) || $_SESSION['tipo_usuario'] != 'assistencia') {
    header('Location: ../login.php');
    exit();
}

$usuario_id = $_SESSION['usuario_id'];
$nome_usuario = $_SESSION['nome']; // Nome do usuário ou empresa

// Conexão com o banco de dados
$servername = "localhost";
$username_db = "u680766645_fixfacilnew";
$password_db = "T3cn0l0g1a@";
$dbname = "u680766645_fixfacilnew";

// Ativar exceções para erros do MySQLi
mysqli_report(MYSQLI_REPORT_ERROR | MYSQLI_REPORT_STRICT);

try {
    $conn = new mysqli($servername, $username_db, $password_db, $dbname);
    $conn->set_charset("utf8");
} catch (mysqli_sql_exception $e) {
    die("Falha na conexão: " . $e->getMessage());
}

// Obter o assistencia_id correspondente ao usuario_id
$sql_assistencia = "SELECT id FROM assistencias_tecnicas WHERE usuario_id = ?";
$stmt_assistencia = $conn->prepare($sql_assistencia);
$stmt_assistencia->bind_param("i", $usuario_id);
$stmt_assistencia->execute();
$result_assistencia = $stmt_assistencia->get_result();

if ($row_assistencia = $result_assistencia->fetch_assoc()) {
    $assistencia_id = $row_assistencia['id'];
} else {
    // Assistência técnica não encontrada
    die("Assistência técnica não encontrada.");
}
$stmt_assistencia->close();
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <title>Solicitar Peças - FixFácil</title>
    <!-- Meta Tags para Responsividade -->
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome 6 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- SweetAlert2 CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Estilos personalizados -->
    <style>
        :root {
            --primary-color: #2563eb;
            --primary-dark: #1d4ed8;
            --secondary-color: #475569;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --light-bg: #f8fafc;
            --card-bg: #ffffff;
            --text-color: #1e293b;
            --text-muted: #64748b;
            --border-radius: 12px;
            --shadow-sm: 0 1px 3px rgba(0,0,0,0.1);
            --shadow-md: 0 4px 6px -1px rgba(0,0,0,0.1), 0 2px 4px -1px rgba(0,0,0,0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0,0,0,0.1), 0 4px 6px -2px rgba(0,0,0,0.05);
        }
        
        body {
            font-family: 'Inter', sans-serif;
            background-color: var(--light-bg);
            color: var(--text-color);
            margin-bottom: 80px;
            padding-top: 70px;
        }
        
        /* Navbar */
        .navbar {
            background-color: var(--card-bg);
            box-shadow: var(--shadow-sm);
            padding: 12px 0;
        }
        
        .navbar-brand {
            font-weight: 700;
            color: var(--primary-color) !important;
        }
        
        .navbar .nav-link {
            color: var(--text-color) !important;
            font-weight: 500;
            padding: 8px 16px;
            border-radius: var(--border-radius);
            transition: all 0.3s ease;
        }
        
        .navbar .nav-link:hover {
            background-color: rgba(37, 99, 235, 0.1);
            color: var(--primary-color) !important;
        }
        
        .navbar .nav-link.active {
            background-color: var(--primary-color);
            color: white !important;
        }
        
        /* Conteúdo Principal */
        .main-content {
            padding: 20px 12px;
        }
        
        .header-section {
            margin-bottom: 24px;
        }
        
        .header-section h1 {
            font-weight: 700;
            font-size: 1.75rem;
            margin-bottom: 8px;
            color: var(--text-color);
        }
        
        .header-section p {
            color: var(--text-muted);
            font-size: 1rem;
            margin-bottom: 0;
        }
        
        /* Content card */
        .content-card {
            background-color: var(--card-bg);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-md);
            padding: 25px;
            margin-bottom: 30px;
        }
        
        /* Feature unavailable section */
        .feature-unavailable {
            text-align: center;
            padding: 40px 20px;
        }
        
        .feature-unavailable .icon-container {
            margin-bottom: 24px;
            background-color: rgba(37, 99, 235, 0.1);
            width: 100px;
            height: 100px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: auto;
            margin-right: auto;
        }
        
        .feature-unavailable .icon {
            color: var(--primary-color);
            font-size: 3rem;
        }
        
        .feature-unavailable h2 {
            font-weight: 700;
            color: var(--text-color);
            margin-bottom: 16px;
        }
        
        .feature-unavailable p {
            color: var(--text-muted);
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
            margin-bottom: 24px;
            font-size: 1.1rem;
        }
        
        .soon-badge {
            display: inline-block;
            background-color: var(--primary-color);
            color: white;
            padding: 6px 16px;
            border-radius: 50px;
            font-weight: 600;
            font-size: 0.9rem;
            margin-bottom: 24px;
        }
        
        /* Mobile Menu */
        .mobile-menu {
            position: fixed;
            bottom: 0;
            width: 100%;
            background-color: var(--card-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            z-index: 1000;
            padding: 8px 0;
            display: flex;
            justify-content: space-around;
        }
        
        .mobile-menu .menu-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 8px 0;
            width: 20%;
            text-decoration: none;
        }
        
        .mobile-menu .menu-item i {
            font-size: 20px;
            margin-bottom: 4px;
            color: var(--text-muted);
            transition: color 0.3s ease;
        }
        
        .mobile-menu .menu-item span {
            font-size: 12px;
            color: var(--text-muted);
            transition: color 0.3s ease;
        }
        
        .mobile-menu .menu-item.active i,
        .mobile-menu .menu-item.active span {
            color: var(--primary-color);
            font-weight: 600;
        }
        
        .mobile-menu .menu-item:hover i,
        .mobile-menu .menu-item:hover span {
            color: var(--primary-dark);
        }
        
        /* Esconder menu mobile em desktop */
        @media (min-width: 992px) {
            .mobile-menu {
                display: none;
            }
        }
    </style>
</head>
<body>
    <!-- Cabeçalho (Navbar) -->
    <nav class="navbar navbar-expand-lg fixed-top navbar-light">
        <div class="container">
            <a class="navbar-brand" href="#">FixFácil</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" 
                    aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="home.php">Painel</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="solicitacoes.php">Solicitações</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="propostas_enviadas.php">Propostas</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="reparos_em_andamento.php">Reparos</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="meumarktplace.php">Marketplace</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="solicitar_pecas.php">Peças</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="carteira.php">Carteira</a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-user-circle"></i>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="perfil.php">Meu Perfil</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../logout.php">Sair</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Conteúdo Principal -->
    <div class="container main-content">
        <!-- Cabeçalho da página -->
        <div class="header-section">
            <h1>Solicitação de Peças</h1>
            <p>Solicite peças aos fornecedores e acompanhe suas solicitações.</p>
        </div>

        <!-- Conteúdo da página -->
        <div class="content-card">
            <div class="feature-unavailable">
                <div class="icon-container">
                    <i class="fas fa-cogs icon"></i>
                </div>
                <span class="soon-badge">EM BREVE</span>
                <h2>Recurso não disponível no seu plano atual</h2>
                <p>O módulo de Solicitação de Peças será disponibilizado em breve para o seu plano de acesso. Com ele, você poderá solicitar peças diretamente aos fornecedores, receber cotações e acompanhar suas solicitações em um só lugar.</p>
                <p class="text-muted">Este recurso está em desenvolvimento e estará disponível em uma atualização futura do FixFácil.</p>
            </div>
        </div>
    </div>

    <!-- Menu de navegação móvel -->
    <div class="mobile-menu d-lg-none">
        <a href="home.php" class="menu-item">
            <i class="fas fa-home"></i>
            <span>Painel</span>
        </a>
        <a href="solicitacoes.php" class="menu-item">
            <i class="fas fa-clipboard-list"></i>
            <span>Solicitações</span>
        </a>
        <a href="reparos_em_andamento.php" class="menu-item">
            <i class="fas fa-tools"></i>
            <span>Reparos</span>
        </a>
        <a href="solicitar_pecas.php" class="menu-item active">
            <i class="fas fa-cogs"></i>
            <span>Peças</span>
        </a>
        <a href="perfil.php" class="menu-item">
            <i class="fas fa-user"></i>
            <span>Perfil</span>
        </a>
    </div>

    <!-- Bootstrap JS Bundle com Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- JQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</body>
</html>