# 🎯 Menu Dinâmico por Plano - FixFácil Assistências

## 📋 **Visão Geral**

O Menu Dinâmico é um sistema inteligente que adapta automaticamente a navegação baseada no plano do usuário (Free, Premium ou Master), proporcionando uma experiência personalizada e incentivando upgrades de plano.

---

## 🎨 **Características Principais**

### **📱 Dois Tipos de Menu**
- **Desktop**: Sidebar completa com todas as opções
- **Mobile**: Bottom navigation com 5 itens principais

### **🔐 Controle de Acesso Inteligente**
- **Mostra apenas recursos disponíveis** para o plano atual
- **Indica recursos bloqueados** com badges de upgrade
- **Incentiva melhorias** de plano de forma visual

### **⚡ Performance Otimizada**
- **Carregamento dinâmico** baseado em permissões
- **CSS modular** incluído apenas quando necessário
- **JavaScript mínimo** para máxima performance

---

## 📊 **Funcionalidades por Plano**

### **🆓 Plano Free (Taxa 25%)**
```
✅ Dashboard - Visão geral e estatísticas
✅ Solicitações - Receber e gerenciar solicitações
✅ Propostas - Enviar orçamentos
✅ Reparos - Acompanhar reparos em andamento
✅ Carteira - Visualizar ganhos
✅ Perfil - Dados pessoais e da empresa
```

### **💎 Plano Premium (Taxa 20% - R$89,90/mês)**
```
✅ Todas as funcionalidades do Free +
⭐ Marketplace - Vender produtos e peças
⭐ Chat - Conversar com clientes em tempo real
⭐ Estatísticas Avançadas - Relatórios detalhados
```

### **👑 Plano Master (Taxa 10% - R$159,90/mês)**
```
✅ Todas as funcionalidades do Premium +
👑 Link Fix - Página personalizada da assistência
👑 Selo FixFácil - Certificação de qualidade
👑 Retirada Presencial - Agendamentos na loja
👑 Menor Taxa - Apenas 10% por serviço
```

---

## 🛠️ **Implementação Técnica**

### **Arquivos do Sistema**
```
assistencia/
├── includes/
│   └── menu_dinamico.php          # Sistema principal
├── aplicar_menu_dinamico.php      # Script de aplicação
└── [páginas].php                  # Páginas com menu integrado
```

### **Funções Principais**

#### **1. getMenuItems($usuario_id, $taxaCalculator)**
```php
// Retorna array com todos os itens de menu e suas permissões
$menu_items = getMenuItems($usuario_id, $taxaCalculator);
```

#### **2. renderMenuDesktop($usuario_id, $taxaCalculator, $pagina_atual)**
```php
// Renderiza sidebar completa para desktop
echo renderMenuDesktop($usuario_id, $taxaCalculator, 'dashboard');
```

#### **3. renderMenuMobile($usuario_id, $taxaCalculator, $pagina_atual)**
```php
// Renderiza bottom navigation para mobile
echo renderMenuMobile($usuario_id, $taxaCalculator, 'dashboard');
```

#### **4. getMenuCSS()**
```php
// Retorna CSS completo do sistema de menu
echo getMenuCSS();
```

---

## 🎯 **Como Aplicar em Páginas**

### **1. Incluir o Sistema**
```php
// No início da página PHP
require_once 'includes/menu_dinamico.php';
```

### **2. Adicionar CSS**
```php
// No <head> da página
<?php echo getMenuCSS(); ?>
```

### **3. Renderizar Menus**
```php
// No <body>
<?php echo renderMenuDesktop($usuario_id, $taxaCalculator, 'nome_da_pagina'); ?>

<div class="main-content-with-sidebar">
    <!-- Conteúdo da página -->
</div>

<?php echo renderMenuMobile($usuario_id, $taxaCalculator, 'nome_da_pagina'); ?>
```

---

## 📱 **Responsividade**

### **Desktop (1024px+)**
- **Sidebar fixa** à esquerda (280px)
- **Conteúdo principal** com margem esquerda
- **Menu completo** com todas as opções

### **Tablet (768px - 1024px)**
- **Sidebar oculta** por padrão
- **Botão toggle** para mostrar/ocultar
- **Overlay** quando sidebar está aberta

### **Mobile (< 768px)**
- **Sidebar completamente oculta**
- **Bottom navigation** fixo na parte inferior
- **5 itens principais** baseados no plano

---

## 🎨 **Personalização Visual**

### **Cores por Plano**
```css
/* Free */
.plano-badge.plano-free {
    background: #6c757d;
    color: white;
}

/* Premium */
.plano-badge.plano-premium {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

/* Master */
.plano-badge.plano-master {
    background: linear-gradient(135deg, #ffd700, #ffed4e);
    color: #92400e;
}
```

### **Estados dos Itens**
```css
.menu-item.active     /* Item ativo */
.menu-item.disabled   /* Item bloqueado */
.menu-item:hover      /* Hover state */
```

---

## 🔧 **Configuração Avançada**

### **Adicionar Novo Item de Menu**
```php
// Em includes/menu_dinamico.php
$menu_items['nova_funcionalidade'] = [
    'titulo' => 'Nova Função',
    'url' => 'nova_funcao.php',
    'icone' => 'fas fa-star',
    'descricao' => 'Descrição da nova função',
    'disponivel' => $plano_info['nova_funcao'] ?? false,
    'plano_minimo' => 'Premium',
    'badge_premium' => true
];
```

### **Personalizar Prioridades Mobile**
```php
// Modificar $priority_order em renderMenuMobile()
$priority_order = ['dashboard', 'solicitacoes', 'propostas', 'nova_funcao', 'perfil'];
```

---

## 📊 **Benefícios do Sistema**

### **Para os Usuários**
- ✅ **Interface limpa** - Veem apenas o que podem usar
- ✅ **Navegação intuitiva** - Menu adaptado ao plano
- ✅ **Experiência mobile** - Otimizada para smartphones
- ✅ **Feedback visual** - Sabem exatamente seu plano

### **Para o Negócio**
- ✅ **Incentiva upgrades** - Mostra recursos bloqueados
- ✅ **Reduz confusão** - Interface simplificada
- ✅ **Aumenta conversão** - Upgrade calls-to-action
- ✅ **Melhora retenção** - Experiência personalizada

---

## 🚀 **Aplicação Automática**

### **Script de Aplicação**
```bash
# Via navegador
https://seudominio.com/assistencia/aplicar_menu_dinamico.php

# O script irá:
✅ Adicionar includes necessários
✅ Integrar CSS do menu
✅ Substituir menus antigos
✅ Configurar páginas ativas
✅ Gerar relatório de sucesso
```

---

## 🔍 **Troubleshooting**

### **Menu não aparece**
- Verificar se `includes/menu_dinamico.php` existe
- Confirmar que `getMenuCSS()` está sendo chamado
- Checar permissões de arquivo (644)

### **Itens não aparecem corretamente**
- Verificar configuração do plano no banco
- Confirmar que `$taxaCalculator` está funcionando
- Testar função `temAcesso()`

### **Mobile menu não funciona**
- Verificar CSS responsivo
- Confirmar que está em dispositivo < 768px
- Checar se JavaScript está carregado

---

## 📞 **Suporte**

### **Logs de Debug**
```php
// Adicionar para debug
var_dump($menu_items);
var_dump($plano_info);
```

### **Verificação de Plano**
```php
// Testar acesso
$tem_acesso = $taxaCalculator->temAcesso($usuario_id, 'funcionalidade');
echo $tem_acesso ? 'Tem acesso' : 'Sem acesso';
```

---

## 🎊 **Resultado Final**

O Menu Dinâmico transforma a experiência do usuário, proporcionando:

- **Interface profissional** adaptada ao plano
- **Navegação intuitiva** em todos os dispositivos  
- **Incentivo natural** para upgrades de plano
- **Performance superior** com carregamento otimizado
- **Experiência mobile** de primeira classe

**🚀 Execute o script de aplicação e transforme sua interface!**
