<?php
/**
 * Sistema de Layout Base
 * FixFácil Assistências - Sistema Novo
 */

class Layout {
    private $auth;
    private $usuario;
    private $plano;
    
    public function __construct() {
        try {
            $this->auth = getAuth();
            $this->usuario = $this->auth->getUsuarioLogado();

            if (!$this->usuario) {
                throw new Exception("Usuário não encontrado");
            }

            $this->plano = $this->auth->getPlanoInfo($this->usuario['id']);

            if (!$this->plano) {
                // Plano padrão se não encontrar
                $this->plano = [
                    'nome' => 'Free',
                    'taxa_servico' => 25.00
                ];
            }
        } catch (Exception $e) {
            error_log("Erro no construtor do Layout: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * Renderizar cabeçalho HTML
     */
    public function renderHead($title = "FixFácil Assistências", $extraCSS = "") {
        ?>
        <!DOCTYPE html>
        <html lang="pt-BR">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title><?php echo htmlspecialchars($title); ?></title>
            
            <!-- CSS Framework -->
            <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
            <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
            
            <!-- CSS Personalizado -->
            <style>
                :root {
                    --primary: #2563eb;
                    --primary-dark: #1d4ed8;
                    --secondary: #64748b;
                    --success: #059669;
                    --warning: #d97706;
                    --danger: #dc2626;
                    --info: #0284c7;
                    --light: #f8fafc;
                    --dark: #0f172a;
                    --gray-50: #f9fafb;
                    --gray-100: #f3f4f6;
                    --gray-200: #e5e7eb;
                    --gray-300: #d1d5db;
                    --gray-600: #4b5563;
                    --gray-700: #374151;
                    --gray-800: #1f2937;
                    --gray-900: #111827;
                    --sidebar-width: 260px;
                }

                body {
                    background: var(--gray-50);
                    min-height: 100vh;
                    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                    color: var(--gray-900);
                }
                
                .main-wrapper {
                    display: flex;
                    min-height: 100vh;
                }
                
                .sidebar {
                    width: var(--sidebar-width);
                    background: white;
                    border-right: 1px solid var(--gray-200);
                    position: fixed;
                    height: 100vh;
                    overflow-y: auto;
                    z-index: 1000;
                    transition: transform 0.3s ease;
                    box-shadow: 0 0 15px rgba(0, 0, 0, 0.05);
                }

                .sidebar-header {
                    padding: 1.5rem;
                    border-bottom: 1px solid var(--gray-200);
                    background: white;
                    color: var(--gray-900);
                }
                
                .sidebar-brand {
                    font-size: 1.25rem;
                    font-weight: 600;
                    margin: 0;
                    color: var(--primary);
                }

                .sidebar-user {
                    margin-top: 0.75rem;
                    font-size: 0.875rem;
                    color: var(--gray-600);
                }
                
                .sidebar-nav {
                    padding: 1rem 0;
                }
                
                .nav-item {
                    margin: 0.25rem 1rem;
                }
                
                .nav-link {
                    display: flex;
                    align-items: center;
                    padding: 0.75rem 1rem;
                    color: var(--gray-700);
                    text-decoration: none;
                    border-radius: 0.375rem;
                    transition: all 0.15s ease;
                    font-weight: 500;
                    font-size: 0.875rem;
                }

                .nav-link:hover {
                    background: var(--gray-100);
                    color: var(--primary);
                }

                .nav-link.active {
                    background: var(--primary);
                    color: white;
                    font-weight: 600;
                }
                
                .nav-link i {
                    width: 18px;
                    margin-right: 0.75rem;
                    font-size: 1rem;
                }

                .nav-badge {
                    margin-left: auto;
                    background: var(--danger);
                    color: white;
                    font-size: 0.75rem;
                    padding: 0.125rem 0.375rem;
                    border-radius: 0.75rem;
                    min-width: 18px;
                    text-align: center;
                    font-weight: 600;
                }
                
                .main-content {
                    flex: 1;
                    margin-left: var(--sidebar-width);
                    padding: 1.5rem;
                    transition: margin-left 0.3s ease;
                    background: var(--gray-50);
                }

                .content-header {
                    background: white;
                    border-radius: 0.5rem;
                    padding: 1.5rem;
                    margin-bottom: 1.5rem;
                    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
                    border: 1px solid var(--gray-200);
                }
                
                .page-title {
                    font-size: 1.5rem;
                    font-weight: 600;
                    color: var(--gray-900);
                    margin: 0;
                    line-height: 1.25;
                }

                .page-subtitle {
                    color: var(--gray-600);
                    margin: 0.5rem 0 0 0;
                    font-size: 0.875rem;
                }
                
                .plano-badge {
                    display: inline-flex;
                    align-items: center;
                    padding: 0.25rem 0.75rem;
                    border-radius: 0.375rem;
                    font-weight: 500;
                    font-size: 0.75rem;
                    margin-top: 0.5rem;
                    text-transform: uppercase;
                    letter-spacing: 0.025em;
                }

                .plano-free {
                    background: var(--gray-100);
                    color: var(--gray-700);
                    border: 1px solid var(--gray-300);
                }

                .plano-premium {
                    background: #dbeafe;
                    color: var(--primary);
                    border: 1px solid #93c5fd;
                }

                .plano-master {
                    background: #fef3c7;
                    color: #d97706;
                    border: 1px solid #fcd34d;
                }
                
                .card {
                    background: white;
                    border: 1px solid var(--gray-200);
                    border-radius: 0.5rem;
                    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
                    transition: box-shadow 0.15s ease;
                }

                .card:hover {
                    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                }

                .card-header {
                    background: var(--gray-50);
                    border-bottom: 1px solid var(--gray-200);
                    padding: 1rem 1.5rem;
                    font-weight: 600;
                    color: var(--gray-900);
                }

                .btn-primary {
                    background: var(--primary);
                    border: 1px solid var(--primary);
                    border-radius: 0.375rem;
                    padding: 0.5rem 1rem;
                    font-weight: 500;
                    font-size: 0.875rem;
                    transition: all 0.15s ease;
                }

                .btn-primary:hover {
                    background: var(--primary-dark);
                    border-color: var(--primary-dark);
                }
                
                /* Estilos adicionais profissionais */
                .table {
                    font-size: 0.875rem;
                }

                .table th {
                    background: var(--gray-50);
                    border-bottom: 2px solid var(--gray-200);
                    font-weight: 600;
                    color: var(--gray-700);
                    text-transform: uppercase;
                    font-size: 0.75rem;
                    letter-spacing: 0.025em;
                }

                .badge {
                    font-weight: 500;
                    font-size: 0.75rem;
                }

                .alert {
                    border: 1px solid;
                    border-radius: 0.375rem;
                }

                .form-control, .form-select {
                    border: 1px solid var(--gray-300);
                    border-radius: 0.375rem;
                    font-size: 0.875rem;
                }

                .form-control:focus, .form-select:focus {
                    border-color: var(--primary);
                    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
                }

                /* Mobile */
                @media (max-width: 768px) {
                    .sidebar {
                        transform: translateX(-100%);
                    }

                    .sidebar.show {
                        transform: translateX(0);
                    }

                    .main-content {
                        margin-left: 0;
                        padding: 1rem;
                    }

                    .mobile-menu-toggle {
                        position: fixed;
                        top: 1rem;
                        left: 1rem;
                        z-index: 1001;
                        background: var(--primary);
                        color: white;
                        border: none;
                        border-radius: 0.375rem;
                        padding: 0.75rem;
                        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
                    }

                    .mobile-overlay {
                        position: fixed;
                        top: 0;
                        left: 0;
                        width: 100%;
                        height: 100%;
                        background: rgba(0, 0, 0, 0.5);
                        z-index: 999;
                        opacity: 0;
                        visibility: hidden;
                        transition: all 0.3s;
                    }

                    .mobile-overlay.show {
                        opacity: 1;
                        visibility: visible;
                    }
                }
                
                /* Scrollbar personalizada */
                .sidebar::-webkit-scrollbar {
                    width: 6px;
                }
                
                .sidebar::-webkit-scrollbar-track {
                    background: transparent;
                }
                
                .sidebar::-webkit-scrollbar-thumb {
                    background: rgba(102, 126, 234, 0.3);
                    border-radius: 3px;
                }
                
                .sidebar::-webkit-scrollbar-thumb:hover {
                    background: rgba(102, 126, 234, 0.5);
                }
            </style>
            
            <?php echo $extraCSS; ?>
        </head>
        <body>
        <?php
    }
    
    /**
     * Renderizar sidebar
     */
    public function renderSidebar($currentPage = '') {
        $menuItems = $this->getMenuItems();
        ?>
        <!-- Mobile Menu Toggle -->
        <button class="mobile-menu-toggle d-lg-none" onclick="toggleSidebar()">
            <i class="fas fa-bars"></i>
        </button>
        
        <!-- Mobile Overlay -->
        <div class="mobile-overlay d-lg-none" onclick="toggleSidebar()"></div>
        
        <!-- Sidebar -->
        <nav class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <h1 class="sidebar-brand">
                    <i class="fas fa-wrench me-2"></i>
                    FixFácil
                </h1>
                <div class="sidebar-user">
                    <div style="font-weight: 600; color: var(--gray-900);">
                        <?php echo htmlspecialchars($this->usuario['nome']); ?>
                    </div>
                    <div style="font-size: 0.75rem; color: var(--gray-500); margin-top: 0.25rem;">
                        <?php echo htmlspecialchars($this->usuario['nome_empresa'] ?? 'Assistência Técnica'); ?>
                    </div>
                    <div class="plano-badge plano-<?php echo strtolower($this->plano['nome']); ?>">
                        <?php if ($this->plano['nome'] === 'Master'): ?>
                            <i class="fas fa-crown me-1"></i>
                        <?php elseif ($this->plano['nome'] === 'Premium'): ?>
                            <i class="fas fa-star me-1"></i>
                        <?php else: ?>
                            <i class="fas fa-user me-1"></i>
                        <?php endif; ?>
                        <?php echo $this->plano['nome']; ?>
                    </div>
                </div>
            </div>
            
            <div class="sidebar-nav">
                <?php foreach ($menuItems as $item): ?>
                    <div class="nav-item">
                        <a href="<?php echo $item['url']; ?>" 
                           class="nav-link <?php echo $currentPage === $item['page'] ? 'active' : ''; ?>">
                            <i class="<?php echo $item['icon']; ?>"></i>
                            <?php echo $item['title']; ?>
                            <?php if (isset($item['badge']) && $item['badge'] > 0): ?>
                                <span class="nav-badge"><?php echo $item['badge']; ?></span>
                            <?php endif; ?>
                        </a>
                    </div>
                <?php endforeach; ?>
                
                <div class="nav-item mt-3">
                    <a href="logout.php" class="nav-link text-danger">
                        <i class="fas fa-sign-out-alt"></i>
                        Sair
                    </a>
                </div>
            </div>
        </nav>
        <?php
    }
    
    /**
     * Obter itens do menu baseado no plano
     */
    private function getMenuItems() {
        $items = [
            [
                'title' => 'Dashboard',
                'icon' => 'fas fa-home',
                'url' => 'dashboard.php',
                'page' => 'dashboard'
            ],
            [
                'title' => 'Solicitações',
                'icon' => 'fas fa-inbox',
                'url' => 'solicitacoes.php',
                'page' => 'solicitacoes',
                'badge' => $this->getNotificationCount('solicitacoes')
            ],
            [
                'title' => 'Propostas',
                'icon' => 'fas fa-paper-plane',
                'url' => 'propostas.php',
                'page' => 'propostas',
                'badge' => $this->getNotificationCount('propostas')
            ],
            [
                'title' => 'Reparos',
                'icon' => 'fas fa-tools',
                'url' => 'reparos.php',
                'page' => 'reparos',
                'badge' => $this->getNotificationCount('reparos')
            ],
            [
                'title' => 'Carteira',
                'icon' => 'fas fa-wallet',
                'url' => 'carteira.php',
                'page' => 'carteira'
            ]
        ];
        
        // Adicionar itens baseados no plano
        if ($this->auth->hasAccess('marketplace')) {
            $items[] = [
                'title' => 'Marketplace',
                'icon' => 'fas fa-store',
                'url' => 'marketplace.php',
                'page' => 'marketplace'
            ];
        }
        
        if ($this->auth->hasAccess('chat')) {
            $items[] = [
                'title' => 'Chat',
                'icon' => 'fas fa-comments',
                'url' => 'chat.php',
                'page' => 'chat',
                'badge' => $this->getNotificationCount('chat')
            ];
        }

        // Assistência Virtual (exclusivo Master)
        if ($this->auth->hasAccess('assistencia_virtual')) {
            $items[] = [
                'title' => 'Assistência Virtual',
                'icon' => 'fas fa-globe',
                'url' => 'assistencia_virtual.php',
                'page' => 'assistencia_virtual'
            ];
        }

        $items[] = [
            'title' => 'Perfil',
            'icon' => 'fas fa-user',
            'url' => 'perfil.php',
            'page' => 'perfil'
        ];
        
        return $items;
    }
    
    /**
     * Obter contadores de notificação
     */
    private function getNotificationCount($type) {
        try {
            $assistencia_id = $this->usuario['assistencia_id'] ?? null;
            if (!$assistencia_id) {
                return 0;
            }

            $db = getDatabase();

            switch ($type) {
                case 'solicitacoes':
                    $sql = "SELECT COUNT(*) as count FROM solicitacoes_reparo WHERE status = 'enviado'";
                    $result = $db->query($sql);
                    break;
                case 'propostas':
                    $sql = "SELECT COUNT(*) as count FROM propostas_assistencia WHERE assistencia_id = ? AND status = 'aceita'";
                    $result = $db->query($sql, [$assistencia_id]);
                    break;
                case 'reparos':
                    $sql = "SELECT COUNT(*) as count FROM propostas_assistencia WHERE assistencia_id = ? AND status = 'Em Andamento'";
                    $result = $db->query($sql, [$assistencia_id]);
                    break;
                case 'chat':
                    // Verificar se tabela existe primeiro
                    $check = $db->query("SHOW TABLES LIKE 'mensagens_chat'");
                    if ($check->num_rows === 0) {
                        return 0;
                    }
                    $sql = "SELECT COUNT(*) as count FROM mensagens_chat mc
                            JOIN propostas_assistencia pa ON mc.proposta_id = pa.id
                            WHERE pa.assistencia_id = ? AND mc.remetente_tipo = 'usuario' AND mc.lida = 0";
                    $result = $db->query($sql, [$assistencia_id]);
                    break;
                default:
                    return 0;
            }

            if ($result) {
                $row = $result->fetch_assoc();
                return $row ? (int)$row['count'] : 0;
            }

            return 0;

        } catch (Exception $e) {
            error_log("Erro ao obter notificações ($type): " . $e->getMessage());
            return 0;
        }
    }
    
    /**
     * Renderizar rodapé
     */
    public function renderFooter($extraJS = "") {
        ?>
        <!-- JavaScript -->
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
        <script>
            // Toggle sidebar mobile
            function toggleSidebar() {
                const sidebar = document.getElementById('sidebar');
                const overlay = document.querySelector('.mobile-overlay');
                
                sidebar.classList.toggle('show');
                overlay.classList.toggle('show');
            }
            
            // Auto-hide mobile menu on link click
            document.querySelectorAll('.nav-link').forEach(link => {
                link.addEventListener('click', () => {
                    if (window.innerWidth <= 768) {
                        toggleSidebar();
                    }
                });
            });
        </script>
        
        <?php echo $extraJS; ?>
        </body>
        </html>
        <?php
    }
}
?>
