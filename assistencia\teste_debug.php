<?php
/**
 * Arquivo de teste e debug para assistências
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔍 Teste do Sistema de Assistências</h1>";

// 1. Testar conexão com banco
echo "<h2>1. ✅ Teste de Conexão com Banco</h2>";

try {
    require_once 'config/database.php';
    $db = getDatabase();
    echo "✅ Conexão com banco estabelecida com sucesso<br>";
    
    // Testar uma query simples
    $result = $db->query("SELECT 1 as test");
    if ($result) {
        echo "✅ Query de teste executada com sucesso<br>";
    }
} catch (Exception $e) {
    echo "❌ Erro na conexão: " . $e->getMessage() . "<br>";
}

// 2. Testar autenticação
echo "<h2>2. ✅ Teste de Autenticação</h2>";

try {
    require_once 'config/auth.php';
    $auth = getAuth();
    echo "✅ Classe de autenticação carregada<br>";
} catch (Exception $e) {
    echo "❌ Erro na autenticação: " . $e->getMessage() . "<br>";
}

// 3. Testar layout
echo "<h2>3. ✅ Teste de Layout</h2>";

try {
    require_once 'includes/layout.php';
    echo "✅ Classe de layout carregada<br>";
} catch (Exception $e) {
    echo "❌ Erro no layout: " . $e->getMessage() . "<br>";
}

// 4. Verificar tabelas necessárias
echo "<h2>4. ✅ Verificação de Tabelas</h2>";

$tabelas_necessarias = [
    'usuarios',
    'assistencias_tecnicas',
    'planos',
    'assinaturas_assistencias',
    'solicitacoes_reparo',
    'propostas_assistencia'
];

try {
    foreach ($tabelas_necessarias as $tabela) {
        $result = $db->query("SHOW TABLES LIKE '$tabela'");
        if ($result->num_rows > 0) {
            echo "✅ Tabela <strong>$tabela</strong> existe<br>";
        } else {
            echo "❌ Tabela <strong>$tabela</strong> NÃO existe<br>";
        }
    }
} catch (Exception $e) {
    echo "❌ Erro ao verificar tabelas: " . $e->getMessage() . "<br>";
}

// 5. Verificar dados de exemplo
echo "<h2>5. ✅ Verificação de Dados</h2>";

try {
    // Contar usuários assistência
    $result = $db->query("SELECT COUNT(*) as total FROM usuarios WHERE tipo_usuario = 'assistencia'");
    $row = $result->fetch_assoc();
    echo "👥 Total de assistências cadastradas: <strong>" . $row['total'] . "</strong><br>";
    
    // Contar planos
    $result = $db->query("SELECT COUNT(*) as total FROM planos");
    $row = $result->fetch_assoc();
    echo "📋 Total de planos disponíveis: <strong>" . $row['total'] . "</strong><br>";
    
    // Contar solicitações
    $result = $db->query("SELECT COUNT(*) as total FROM solicitacoes_reparo");
    $row = $result->fetch_assoc();
    echo "🔧 Total de solicitações: <strong>" . $row['total'] . "</strong><br>";
    
} catch (Exception $e) {
    echo "❌ Erro ao verificar dados: " . $e->getMessage() . "<br>";
}

// 6. Testar sessão
echo "<h2>6. ✅ Teste de Sessão</h2>";

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

if (isset($_SESSION['usuario_id'])) {
    echo "✅ Usuário logado: ID " . $_SESSION['usuario_id'] . "<br>";
    echo "✅ Tipo: " . ($_SESSION['tipo_usuario'] ?? 'não definido') . "<br>";
} else {
    echo "⚠️ Nenhum usuário logado<br>";
    echo "🔗 <a href='../login.php'>Fazer login</a><br>";
}

// 7. Verificar arquivos importantes
echo "<h2>7. ✅ Verificação de Arquivos</h2>";

$arquivos_importantes = [
    'config/auth.php',
    'config/database.php',
    'includes/layout.php',
    'dashboard.php',
    'solicitacoes.php',
    'propostas.php',
    'reparos.php',
    'carteira.php'
];

foreach ($arquivos_importantes as $arquivo) {
    if (file_exists($arquivo)) {
        echo "✅ Arquivo <strong>$arquivo</strong> existe<br>";
    } else {
        echo "❌ Arquivo <strong>$arquivo</strong> NÃO existe<br>";
    }
}

// 8. Teste de planos
echo "<h2>8. ✅ Teste de Planos</h2>";

try {
    $result = $db->query("SELECT * FROM planos ORDER BY id");
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>ID</th><th>Nome</th><th>Preço</th><th>Taxa</th><th>Status</th></tr>";
    
    while ($plano = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $plano['id'] . "</td>";
        echo "<td>" . htmlspecialchars($plano['nome']) . "</td>";
        echo "<td>R$ " . number_format($plano['preco_mensal'], 2, ',', '.') . "</td>";
        echo "<td>" . $plano['taxa_servico'] . "%</td>";
        echo "<td>" . ($plano['ativo'] ? 'Ativo' : 'Inativo') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
} catch (Exception $e) {
    echo "❌ Erro ao listar planos: " . $e->getMessage() . "<br>";
}

// 9. Links de teste
echo "<h2>9. 🔗 Links de Teste</h2>";

echo "<ul>";
echo "<li><a href='dashboard.php'>Dashboard</a></li>";
echo "<li><a href='solicitacoes.php'>Solicitações</a></li>";
echo "<li><a href='propostas.php'>Propostas</a></li>";
echo "<li><a href='reparos.php'>Reparos</a></li>";
echo "<li><a href='carteira.php'>Carteira</a></li>";
echo "<li><a href='perfil.php'>Perfil</a></li>";
echo "<li><a href='../login.php'>Login</a></li>";
echo "</ul>";

// 10. Informações do servidor
echo "<h2>10. 🖥️ Informações do Servidor</h2>";

echo "PHP Version: " . phpversion() . "<br>";
echo "MySQL Extension: " . (extension_loaded('mysqli') ? 'Carregada' : 'NÃO carregada') . "<br>";
echo "Session Status: " . session_status() . "<br>";
echo "Error Reporting: " . error_reporting() . "<br>";
echo "Display Errors: " . ini_get('display_errors') . "<br>";

?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    background: #f8fafc;
}

h1 {
    color: #1e40af;
    border-bottom: 3px solid #3b82f6;
    padding-bottom: 10px;
}

h2 {
    color: #374151;
    margin-top: 30px;
    padding: 10px;
    background: white;
    border-left: 4px solid #10b981;
    border-radius: 4px;
}

table {
    background: white;
    margin: 10px 0;
    border-radius: 4px;
    overflow: hidden;
}

th {
    background: #f3f4f6;
    padding: 10px;
    font-weight: bold;
}

td {
    padding: 8px 10px;
}

a {
    color: #2563eb;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}

ul {
    background: white;
    padding: 20px;
    border-radius: 4px;
    border-left: 4px solid #3b82f6;
}

li {
    margin: 5px 0;
}
</style>
