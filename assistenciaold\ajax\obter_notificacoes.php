<?php
/**
 * AJAX - Obter Notificações em Tempo Real
 * FixFácil Assistências - Sistema Novo
 */

header('Content-Type: application/json');

require_once '../config/auth.php';
require_once '../config/database.php';

// Verificar autenticação
$auth = getAuth();
try {
    $auth->checkAssistenciaAuth();
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'Não autorizado']);
    exit();
}

// Obter dados do usuário
$usuario = $auth->getUsuarioLogado();
$db = getDatabase();

if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    echo json_encode(['success' => false, 'message' => 'Método não permitido']);
    exit();
}

$ultima_verificacao = $_GET['ultima_verificacao'] ?? null;
$marcar_lidas = $_GET['marcar_lidas'] ?? false;

try {
    // Construir query baseada na última verificação
    $where_clause = "n.usuario_id = ?";
    $params = [$usuario['id']];
    
    if ($ultima_verificacao) {
        $where_clause .= " AND n.data_criacao > ?";
        $params[] = $ultima_verificacao;
    }
    
    // Obter notificações
    $sql = "
        SELECT 
            n.id,
            n.tipo,
            n.titulo,
            n.mensagem,
            n.lida,
            n.data_criacao,
            n.data_leitura,
            CASE 
                WHEN n.data_criacao > DATE_SUB(NOW(), INTERVAL 1 HOUR) THEN 'nova'
                WHEN n.data_criacao > DATE_SUB(NOW(), INTERVAL 1 DAY) THEN 'recente'
                ELSE 'antiga'
            END as categoria_tempo
        FROM notificacoes n
        WHERE $where_clause
        ORDER BY n.data_criacao DESC
        LIMIT 50
    ";
    
    $result = $db->query($sql, $params);
    $notificacoes = [];
    
    while ($row = $result->fetch_assoc()) {
        $notificacoes[] = [
            'id' => $row['id'],
            'tipo' => $row['tipo'],
            'titulo' => $row['titulo'],
            'mensagem' => $row['mensagem'],
            'lida' => (bool)$row['lida'],
            'data_criacao' => $row['data_criacao'],
            'data_leitura' => $row['data_leitura'],
            'categoria_tempo' => $row['categoria_tempo'],
            'tempo_relativo' => calcularTempoRelativo($row['data_criacao']),
            'icone' => obterIconeNotificacao($row['tipo']),
            'cor' => obterCorNotificacao($row['tipo'])
        ];
    }
    
    // Contar notificações não lidas
    $sql = "SELECT COUNT(*) as nao_lidas FROM notificacoes WHERE usuario_id = ? AND lida = 0";
    $result = $db->query($sql, [$usuario['id']]);
    $contadores = $result->fetch_assoc();
    
    // Marcar como lidas se solicitado
    if ($marcar_lidas && !empty($notificacoes)) {
        $ids = array_column($notificacoes, 'id');
        $placeholders = str_repeat('?,', count($ids) - 1) . '?';
        
        $sql = "UPDATE notificacoes SET lida = 1, data_leitura = NOW() WHERE id IN ($placeholders)";
        $db->query($sql, $ids);
        
        // Atualizar array de notificações
        foreach ($notificacoes as &$notif) {
            $notif['lida'] = true;
            $notif['data_leitura'] = date('Y-m-d H:i:s');
        }
        
        $contadores['nao_lidas'] = max(0, $contadores['nao_lidas'] - count($ids));
    }
    
    // Obter estatísticas adicionais
    $stats = obterEstatisticasNotificacoes($usuario['id'], $db);
    
    echo json_encode([
        'success' => true,
        'notificacoes' => $notificacoes,
        'contadores' => $contadores,
        'stats' => $stats,
        'timestamp' => date('Y-m-d H:i:s')
    ]);
    
} catch (Exception $e) {
    error_log("Erro ao obter notificações: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Erro interno do servidor']);
}

/**
 * Calcular tempo relativo
 */
function calcularTempoRelativo($data) {
    $agora = new DateTime();
    $data_notif = new DateTime($data);
    $diff = $agora->diff($data_notif);
    
    if ($diff->days > 0) {
        return $diff->days . ' dia' . ($diff->days > 1 ? 's' : '') . ' atrás';
    } elseif ($diff->h > 0) {
        return $diff->h . ' hora' . ($diff->h > 1 ? 's' : '') . ' atrás';
    } elseif ($diff->i > 0) {
        return $diff->i . ' minuto' . ($diff->i > 1 ? 's' : '') . ' atrás';
    } else {
        return 'Agora mesmo';
    }
}

/**
 * Obter ícone da notificação
 */
function obterIconeNotificacao($tipo) {
    $icones = [
        'nova_solicitacao' => 'fas fa-inbox',
        'proposta_aceita' => 'fas fa-check-circle',
        'proposta_rejeitada' => 'fas fa-times-circle',
        'status_reparo' => 'fas fa-tools',
        'nova_mensagem' => 'fas fa-comments',
        'pagamento_recebido' => 'fas fa-dollar-sign',
        'upgrade_sucesso' => 'fas fa-crown',
        'produto_indisponivel' => 'fas fa-exclamation-triangle',
        'sistema' => 'fas fa-cog',
        'default' => 'fas fa-bell'
    ];
    
    return $icones[$tipo] ?? $icones['default'];
}

/**
 * Obter cor da notificação
 */
function obterCorNotificacao($tipo) {
    $cores = [
        'nova_solicitacao' => 'primary',
        'proposta_aceita' => 'success',
        'proposta_rejeitada' => 'danger',
        'status_reparo' => 'info',
        'nova_mensagem' => 'info',
        'pagamento_recebido' => 'success',
        'upgrade_sucesso' => 'warning',
        'produto_indisponivel' => 'warning',
        'sistema' => 'secondary',
        'default' => 'primary'
    ];
    
    return $cores[$tipo] ?? $cores['default'];
}

/**
 * Obter estatísticas das notificações
 */
function obterEstatisticacoes($usuario_id, $db) {
    try {
        $sql = "
            SELECT 
                COUNT(*) as total,
                COUNT(CASE WHEN lida = 0 THEN 1 END) as nao_lidas,
                COUNT(CASE WHEN DATE(data_criacao) = CURDATE() THEN 1 END) as hoje,
                COUNT(CASE WHEN data_criacao >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 END) as semana
            FROM notificacoes 
            WHERE usuario_id = ?
        ";
        
        $result = $db->query($sql, [$usuario_id]);
        return $result->fetch_assoc();
        
    } catch (Exception $e) {
        return [
            'total' => 0,
            'nao_lidas' => 0,
            'hoje' => 0,
            'semana' => 0
        ];
    }
}
?>
