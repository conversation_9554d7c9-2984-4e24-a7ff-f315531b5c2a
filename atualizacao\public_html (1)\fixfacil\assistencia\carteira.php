<?php
session_start();

// Verificar se o usuário está logado e é do tipo 'assistencia'
if (!isset($_SESSION['usuario_id']) || $_SESSION['tipo_usuario'] != 'assistencia') {
    header('Location: ../login.php');
    exit();
}

$usuario_id = $_SESSION['usuario_id'];
$nome_assistencia = $_SESSION['nome'];

// Configurações do banco de dados
$servername = "localhost";
$username_db = "u682219090_fixfacilnew";
$password_db = "T3cn0l0g1a@";
$dbname = "u682219090_fixfacilnew";

// Ativar exceções para erros do MySQLi
mysqli_report(MYSQLI_REPORT_ERROR | MYSQLI_REPORT_STRICT);

try {
    // Conectar ao banco de dados
    $conn = new mysqli($servername, $username_db, $password_db, $dbname);
    $conn->set_charset("utf8mb4");
} catch (mysqli_sql_exception $e) {
    die("Falha na conexão: " . $e->getMessage());
}

// **Novo Código**: Obter o `id` da assistência técnica a partir do `usuario_id`
try {
    $sql_assistencia = "SELECT id FROM assistencias_tecnicas WHERE usuario_id = ?";
    $stmt_assistencia = $conn->prepare($sql_assistencia);
    $stmt_assistencia->bind_param("i", $usuario_id);
    $stmt_assistencia->execute();
    $result_assistencia = $stmt_assistencia->get_result();
    $stmt_assistencia->close();

    if ($result_assistencia->num_rows > 0) {
        $row_assistencia = $result_assistencia->fetch_assoc();
        $assistencia_id = $row_assistencia['id'];
    } else {
        die("Assistência técnica não encontrada.");
    }
} catch (mysqli_sql_exception $e) {
    die("Erro ao obter dados da assistência técnica: " . $e->getMessage());
}

// Inicializar variáveis para mensagens
$mensagem = "";
$tipo_alerta = "";

// Processar ações (Marcar pagamento como recebido, etc.) se necessário
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Marcar Pagamento como Recebido
    if (isset($_POST['marcar_recebido'])) {
        $proposta_id = intval($_POST['proposta_id']);

        try {
            // Atualizar o status da proposta para 'Em Andamento' e marcar como pago
            $sql_receber = "UPDATE propostas_assistencia SET status = 'Em Andamento', pago = 1 WHERE id = ? AND assistencia_id = ?";
            $stmt_receber = $conn->prepare($sql_receber);
            $stmt_receber->bind_param("ii", $proposta_id, $assistencia_id);
            $stmt_receber->execute();
            $stmt_receber->close();

            $mensagem = "Pagamento marcado como recebido com sucesso!";
            $tipo_alerta = "success";
        } catch (mysqli_sql_exception $e) {
            $mensagem = "Erro ao atualizar pagamento: " . $e->getMessage();
            $tipo_alerta = "danger";
        }
    }
}

// Recuperar pagamentos pendentes (status 'aceita')
try {
    $sql_pendentes = "
        SELECT 
            pa.id AS proposta_id, 
            sr.id AS solicitacao_id,
            sr.descricao_problema, 
            sr.dispositivo, 
            sr.marca, 
            sr.modelo, 
            TRIM(pa.status) AS status, 
            sr.data_solicitacao, 
            pa.preco
        FROM solicitacoes_reparo sr
        INNER JOIN propostas_assistencia pa ON sr.id = pa.solicitacao_id
        WHERE pa.assistencia_id = ? AND LOWER(TRIM(pa.status)) = LOWER('aceita')
        ORDER BY sr.data_solicitacao DESC
    ";
    $stmt_pendentes = $conn->prepare($sql_pendentes);
    $stmt_pendentes->bind_param("i", $assistencia_id);
    $stmt_pendentes->execute();
    $result_pendentes = $stmt_pendentes->get_result();
    $stmt_pendentes->close();
} catch (mysqli_sql_exception $e) {
    die("Erro ao recuperar pagamentos pendentes: " . $e->getMessage());
}

// Recuperar pagamentos recebidos (status 'Em Andamento')
try {
    $sql_recebidos = "
        SELECT 
            pa.id AS proposta_id, 
            sr.id AS solicitacao_id,
            sr.descricao_problema, 
            sr.dispositivo, 
            sr.marca, 
            sr.modelo, 
            TRIM(pa.status) AS status, 
            sr.data_solicitacao, 
            pa.preco,
            pa.pago
        FROM solicitacoes_reparo sr
        INNER JOIN propostas_assistencia pa ON sr.id = pa.solicitacao_id
        WHERE pa.assistencia_id = ? AND LOWER(TRIM(pa.status)) = LOWER('Em Andamento')
        ORDER BY sr.data_solicitacao DESC
    ";
    $stmt_recebidos = $conn->prepare($sql_recebidos);
    $stmt_recebidos->bind_param("i", $assistencia_id);
    $stmt_recebidos->execute();
    $result_recebidos = $stmt_recebidos->get_result();
    $stmt_recebidos->close();
} catch (mysqli_sql_exception $e) {
    die("Erro ao recuperar pagamentos recebidos: " . $e->getMessage());
}

// Calcular total a receber (15% FixFácil)
$total_receber = 0;
$recebidos_array = []; // Armazenar os resultados para uso posterior
while ($recebido = $result_recebidos->fetch_assoc()) {
    // Supondo que a taxa do motoboy não está incluída no 'preco'
    $total_receber += $recebido['preco'] * 0.85; // Desconto de 15%
    $recebidos_array[] = $recebido;
}

// Fechar conexão
$conn->close();
?>

<!-- O restante do código HTML permanece o mesmo -->

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <title>Carteira - FixFácil</title>
    <!-- Meta Tags para Responsividade -->
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <!-- Incluindo Bootstrap CSS -->
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <!-- Font Awesome para ícones -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.1/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css?family=Poppins:300,400,500,600,700&display=swap" rel="stylesheet">
    <!-- Estilos personalizados -->
    <style>
        body {
            font-family: 'Poppins', sans-serif;
            background-color: #f7f9fc;
            margin-bottom: 60px; /* Espaço para a navbar inferior */
            color: #495057;
        }
        /* Navbar */
        .navbar {
            background-color: #fff;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .navbar-brand img {
            width: 150px;
        }
        .navbar-nav .nav-link {
            color: #495057 !important;
            font-weight: 500;
            margin-right: 15px;
        }
        .navbar-nav .nav-link.active {
            color: #007BFF !important;
        }
        .navbar-nav .nav-link:hover {
            color: #0056b3 !important;
        }
        /* Conteúdo Principal */
        .main-content {
            padding: 80px 20px 20px; /* Ajuste o padding-top para evitar sobreposição com a navbar fixa */
        }
        .welcome {
            margin-bottom: 40px;
        }
        .welcome h2 {
            font-weight: 600;
            color: #343a40;
        }
        /* Tabela */
        .table thead th {
            border-bottom: none;
            font-weight: 600;
            color: #343a40;
        }
        .table tbody td {
            vertical-align: middle;
        }
        /* Status Badges */
        .badge-pendente {
            background-color: #ffc107;
            color: #212529;
        }
        .badge-recebido {
            background-color: #28a745;
            color: #fff;
        }
        /* Footer */
        footer.footer {
            background-color: #fff;
            padding: 20px 0;
            position: fixed;
            width: 100%;
            bottom: 0;
            box-shadow: 0 -2px 4px rgba(0,0,0,0.1);
        }
        footer.footer span {
            color: #6c757d;
        }
        /* Navbar Inferior (Mobile) */
        .footer-nav {
            position: fixed;
            bottom: 0;
            width: 100%;
            background-color: #fff;
            box-shadow: 0 -2px 5px rgba(0,0,0,0.1);
            z-index: 1000;
        }
        .footer-nav .nav-link {
            color: #6c757d;
            text-align: center;
            padding: 10px 0;
            font-size: 12px;
        }
        .footer-nav .nav-link.active {
            color: #007BFF;
        }
        .footer-nav .nav-link i {
            font-size: 20px;
        }
        @media (min-width: 768px) {
            .footer-nav {
                display: none;
            }
        }
        /* Botões */
        .btn-custom {
            background-color: #007BFF;
            color: #fff;
            border-radius: 5px;
            font-weight: 500;
            transition: background-color 0.3s;
        }
        .btn-custom:hover {
            background-color: #0056b3;
            color: #fff;
        }
    </style>
</head>
<body>
    <!-- Cabeçalho -->
    <nav class="navbar navbar-expand-lg navbar-light fixed-top">
       
        <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarCarteira" 
                aria-controls="navbarCarteira" aria-expanded="false" aria-label="Alternar navegação">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse justify-content-end" id="navbarCarteira">
             <ul class="navbar-nav">
                <li class="nav-item">
                    <a class="nav-link " href="home.php"><i class="fas fa-home"></i> Painel</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="solicitacoes.php"><i class="fas fa-envelope"></i> Solicitações</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="meumarktplace.php"><i class="fas fa-store"></i> Marketplace</a> <!-- Corrigido o link e ícone -->
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="solicitar_pecas.php"><i class="fas fa-plus"></i> Solicitação Peças</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="propostas_enviadas.php"><i class="fas fa-paper-plane"></i> Propostas Enviadas</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="reparos_em_andamento.php"><i class="fas fa-tools"></i> Reparos em Andamento</a>
                </li>
                 <li class="nav-item">
                    <a class="nav-link active" href="carteira.php"><i class="fas fa-wallet"></i> Carteira</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="perfil.php"><i class="fas fa-user-circle"></i> Perfil</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="../logout.php"><i class="fas fa-sign-out-alt"></i> Sair</a>
                </li>
            </ul>
        </div>
    </nav>

    <!-- Conteúdo Principal -->
    <div class="container main-content">
        <div class="welcome text-center">
            <h2>Minha Carteira</h2>
            <p class="text-muted">Confira seus pagamentos pendentes e recebidos.</p>
        </div>

        <!-- Exibir Mensagens -->
        <?php if (!empty($mensagem)): ?>
            <div class="alert alert-<?php echo htmlspecialchars($tipo_alerta); ?>" role="alert">
                <?php echo htmlspecialchars($mensagem); ?>
            </div>
        <?php endif; ?>

        <!-- Seção de Pagamentos Pendentes -->
        <div class="card mb-4">
            <h5 class="card-header">Pagamentos Pendentes</h5>
            <div class="card-body">
                <?php if ($result_pendentes && $result_pendentes->num_rows > 0): ?>
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>ID da Proposta</th>
                                    <th>ID da Solicitação</th>
                                    <th>Dispositivo</th>
                                    <th>Marca</th>
                                    <th>Modelo</th>
                                    <th>Descrição</th>
                                    <th>Valor (R$)</th>
                                    <th>Data da Solicitação</th>
                                    <th>Status</th>
                                    <th>Ação</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php while ($pendente = $result_pendentes->fetch_assoc()): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($pendente['proposta_id']); ?></td>
                                        <td><?php echo htmlspecialchars($pendente['solicitacao_id']); ?></td>
                                        <td><?php echo htmlspecialchars($pendente['dispositivo']); ?></td>
                                        <td><?php echo htmlspecialchars($pendente['marca']); ?></td>
                                        <td><?php echo htmlspecialchars($pendente['modelo']); ?></td>
                                        <td><?php echo htmlspecialchars($pendente['descricao_problema']); ?></td>
                                        <td><?php echo number_format($pendente['preco'], 2, ',', '.'); ?></td>
                                        <td><?php echo date('d/m/Y H:i', strtotime($pendente['data_solicitacao'])); ?></td>
                                        <td>
                                            <span class="badge badge-pendente"><?php echo htmlspecialchars($pendente['status']); ?></span>
                                        </td>
                                        <td>
                                            <!-- Botão para marcar como recebido -->
                                            <form action="carteira.php" method="POST">
                                                <input type="hidden" name="proposta_id" value="<?php echo htmlspecialchars($pendente['proposta_id']); ?>">
                                                <button type="submit" name="marcar_recebido" class="btn btn-sm btn-success" onclick="return confirm('Marcar este pagamento como recebido?');">
                                                    <i class="fas fa-check-circle"></i> Receber
                                                </button>
                                            </form>
                                        </td>
                                    </tr>
                                <?php endwhile; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <p class="text-center">Você não possui pagamentos pendentes.</p>
                <?php endif; ?>
            </div>
        </div>

        <!-- Seção de Pagamentos Recebidos -->
        <div class="card mb-4">
            <h5 class="card-header">Pagamentos Recebidos</h5>
            <div class="card-body">
                <?php if (!empty($recebidos_array)): ?>
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>ID da Proposta</th>
                                    <th>ID da Solicitação</th>
                                    <th>Dispositivo</th>
                                    <th>Marca</th>
                                    <th>Modelo</th>
                                    <th>Descrição</th>
                                    <th>Valor Total (R$)</th>
                                    <th>Valor a Receber (R$)</th>
                                    <th>Data da Solicitação</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($recebidos_array as $recebido): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($recebido['proposta_id']); ?></td>
                                        <td><?php echo htmlspecialchars($recebido['solicitacao_id']); ?></td>
                                        <td><?php echo htmlspecialchars($recebido['dispositivo']); ?></td>
                                        <td><?php echo htmlspecialchars($recebido['marca']); ?></td>
                                        <td><?php echo htmlspecialchars($recebido['modelo']); ?></td>
                                        <td><?php echo htmlspecialchars($recebido['descricao_problema']); ?></td>
                                        <td><?php echo number_format($recebido['preco'], 2, ',', '.'); ?></td>
                                        <td><?php echo number_format($recebido['preco'] * 0.85, 2, ',', '.'); ?></td> <!-- Desconto de 15% -->
                                        <td><?php echo date('d/m/Y H:i', strtotime($recebido['data_solicitacao'])); ?></td>
                                        <td>
                                            <?php
                                                if (isset($recebido['pago']) && $recebido['pago'] == 1) {
                                                    echo '<span class="badge badge-recebido">Pago</span>';
                                                } else {
                                                    echo '<span class="badge badge-pendente">Pendente</span>';
                                                }
                                            ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    <div class="text-right">
                        <h4>Total a Receber: <?php echo number_format($total_receber, 2, ',', '.'); ?> R$</h4>
                    </div>
                <?php else: ?>
                    <p class="text-center">Você não possui pagamentos recebidos.</p>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Barra de Navegação Inferior (Mobile) -->
    <nav class="footer-nav d-md-none">
        <ul class="nav justify-content-around">
            <li class="nav-item">
                <a class="nav-link" href="home_assistencia.php">
                    <i class="fas fa-home"></i><br>Início
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link active" href="carteira.php">
                    <i class="fas fa-wallet"></i><br>Carteira
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="perfil_assistencia.php">
                    <i class="fas fa-user-circle"></i><br>Perfil
                </a>
            </li>
        </ul>
    </nav>

    <!-- Rodapé -->
    <footer class="footer d-none d-md-block">
        <div class="container text-center">
            <span>&copy; <?php echo date("Y"); ?> FixFácil. Todos os direitos reservados.</span>
        </div>
    </footer>

    <!-- Incluindo Bootstrap JS e dependências -->
    <script src="https://code.jquery.com/jquery-3.5.1.min.js" ></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.5.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
