<?php
/**
 * Enviar Proposta - Mobile First
 * FixFácil Assistências - Padrão Mobile
 */

// Configurar relatório de erros para debug
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Iniciar sessão se não estiver iniciada
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Verificar se o usuário está logado
if (!isset($_SESSION['usuario_id']) || $_SESSION['tipo_usuario'] !== 'assistencia') {
    header('Location: ../login.php');
    exit();
}

// Configuração de banco de dados
$host = "localhost";
$username = "u680766645_fixfacilnew";
$password = "T3cn0l0g1a@";
$database = "u680766645_fixfacilnew";

$mysqli = new mysqli($host, $username, $password, $database);

if ($mysqli->connect_error) {
    die("Erro de conexão: " . $mysqli->connect_error);
}

$mysqli->set_charset("utf8");

// Obter dados do usuário logado
$usuario_id = $_SESSION['usuario_id'];
$usuario = null;

// Buscar dados do usuário de forma simples
$sql = "SELECT u.id, u.nome, u.email, u.telefone, u.plano_id, at.id as assistencia_id
        FROM usuarios u
        LEFT JOIN assistencias_tecnicas at ON u.id = at.usuario_id
        WHERE u.id = $usuario_id";
$result = $mysqli->query($sql);
if ($result && $result->num_rows > 0) {
    $usuario = $result->fetch_assoc();
}

// Dados padrão se não encontrar
if (!$usuario) {
    $usuario = [
        'id' => $usuario_id,
        'nome' => 'Assistência Técnica',
        'email' => '',
        'telefone' => '',
        'plano_id' => 1,
        'assistencia_id' => null
    ];
}

// Obter ID da solicitação
$solicitacao_id = $_GET['solicitacao_id'] ?? 0;

if (!$solicitacao_id) {
    header('Location: solicitacoes.php');
    exit();
}

// Obter dados da solicitação
$solicitacao = null;
try {
    $sql = "
        SELECT
            sr.*,
            u.nome as cliente_nome,
            u.telefone as cliente_telefone,
            u.endereco as cliente_endereco,
            u.email as cliente_email
        FROM solicitacoes_reparo sr
        JOIN usuarios u ON sr.usuario_id = u.id
        WHERE sr.id = ? AND sr.status = 'enviado'
    ";

    $stmt = $mysqli->prepare($sql);
    $stmt->bind_param("i", $solicitacao_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $solicitacao = $result->fetch_assoc();

    if (!$solicitacao) {
        header('Location: solicitacoes.php');
        exit();
    }

    // Verificar se já enviei proposta
    $sql_check = "SELECT id FROM propostas_assistencia WHERE solicitacao_id = ? AND assistencia_id = ?";
    $stmt_check = $mysqli->prepare($sql_check);
    $stmt_check->bind_param("ii", $solicitacao_id, $usuario['assistencia_id']);
    $stmt_check->execute();
    $result_check = $stmt_check->get_result();

    if ($result_check->num_rows > 0) {
        header('Location: detalhes_solicitacao.php?id=' . $solicitacao_id);
        exit();
    }

} catch (Exception $e) {
    error_log("Erro ao obter solicitação: " . $e->getMessage());
    header('Location: solicitacoes.php');
    exit();
}

// Processar envio da proposta
$erro = '';
$sucesso = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $preco_str = $_POST['preco'] ?? '';
    $prazo = $_POST['prazo'] ?? '';
    $observacoes = $_POST['observacoes'] ?? '';
    $retirada_expressa = isset($_POST['retirada_expressa']) ? 1 : 0;

    // Converter preço de formato brasileiro para decimal
    $preco = 0;
    if (!empty($preco_str)) {
        $preco = floatval(str_replace(['.', ','], ['', '.'], $preco_str));
    }

    // Validações
    if (empty($preco_str) || $preco <= 0) {
        $erro = 'Preço é obrigatório e deve ser maior que zero.';
    } elseif (empty($prazo) || !is_numeric($prazo) || $prazo <= 0) {
        $erro = 'Prazo é obrigatório e deve ser maior que zero.';
    } else {
        try {
            // Inserir proposta
            $sql = "
                INSERT INTO propostas_assistencia
                (solicitacao_id, assistencia_id, preco, prazo, observacoes, retirada_expressa, status, data_proposta)
                VALUES (?, ?, ?, ?, ?, ?, 'enviada', NOW())
            ";

            $stmt = $mysqli->prepare($sql);
            $stmt->bind_param("iidisi", $solicitacao_id, $usuario['assistencia_id'], $preco, $prazo, $observacoes, $retirada_expressa);

            if ($stmt->execute()) {
                $sucesso = 'Proposta enviada com sucesso!';
                // Redirecionar após 2 segundos
                header("refresh:2;url=detalhes_solicitacao.php?id=" . $solicitacao_id);
            } else {
                $erro = 'Erro ao enviar proposta. Tente novamente.';
            }

        } catch (Exception $e) {
            error_log("Erro ao enviar proposta: " . $e->getMessage());
            $erro = 'Erro interno. Tente novamente.';
        }
    }
}

?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enviar Proposta - FixFácil Assistências</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            min-height: 100vh;
            color: #1e293b;
        }

        .container {
            max-width: 414px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
            position: relative;
            overflow-x: hidden;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }

        .header {
            background: linear-gradient(135deg, #059669 0%, #065f46 100%);
            color: white;
            padding: 24px 20px 20px 20px;
            position: relative;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 20"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="0.5" fill="white" opacity="0.05"/></pattern></defs><rect width="100" height="20" fill="url(%23grain)"/></svg>');
        }

        .header-content {
            position: relative;
            z-index: 1;
        }

        .header-top {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 20px;
        }

        .back-btn {
            width: 40px;
            height: 40px;
            background: rgba(255,255,255,0.15);
            border: none;
            border-radius: 12px;
            color: white;
            font-size: 18px;
            cursor: pointer;
            backdrop-filter: blur(10px);
            transition: all 0.2s ease;
            text-decoration: none;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .back-btn:hover {
            background: rgba(255,255,255,0.25);
        }

        .header-title {
            flex: 1;
            text-align: center;
            margin: 0 16px;
        }

        .header-title h1 {
            font-size: 18px;
            font-weight: 700;
            margin-bottom: 4px;
        }

        .header-title p {
            font-size: 12px;
            opacity: 0.8;
        }

        .action-btn {
            width: 40px;
            height: 40px;
            background: rgba(255,255,255,0.15);
            border: none;
            border-radius: 12px;
            color: white;
            font-size: 18px;
            cursor: pointer;
            backdrop-filter: blur(10px);
            transition: all 0.2s ease;
            text-decoration: none;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .content {
            padding: 20px;
            padding-bottom: 100px;
        }

        .device-card {
            background: white;
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
            border: 1px solid #f1f5f9;
        }

        .device-header {
            display: flex;
            align-items: center;
            gap: 16px;
            margin-bottom: 16px;
        }

        .device-icon {
            width: 48px;
            height: 48px;
            background: linear-gradient(135deg, #059669, #065f46);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            color: white;
        }

        .device-info h3 {
            font-size: 16px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 4px;
        }

        .device-meta {
            font-size: 12px;
            color: #64748b;
        }

        .form-card {
            background: white;
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
            border: 1px solid #f1f5f9;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            font-size: 14px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 8px;
        }

        .form-input {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            font-size: 14px;
            background: #f8fafc;
            transition: all 0.2s;
        }

        .form-input:focus {
            outline: none;
            border-color: #059669;
            background: white;
            box-shadow: 0 0 0 3px rgba(5, 150, 105, 0.1);
        }

        .form-select {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            font-size: 14px;
            background: #f8fafc;
            transition: all 0.2s;
        }

        .form-textarea {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            font-size: 14px;
            background: #f8fafc;
            resize: vertical;
            min-height: 100px;
            transition: all 0.2s;
        }

        .form-textarea:focus {
            outline: none;
            border-color: #059669;
            background: white;
            box-shadow: 0 0 0 3px rgba(5, 150, 105, 0.1);
        }

        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px;
            background: #f8fafc;
            border-radius: 12px;
            border: 1px solid #e2e8f0;
        }

        .checkbox-input {
            width: 20px;
            height: 20px;
            accent-color: #059669;
        }

        .checkbox-label {
            font-size: 14px;
            color: #1e293b;
            font-weight: 500;
        }

        .btn {
            padding: 12px 20px;
            border-radius: 12px;
            font-size: 14px;
            font-weight: 600;
            text-decoration: none;
            text-align: center;
            cursor: pointer;
            border: none;
            transition: all 0.2s;
            display: block;
            width: 100%;
            margin-bottom: 12px;
        }

        .btn-primary {
            background: #059669;
            color: white;
        }

        .btn-primary:hover {
            background: #047857;
        }

        .btn-outline {
            background: white;
            color: #059669;
            border: 2px solid #059669;
        }

        .btn-outline:hover {
            background: #f0fdf4;
        }

        .alert {
            padding: 12px 16px;
            border-radius: 12px;
            margin-bottom: 20px;
            font-size: 14px;
        }

        .alert-success {
            background: #dcfce7;
            color: #166534;
            border: 1px solid #bbf7d0;
        }

        .alert-error {
            background: #fef2f2;
            color: #dc2626;
            border: 1px solid #fecaca;
        }

        .resumo-card {
            background: #f0fdf4;
            border: 1px solid #bbf7d0;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 20px;
        }

        .resumo-title {
            font-size: 14px;
            font-weight: 600;
            color: #166534;
            margin-bottom: 12px;
        }

        .resumo-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 13px;
        }

        .resumo-label {
            color: #64748b;
        }

        .resumo-value {
            font-weight: 600;
            color: #1e293b;
        }

        .resumo-total {
            border-top: 1px solid #bbf7d0;
            padding-top: 8px;
            margin-top: 8px;
            font-size: 16px;
            font-weight: 700;
            color: #059669;
        }

        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 100%;
            max-width: 414px;
            background: white;
            border-top: 1px solid #e2e8f0;
            padding: 12px 20px;
            display: flex;
            justify-content: space-around;
            box-shadow: 0 -4px 20px rgba(0,0,0,0.1);
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
            cursor: pointer;
            padding: 8px;
            border-radius: 12px;
            transition: all 0.2s ease;
            position: relative;
            text-decoration: none;
            color: #64748b;
        }

        .nav-item.active {
            background: #f0fdf4;
            color: #059669;
        }

        .nav-icon {
            font-size: 20px;
        }

        .nav-label {
            font-size: 10px;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="header-content">
                <div class="header-top">
                    <a href="detalhes_solicitacao.php?id=<?php echo $solicitacao['id']; ?>" class="back-btn">←</a>
                    <div class="header-title">
                        <h1>Enviar Proposta</h1>
                        <p>Solicitação #<?php echo $solicitacao['id']; ?></p>
                    </div>
                    <a href="solicitacoes.php" class="action-btn">📋</a>
                </div>
            </div>
        </div>

        <div class="content">
            <!-- Alertas -->
            <?php if ($erro): ?>
            <div class="alert alert-error">
                ⚠️ <?php echo htmlspecialchars($erro); ?>
            </div>
            <?php endif; ?>

            <?php if ($sucesso): ?>
            <div class="alert alert-success">
                ✅ <?php echo htmlspecialchars($sucesso); ?>
            </div>
            <?php endif; ?>

            <!-- Informações do Dispositivo -->
            <div class="device-card">
                <div class="device-header">
                    <div class="device-icon">📱</div>
                    <div class="device-info">
                        <h3><?php echo htmlspecialchars($solicitacao['marca'] . ' ' . $solicitacao['modelo']); ?></h3>
                        <div class="device-meta">
                            Cliente: <?php echo htmlspecialchars($solicitacao['cliente_nome']); ?> •
                            <?php echo htmlspecialchars($solicitacao['cliente_telefone']); ?>
                        </div>
                    </div>
                </div>

                <div style="background: #f8fafc; border-radius: 12px; padding: 12px; font-size: 14px; color: #475569;">
                    <strong>Problema:</strong> <?php echo htmlspecialchars($solicitacao['descricao_problema']); ?>
                </div>
            </div>

            <!-- Formulário da Proposta -->
            <form method="POST" class="form-card">
                <h3 style="margin-bottom: 20px; color: #1e293b;">💼 Dados da Proposta</h3>

                <div class="form-group">
                    <label class="form-label">💰 Preço do Reparo</label>
                    <input type="text" name="preco" class="form-input" placeholder="Ex: 150,00"
                           value="<?php echo htmlspecialchars($_POST['preco'] ?? ''); ?>"
                           oninput="formatarPreco(this); calcularResumo();" required>
                </div>

                <div class="form-group">
                    <label class="form-label">📅 Prazo de Entrega</label>
                    <select name="prazo" class="form-select" onchange="calcularResumo();" required>
                        <option value="">Selecione o prazo</option>
                        <option value="1" <?php echo ($_POST['prazo'] ?? '') == '1' ? 'selected' : ''; ?>>1 dia (Express)</option>
                        <option value="2" <?php echo ($_POST['prazo'] ?? '') == '2' ? 'selected' : ''; ?>>2 dias</option>
                        <option value="3" <?php echo ($_POST['prazo'] ?? '') == '3' ? 'selected' : ''; ?>>3 dias</option>
                        <option value="5" <?php echo ($_POST['prazo'] ?? '') == '5' ? 'selected' : ''; ?>>5 dias</option>
                        <option value="7" <?php echo ($_POST['prazo'] ?? '') == '7' ? 'selected' : ''; ?>>1 semana</option>
                        <option value="10" <?php echo ($_POST['prazo'] ?? '') == '10' ? 'selected' : ''; ?>>10 dias</option>
                        <option value="15" <?php echo ($_POST['prazo'] ?? '') == '15' ? 'selected' : ''; ?>>15 dias</option>
                        <option value="30" <?php echo ($_POST['prazo'] ?? '') == '30' ? 'selected' : ''; ?>>30 dias</option>
                    </select>
                </div>

                <div class="form-group">
                    <label class="form-label">📝 Observações e Detalhes</label>
                    <textarea name="observacoes" class="form-textarea"
                              placeholder="Descreva detalhes sobre o reparo, peças necessárias, garantia oferecida, etc."><?php echo htmlspecialchars($_POST['observacoes'] ?? ''); ?></textarea>
                </div>

                <div class="form-group">
                    <div class="checkbox-group">
                        <input type="checkbox" name="retirada_expressa" class="checkbox-input" id="retirada_expressa"
                               <?php echo isset($_POST['retirada_expressa']) ? 'checked' : ''; ?>>
                        <label for="retirada_expressa" class="checkbox-label">
                            🏍️ Retirada Express (buscar e entregar na residência)
                        </label>
                    </div>
                </div>

                <!-- Resumo da Proposta -->
                <div class="resumo-card" id="resumoCard" style="display: none;">
                    <div class="resumo-title">📊 Resumo da Proposta</div>
                    <div class="resumo-item">
                        <span class="resumo-label">Valor do Reparo:</span>
                        <span class="resumo-value" id="resumoPreco">R$ 0,00</span>
                    </div>
                    <div class="resumo-item">
                        <span class="resumo-label">Taxa da Plataforma (15%):</span>
                        <span class="resumo-value" id="resumoTaxa">R$ 0,00</span>
                    </div>
                    <div class="resumo-item resumo-total">
                        <span>Você receberá:</span>
                        <span id="resumoRecebera">R$ 0,00</span>
                    </div>
                </div>

                <button type="submit" class="btn btn-primary">
                    📤 Enviar Proposta
                </button>
            </form>
        </div>

        <!-- Bottom Navigation -->
        <div class="bottom-nav">
            <a href="dashboard_mobile_final.php" class="nav-item">
                <div class="nav-icon">🏠</div>
                <div class="nav-label">Início</div>
            </a>
            <a href="solicitacoes.php" class="nav-item active">
                <div class="nav-icon">📋</div>
                <div class="nav-label">Solicitações</div>
            </a>
            <a href="reparos_new.php" class="nav-item">
                <div class="nav-icon">🔧</div>
                <div class="nav-label">Reparos</div>
            </a>
            <a href="marketplace.php" class="nav-item">
                <div class="nav-icon">🛒</div>
                <div class="nav-label">Loja</div>
            </a>
            <a href="carteira.php" class="nav-item">
                <div class="nav-icon">💳</div>
                <div class="nav-label">Carteira</div>
            </a>
        </div>
    </div>

    <script>
        // Função para formatar preço
        function formatarPreco(input) {
            let valor = input.value.replace(/\D/g, '');

            if (valor.length === 0) {
                input.value = '';
                return;
            }

            valor = valor.padStart(3, '0');
            valor = valor.slice(0, -2) + ',' + valor.slice(-2);
            valor = valor.replace(/\B(?=(\d{3})+(?!\d))/g, '.');

            input.value = valor;
        }

        // Função para calcular resumo
        function calcularResumo() {
            const precoInput = document.querySelector('input[name="preco"]');
            const prazoSelect = document.querySelector('select[name="prazo"]');
            const resumoCard = document.getElementById('resumoCard');

            if (!precoInput.value || !prazoSelect.value) {
                resumoCard.style.display = 'none';
                return;
            }

            // Converter preço para número
            let preco = 0;
            if (precoInput.value) {
                preco = parseFloat(precoInput.value.replace(/\./g, '').replace(',', '.')) || 0;
            }

            // Calcular valores
            const taxa = preco * 0.15;
            const recebera = preco - taxa;

            // Atualizar resumo
            document.getElementById('resumoPreco').textContent = 'R$ ' + preco.toLocaleString('pt-BR', {minimumFractionDigits: 2});
            document.getElementById('resumoTaxa').textContent = 'R$ ' + taxa.toLocaleString('pt-BR', {minimumFractionDigits: 2});
            document.getElementById('resumoRecebera').textContent = 'R$ ' + recebera.toLocaleString('pt-BR', {minimumFractionDigits: 2});

            resumoCard.style.display = 'block';
        }
    </script>
</body>
</html>
