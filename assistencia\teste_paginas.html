<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste de Páginas - FixFácil Assistências</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
        }

        .header {
            background: linear-gradient(135deg, #059669 0%, #065f46 100%);
            color: white;
            padding: 32px;
            border-radius: 20px;
            margin-bottom: 32px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            font-weight: 800;
            margin-bottom: 16px;
        }

        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .page-test {
            background: white;
            border-radius: 16px;
            padding: 24px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            transition: transform 0.2s ease;
        }

        .page-test:hover {
            transform: translateY(-4px);
        }

        .page-title {
            font-size: 1.2rem;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .test-btn {
            width: 100%;
            background: linear-gradient(135deg, #059669, #065f46);
            color: white;
            border: none;
            border-radius: 10px;
            padding: 12px 20px;
            font-size: 0.9rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: block;
            text-align: center;
            margin-bottom: 8px;
        }

        .test-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(5, 150, 105, 0.3);
        }

        .status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 600;
            text-align: center;
            margin-top: 8px;
        }

        .status.working {
            background: #d1fae5;
            color: #065f46;
        }

        .status.error {
            background: #fee2e2;
            color: #991b1b;
        }

        .status.unknown {
            background: #f3f4f6;
            color: #374151;
        }

        .instructions {
            background: white;
            border-radius: 16px;
            padding: 24px;
            margin-bottom: 20px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        }

        .instructions h2 {
            color: #1e293b;
            margin-bottom: 16px;
        }

        .instructions p {
            color: #64748b;
            line-height: 1.6;
            margin-bottom: 12px;
        }

        @media (max-width: 768px) {
            .test-grid {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 Teste de Páginas</h1>
            <p>Verificação rápida de todas as páginas padronizadas</p>
        </div>

        <div class="instructions">
            <h2>📋 Como testar:</h2>
            <p><strong>1.</strong> Clique nos botões abaixo para abrir cada página</p>
            <p><strong>2.</strong> Verifique se a página carrega sem erro HTTP 500</p>
            <p><strong>3.</strong> Confirme se o menu inferior está presente e funcionando</p>
            <p><strong>4.</strong> Teste a responsividade redimensionando a janela</p>
        </div>

        <div class="test-grid">
            <!-- Dashboard -->
            <div class="page-test">
                <div class="page-title">
                    <span>🏠</span>
                    Dashboard Principal
                </div>
                <a href="dashboard_mobile_final.php" class="test-btn" target="_blank">
                    Testar dashboard_mobile_final.php
                </a>
                <div class="status working">✅ Funcionando (Referência)</div>
            </div>

            <!-- Solicitações -->
            <div class="page-test">
                <div class="page-title">
                    <span>📋</span>
                    Solicitações
                </div>
                <a href="solicitacoes.php" class="test-btn" target="_blank">
                    Testar solicitacoes.php
                </a>
                <div class="status unknown">🔄 Corrigido - Testar</div>
            </div>

            <!-- Marketplace -->
            <div class="page-test">
                <div class="page-title">
                    <span>🛒</span>
                    Marketplace
                </div>
                <a href="marketplace.php" class="test-btn" target="_blank">
                    Testar marketplace.php
                </a>
                <div class="status unknown">🔄 Corrigido - Testar</div>
            </div>

            <!-- Chat -->
            <div class="page-test">
                <div class="page-title">
                    <span>💬</span>
                    Chat
                </div>
                <a href="chat.php" class="test-btn" target="_blank">
                    Testar chat.php
                </a>
                <div class="status unknown">🔄 Corrigido - Testar</div>
            </div>

            <!-- Propostas -->
            <div class="page-test">
                <div class="page-title">
                    <span>💼</span>
                    Propostas
                </div>
                <a href="propostas.php" class="test-btn" target="_blank">
                    Testar propostas.php
                </a>
                <div class="status unknown">🔄 Recriado - Testar</div>
            </div>

            <!-- Carteira -->
            <div class="page-test">
                <div class="page-title">
                    <span>💳</span>
                    Carteira
                </div>
                <a href="carteira.php" class="test-btn" target="_blank">
                    Testar carteira.php
                </a>
                <div class="status working">✅ Já funcionando</div>
            </div>

            <!-- Perfil -->
            <div class="page-test">
                <div class="page-title">
                    <span>👤</span>
                    Perfil
                </div>
                <a href="perfil_new.php" class="test-btn" target="_blank">
                    Testar perfil_new.php
                </a>
                <div class="status working">✅ Já funcionando</div>
            </div>

            <!-- Reparos -->
            <div class="page-test">
                <div class="page-title">
                    <span>🔧</span>
                    Reparos
                </div>
                <a href="reparos_new.php" class="test-btn" target="_blank">
                    Testar reparos_new.php
                </a>
                <div class="status working">✅ Já funcionando</div>
            </div>

            <!-- Detalhes -->
            <div class="page-test">
                <div class="page-title">
                    <span>👁️</span>
                    Detalhes
                </div>
                <a href="detalhes_solicitacao.php?id=1" class="test-btn" target="_blank">
                    Testar detalhes_solicitacao.php
                </a>
                <div class="status unknown">⚠️ Página complexa - Verificar</div>
            </div>
        </div>

        <div style="margin-top: 40px; text-align: center; color: #64748b;">
            <p><strong>Última atualização:</strong> 22/07/2025</p>
            <p><strong>Status:</strong> ✅ CSS incorporado diretamente - Todas as páginas devem funcionar</p>
            <p><strong>Correção:</strong> Removida dependência do arquivo mobile_template.php</p>
        </div>
    </div>

    <script>
        // Adicionar feedback visual ao clicar nos botões
        document.querySelectorAll('.test-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const status = this.parentElement.querySelector('.status');
                status.textContent = '🔄 Testando...';
                status.className = 'status unknown';
                
                // Simular teste (em produção, isso seria uma verificação real)
                setTimeout(() => {
                    status.textContent = '✅ Clicado - Verifique a nova aba';
                    status.className = 'status working';
                }, 1000);
            });
        });
    </script>
</body>
</html>
