<?php
/**
 * Sistema de Autenticação
 * FixFácil Assistências - Sistema Novo
 */

require_once __DIR__ . '/database.php';

class Auth {
    private $db;
    
    public function __construct() {
        $this->db = getDatabase();
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
    }
    
    /**
     * Verificar se usuário está logado como assistência
     */
    public function checkAssistenciaAuth() {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }

        if (!isset($_SESSION['usuario_id']) || $_SESSION['tipo_usuario'] !== 'assistencia') {
            header('Location: ../login.php');
            exit();
        }
        return true;
    }
    
    /**
     * Obter dados do usuário logado
     */
    public function getUsuarioLogado() {
        if (!isset($_SESSION['usuario_id'])) {
            return null;
        }
        
        try {
            $sql = "
                SELECT
                    u.id,
                    u.nome,
                    u.email,
                    u.telefone,
                    u.plano_id,
                    at.id as assistencia_id,
                    at.nome_empresa,
                    at.endereco,
                    at.cep,
                    at.numero_endereco,
                    at.complemento,
                    at.bairro,
                    at.cidade,
                    at.estado,
                    at.latitude,
                    at.longitude,
                    at.ponto_referencia,
                    at.telefone as telefone_empresa,
                    at.email as email_empresa,
                    at.site
                FROM usuarios u
                LEFT JOIN assistencias_tecnicas at ON u.id = at.usuario_id
                WHERE u.id = ? AND u.tipo_usuario = 'assistencia'
            ";
            
            $result = $this->db->query($sql, [$_SESSION['usuario_id']]);
            return $result->fetch_assoc();
            
        } catch (Exception $e) {
            error_log("Erro ao obter usuário logado: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Obter informações do plano da assistência
     */
    public function getPlanoInfo($usuario_id) {
        try {
            $sql = "
                SELECT
                    p.id,
                    p.nome,
                    p.descricao,
                    p.preco_mensal as preco,
                    p.taxa_servico,
                    p.acesso_chat,
                    p.acesso_marketplace,
                    p.retirada_presencial,
                    p.selo_fixfacil,
                    p.link_personalizado,
                    p.retirada_express_prioritaria,
                    p.acesso_assistencia_virtual,
                    aa.status as assinatura_status,
                    aa.data_inicio,
                    aa.data_fim,
                    aa.data_proximo_pagamento
                FROM assinaturas_assistencias aa
                JOIN assistencias_tecnicas at ON aa.assistencia_id = at.id
                JOIN planos p ON aa.plano_id = p.id
                WHERE at.usuario_id = ? AND aa.status = 'ativa'
                ORDER BY aa.data_inicio DESC
                LIMIT 1
            ";

            $result = $this->db->query($sql, [$usuario_id]);
            $plano = $result->fetch_assoc();

            if (!$plano) {
                // Plano padrão Free se não encontrar assinatura ativa
                return [
                    'id' => 1,
                    'nome' => 'Free',
                    'descricao' => 'Plano gratuito com funcionalidades básicas para começar',
                    'preco' => 0.00,
                    'taxa_servico' => 25.00,
                    'acesso_chat' => 0,
                    'acesso_marketplace' => 0,
                    'retirada_presencial' => 1,
                    'selo_fixfacil' => 0,
                    'link_personalizado' => 0,
                    'retirada_express_prioritaria' => 0,
                    'acesso_assistencia_virtual' => 0,
                    'assinatura_status' => 'ativa',
                    'data_inicio' => date('Y-m-d'),
                    'data_fim' => null,
                    'data_proximo_pagamento' => null
                ];
            }

            return $plano;

        } catch (Exception $e) {
            error_log("Erro ao obter plano: " . $e->getMessage());
            // Retornar plano Free como fallback
            return [
                'id' => 1,
                'nome' => 'Free',
                'descricao' => 'Plano gratuito',
                'preco' => 0.00,
                'taxa_servico' => 25.00,
                'acesso_chat' => 0,
                'acesso_marketplace' => 0,
                'retirada_presencial' => 1,
                'selo_fixfacil' => 0,
                'link_personalizado' => 0,
                'retirada_express_prioritaria' => 0,
                'acesso_assistencia_virtual' => 0,
                'assinatura_status' => 'ativa',
                'data_inicio' => date('Y-m-d'),
                'data_fim' => null,
                'data_proximo_pagamento' => null
            ];
        }
    }
    
    /**
     * Verificar se usuário tem acesso a funcionalidade
     */
    public function hasAccess($funcionalidade, $usuario_id = null) {
        if ($usuario_id === null) {
            $usuario_id = $_SESSION['usuario_id'] ?? null;
        }
        
        if (!$usuario_id) {
            return false;
        }
        
        $plano = $this->getPlanoInfo($usuario_id);
        if (!$plano) {
            return false;
        }
        
        switch ($funcionalidade) {
            case 'chat':
                return (bool)$plano['acesso_chat'];
            case 'marketplace':
                return (bool)$plano['acesso_marketplace'];
            case 'retirada_presencial':
                return (bool)$plano['retirada_presencial'];
            case 'selo_fixfacil':
                return (bool)$plano['selo_fixfacil'];
            case 'link_personalizado':
                return (bool)$plano['link_personalizado'];
            case 'retirada_express_prioritaria':
                return (bool)$plano['retirada_express_prioritaria'];
            case 'assistencia_virtual':
                return isset($plano['acesso_assistencia_virtual']) && (bool)$plano['acesso_assistencia_virtual'];
            default:
                return true; // Funcionalidades básicas sempre disponíveis
        }
    }
    
    /**
     * Calcular taxa de serviço
     */
    public function getTaxaServico($usuario_id = null) {
        if ($usuario_id === null) {
            $usuario_id = $_SESSION['usuario_id'] ?? null;
        }
        
        $plano = $this->getPlanoInfo($usuario_id);
        return $plano ? $plano['taxa_servico'] : 25.00;
    }
    
    /**
     * Logout
     */
    public function logout() {
        session_destroy();
        header('Location: ../login.php');
        exit();
    }
}

/**
 * Função helper para obter instância de autenticação
 */
function getAuth() {
    static $auth = null;
    if ($auth === null) {
        $auth = new Auth();
    }
    return $auth;
}
?>
