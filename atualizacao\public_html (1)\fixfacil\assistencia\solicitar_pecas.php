<?php
// **Ativar exibição de erros (apenas para desenvolvimento)**
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

session_start();

// Verificar se o usuário está logado e é uma assistência técnica
if (!isset($_SESSION['usuario_id']) || $_SESSION['tipo_usuario'] != 'assistencia') {
    header('Location: ../login.php');
    exit();
}

$usuario_id = $_SESSION['usuario_id'];
$nome_usuario = $_SESSION['nome']; // Nome do usuário ou empresa

// Conexão com o banco de dados
$servername = "localhost";
$username_db = "u682219090_fixfacilnew";
$password_db = "T3cn0l0g1a@";
$dbname = "u682219090_fixfacilnew";

// Ativar exceções para erros do MySQLi
mysqli_report(MYSQLI_REPORT_ERROR | MYSQLI_REPORT_STRICT);

try {
    $conn = new mysqli($servername, $username_db, $password_db, $dbname);
    $conn->set_charset("utf8");
} catch (mysqli_sql_exception $e) {
    die("Falha na conexão: " . $e->getMessage());
}

// Inicializar variáveis para mensagens
$mensagem = "";
$tipo_alerta = "";

// Obter o assistencia_id correspondente ao usuario_id
$sql_assistencia = "SELECT id FROM assistencias_tecnicas WHERE usuario_id = ?";
$stmt_assistencia = $conn->prepare($sql_assistencia);
$stmt_assistencia->bind_param("i", $usuario_id);
$stmt_assistencia->execute();
$result_assistencia = $stmt_assistencia->get_result();

if ($row_assistencia = $result_assistencia->fetch_assoc()) {
    $assistencia_id = $row_assistencia['id'];
} else {
    // Assistência técnica não encontrada
    die("Assistência técnica não encontrada.");
}
$stmt_assistencia->close();

// Processamento do formulário de solicitação de peça
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['solicitar_peca'])) {
    try {
        // Sanitizar e validar os inputs
        $nome_peca = trim($_POST['nome_peca']);
        $modelo = trim($_POST['modelo']);
        $descricao = trim($_POST['descricao']);

        // Validar campos obrigatórios
        if (empty($nome_peca) || empty($modelo)) {
            throw new Exception("Por favor, preencha todos os campos obrigatórios.");
        }

        // Gerenciar upload de imagem (opcional)
        if (isset($_FILES['imagem']) && $_FILES['imagem']['error'] == UPLOAD_ERR_OK) {
            $upload_dir = '../uploads/solicitacoes/';
            if (!is_dir($upload_dir)) {
                mkdir($upload_dir, 0755, true);
            }

            $nome_imagem = basename($_FILES['imagem']['name']);
            $imagem_path = $upload_dir . $nome_imagem;
            $imageFileType = strtolower(pathinfo($imagem_path, PATHINFO_EXTENSION));

            // Validar o tipo de arquivo
            $check = getimagesize($_FILES['imagem']['tmp_name']);
            if ($check !== false) {
                // Permitir apenas certos formatos
                if (in_array($imageFileType, ['jpg', 'jpeg', 'png', 'gif'])) {
                    // Gerar um nome único para evitar conflitos
                    $nome_imagem = uniqid() . '.' . $imageFileType;
                    move_uploaded_file($_FILES['imagem']['tmp_name'], $imagem_path);
                } else {
                    throw new Exception("Formato de imagem inválido. Permissões: JPG, JPEG, PNG, GIF.");
                }
            } else {
                throw new Exception("O arquivo enviado não é uma imagem.");
            }
        } else {
            $nome_imagem = null; // Imagem não obrigatória
        }

        // Inserir a solicitação na tabela solicitacoes_pecas
        $sql_inserir_solicitacao = "INSERT INTO solicitacoes_pecas (assistencia_id, nome_peca, modelo, descricao, imagem, status, data_solicitacao)
                                    VALUES (?, ?, ?, ?, ?, 'pendente', NOW())";
        $stmt_inserir = $conn->prepare($sql_inserir_solicitacao);
        $stmt_inserir->bind_param("issss", $assistencia_id, $nome_peca, $modelo, $descricao, $nome_imagem);

        if ($stmt_inserir->execute()) {
            $mensagem = "Solicitação de peça enviada com sucesso!";
            $tipo_alerta = "success";
        } else {
            throw new Exception("Erro ao enviar a solicitação de peça.");
        }

        $stmt_inserir->close();
    } catch (Exception $e) {
        $mensagem = "Erro: " . $e->getMessage();
        $tipo_alerta = "danger";
    }
}

// Obter a lista de solicitações feitas pela assistência
try {
    $sql_solicitacoes = "SELECT id, nome_peca, modelo, descricao, imagem, status, data_solicitacao FROM solicitacoes_pecas WHERE assistencia_id = ? ORDER BY data_solicitacao DESC";
    $stmt_solicitacoes = $conn->prepare($sql_solicitacoes);
    $stmt_solicitacoes->bind_param("i", $assistencia_id);
    $stmt_solicitacoes->execute();
    $result_solicitacoes = $stmt_solicitacoes->get_result();
} catch (mysqli_sql_exception $e) {
    die("Erro na consulta SQL: " . $e->getMessage());
}
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <title>Solicitar Peças - FixFácil</title>
    <!-- Meta Tags para Responsividade -->
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <!-- Incluindo Bootstrap CSS -->
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <!-- Font Awesome para ícones -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.1/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css?family=Poppins:300,400,500,600,700&display=swap" rel="stylesheet">
    <!-- Estilos personalizados -->
    <style>
        body {
            font-family: 'Poppins', sans-serif;
            background-color: #f7f9fc;
            margin-bottom: 60px; /* Espaço para a navbar inferior */
            color: #495057;
        }
        /* Navbar */
        .navbar {
            background-color: #fff;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .navbar-brand img {
            width: 150px;
        }
        .navbar-nav .nav-link {
            color: #495057 !important;
            font-weight: 500;
            margin-right: 15px;
        }
        .navbar-nav .nav-link.active {
            color: #007BFF !important;
        }
        .navbar-nav .nav-link:hover {
            color: #0056b3 !important;
        }
        /* Conteúdo Principal */
        .main-content {
            padding: 80px 20px 20px; /* Ajuste o padding-top para evitar sobreposição com a navbar fixa */
        }
        .welcome {
            margin-bottom: 40px;
        }
        .welcome h2 {
            font-weight: 600;
            color: #343a40;
        }
        .card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.05);
            background-color: #fff;
            padding: 30px;
            margin-bottom: 20px;
        }
        /* Tabela */
        .table thead th {
            border-bottom: none;
            font-weight: 600;
            color: #343a40;
        }
        .table tbody td {
            vertical-align: middle;
        }
        /* Footer */
        footer.footer {
            background-color: #fff;
            padding: 20px 0;
            position: fixed;
            width: 100%;
            bottom: 0;
            box-shadow: 0 -2px 4px rgba(0,0,0,0.1);
        }
        footer.footer span {
            color: #6c757d;
        }
        /* Navbar Inferior (Mobile) */
        .footer-nav {
            position: fixed;
            bottom: 0;
            width: 100%;
            background-color: #fff;
            box-shadow: 0 -2px 5px rgba(0,0,0,0.1);
            z-index: 1000;
        }
        .footer-nav .nav-link {
            color: #6c757d;
            text-align: center;
            padding: 10px 0;
            font-size: 12px;
        }
        .footer-nav .nav-link.active {
            color: #007BFF;
        }
        .footer-nav .nav-link i {
            font-size: 20px;
        }
        @media (min-width: 768px) {
            .footer-nav {
                display: none;
            }
        }
        /* Estilos para Imagens */
        .peca-imagem {
            width: 100px;
            height: auto;
        }
    </style>
</head>
<body>
    <!-- Cabeçalho -->
   <nav class="navbar navbar-expand-lg navbar-light fixed-top">
       
        <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarNavAssistencia" 
                aria-controls="navbarNavAssistencia" aria-expanded="false" aria-label="Alternar navegação">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse justify-content-end" id="navbarNavAssistencia">
             <ul class="navbar-nav">
                <li class="nav-item">
                    <a class="nav-link " href="home.php"><i class="fas fa-home"></i> Painel</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link " href="solicitacoes.php"><i class="fas fa-envelope"></i> Solicitações</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link " href="meumarktplace.php"><i class="fas fa-store"></i> Marketplace</a> <!-- Corrigido o link e ícone -->
                </li>
                <li class="nav-item">
                    <a class="nav-link active" href="solicitar_pecas.php"><i class="fas fa-plus"></i> Solicitação Peças</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="propostas_enviadas.php"><i class="fas fa-paper-plane"></i> Propostas Enviadas</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="reparos_em_andamento.php"><i class="fas fa-tools"></i> Reparos em Andamento</a>
                </li>
                 <li class="nav-item">
                    <a class="nav-link " href="carteira.php"><i class="fas fa-wallet"></i> Carteira</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="perfil.php"><i class="fas fa-user-circle"></i> Perfil</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="../logout.php"><i class="fas fa-sign-out-alt"></i> Sair</a>
                </li>
            </ul>
        </div>
    </nav>
    <!-- Conteúdo Principal -->
    <div class="container main-content">
        <div class="welcome text-center">
            <h2>Nova Solicitação de Peças</h2>
            <p class="text-muted">Preencha os detalhes da peça que deseja solicitar aos fornecedores.</p>
        </div>

        <!-- Exibir Mensagens -->
        <?php if (!empty($mensagem)): ?>
            <div class="alert alert-<?php echo $tipo_alerta; ?>" role="alert">
                <?php echo htmlspecialchars($mensagem); ?>
            </div>
        <?php endif; ?>

        <!-- Formulário de Solicitação de Peça -->
        <div class="card">
            <form action="solicitar_pecas.php" method="POST" enctype="multipart/form-data">
                <div class="form-group">
                    <label for="nome_peca">Nome da Peça <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="nome_peca" name="nome_peca" required>
                </div>
                <div class="form-group">
                    <label for="modelo">Modelo <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="modelo" name="modelo" required>
                </div>
                <div class="form-group">
                    <label for="descricao">Descrição</label>
                    <textarea class="form-control" id="descricao" name="descricao" rows="3"></textarea>
                </div>
                <div class="form-group">
                    <label for="imagem">Anexar Imagem</label>
                    <input type="file" class="form-control-file" id="imagem" name="imagem" accept="image/*">
                </div>
                <button type="submit" name="solicitar_peca" class="btn btn-primary">Enviar Solicitação</button>
            </form>
        </div>

        <!-- Lista de Solicitações Feitas -->
        <div class="welcome text-center mt-5">
            <h3>Minhas Solicitações</h3>
            <p class="text-muted">Acompanhe o status das suas solicitações de peças.</p>
        </div>

        <div class="card">
            <div class="table-responsive">
                <table class="table table-striped" id="tabela-solicitacoes">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Peça</th>
                            <th>Modelo</th>
                            <th>Descrição</th>
                            <th>Imagem</th>
                            <th>Status</th>
                            <th>Data Solicitação</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if ($result_solicitacoes->num_rows > 0): ?>
                            <?php while ($solicitacao = $result_solicitacoes->fetch_assoc()): ?>
                                <tr>
                                    <td><?php echo $solicitacao['id']; ?></td>
                                    <td><?php echo htmlspecialchars($solicitacao['nome_peca']); ?></td>
                                    <td><?php echo htmlspecialchars($solicitacao['modelo']); ?></td>
                                    <td><?php echo htmlspecialchars($solicitacao['descricao']); ?></td>
                                    <td>
                                        <?php if (!empty($solicitacao['imagem'])): ?>
                                            <img src="../uploads/solicitacoes/<?php echo htmlspecialchars($solicitacao['imagem']); ?>" alt="Imagem da Peça" class="peca-imagem">
                                        <?php else: ?>
                                            <span class="text-muted">Sem Imagem</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php
                                        $status = $solicitacao['status'];
                                        if ($status == 'pendente') {
                                            echo '<span class="badge badge-warning">Pendente</span>';
                                        } elseif ($status == 'aprovado') {
                                            echo '<span class="badge badge-success">Aprovado</span>';
                                        } elseif ($status == 'cancelado') {
                                            echo '<span class="badge badge-danger">Cancelado</span>';
                                        }
                                        ?>
                                    </td>
                                    <td><?php echo date('d/m/Y H:i', strtotime($solicitacao['data_solicitacao'])); ?></td>
                                </tr>
                            <?php endwhile; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="7" class="text-center">Nenhuma solicitação feita.</td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Barra de Navegação Inferior (Mobile) -->
    <nav class="footer-nav d-md-none">
        <ul class="nav justify-content-around">
            <li class="nav-item">
                <a class="nav-link" href="home.php">
                    <i class="fas fa-home"></i><br>Painel
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="solicitacoes.php">
                    <i class="fas fa-envelope"></i><br>Solicitações
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link active" href="solicitar_pecas.php">
                    <i class="fas fa-plus"></i><br>Solicitar Peças
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="propostas_recebidas.php">
                    <i class="fas fa-comments"></i><br>Propostas
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="perfil.php">
                    <i class="fas fa-user-circle"></i><br>Perfil
                </a>
            </li>
        </ul>
    </nav>

    <!-- Rodapé -->
    <footer class="footer d-none d-md-block">
        <div class="container text-center">
            <span>&copy; <?php echo date("Y"); ?> FixFácil. Todos os direitos reservados.</span>
        </div>
    </footer>

    <!-- Incluindo Bootstrap JS e dependências -->
    <script src="https://code.jquery.com/jquery-3.5.1.min.js" ></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.5.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
