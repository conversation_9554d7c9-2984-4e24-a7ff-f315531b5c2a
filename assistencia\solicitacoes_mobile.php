<?php
/**
 * Página de Solicitações - Layout Dashboard Style
 * FixFácil Assistências - Sistema Novo
 */

// Redirecionar para versão mobile final
header('Location: solicitacoes.php?' . $_SERVER['QUERY_STRING']);
exit();
?>

    // Verificar autenticação
    $auth = getAuth();
    $auth->checkAssistenciaAuth();

    // Obter dados do usuário
    $usuario = $auth->getUsuarioLogado();
    if (!$usuario) {
        throw new Exception("Usuário não encontrado");
    }

    $plano = $auth->getPlanoInfo($usuario['id']);
    if (!$plano) {
        throw new Exception("Plano não encontrado");
    }

    $db = getDatabase();
    if (!$db) {
        throw new Exception("Erro na conexão com banco de dados");
    }

} catch (Exception $e) {
    error_log("Erro na página de solicitações: " . $e->getMessage());
    die("Erro interno do servidor. Verifique os logs para mais detalhes.");
}

// Filtros
$status_filter = $_GET['status'] ?? 'enviado';
$search = $_GET['search'] ?? '';

// Inicializar arrays
$solicitacoes = [];
$stats = [
    'total' => 0,
    'pendentes' => 0,
    'em_andamento' => 0,
    'finalizadas' => 0
];

try {
    $assistencia_id = $usuario['assistencia_id'] ?? null;
    if (!$assistencia_id) {
        throw new Exception("ID da assistência não encontrado");
    }

    // Buscar estatísticas
    $sql_stats = "
        SELECT 
            COUNT(*) as total,
            SUM(CASE WHEN status = 'enviado' THEN 1 ELSE 0 END) as pendentes,
            SUM(CASE WHEN status = 'em_andamento' THEN 1 ELSE 0 END) as em_andamento,
            SUM(CASE WHEN status = 'finalizado' THEN 1 ELSE 0 END) as finalizadas
        FROM solicitacoes_reparo 
        WHERE visivel = 1
    ";
    
    $result_stats = $db->query($sql_stats);
    $stats = $result_stats->fetch_assoc();

    // Obter solicitações
    $where_conditions = ["sr.status = ?", "sr.visivel = 1"];
    $params = [$status_filter];

    if (!empty($search)) {
        $where_conditions[] = "(sr.descricao_problema LIKE ? OR sr.dispositivo LIKE ? OR sr.marca LIKE ? OR sr.modelo LIKE ? OR u.nome LIKE ?)";
        $search_param = "%$search%";
        $params = array_merge($params, [$search_param, $search_param, $search_param, $search_param, $search_param]);
    }

    $where_clause = implode(' AND ', $where_conditions);

    $sql = "
        SELECT
            sr.id,
            sr.usuario_id,
            sr.celular_id,
            sr.tipo_solicitacao,
            sr.descricao_problema,
            sr.descricao_detalhada,
            sr.video,
            sr.metodo_entrega,
            sr.endereco,
            sr.dispositivo,
            sr.marca,
            sr.modelo,
            sr.memoria,
            sr.status,
            sr.data_solicitacao,
            sr.verificacoes,
            sr.origem,
            u.nome as cliente_nome,
            u.telefone as cliente_telefone,
            u.endereco as cliente_endereco,
            u.cep as cliente_cep,
            u.cidade as cliente_cidade,
            u.estado as cliente_estado,
            COALESCE(COUNT(pa.id), 0) as total_propostas,
            COALESCE(COUNT(CASE WHEN pa.assistencia_id = ? THEN 1 END), 0) as minhas_propostas
        FROM solicitacoes_reparo sr
        JOIN usuarios u ON sr.usuario_id = u.id
        LEFT JOIN propostas_assistencia pa ON sr.id = pa.solicitacao_id
        WHERE $where_clause
        GROUP BY sr.id, sr.usuario_id, sr.celular_id, sr.tipo_solicitacao, sr.descricao_problema,
                 sr.descricao_detalhada, sr.video, sr.metodo_entrega, sr.endereco, sr.dispositivo,
                 sr.marca, sr.modelo, sr.memoria, sr.status, sr.data_solicitacao, sr.verificacoes,
                 sr.origem, u.nome, u.telefone, u.endereco, u.cep, u.cidade, u.estado
        ORDER BY sr.data_solicitacao DESC
        LIMIT 50
    ";

    $all_params = array_merge([$assistencia_id], $params);
    $result = $db->query($sql, $all_params);

    if ($result) {
        while ($row = $result->fetch_assoc()) {
            $solicitacoes[] = $row;
        }
    }

} catch (Exception $e) {
    error_log("Erro ao obter solicitações: " . $e->getMessage());
    $solicitacoes = [];
}

// Dados da empresa
$empresa_nome = $usuario['nome'] ?? 'Assistência Técnica';
$empresa_logo = strtoupper(substr($empresa_nome, 0, 2));
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Solicitações - FixFácil</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            min-height: 100vh;
            color: #1e293b;
        }

        .container {
            max-width: 414px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
            position: relative;
            overflow-x: hidden;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }

        .header {
            background: linear-gradient(135deg, #059669 0%, #065f46 100%);
            color: white;
            padding: 24px 20px 20px 20px;
            position: relative;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 20"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="0.5" fill="white" opacity="0.05"/></pattern></defs><rect width="100" height="20" fill="url(%23grain)"/></svg>');
        }

        .header-content {
            position: relative;
            z-index: 1;
        }

        .header-top {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 16px;
        }

        .back-btn {
            background: rgba(255,255,255,0.15);
            border: none;
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 12px;
            font-size: 18px;
            cursor: pointer;
            backdrop-filter: blur(10px);
            transition: all 0.2s ease;
            text-decoration: none;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .back-btn:hover {
            background: rgba(255,255,255,0.25);
            color: white;
        }

        .header-actions {
            display: flex;
            gap: 8px;
        }

        .action-btn {
            background: rgba(255,255,255,0.15);
            border: none;
            color: white;
            padding: 8px;
            border-radius: 12px;
            font-size: 16px;
            cursor: pointer;
            backdrop-filter: blur(10px);
            transition: all 0.2s ease;
            text-decoration: none;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
        }

        .action-btn:hover {
            background: rgba(255,255,255,0.25);
            color: white;
        }

        .page-info {
            text-align: center;
        }

        .page-title {
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 4px;
        }

        .page-subtitle {
            font-size: 14px;
            opacity: 0.8;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 12px;
            margin-top: 16px;
        }

        .stat-card {
            background: rgba(255,255,255,0.15);
            border-radius: 12px;
            padding: 16px 12px;
            text-align: center;
            backdrop-filter: blur(10px);
        }

        .stat-number {
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 4px;
        }

        .stat-label {
            font-size: 11px;
            opacity: 0.8;
        }

        .content {
            padding: 20px;
            padding-bottom: 100px;
        }

        .filter-card {
            background: white;
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
        }

        .filter-title {
            font-size: 16px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .filter-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 12px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            gap: 6px;
        }

        .form-label {
            font-size: 14px;
            font-weight: 600;
            color: #1e293b;
        }

        .form-input, .form-select {
            padding: 12px 16px;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            font-size: 14px;
            background: white;
            transition: border-color 0.2s ease;
        }

        .form-input:focus, .form-select:focus {
            outline: none;
            border-color: #059669;
        }

        .filter-actions {
            display: flex;
            gap: 12px;
            margin-top: 16px;
        }

        .btn {
            padding: 12px 20px;
            border-radius: 12px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            border: none;
            display: flex;
            align-items: center;
            gap: 8px;
            text-decoration: none;
            text-align: center;
            justify-content: center;
        }

        .btn-primary {
            background: #059669;
            color: white;
            flex: 1;
        }

        .btn-primary:hover {
            background: #065f46;
        }

        .btn-secondary {
            background: #f1f5f9;
            color: #64748b;
            flex: 1;
        }

        .btn-secondary:hover {
            background: #e2e8f0;
        }

        .request-card {
            background: white;
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
            transition: all 0.2s ease;
            cursor: pointer;
        }

        .request-card:hover {
            box-shadow: 0 4px 20px rgba(0,0,0,0.12);
            transform: translateY(-2px);
        }

        .request-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 12px;
        }

        .device-icon {
            width: 48px;
            height: 48px;
            background: #f0fdf4;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            color: #059669;
            flex-shrink: 0;
        }

        .device-icon.urgent {
            background: #fef2f2;
            color: #ef4444;
        }

        .device-info {
            flex: 1;
        }

        .device-title {
            font-size: 16px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 4px;
        }

        .device-subtitle {
            font-size: 14px;
            color: #64748b;
        }

        .request-status {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .status-badge {
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 600;
        }

        .status-badge.pending {
            background: #fef3c7;
            color: #d97706;
        }

        .status-badge.accepted {
            background: #dcfce7;
            color: #059669;
        }

        .status-badge.rejected {
            background: #fef2f2;
            color: #ef4444;
        }

        .status-badge.completed {
            background: #eff6ff;
            color: #3b82f6;
        }

        .request-description {
            font-size: 14px;
            color: #64748b;
            line-height: 1.4;
            margin-bottom: 12px;
        }

        .request-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }

        .meta-item {
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 12px;
            color: #94a3b8;
        }

        .request-actions {
            display: flex;
            gap: 8px;
            margin-top: 12px;
        }

        .action-btn-small {
            padding: 8px 12px;
            border-radius: 8px;
            font-size: 12px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            border: none;
            display: flex;
            align-items: center;
            gap: 4px;
            text-decoration: none;
            flex: 1;
            justify-content: center;
        }

        .action-btn-primary {
            background: #059669;
            color: white;
        }

        .action-btn-primary:hover {
            background: #065f46;
        }

        .action-btn-outline {
            background: #f8fafc;
            color: #059669;
            border: 1px solid #e2e8f0;
        }

        .action-btn-outline:hover {
            background: #f0fdf4;
        }

        .no-requests {
            text-align: center;
            padding: 60px 20px;
            color: #64748b;
        }

        .no-requests-icon {
            font-size: 64px;
            margin-bottom: 20px;
            opacity: 0.5;
        }

        .no-requests-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .no-requests-subtitle {
            font-size: 14px;
            opacity: 0.8;
        }

        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 100%;
            max-width: 414px;
            background: white;
            border-top: 1px solid #e2e8f0;
            padding: 12px 20px;
            display: flex;
            justify-content: space-around;
            box-shadow: 0 -4px 20px rgba(0,0,0,0.1);
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
            cursor: pointer;
            padding: 8px;
            border-radius: 12px;
            transition: all 0.2s ease;
            position: relative;
            text-decoration: none;
            color: #64748b;
        }

        .nav-item.active {
            background: #f0fdf4;
            color: #059669;
        }

        .nav-item:hover {
            color: #059669;
            text-decoration: none;
        }

        .nav-icon {
            font-size: 20px;
        }

        .nav-label {
            font-size: 10px;
            font-weight: 500;
        }

        .nav-badge {
            position: absolute;
            top: 4px;
            right: 4px;
            background: #ef4444;
            color: white;
            border-radius: 50%;
            width: 16px;
            height: 16px;
            font-size: 10px;
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 1000;
        }

        .modal.show {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-content {
            background: white;
            border-radius: 16px;
            width: 90%;
            max-width: 500px;
            max-height: 80vh;
            overflow-y: auto;
            position: relative;
        }

        .modal-header {
            background: #059669;
            color: white;
            padding: 20px;
            border-radius: 16px 16px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-title {
            font-size: 18px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .modal-close {
            background: none;
            border: none;
            color: white;
            font-size: 24px;
            cursor: pointer;
        }

        .modal-body {
            padding: 20px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            font-size: 14px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 8px;
            display: block;
        }

        .form-input, .form-select, .form-textarea {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            font-size: 14px;
            transition: border-color 0.2s ease;
        }

        .form-input:focus, .form-select:focus, .form-textarea:focus {
            outline: none;
            border-color: #059669;
        }

        .input-group {
            position: relative;
            display: flex;
            align-items: center;
        }

        .input-group-text {
            background: #f1f5f9;
            border: 2px solid #e2e8f0;
            border-right: none;
            border-radius: 12px 0 0 12px;
            padding: 12px 16px;
            font-weight: 600;
            color: #059669;
        }

        .input-group .form-input {
            border-left: none;
            border-radius: 0 12px 12px 0;
        }

        .form-check {
            background: #f0fdf4;
            border: 2px solid #dcfce7;
            border-radius: 12px;
            padding: 16px;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .form-check-input {
            width: 20px;
            height: 20px;
            accent-color: #059669;
        }

        .form-check-label {
            flex: 1;
            font-size: 14px;
            color: #1e293b;
        }

        .resume-card {
            background: #f8fafc;
            border: 2px solid #e2e8f0;
            border-radius: 16px;
            padding: 20px;
            margin-top: 20px;
        }

        .resume-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
        }

        .resume-item {
            text-align: center;
        }

        .resume-value {
            font-size: 18px;
            font-weight: 700;
            margin-bottom: 4px;
        }

        .resume-label {
            font-size: 12px;
            color: #64748b;
            font-weight: 500;
        }

        .modal-footer {
            padding: 20px;
            background: #f8fafc;
            border-top: 1px solid #e2e8f0;
            border-radius: 0 0 16px 16px;
            display: flex;
            gap: 12px;
        }

        .btn {
            padding: 12px 20px;
            border-radius: 12px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            border: none;
            display: flex;
            align-items: center;
            gap: 8px;
            text-decoration: none;
            text-align: center;
            justify-content: center;
            flex: 1;
        }

        .btn-primary {
            background: #059669;
            color: white;
        }

        .btn-primary:hover {
            background: #065f46;
        }

        .btn-secondary {
            background: #f1f5f9;
            color: #64748b;
        }

        .btn-secondary:hover {
            background: #e2e8f0;
        }

        @media (max-width: 480px) {
            .container {
                max-width: 100%;
            }
            
            .header {
                padding: 20px 15px 16px 15px;
            }
            
            .content {
                padding: 15px;
                padding-bottom: 100px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="header-content">
                <div class="header-top">
                    <a href="dashboard.php" class="back-btn">←</a>
                    <div class="header-actions">
                        <button class="action-btn" onclick="toggleFilter()">🔍</button>
                        <button class="action-btn" onclick="refreshPage()">🔄</button>
                    </div>
                </div>
                <div class="page-info">
                    <div class="page-title">Solicitações de Reparo</div>
                    <div class="page-subtitle">Visualize e responda às solicitações</div>
                </div>
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number"><?php echo $stats['pendentes'] ?? 0; ?></div>
                        <div class="stat-label">Pendentes</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number"><?php echo $stats['em_andamento'] ?? 0; ?></div>
                        <div class="stat-label">Em Andamento</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number"><?php echo $stats['finalizadas'] ?? 0; ?></div>
                        <div class="stat-label">Finalizadas</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Content -->
        <div class="content">
            <!-- Filtros -->
            <div class="filter-card" id="filterCard" style="display: none;">
                <div class="filter-title">🔍 Filtros de Busca</div>
                <form method="GET">
                    <div class="filter-grid">
                        <div class="form-group">
                            <label class="form-label">Status</label>
                            <select name="status" class="form-select">
                                <option value="enviado" <?php echo $status_filter === 'enviado' ? 'selected' : ''; ?>>
                                    Pendentes
                                </option>
                                <option value="aceita" <?php echo $status_filter === 'aceita' ? 'selected' : ''; ?>>
                                    Aceitas
                                </option>
                                <option value="rejeitada" <?php echo $status_filter === 'rejeitada' ? 'selected' : ''; ?>>
                                    Rejeitadas
                                </option>
                                <option value="concluido" <?php echo $status_filter === 'concluido' ? 'selected' : ''; ?>>
                                    Concluídas
                                </option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Buscar</label>
                            <input type="text" name="search" class="form-input" 
                                   placeholder="Dispositivo, marca, modelo ou cliente..."
                                   value="<?php echo htmlspecialchars($search); ?>">
                        </div>
                    </div>
                    <div class="filter-actions">
                        <button type="submit" class="btn btn-primary">
                            🔍 Filtrar
                        </button>
                        <a href="solicitacoes_mobile.php" class="btn btn-secondary">
                            🗑️ Limpar
                        </a>
                    </div>
                </form>
            </div>

            <!-- Lista de Solicitações -->
            <?php if (empty($solicitacoes)): ?>
                <div class="no-requests">
                    <div class="no-requests-icon">📋</div>
                    <div class="no-requests-title">Nenhuma solicitação encontrada</div>
                    <div class="no-requests-subtitle">
                        <?php if ($status_filter === 'enviado'): ?>
                            Não há solicitações pendentes no momento.
                        <?php else: ?>
                            Não há solicitações com o status "<?php echo $status_filter; ?>".
                        <?php endif; ?>
                    </div>
                </div>
            <?php else: ?>
                <?php foreach ($solicitacoes as $solicitacao): ?>
                <div class="request-card" onclick="viewRequest(<?php echo $solicitacao['id']; ?>)">
                    <div class="request-header">
                        <div class="device-icon <?php echo rand(0, 1) ? 'urgent' : ''; ?>">
                            📱
                        </div>
                        <div class="device-info">
                            <div class="device-title">
                                <?php echo htmlspecialchars($solicitacao['marca'] . ' ' . $solicitacao['modelo']); ?>
                            </div>
                            <div class="device-subtitle">
                                <?php echo htmlspecialchars($solicitacao['memoria']); ?> • 
                                <?php echo htmlspecialchars($solicitacao['cliente_nome']); ?>
                            </div>
                        </div>
                        <div class="request-status">
                            <?php
                            $status_class = [
                                'enviado' => 'pending',
                                'aceita' => 'accepted',
                                'rejeitada' => 'rejected',
                                'concluido' => 'completed'
                            ];
                            $status_text = [
                                'enviado' => 'Pendente',
                                'aceita' => 'Aceita',
                                'rejeitada' => 'Rejeitada',
                                'concluido' => 'Concluída'
                            ];
                            ?>
                            <span class="status-badge <?php echo $status_class[$solicitacao['status']]; ?>">
                                <?php echo $status_text[$solicitacao['status']]; ?>
                            </span>
                        </div>
                    </div>
                    
                    <div class="request-description">
                        <?php echo htmlspecialchars(substr($solicitacao['descricao_problema'], 0, 120)); ?>
                        <?php if (strlen($solicitacao['descricao_problema']) > 120): ?>...<?php endif; ?>
                    </div>
                    
                    <div class="request-meta">
                        <div class="meta-item">
                            📅 <?php echo date('d/m/Y H:i', strtotime($solicitacao['data_solicitacao'])); ?>
                        </div>
                        <div class="meta-item">
                            📦 <?php echo $solicitacao['total_propostas']; ?> proposta(s)
                        </div>
                    </div>

                    <?php if ($solicitacao['minhas_propostas'] > 0): ?>
                    <div class="meta-item" style="color: #059669; font-weight: 600;">
                        ✅ Você já enviou proposta
                    </div>
                    <?php endif; ?>
                    
                    <div class="request-actions" onclick="event.stopPropagation();">
                        <a href="detalhes_solicitacao_new.php?id=<?php echo $solicitacao['id']; ?>" 
                           class="action-btn-small action-btn-outline">
                            👁️ Ver Detalhes
                        </a>
                        
                        <?php if ($solicitacao['status'] === 'enviado' && $solicitacao['minhas_propostas'] == 0): ?>
                        <button onclick="abrirModalProposta(<?php echo $solicitacao['id']; ?>)" 
                                class="action-btn-small action-btn-primary">
                            ✈️ Proposta
                        </button>
                        <?php endif; ?>
                        
                        <?php if (!empty($solicitacao['video'])): ?>
                        <button type="button" class="action-btn-small action-btn-outline" 
                                onclick="verVideo('<?php echo htmlspecialchars($solicitacao['video']); ?>')">
                            🎥 Vídeo
                        </button>
                        <?php endif; ?>
                    </div>
                </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>

        <!-- Bottom Navigation -->
        <div class="bottom-nav">
            <a href="dashboard.php" class="nav-item">
                <div class="nav-icon">🏠</div>
                <div class="nav-label">Início</div>
            </a>
            <a href="solicitacoes_mobile.php" class="nav-item active">
                <div class="nav-icon">📋</div>
                <div class="nav-label">Solicitações</div>
                <?php if (($stats['pendentes'] ?? 0) > 0): ?>
                    <div class="nav-badge"><?php echo $stats['pendentes']; ?></div>
                <?php endif; ?>
            </a>
            <a href="reparos.php" class="nav-item">
                <div class="nav-icon">🔧</div>
                <div class="nav-label">Reparos</div>
            </a>
            <a href="marketplace.php" class="nav-item">
                <div class="nav-icon">🛒</div>
                <div class="nav-label">Loja</div>
            </a>
            <a href="perfil.php" class="nav-item">
                <div class="nav-icon">👤</div>
                <div class="nav-label">Perfil</div>
            </a>
        </div>
    </div>

    <!-- Modal para vídeo -->
    <div class="modal" id="videoModal">
        <div class="modal-content">
            <div class="modal-header">
                <div class="modal-title">🎥 Vídeo do Problema</div>
                <button class="modal-close" onclick="fecharVideoModal()">&times;</button>
            </div>
            <div class="modal-body">
                <video id="videoPlayer" controls style="width: 100%; border-radius: 12px;">
                    Seu navegador não suporta vídeos.
                </video>
            </div>
        </div>
    </div>

    <!-- Modal para Proposta -->
    <div class="modal" id="propostaModal">
        <div class="modal-content">
            <div class="modal-header">
                <div class="modal-title">✈️ Enviar Proposta</div>
                <button class="modal-close" onclick="fecharPropostaModal()">&times;</button>
            </div>
            <div class="modal-body">
                <form id="formProposta">
                    <input type="hidden" name="solicitacao_id" id="solicitacao_id_input">
                    
                    <div class="form-group">
                        <label for="preco" class="form-label">💰 Preço do Reparo *</label>
                        <div class="input-group">
                            <span class="input-group-text">R$</span>
                            <input type="text" class="form-input" id="preco" name="preco" 
                                   placeholder="0,00" required>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="prazo" class="form-label">📅 Prazo (dias) *</label>
                        <select class="form-select" id="prazo" name="prazo" required>
                            <option value="">Selecione o prazo</option>
                            <option value="1">1 dia (Express)</option>
                            <option value="2">2 dias</option>
                            <option value="3">3 dias</option>
                            <option value="5">5 dias</option>
                            <option value="7">1 semana</option>
                            <option value="10">10 dias</option>
                            <option value="15">15 dias</option>
                            <option value="20">20 dias</option>
                            <option value="30">30 dias</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="observacoes" class="form-label">💬 Observações</label>
                        <textarea class="form-textarea" id="observacoes" name="observacoes" rows="3"
                                  placeholder="Descreva detalhes sobre o reparo, peças, garantia..."></textarea>
                    </div>
                    
                    <div class="form-group">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="retirada_expressa" name="retirada_expressa">
                            <label class="form-check-label" for="retirada_expressa">
                                🏍️ <strong>Retirada Express</strong><br>
                                <small>Buscar e entregar na residência</small>
                            </label>
                        </div>
                    </div>
                    
                    <!-- Resumo -->
                    <div class="resume-card">
                        <div class="section-title" style="margin-bottom: 12px;">
                            🧮 Resumo da Proposta
                        </div>
                        <div class="resume-grid">
                            <div class="resume-item">
                                <div class="resume-value" style="color: #059669;" id="resumoPreco">R$ 0,00</div>
                                <div class="resume-label">Valor Total</div>
                            </div>
                            <div class="resume-item">
                                <div class="resume-value" style="color: #3b82f6;" id="resumoPrazo">-</div>
                                <div class="resume-label">Prazo</div>
                            </div>
                            <div class="resume-item">
                                <div class="resume-value" style="color: #dc2626;" id="resumoTaxa">R$ 0,00</div>
                                <div class="resume-label">Taxa (15%)</div>
                            </div>
                            <div class="resume-item">
                                <div class="resume-value" style="color: #059669;" id="resumoRecebera">R$ 0,00</div>
                                <div class="resume-label">Você Recebe</div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="fecharPropostaModal()">
                    ❌ Cancelar
                </button>
                <button type="button" class="btn btn-primary" onclick="enviarProposta()" id="btnEnviarProposta">
                    ✈️ Enviar Proposta
                </button>
            </div>
        </div>
    </div>

    <script>
        let currentSolicitacao = null;

        function viewRequest(id) {
            window.location.href = 'detalhes_solicitacao_new.php?id=' + id;
        }

        function verVideo(videoUrl) {
            const modal = document.getElementById('videoModal');
            const video = document.getElementById('videoPlayer');
            video.src = videoUrl;
            modal.classList.add('show');
        }

        function fecharVideoModal() {
            const modal = document.getElementById('videoModal');
            const video = document.getElementById('videoPlayer');
            modal.classList.remove('show');
            video.pause();
            video.currentTime = 0;
        }

        function abrirModalProposta(solicitacaoId) {
            currentSolicitacao = solicitacaoId;
            document.getElementById('solicitacao_id_input').value = solicitacaoId;
            document.getElementById('propostaModal').classList.add('show');
            
            // Limpar formulário
            document.getElementById('formProposta').reset();
            document.getElementById('solicitacao_id_input').value = solicitacaoId;
            atualizarResumo();
        }

        function fecharPropostaModal() {
            const modal = document.getElementById('propostaModal');
            modal.classList.remove('show');
            currentSolicitacao = null;
        }

        function formatarPreco(input) {
            let valor = input.value.replace(/\D/g, '');
            
            if (valor.length === 0) {
                input.value = '';
                atualizarResumo();
                return;
            }
            
            valor = valor.padStart(3, '0');
            valor = valor.slice(0, -2) + ',' + valor.slice(-2);
            valor = valor.replace(/\B(?=(\d{3})+(?!\d))/g, '.');
            
            input.value = valor;
            atualizarResumo();
        }

        function atualizarResumo() {
            const precoInput = document.getElementById('preco').value;
            const prazoSelect = document.getElementById('prazo');
            
            // Converter preço para número
            let preco = 0;
            if (precoInput) {
                preco = parseFloat(precoInput.replace(/\./g, '').replace(',', '.')) || 0;
            }
            
            // Calcular valores
            const taxa = preco * 0.15;
            const recebera = preco - taxa;
            
            // Atualizar resumo
            document.getElementById('resumoPreco').textContent = 'R$ ' + preco.toLocaleString('pt-BR', {minimumFractionDigits: 2});
            document.getElementById('resumoTaxa').textContent = 'R$ ' + taxa.toLocaleString('pt-BR', {minimumFractionDigits: 2});
            document.getElementById('resumoRecebera').textContent = 'R$ ' + recebera.toLocaleString('pt-BR', {minimumFractionDigits: 2});
            
            // Atualizar prazo
            const prazoTexto = prazoSelect.value ? prazoSelect.options[prazoSelect.selectedIndex].text : '-';
            document.getElementById('resumoPrazo').textContent = prazoTexto;
        }

        function enviarProposta() {
            const form = document.getElementById('formProposta');
            const btnEnviar = document.getElementById('btnEnviarProposta');
            
            // Validar campos obrigatórios
            const preco = document.getElementById('preco').value;
            const prazo = document.getElementById('prazo').value;
            
            if (!preco || !prazo) {
                alert('Por favor, preencha o preço e o prazo.');
                return;
            }
            
            // Desabilitar botão durante envio
            btnEnviar.disabled = true;
            btnEnviar.innerHTML = '⏳ Enviando...';
            
            // Preparar dados do formulário
            const formData = new FormData(form);
            
            // Enviar via AJAX
            fetch('ajax/enviar_proposta.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    fecharPropostaModal();
                    alert('✅ Proposta enviada com sucesso!');
                    window.location.reload();
                } else {
                    alert('❌ Erro: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Erro:', error);
                alert('❌ Erro ao enviar proposta. Tente novamente.');
            })
            .finally(() => {
                btnEnviar.disabled = false;
                btnEnviar.innerHTML = '✈️ Enviar Proposta';
            });
        }

        function toggleFilter() {
            const filterCard = document.getElementById('filterCard');
            const isVisible = filterCard.style.display !== 'none';
            filterCard.style.display = isVisible ? 'none' : 'block';
        }

        function refreshPage() {
            window.location.reload();
        }

        // Event listeners
        document.addEventListener('DOMContentLoaded', function() {
            // Formatação de preço
            const precoInput = document.getElementById('preco');
            if (precoInput) {
                precoInput.addEventListener('input', function() {
                    formatarPreco(this);
                });
            }
            
            // Atualizar resumo quando prazo mudar
            const prazoSelect = document.getElementById('prazo');
            if (prazoSelect) {
                prazoSelect.addEventListener('change', atualizarResumo);
            }
            
            // Listeners para fechar modais clicando fora
            document.getElementById('videoModal').addEventListener('click', function(e) {
                if (e.target === this) {
                    fecharVideoModal();
                }
            });
            
            document.getElementById('propostaModal').addEventListener('click', function(e) {
                if (e.target === this) {
                    fecharPropostaModal();
                }
            });

            // Animações das cards
            const cards = document.querySelectorAll('.request-card');
            cards.forEach(card => {
                card.addEventListener('click', function() {
                    this.style.transform = 'scale(0.98)';
                    setTimeout(() => {
                        this.style.transform = '';
                    }, 150);
                });
            });
        });
    </script>
</body>
</html>
