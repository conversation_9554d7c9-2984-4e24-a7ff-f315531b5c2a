{"id": 1340404, "date_created": "2016-08-19T13:50:17.000-04:00", "date_approved": "2016-08-19T13:50:17.000-04:00", "date_last_updated": "2016-08-19T13:50:17.000-04:00", "money_release_date": null, "operation_type": "regular_payment", "issuer_id": "1", "payment_method_id": "visa", "payment_type_id": "credit_card", "status": "approved", "status_detail": "accredited", "currency_id": "ARS", "description": null, "live_mode": false, "sponsor_id": null, "authorization_code": null, "collector_id": 192627424, "payer": {"type": "customer", "id": "204943005-G4n3trGt71WZEb", "email": "<EMAIL>", "identification": {"type": "DNI", "number": "1111111"}, "phone": {"area_code": "01", "number": "1111-1111", "extension": ""}, "first_name": "Test", "last_name": "Test"}, "metadata": [], "order": [], "external_reference": null, "transaction_amount": 100, "transaction_amount_refunded": 0, "coupon_amount": 0, "differential_pricing_id": null, "deduction_schema": null, "transaction_details": {"net_received_amount": 80.41, "total_paid_amount": 100, "overpaid_amount": 0, "external_resource_url": null, "installment_amount": 100, "financial_institution": null, "payment_method_reference_id": null}, "fee_details": [{"type": "mercadopago_fee", "amount": 19.59, "fee_payer": "collector"}], "captured": true, "binary_mode": false, "call_for_authorize_id": null, "statement_descriptor": "WWW.MERCADOPAGO.COM", "installments": 1, "card": {"id": "1461592544092", "first_six_digits": "407559", "last_four_digits": "3764", "expiration_month": 3, "expiration_year": 2018, "date_created": "2016-04-25T09:55:00.000-04:00", "date_last_updated": "2016-04-25T09:55:34.000-04:00", "cardholder": {"name": "nam", "identification": {"number": "12345678", "type": "DNI"}}}, "notification_url": null, "refunds": []}