<?php
/**
 * Marketplace - Mobile First
 * FixFácil Assistências
 */

// Redirecionar para versão mobile final
header('Location: marketplace.php?' . $_SERVER['QUERY_STRING']);
exit();
?>

// Configuração de banco de dados
$host = "localhost";
$username = "u680766645_fixfacilnew";
$password = "T3cn0l0g1a@";
$database = "u680766645_fixfacilnew";

$mysqli = new mysqli($host, $username, $password, $database);

if ($mysqli->connect_error) {
    die("Erro de conexão: " . $mysqli->connect_error);
}

// Obter dados do usuário logado
$usuario_id = $_SESSION['usuario_id'];
$usuario = null;

try {
    // Buscar dados do usuário
    $sql = "SELECT u.id, u.nome, u.email, u.telefone, u.plano_id, at.id as assistencia_id 
            FROM usuarios u 
            LEFT JOIN assistencias_tecnicas at ON u.id = at.usuario_id 
            WHERE u.id = ?";
    $stmt = $mysqli->prepare($sql);
    $stmt->bind_param("i", $usuario_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $usuario = $result->fetch_assoc();
    
} catch (Exception $e) {
    error_log("Erro ao buscar dados do usuário: " . $e->getMessage());
    $usuario = ['nome' => 'Usuário', 'email' => '', 'assistencia_id' => null];
}

// Filtros
$categoria_filter = $_GET['categoria'] ?? 'todos';
$status_filter = $_GET['status'] ?? 'ativo';
$search = $_GET['search'] ?? '';

// Obter produtos
$produtos = [];
try {
    if ($usuario && isset($usuario['assistencia_id'])) {
        $where_conditions = ["p.assistencia_id = ?"];
        $params = [$usuario['assistencia_id']];
        
        if ($status_filter !== 'todos') {
            $where_conditions[] = "p.status = ?";
            $params[] = $status_filter;
        }
        
        if ($categoria_filter !== 'todos') {
            $where_conditions[] = "p.categoria = ?";
            $params[] = $categoria_filter;
        }
        
        if (!empty($search)) {
            $where_conditions[] = "(p.nome LIKE ? OR p.descricao LIKE ? OR p.marca LIKE ?)";
            $search_param = "%$search%";
            $params = array_merge($params, [$search_param, $search_param, $search_param]);
        }
        
        $where_clause = implode(' AND ', $where_conditions);
        
        $sql = "SELECT p.*, COUNT(v.id) as total_vendas
                FROM produtos p
                LEFT JOIN vendas v ON p.id = v.produto_id
                WHERE $where_clause
                GROUP BY p.id
                ORDER BY p.created_at DESC";
        
        $stmt = $mysqli->prepare($sql);
        $stmt->bind_param(str_repeat('s', count($params)), ...$params);
        $stmt->execute();
        $result = $stmt->get_result();
        
        while ($row = $result->fetch_assoc()) {
            $produtos[] = $row;
        }
    }
} catch (Exception $e) {
    error_log("Erro ao buscar produtos: " . $e->getMessage());
}

// Obter estatísticas
$stats = [
    'total_produtos' => 0,
    'produtos_ativos' => 0,
    'vendas_mes' => 0,
    'receita_mes' => 0
];

try {
    if ($usuario && isset($usuario['assistencia_id'])) {
        $assistencia_id = $usuario['assistencia_id'];
        
        // Total de produtos
        $sql = "SELECT COUNT(*) as total FROM produtos WHERE assistencia_id = ?";
        $stmt = $mysqli->prepare($sql);
        $stmt->bind_param("i", $assistencia_id);
        $stmt->execute();
        $result = $stmt->get_result();
        $stats['total_produtos'] = $result->fetch_assoc()['total'];
        
        // Produtos ativos
        $sql = "SELECT COUNT(*) as total FROM produtos WHERE assistencia_id = ? AND status = 'ativo'";
        $stmt = $mysqli->prepare($sql);
        $stmt->bind_param("i", $assistencia_id);
        $stmt->execute();
        $result = $stmt->get_result();
        $stats['produtos_ativos'] = $result->fetch_assoc()['total'];
        
        // Vendas do mês
        $sql = "SELECT COUNT(*) as total FROM vendas v 
                JOIN produtos p ON v.produto_id = p.id 
                WHERE p.assistencia_id = ? 
                AND MONTH(v.data_venda) = MONTH(NOW()) 
                AND YEAR(v.data_venda) = YEAR(NOW())";
        $stmt = $mysqli->prepare($sql);
        $stmt->bind_param("i", $assistencia_id);
        $stmt->execute();
        $result = $stmt->get_result();
        $stats['vendas_mes'] = $result->fetch_assoc()['total'];
        
        // Receita do mês
        $sql = "SELECT SUM(v.valor_total) as receita FROM vendas v 
                JOIN produtos p ON v.produto_id = p.id 
                WHERE p.assistencia_id = ? 
                AND MONTH(v.data_venda) = MONTH(NOW()) 
                AND YEAR(v.data_venda) = YEAR(NOW())";
        $stmt = $mysqli->prepare($sql);
        $stmt->bind_param("i", $assistencia_id);
        $stmt->execute();
        $result = $stmt->get_result();
        $receita = $result->fetch_assoc()['receita'];
        $stats['receita_mes'] = $receita ? $receita : 0;
    }
} catch (Exception $e) {
    error_log("Erro ao obter estatísticas: " . $e->getMessage());
}

// Categorias disponíveis
$categorias = [
    'todos' => 'Todos',
    'acessorios' => 'Acessórios',
    'capas' => 'Capas',
    'carregadores' => 'Carregadores',
    'fones' => 'Fones',
    'pecas' => 'Peças',
    'outros' => 'Outros'
];

// Status disponíveis
$status_options = [
    'todos' => 'Todos',
    'ativo' => 'Ativo',
    'inativo' => 'Inativo',
    'esgotado' => 'Esgotado'
];

// Função para obter cor do status
function getStatusColor($status) {
    switch ($status) {
        case 'ativo':
            return 'success';
        case 'inativo':
            return 'warning';
        case 'esgotado':
            return 'danger';
        default:
            return 'secondary';
    }
}

// Função para obter badge do status
function getStatusBadge($status) {
    switch ($status) {
        case 'ativo':
            return '🟢 Ativo';
        case 'inativo':
            return '🟡 Inativo';
        case 'esgotado':
            return '🔴 Esgotado';
        default:
            return '⚪ Desconhecido';
    }
}
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FixFacil - Marketplace</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            min-height: 100vh;
            color: #1e293b;
        }

        .container {
            max-width: 414px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
            position: relative;
            overflow-x: hidden;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }

        .header {
            background: linear-gradient(135deg, #059669 0%, #065f46 100%);
            color: white;
            padding: 24px 20px 20px 20px;
            position: relative;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 20"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="0.5" fill="white" opacity="0.05"/></pattern></defs><rect width="100" height="20" fill="url(%23grain)"/></svg>');
        }

        .header-content {
            position: relative;
            z-index: 1;
        }

        .header-top {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 20px;
        }

        .page-title {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .back-btn {
            width: 40px;
            height: 40px;
            background: rgba(255,255,255,0.15);
            border: none;
            border-radius: 12px;
            color: white;
            font-size: 18px;
            cursor: pointer;
            backdrop-filter: blur(10px);
            transition: all 0.2s ease;
            text-decoration: none;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .back-btn:hover {
            background: rgba(255,255,255,0.25);
        }

        .page-info h1 {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 2px;
        }

        .page-info p {
            font-size: 12px;
            opacity: 0.9;
        }

        .header-actions {
            display: flex;
            gap: 8px;
        }

        .action-btn {
            width: 40px;
            height: 40px;
            background: rgba(255,255,255,0.15);
            border: none;
            border-radius: 12px;
            color: white;
            font-size: 18px;
            cursor: pointer;
            backdrop-filter: blur(10px);
            transition: all 0.2s ease;
            text-decoration: none;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .action-btn:hover {
            background: rgba(255,255,255,0.25);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 12px;
        }

        .stat-card {
            background: rgba(255,255,255,0.15);
            border-radius: 12px;
            padding: 16px 12px;
            text-align: center;
            backdrop-filter: blur(10px);
        }

        .stat-number {
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 4px;
        }

        .stat-label {
            font-size: 11px;
            opacity: 0.8;
        }

        .content {
            padding: 20px;
            padding-bottom: 100px;
        }

        .filters-section {
            background: white;
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
        }

        .section-title {
            font-size: 18px;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .search-box {
            position: relative;
            margin-bottom: 16px;
        }

        .search-input {
            width: 100%;
            padding: 12px 16px 12px 40px;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            font-size: 14px;
            background: #f8fafc;
            transition: all 0.2s ease;
        }

        .search-input:focus {
            outline: none;
            border-color: #059669;
            background: white;
        }

        .search-icon {
            position: absolute;
            left: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: #64748b;
            font-size: 16px;
        }

        .filters-row {
            display: flex;
            gap: 8px;
        }

        .filter-select {
            flex: 1;
            padding: 8px 12px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 12px;
            background: #f8fafc;
            transition: all 0.2s ease;
        }

        .filter-select:focus {
            outline: none;
            border-color: #059669;
            background: white;
        }

        .products-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
        }

        .product-card {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
            position: relative;
        }

        .product-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(0,0,0,0.12);
        }

        .product-image {
            width: 100%;
            height: 120px;
            background: linear-gradient(135deg, #f1f5f9, #e2e8f0);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 32px;
            color: #64748b;
            position: relative;
        }

        .product-status {
            position: absolute;
            top: 8px;
            right: 8px;
            background: rgba(255,255,255,0.9);
            border-radius: 6px;
            padding: 2px 6px;
            font-size: 10px;
            font-weight: 600;
            backdrop-filter: blur(10px);
        }

        .product-status.ativo {
            color: #059669;
        }

        .product-status.inativo {
            color: #f59e0b;
        }

        .product-status.esgotado {
            color: #ef4444;
        }

        .product-info {
            padding: 12px;
        }

        .product-name {
            font-size: 14px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 4px;
            line-height: 1.2;
        }

        .product-price {
            font-size: 16px;
            font-weight: 700;
            color: #059669;
            margin-bottom: 4px;
        }

        .product-stock {
            font-size: 11px;
            color: #64748b;
            margin-bottom: 8px;
        }

        .product-actions {
            display: flex;
            gap: 4px;
        }

        .action-btn-small {
            flex: 1;
            padding: 6px 8px;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            font-size: 10px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            text-decoration: none;
            text-align: center;
        }

        .action-btn-small.primary {
            background: #059669;
            color: white;
            border-color: #059669;
        }

        .action-btn-small.secondary {
            background: #f8fafc;
            color: #64748b;
        }

        .action-btn-small:hover {
            transform: translateY(-1px);
        }

        .empty-state {
            text-align: center;
            padding: 40px 20px;
            color: #64748b;
        }

        .empty-state-icon {
            font-size: 48px;
            margin-bottom: 16px;
        }

        .empty-state-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .empty-state-description {
            font-size: 14px;
            margin-bottom: 20px;
        }

        .add-product-btn {
            background: #059669;
            color: white;
            border: none;
            border-radius: 8px;
            padding: 12px 24px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            text-decoration: none;
            display: inline-block;
        }

        .add-product-btn:hover {
            background: #065f46;
        }

        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 100%;
            max-width: 414px;
            background: white;
            border-top: 1px solid #e2e8f0;
            padding: 12px 20px;
            display: flex;
            justify-content: space-around;
            box-shadow: 0 -4px 20px rgba(0,0,0,0.1);
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
            cursor: pointer;
            padding: 8px;
            border-radius: 12px;
            transition: all 0.2s ease;
            position: relative;
            text-decoration: none;
            color: #64748b;
        }

        .nav-item.active {
            background: #f0fdf4;
            color: #059669;
        }

        .nav-icon {
            font-size: 20px;
        }

        .nav-label {
            font-size: 10px;
            font-weight: 500;
        }

        .nav-badge {
            position: absolute;
            top: 4px;
            right: 4px;
            background: #ef4444;
            color: white;
            border-radius: 50%;
            width: 16px;
            height: 16px;
            font-size: 10px;
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .floating-action {
            position: fixed;
            bottom: 80px;
            right: 20px;
            width: 56px;
            height: 56px;
            background: linear-gradient(135deg, #059669, #065f46);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            cursor: pointer;
            box-shadow: 0 4px 16px rgba(5, 150, 105, 0.4);
            transition: all 0.3s ease;
            z-index: 100;
            text-decoration: none;
        }

        .floating-action:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 20px rgba(5, 150, 105, 0.5);
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .content > * {
            animation: fadeIn 0.6s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="header-content">
                <div class="header-top">
                    <div class="page-title">
                        <a href="dashboard_mobile_final.php" class="back-btn">←</a>
                        <div class="page-info">
                            <h1>🛒 Marketplace</h1>
                            <p>Gerencie seus produtos</p>
                        </div>
                    </div>
                    <div class="header-actions">
                        <a href="adicionar_produto.php" class="action-btn" title="Adicionar produto">
                            ➕
                        </a>
                        <button class="action-btn" onclick="toggleFilters()" title="Filtros">
                            🔍
                        </button>
                    </div>
                </div>

                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number"><?php echo $stats['total_produtos']; ?></div>
                        <div class="stat-label">Produtos</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number"><?php echo $stats['vendas_mes']; ?></div>
                        <div class="stat-label">Vendas</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">R$ <?php echo number_format($stats['receita_mes'], 0, ',', '.'); ?></div>
                        <div class="stat-label">Receita</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Content -->
        <div class="content">
            <!-- Filters -->
            <div class="filters-section">
                <div class="section-title">🔍 Filtros</div>
                
                <form method="GET" action="">
                    <div class="search-box">
                        <div class="search-icon">🔍</div>
                        <input type="text" 
                               name="search" 
                               class="search-input" 
                               placeholder="Buscar produtos..."
                               value="<?php echo htmlspecialchars($search); ?>">
                    </div>
                    
                    <div class="filters-row">
                        <select name="categoria" class="filter-select">
                            <?php foreach ($categorias as $value => $label): ?>
                                <option value="<?php echo $value; ?>" <?php echo $categoria_filter === $value ? 'selected' : ''; ?>>
                                    <?php echo $label; ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                        
                        <select name="status" class="filter-select">
                            <?php foreach ($status_options as $value => $label): ?>
                                <option value="<?php echo $value; ?>" <?php echo $status_filter === $value ? 'selected' : ''; ?>>
                                    <?php echo $label; ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                        
                        <button type="submit" class="action-btn-small primary">
                            Filtrar
                        </button>
                    </div>
                </form>
            </div>

            <!-- Products -->
            <?php if (empty($produtos)): ?>
                <div class="empty-state">
                    <div class="empty-state-icon">📦</div>
                    <div class="empty-state-title">Nenhum produto encontrado</div>
                    <div class="empty-state-description">
                        <?php if (!empty($search) || $categoria_filter !== 'todos' || $status_filter !== 'todos'): ?>
                            Tente ajustar os filtros ou buscar por outros termos.
                        <?php else: ?>
                            Comece adicionando seu primeiro produto ao marketplace.
                        <?php endif; ?>
                    </div>
                    <a href="adicionar_produto.php" class="add-product-btn">
                        ➕ Adicionar Produto
                    </a>
                </div>
            <?php else: ?>
                <div class="products-grid">
                    <?php foreach ($produtos as $produto): ?>
                        <div class="product-card">
                            <div class="product-image">
                                <?php if (!empty($produto['imagem'])): ?>
                                    <img src="<?php echo htmlspecialchars($produto['imagem']); ?>" 
                                         alt="<?php echo htmlspecialchars($produto['nome']); ?>"
                                         style="width: 100%; height: 100%; object-fit: cover;">
                                <?php else: ?>
                                    📦
                                <?php endif; ?>
                                <div class="product-status <?php echo $produto['status']; ?>">
                                    <?php echo getStatusBadge($produto['status']); ?>
                                </div>
                            </div>
                            
                            <div class="product-info">
                                <div class="product-name"><?php echo htmlspecialchars($produto['nome']); ?></div>
                                <div class="product-price">R$ <?php echo number_format($produto['preco'], 2, ',', '.'); ?></div>
                                <div class="product-stock">
                                    Estoque: <?php echo $produto['estoque']; ?> | 
                                    Vendas: <?php echo $produto['total_vendas']; ?>
                                </div>
                                
                                <div class="product-actions">
                                    <a href="editar_produto.php?id=<?php echo $produto['id']; ?>" 
                                       class="action-btn-small secondary">
                                        ✏️ Editar
                                    </a>
                                    <a href="produto_detalhes.php?id=<?php echo $produto['id']; ?>" 
                                       class="action-btn-small primary">
                                        👁️ Ver
                                    </a>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>

        <!-- Floating Action Button -->
        <a href="adicionar_produto.php" class="floating-action" title="Adicionar produto">
            <span>+</span>
        </a>

        <!-- Bottom Navigation -->
        <div class="bottom-nav">
            <a href="dashboard_mobile_final.php" class="nav-item">
                <div class="nav-icon">🏠</div>
                <div class="nav-label">Início</div>
            </a>
            <a href="solicitacoes.php" class="nav-item">
                <div class="nav-icon">📋</div>
                <div class="nav-label">Solicitações</div>
            </a>
            <a href="reparos.php" class="nav-item">
                <div class="nav-icon">🔧</div>
                <div class="nav-label">Reparos</div>
            </a>
            <a href="marketplace.php" class="nav-item active">
                <div class="nav-icon">🛒</div>
                <div class="nav-label">Loja</div>
            </a>
            <a href="carteira.php" class="nav-item">
                <div class="nav-icon">💳</div>
                <div class="nav-label">Carteira</div>
            </a>
        </div>
    </div>

    <script>
        // Função para mostrar notificações
        function showNotification(message) {
            const existingNotification = document.querySelector('.notification');
            if (existingNotification) {
                existingNotification.remove();
            }
            
            const notification = document.createElement('div');
            notification.className = 'notification';
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                left: 50%;
                transform: translateX(-50%);
                background: #059669;
                color: white;
                padding: 12px 20px;
                border-radius: 8px;
                font-size: 14px;
                font-weight: 600;
                z-index: 2000;
                box-shadow: 0 4px 12px rgba(5, 150, 105, 0.3);
                animation: slideDown 0.3s ease;
                max-width: 90%;
                text-align: center;
            `;
            notification.textContent = message;
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.style.animation = 'slideOut 0.3s ease';
                setTimeout(() => notification.remove(), 300);
            }, 3000);
        }

        // Função para alternar filtros
        function toggleFilters() {
            const filtersSection = document.querySelector('.filters-section');
            const isVisible = filtersSection.style.display !== 'none';
            
            if (isVisible) {
                filtersSection.style.display = 'none';
                showNotification('Filtros ocultos');
            } else {
                filtersSection.style.display = 'block';
                showNotification('Filtros exibidos');
            }
        }

        // Inicialização
        document.addEventListener('DOMContentLoaded', function() {
            // Mensagem de boas-vindas
            setTimeout(() => {
                const totalProdutos = <?php echo $stats['total_produtos']; ?>;
                const vendasMes = <?php echo $stats['vendas_mes']; ?>;
                
                if (totalProdutos === 0) {
                    showNotification('🛒 Marketplace vazio! Adicione seu primeiro produto.');
                } else if (vendasMes > 0) {
                    showNotification(`🎉 ${vendasMes} vendas este mês! Continue assim!`);
                } else {
                    showNotification(`📦 ${totalProdutos} produtos no marketplace.`);
                }
            }, 1000);
        });

        // Adicionar CSS das animações
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideDown {
                from {
                    opacity: 0;
                    transform: translateX(-50%) translateY(-20px);
                }
                to {
                    opacity: 1;
                    transform: translateX(-50%) translateY(0);
                }
            }
            
            @keyframes slideOut {
                from {
                    opacity: 1;
                    transform: translateX(-50%) translateY(0);
                }
                to {
                    opacity: 0;
                    transform: translateX(-50%) translateY(-20px);
                }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
