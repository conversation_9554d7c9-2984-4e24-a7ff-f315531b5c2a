<?php
/**
 * Teste de Diagnóstico para Chat
 * FixFácil Assistências - Sistema Novo
 */

// Ativar exibição de erros
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔍 Diagnóstico do Chat</h1>";
echo "<style>body { font-family: Arial, sans-serif; margin: 20px; }</style>";

// Teste 1: Verificar se arquivos existem
echo "<h3>📁 Verificação de Arquivos:</h3>";

$arquivos = [
    'config/auth.php',
    'config/database.php',
    'includes/layout.php'
];

foreach ($arquivos as $arquivo) {
    if (file_exists($arquivo)) {
        echo "<p>✅ $arquivo existe</p>";
    } else {
        echo "<p>❌ $arquivo NÃO EXISTE</p>";
    }
}

// Teste 2: Incluir arquivos
echo "<h3>🔗 Teste de Inclusão:</h3>";

try {
    require_once 'config/database.php';
    echo "<p>✅ database.php incluído</p>";
    
    $db = getDatabase();
    echo "<p>✅ Conexão com banco estabelecida</p>";
    
} catch (Exception $e) {
    echo "<p>❌ Erro no database: " . $e->getMessage() . "</p>";
    die();
}

try {
    require_once 'config/auth.php';
    echo "<p>✅ auth.php incluído</p>";
    
    $auth = getAuth();
    echo "<p>✅ Classe Auth inicializada</p>";
    
} catch (Exception $e) {
    echo "<p>❌ Erro no auth: " . $e->getMessage() . "</p>";
    die();
}

// Teste 3: Verificar sessão
echo "<h3>🔐 Verificação de Sessão:</h3>";

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

if (isset($_SESSION['usuario_id'])) {
    echo "<p>✅ Usuário logado: ID {$_SESSION['usuario_id']}</p>";
    echo "<p>✅ Tipo: {$_SESSION['tipo_usuario']}</p>";
    
    // Obter dados do usuário
    $usuario = $auth->getUsuarioLogado();
    if ($usuario) {
        echo "<p>✅ Dados do usuário obtidos</p>";
        echo "<p>Nome: " . htmlspecialchars($usuario['nome']) . "</p>";
        echo "<p>Email: " . htmlspecialchars($usuario['email']) . "</p>";
        echo "<p>Assistência ID: " . ($usuario['assistencia_id'] ?? 'Não definido') . "</p>";
    } else {
        echo "<p>❌ Erro ao obter dados do usuário</p>";
    }
    
} else {
    echo "<p>❌ Usuário não está logado</p>";
    echo "<p><a href='../login.php'>Fazer Login</a></p>";
    die();
}

// Teste 4: Verificar plano
echo "<h3>📋 Verificação de Plano:</h3>";

try {
    $plano = $auth->getPlanoInfo($usuario['id']);
    if ($plano) {
        echo "<p>✅ Plano obtido: {$plano['nome']}</p>";
        echo "<p>Acesso Chat: " . ($plano['acesso_chat'] ? 'SIM' : 'NÃO') . "</p>";
        echo "<p>Acesso Marketplace: " . ($plano['acesso_marketplace'] ? 'SIM' : 'NÃO') . "</p>";
        echo "<p>Taxa de Serviço: {$plano['taxa_servico']}%</p>";
    } else {
        echo "<p>❌ Erro ao obter plano</p>";
    }
} catch (Exception $e) {
    echo "<p>❌ Erro no plano: " . $e->getMessage() . "</p>";
}

// Teste 5: Verificar acesso ao chat
echo "<h3>💬 Verificação de Acesso ao Chat:</h3>";

try {
    $temAcessoChat = $auth->hasAccess('chat');
    if ($temAcessoChat) {
        echo "<p>✅ <strong>TEM ACESSO AO CHAT</strong></p>";
    } else {
        echo "<p>❌ <strong>NÃO TEM ACESSO AO CHAT</strong></p>";
        echo "<p>Plano atual: {$plano['nome']}</p>";
        echo "<p>Para ter acesso ao chat, você precisa do plano Premium ou Master</p>";
        echo "<p><a href='upgrade_plano.php?feature=chat'>Fazer Upgrade</a></p>";
    }
} catch (Exception $e) {
    echo "<p>❌ Erro ao verificar acesso: " . $e->getMessage() . "</p>";
}

// Teste 6: Verificar tabelas necessárias
echo "<h3>🗄️ Verificação de Tabelas:</h3>";

$tabelas = [
    'propostas_assistencia',
    'solicitacoes_reparo',
    'usuarios',
    'assistencias_tecnicas',
    'planos',
    'assinaturas_assistencias'
];

foreach ($tabelas as $tabela) {
    try {
        $result = $db->query("SELECT COUNT(*) as total FROM $tabela");
        $row = $result->fetch_assoc();
        echo "<p>✅ Tabela '$tabela': {$row['total']} registros</p>";
    } catch (Exception $e) {
        echo "<p>❌ Tabela '$tabela': " . $e->getMessage() . "</p>";
    }
}

// Teste 7: Verificar conversas
if ($temAcessoChat && $usuario['assistencia_id']) {
    echo "<h3>💬 Verificação de Conversas:</h3>";
    
    try {
        $sql = "
            SELECT
                pa.id as proposta_id,
                pa.status,
                sr.dispositivo,
                sr.marca,
                sr.modelo,
                u.nome as cliente_nome
            FROM propostas_assistencia pa
            JOIN solicitacoes_reparo sr ON pa.solicitacao_id = sr.id
            JOIN usuarios u ON sr.usuario_id = u.id
            WHERE pa.assistencia_id = ?
            AND pa.status IN ('aceita', 'Em Andamento', 'Concluída')
            ORDER BY pa.data_proposta DESC
            LIMIT 5
        ";
        
        $result = $db->query($sql, [$usuario['assistencia_id']]);
        $conversas = [];
        
        while ($row = $result->fetch_assoc()) {
            $conversas[] = $row;
        }
        
        if (!empty($conversas)) {
            echo "<p>✅ Encontradas " . count($conversas) . " conversas</p>";
            foreach ($conversas as $conversa) {
                echo "<p>- {$conversa['cliente_nome']} ({$conversa['marca']} {$conversa['modelo']}) - {$conversa['status']}</p>";
            }
        } else {
            echo "<p>⚠️ Nenhuma conversa encontrada</p>";
            echo "<p>Conversas aparecerão quando houver propostas aceitas</p>";
        }
        
    } catch (Exception $e) {
        echo "<p>❌ Erro ao obter conversas: " . $e->getMessage() . "</p>";
    }
}

// Teste 8: Incluir layout
echo "<h3>🎨 Teste do Layout:</h3>";

try {
    require_once 'includes/layout.php';
    echo "<p>✅ layout.php incluído</p>";
    
    $layout = new Layout();
    echo "<p>✅ Classe Layout inicializada</p>";
    
} catch (Exception $e) {
    echo "<p>❌ Erro no layout: " . $e->getMessage() . "</p>";
    echo "<p>Stack trace:</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<hr>";
echo "<h3>🔗 Links de Teste:</h3>";
echo "<ul>";
echo "<li><a href='chat.php'>Tentar Acessar Chat</a></li>";
echo "<li><a href='dashboard.php'>Dashboard</a></li>";
echo "<li><a href='upgrade_plano.php?feature=chat'>Upgrade de Plano</a></li>";
echo "<li><a href='../login.php'>Login</a></li>";
echo "</ul>";

echo "<p><small>Teste executado em " . date('d/m/Y H:i:s') . "</small></p>";
?>
