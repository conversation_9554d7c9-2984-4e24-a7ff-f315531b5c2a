<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste Modal Proposta - Solicitações</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            padding: 20px;
            color: #1e293b;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            padding: 30px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }

        h1 {
            color: #059669;
            margin-bottom: 20px;
            text-align: center;
        }

        .test-section {
            background: #f8fafc;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .test-section h2 {
            color: #1e293b;
            margin-bottom: 15px;
            font-size: 18px;
        }

        .test-links {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 12px;
        }

        .test-link, .test-btn {
            background: #059669;
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.2s ease;
            text-align: center;
            border: none;
            cursor: pointer;
            font-size: 14px;
        }

        .test-link:hover, .test-btn:hover {
            background: #065f46;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(5, 150, 105, 0.3);
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-ok {
            background: #059669;
        }

        .status-error {
            background: #ef4444;
        }

        .description {
            background: #e0f2fe;
            border-left: 4px solid #0284c7;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 0 8px 8px 0;
        }

        .request-card {
            background: white;
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
            border: 1px solid #e2e8f0;
        }

        .request-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 12px;
        }

        .device-icon {
            font-size: 24px;
        }

        .device-info h3 {
            font-size: 16px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 4px;
        }

        .device-info p {
            font-size: 14px;
            color: #64748b;
        }

        .request-actions {
            display: flex;
            gap: 8px;
            margin-top: 12px;
        }

        .action-btn-small {
            padding: 8px 12px;
            border-radius: 8px;
            font-size: 12px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            border: none;
            display: flex;
            align-items: center;
            gap: 4px;
            text-decoration: none;
            flex: 1;
            justify-content: center;
        }

        .action-btn-primary {
            background: #059669;
            color: white;
        }

        .action-btn-primary:hover {
            background: #065f46;
        }

        .action-btn-outline {
            background: #f8fafc;
            color: #059669;
            border: 1px solid #e2e8f0;
        }

        .action-btn-outline:hover {
            background: #f0fdf4;
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 1000;
        }

        .modal.show {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-content {
            background: white;
            border-radius: 16px;
            width: 90%;
            max-width: 500px;
            max-height: 80vh;
            overflow-y: auto;
            position: relative;
        }

        .modal-header {
            background: #059669;
            color: white;
            padding: 20px;
            border-radius: 16px 16px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-title {
            font-size: 18px;
            font-weight: 600;
        }

        .modal-close {
            background: none;
            border: none;
            color: white;
            font-size: 24px;
            cursor: pointer;
        }

        .modal-body {
            padding: 20px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            font-size: 14px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 8px;
            display: block;
        }

        .form-input, .form-select, .form-textarea {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            font-size: 14px;
            transition: border-color 0.2s ease;
        }

        .form-input:focus, .form-select:focus, .form-textarea:focus {
            outline: none;
            border-color: #059669;
        }

        .input-group {
            position: relative;
            display: flex;
            align-items: center;
        }

        .input-group-text {
            background: #f1f5f9;
            border: 2px solid #e2e8f0;
            border-right: none;
            border-radius: 12px 0 0 12px;
            padding: 12px 16px;
            font-weight: 600;
            color: #059669;
        }

        .input-group .form-input {
            border-left: none;
            border-radius: 0 12px 12px 0;
        }

        .resume-card {
            background: #f8fafc;
            border: 2px solid #e2e8f0;
            border-radius: 16px;
            padding: 20px;
            margin-top: 20px;
        }

        .resume-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
        }

        .resume-item {
            text-align: center;
        }

        .resume-value {
            font-size: 18px;
            font-weight: 700;
            margin-bottom: 4px;
        }

        .resume-label {
            font-size: 12px;
            color: #64748b;
            font-weight: 500;
        }

        .modal-footer {
            padding: 20px;
            background: #f8fafc;
            border-top: 1px solid #e2e8f0;
            border-radius: 0 0 16px 16px;
            display: flex;
            gap: 12px;
        }

        .btn {
            padding: 12px 20px;
            border-radius: 12px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            border: none;
            display: flex;
            align-items: center;
            gap: 8px;
            text-decoration: none;
            text-align: center;
            justify-content: center;
            flex: 1;
        }

        .btn-primary {
            background: #059669;
            color: white;
        }

        .btn-primary:hover {
            background: #065f46;
        }

        .btn-secondary {
            background: #f1f5f9;
            color: #64748b;
        }

        .btn-secondary:hover {
            background: #e2e8f0;
        }

        @media (max-width: 600px) {
            .container {
                padding: 20px;
            }
            
            .test-links {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Teste Modal Proposta - Solicitações</h1>
        
        <div class="description">
            <p><strong>Objetivo:</strong> Testar o funcionamento do modal de proposta na página de solicitações mobile.</p>
        </div>

        <div class="test-section">
            <h2>🔗 Links das Páginas</h2>
            <div class="test-links">
                <a href="solicitacoes_mobile.php" class="test-link">
                    <span class="status-indicator status-ok"></span>
                    Solicitações Mobile
                </a>
                <a href="detalhes_solicitacao_new.php?id=95" class="test-link">
                    <span class="status-indicator status-ok"></span>
                    Detalhes Solicitação ID 95
                </a>
                <a href="ajax/enviar_proposta.php" class="test-link">
                    <span class="status-indicator status-ok"></span>
                    Endpoint AJAX
                </a>
            </div>
        </div>

        <div class="test-section">
            <h2>📱 Simulação de Solicitação</h2>
            <div class="request-card">
                <div class="request-header">
                    <div class="device-icon">📱</div>
                    <div class="device-info">
                        <h3>iPhone 14 Pro Max</h3>
                        <p>256GB • João Silva</p>
                    </div>
                </div>
                <p><strong>Problema:</strong> Tela trincada, display funcionando normalmente</p>
                <div class="request-actions">
                    <button class="action-btn-small action-btn-outline">
                        👁️ Ver Detalhes
                    </button>
                    <button onclick="abrirModalProposta(95)" class="action-btn-small action-btn-primary">
                        ✈️ Proposta
                    </button>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>🧪 Teste do Modal</h2>
            <div class="test-links">
                <button onclick="abrirModalProposta(95)" class="test-btn">
                    <span class="status-indicator status-ok"></span>
                    Abrir Modal de Proposta
                </button>
                <button onclick="testarFormulario()" class="test-btn">
                    <span class="status-indicator status-ok"></span>
                    Testar Validação
                </button>
                <button onclick="testarResumo()" class="test-btn">
                    <span class="status-indicator status-ok"></span>
                    Testar Resumo Dinâmico
                </button>
            </div>
        </div>
    </div>

    <!-- Modal para Proposta -->
    <div class="modal" id="propostaModal">
        <div class="modal-content">
            <div class="modal-header">
                <div class="modal-title">✈️ Enviar Proposta</div>
                <button class="modal-close" onclick="fecharPropostaModal()">&times;</button>
            </div>
            <div class="modal-body">
                <form id="formProposta">
                    <input type="hidden" name="solicitacao_id" id="solicitacao_id_input">
                    
                    <div class="form-group">
                        <label for="preco" class="form-label">💰 Preço do Reparo *</label>
                        <div class="input-group">
                            <span class="input-group-text">R$</span>
                            <input type="text" class="form-input" id="preco" name="preco" 
                                   placeholder="0,00" required>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="prazo" class="form-label">📅 Prazo (dias) *</label>
                        <select class="form-select" id="prazo" name="prazo" required>
                            <option value="">Selecione o prazo</option>
                            <option value="1">1 dia (Express)</option>
                            <option value="2">2 dias</option>
                            <option value="3">3 dias</option>
                            <option value="5">5 dias</option>
                            <option value="7">1 semana</option>
                            <option value="10">10 dias</option>
                            <option value="15">15 dias</option>
                            <option value="20">20 dias</option>
                            <option value="30">30 dias</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="observacoes" class="form-label">💬 Observações</label>
                        <textarea class="form-textarea" id="observacoes" name="observacoes" rows="3"
                                  placeholder="Descreva detalhes sobre o reparo, peças, garantia..."></textarea>
                    </div>
                    
                    <!-- Resumo -->
                    <div class="resume-card">
                        <div style="font-weight: 600; margin-bottom: 12px;">
                            🧮 Resumo da Proposta
                        </div>
                        <div class="resume-grid">
                            <div class="resume-item">
                                <div class="resume-value" style="color: #059669;" id="resumoPreco">R$ 0,00</div>
                                <div class="resume-label">Valor Total</div>
                            </div>
                            <div class="resume-item">
                                <div class="resume-value" style="color: #3b82f6;" id="resumoPrazo">-</div>
                                <div class="resume-label">Prazo</div>
                            </div>
                            <div class="resume-item">
                                <div class="resume-value" style="color: #dc2626;" id="resumoTaxa">R$ 0,00</div>
                                <div class="resume-label">Taxa (15%)</div>
                            </div>
                            <div class="resume-item">
                                <div class="resume-value" style="color: #059669;" id="resumoRecebera">R$ 0,00</div>
                                <div class="resume-label">Você Recebe</div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="fecharPropostaModal()">
                    ❌ Cancelar
                </button>
                <button type="button" class="btn btn-primary" onclick="enviarProposta()" id="btnEnviarProposta">
                    ✈️ Enviar Proposta
                </button>
            </div>
        </div>
    </div>

    <script>
        let currentSolicitacao = null;

        function abrirModalProposta(solicitacaoId) {
            currentSolicitacao = solicitacaoId;
            document.getElementById('solicitacao_id_input').value = solicitacaoId;
            document.getElementById('propostaModal').classList.add('show');
            
            // Limpar formulário
            document.getElementById('formProposta').reset();
            document.getElementById('solicitacao_id_input').value = solicitacaoId;
            atualizarResumo();
        }

        function fecharPropostaModal() {
            const modal = document.getElementById('propostaModal');
            modal.classList.remove('show');
            currentSolicitacao = null;
        }

        function formatarPreco(input) {
            let valor = input.value.replace(/\D/g, '');
            
            if (valor.length === 0) {
                input.value = '';
                atualizarResumo();
                return;
            }
            
            valor = valor.padStart(3, '0');
            valor = valor.slice(0, -2) + ',' + valor.slice(-2);
            valor = valor.replace(/\B(?=(\d{3})+(?!\d))/g, '.');
            
            input.value = valor;
            atualizarResumo();
        }

        function atualizarResumo() {
            const precoInput = document.getElementById('preco').value;
            const prazoSelect = document.getElementById('prazo');
            
            // Converter preço para número
            let preco = 0;
            if (precoInput) {
                preco = parseFloat(precoInput.replace(/\./g, '').replace(',', '.')) || 0;
            }
            
            // Calcular valores
            const taxa = preco * 0.15;
            const recebera = preco - taxa;
            
            // Atualizar resumo
            document.getElementById('resumoPreco').textContent = 'R$ ' + preco.toLocaleString('pt-BR', {minimumFractionDigits: 2});
            document.getElementById('resumoTaxa').textContent = 'R$ ' + taxa.toLocaleString('pt-BR', {minimumFractionDigits: 2});
            document.getElementById('resumoRecebera').textContent = 'R$ ' + recebera.toLocaleString('pt-BR', {minimumFractionDigits: 2});
            
            // Atualizar prazo
            const prazoTexto = prazoSelect.value ? prazoSelect.options[prazoSelect.selectedIndex].text : '-';
            document.getElementById('resumoPrazo').textContent = prazoTexto;
        }

        function enviarProposta() {
            const form = document.getElementById('formProposta');
            const btnEnviar = document.getElementById('btnEnviarProposta');
            
            // Validar campos obrigatórios
            const preco = document.getElementById('preco').value;
            const prazo = document.getElementById('prazo').value;
            
            if (!preco || !prazo) {
                alert('Por favor, preencha o preço e o prazo.');
                return;
            }
            
            // Desabilitar botão durante envio
            btnEnviar.disabled = true;
            btnEnviar.innerHTML = '⏳ Enviando...';
            
            // Simular envio
            setTimeout(() => {
                alert('✅ Proposta enviada com sucesso! (Simulação)');
                fecharPropostaModal();
                btnEnviar.disabled = false;
                btnEnviar.innerHTML = '✈️ Enviar Proposta';
            }, 2000);
        }

        function testarFormulario() {
            abrirModalProposta(95);
            setTimeout(() => {
                document.getElementById('preco').value = '450,00';
                document.getElementById('prazo').value = '3';
                formatarPreco(document.getElementById('preco'));
                atualizarResumo();
            }, 500);
        }

        function testarResumo() {
            abrirModalProposta(95);
            setTimeout(() => {
                const precoInput = document.getElementById('preco');
                precoInput.value = '1.200,00';
                formatarPreco(precoInput);
                
                document.getElementById('prazo').value = '7';
                atualizarResumo();
            }, 500);
        }

        // Event listeners
        document.addEventListener('DOMContentLoaded', function() {
            // Formatação de preço
            const precoInput = document.getElementById('preco');
            if (precoInput) {
                precoInput.addEventListener('input', function() {
                    formatarPreco(this);
                });
            }
            
            // Atualizar resumo quando prazo mudar
            const prazoSelect = document.getElementById('prazo');
            if (prazoSelect) {
                prazoSelect.addEventListener('change', atualizarResumo);
            }
            
            // Listener para fechar modal clicando fora
            document.getElementById('propostaModal').addEventListener('click', function(e) {
                if (e.target === this) {
                    fecharPropostaModal();
                }
            });
        });
    </script>
</body>
</html>
