<?php
/**
 * Página de Perfil
 * FixFácil Assistências - Sistema Novo
 */

require_once 'config/auth.php';
require_once 'config/database.php';
require_once 'includes/layout.php';

// Verificar autenticação
$auth = getAuth();
$auth->checkAssistenciaAuth();

// Obter dados do usuário
$usuario = $auth->getUsuarioLogado();
$plano = $auth->getPlanoInfo($usuario['id']);
$db = getDatabase();

// Processar formulário
$erro = '';
$sucesso = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $acao = $_POST['acao'] ?? '';
    
    if ($acao === 'dados_pessoais') {
        $nome = trim($_POST['nome'] ?? '');
        $email = trim($_POST['email'] ?? '');
        $telefone = trim($_POST['telefone'] ?? '');
        
        // Validações
        if (empty($nome)) {
            $erro = 'Nome é obrigatório.';
        } elseif (empty($email) || !filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $erro = 'E-mail válido é obrigatório.';
        } elseif (empty($telefone)) {
            $erro = 'Telefone é obrigatório.';
        } else {
            try {
                // Verificar se email já existe (exceto o próprio)
                $sql = "SELECT id FROM usuarios WHERE email = ? AND id != ?";
                $result = $db->query($sql, [$email, $usuario['id']]);
                
                if ($result->fetch_assoc()) {
                    $erro = 'Este e-mail já está sendo usado por outro usuário.';
                } else {
                    // Atualizar dados
                    $sql = "UPDATE usuarios SET nome = ?, email = ?, telefone = ? WHERE id = ?";
                    $db->query($sql, [$nome, $email, $telefone, $usuario['id']]);
                    
                    $sucesso = 'Dados pessoais atualizados com sucesso!';
                    
                    // Atualizar sessão
                    $_SESSION['nome'] = $nome;
                    
                    // Recarregar dados do usuário
                    $usuario = $auth->getUsuarioLogado();
                }
            } catch (Exception $e) {
                error_log("Erro ao atualizar dados pessoais: " . $e->getMessage());
                $erro = 'Erro interno. Tente novamente.';
            }
        }
    } elseif ($acao === 'dados_empresa') {
        $nome_empresa = trim($_POST['nome_empresa'] ?? '');
        $endereco = trim($_POST['endereco'] ?? '');
        $cep = trim($_POST['cep'] ?? '');
        $numero_endereco = trim($_POST['numero_endereco'] ?? '');
        $bairro = trim($_POST['bairro'] ?? '');
        $cidade = trim($_POST['cidade'] ?? '');
        $estado = trim($_POST['estado'] ?? '');
        $ponto_referencia = trim($_POST['ponto_referencia'] ?? '');
        $telefone_empresa = trim($_POST['telefone_empresa'] ?? '');
        $email_empresa = trim($_POST['email_empresa'] ?? '');
        $site = trim($_POST['site'] ?? '');

        // Validações
        if (empty($nome_empresa)) {
            $erro = 'Nome da empresa é obrigatório.';
        } elseif (empty($cep) || strlen(preg_replace('/\D/', '', $cep)) !== 8) {
            $erro = 'CEP válido é obrigatório.';
        } elseif (empty($numero_endereco)) {
            $erro = 'Número do endereço é obrigatório.';
        } elseif (empty($bairro)) {
            $erro = 'Bairro é obrigatório.';
        } elseif (empty($cidade)) {
            $erro = 'Cidade é obrigatória.';
        } elseif (empty($estado)) {
            $erro = 'Estado é obrigatório.';
        } elseif (!empty($email_empresa) && !filter_var($email_empresa, FILTER_VALIDATE_EMAIL)) {
            $erro = 'E-mail da empresa deve ser válido.';
        } else {
            try {
                // Limpar CEP
                $cep_limpo = preg_replace('/\D/', '', $cep);

                $sql = "
                    UPDATE assistencias_tecnicas
                    SET nome_empresa = ?, endereco = ?, cep = ?, numero_endereco = ?,
                        bairro = ?, cidade = ?, estado = ?, ponto_referencia = ?,
                        telefone = ?, email = ?, site = ?
                    WHERE usuario_id = ?
                ";
                $db->query($sql, [
                    $nome_empresa, $endereco, $cep_limpo, $numero_endereco,
                    $bairro, $cidade, $estado, $ponto_referencia,
                    $telefone_empresa, $email_empresa, $site, $usuario['id']
                ]);

                $sucesso = 'Dados da empresa atualizados com sucesso!';

                // Recarregar dados do usuário
                $usuario = $auth->getUsuarioLogado();

            } catch (Exception $e) {
                error_log("Erro ao atualizar dados da empresa: " . $e->getMessage());
                $erro = 'Erro interno. Tente novamente.';
            }
        }
    } elseif ($acao === 'alterar_senha') {
        $senha_atual = $_POST['senha_atual'] ?? '';
        $nova_senha = $_POST['nova_senha'] ?? '';
        $confirmar_senha = $_POST['confirmar_senha'] ?? '';
        
        // Validações
        if (empty($senha_atual)) {
            $erro = 'Senha atual é obrigatória.';
        } elseif (empty($nova_senha) || strlen($nova_senha) < 6) {
            $erro = 'Nova senha deve ter pelo menos 6 caracteres.';
        } elseif ($nova_senha !== $confirmar_senha) {
            $erro = 'Confirmação de senha não confere.';
        } else {
            try {
                // Verificar senha atual
                $sql = "SELECT senha FROM usuarios WHERE id = ?";
                $result = $db->query($sql, [$usuario['id']]);
                $user_data = $result->fetch_assoc();
                
                if (!password_verify($senha_atual, $user_data['senha'])) {
                    $erro = 'Senha atual incorreta.';
                } else {
                    // Atualizar senha
                    $nova_senha_hash = password_hash($nova_senha, PASSWORD_DEFAULT);
                    $sql = "UPDATE usuarios SET senha = ? WHERE id = ?";
                    $db->query($sql, [$nova_senha_hash, $usuario['id']]);
                    
                    $sucesso = 'Senha alterada com sucesso!';
                }
            } catch (Exception $e) {
                error_log("Erro ao alterar senha: " . $e->getMessage());
                $erro = 'Erro interno. Tente novamente.';
            }
        }
    }
}

// Inicializar layout
$layout = new Layout();
?>

<?php $layout->renderHead("Perfil - FixFácil Assistências"); ?>

<div class="main-wrapper">
    <?php $layout->renderSidebar('perfil'); ?>
    
    <main class="main-content">
        <!-- Header -->
        <div class="content-header">
            <h1 class="page-title">
                <i class="fas fa-user me-3"></i>
                Meu Perfil
            </h1>
            <p class="page-subtitle">
                Gerencie suas informações pessoais e da empresa
            </p>
        </div>
        
        <?php if ($erro): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <?php echo htmlspecialchars($erro); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>
        
        <?php if ($sucesso): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?php echo htmlspecialchars($sucesso); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>
        
        <div class="row g-4">
            <!-- Informações do Plano -->
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-crown me-2"></i>
                            Plano Atual
                        </h5>
                    </div>
                    <div class="card-body text-center">
                        <div class="plano-badge plano-<?php echo strtolower($plano['nome']); ?> mb-3">
                            <?php if ($plano['nome'] === 'Master'): ?>
                                <i class="fas fa-crown me-1"></i>
                            <?php elseif ($plano['nome'] === 'Premium'): ?>
                                <i class="fas fa-star me-1"></i>
                            <?php else: ?>
                                <i class="fas fa-user me-1"></i>
                            <?php endif; ?>
                            Plano <?php echo $plano['nome']; ?>
                        </div>
                        
                        <div class="mb-3">
                            <h4 class="text-primary">R$ <?php echo number_format($plano['preco_mensal'], 2, ',', '.'); ?></h4>
                            <small class="text-muted">por mês</small>
                        </div>
                        
                        <div class="mb-3">
                            <small class="text-muted d-block">Taxa de Serviço</small>
                            <strong class="fs-5"><?php echo number_format($plano['taxa_servico'], 1); ?>%</strong>
                        </div>
                        
                        <hr>
                        
                        <div class="text-start">
                            <h6 class="mb-2">Recursos Inclusos:</h6>
                            <ul class="list-unstyled">
                                <li class="mb-1">
                                    <i class="fas fa-check text-success me-2"></i>
                                    Solicitações e Propostas
                                </li>
                                <li class="mb-1">
                                    <i class="fas fa-check text-success me-2"></i>
                                    Gerenciamento de Reparos
                                </li>
                                <li class="mb-1">
                                    <i class="fas fa-check text-success me-2"></i>
                                    Carteira Digital
                                </li>
                                <?php if ($plano['acesso_chat']): ?>
                                <li class="mb-1">
                                    <i class="fas fa-check text-success me-2"></i>
                                    Chat com Clientes
                                </li>
                                <?php endif; ?>
                                <?php if ($plano['acesso_marketplace']): ?>
                                <li class="mb-1">
                                    <i class="fas fa-check text-success me-2"></i>
                                    Marketplace
                                </li>
                                <?php endif; ?>
                                <?php if ($plano['retirada_presencial']): ?>
                                <li class="mb-1">
                                    <i class="fas fa-check text-success me-2"></i>
                                    Retirada Presencial
                                </li>
                                <?php endif; ?>
                                <?php if ($plano['selo_fixfacil']): ?>
                                <li class="mb-1">
                                    <i class="fas fa-check text-success me-2"></i>
                                    Selo FixFácil
                                </li>
                                <?php endif; ?>
                                <?php if ($plano['link_personalizado']): ?>
                                <li class="mb-1">
                                    <i class="fas fa-check text-success me-2"></i>
                                    Link Personalizado
                                </li>
                                <?php endif; ?>
                                <?php if ($plano['retirada_express_prioritaria']): ?>
                                <li class="mb-1">
                                    <i class="fas fa-check text-success me-2"></i>
                                    Retirada Express
                                </li>
                                <?php endif; ?>
                            </ul>
                        </div>
                        
                        <div class="d-grid">
                            <a href="upgrade_plano.php" class="btn btn-outline-primary">
                                <i class="fas fa-arrow-up me-2"></i>
                                Upgrade de Plano
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Formulários -->
            <div class="col-lg-8">
                <!-- Dados Pessoais -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-user me-2"></i>
                            Dados Pessoais
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <input type="hidden" name="acao" value="dados_pessoais">
                            
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label for="nome" class="form-label">Nome Completo</label>
                                    <input type="text" class="form-control" id="nome" name="nome" 
                                           value="<?php echo htmlspecialchars($usuario['nome']); ?>" required>
                                </div>
                                
                                <div class="col-md-6">
                                    <label for="email" class="form-label">E-mail</label>
                                    <input type="email" class="form-control" id="email" name="email" 
                                           value="<?php echo htmlspecialchars($usuario['email']); ?>" required>
                                </div>
                                
                                <div class="col-md-6">
                                    <label for="telefone" class="form-label">Telefone</label>
                                    <input type="tel" class="form-control" id="telefone" name="telefone" 
                                           value="<?php echo htmlspecialchars($usuario['telefone']); ?>" required>
                                </div>
                                
                                <div class="col-12">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>
                                        Salvar Dados Pessoais
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
                
                <!-- Dados da Empresa -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-building me-2"></i>
                            Dados da Empresa
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <input type="hidden" name="acao" value="dados_empresa">
                            
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label for="nome_empresa" class="form-label">Nome da Empresa</label>
                                    <input type="text" class="form-control" id="nome_empresa" name="nome_empresa" 
                                           value="<?php echo htmlspecialchars($usuario['nome_empresa'] ?? ''); ?>" required>
                                </div>
                                
                                <div class="col-md-6">
                                    <label for="telefone_empresa" class="form-label">Telefone da Empresa</label>
                                    <input type="tel" class="form-control" id="telefone_empresa" name="telefone_empresa" 
                                           value="<?php echo htmlspecialchars($usuario['telefone_empresa'] ?? ''); ?>">
                                </div>
                                
                                <div class="col-md-3">
                                    <label for="cep" class="form-label">CEP</label>
                                    <input type="text" class="form-control" id="cep" name="cep"
                                           value="<?php echo htmlspecialchars($usuario['cep'] ?? ''); ?>"
                                           placeholder="00000-000" maxlength="9" required>
                                </div>

                                <div class="col-md-2">
                                    <label for="numero_endereco" class="form-label">Número</label>
                                    <input type="text" class="form-control" id="numero_endereco" name="numero_endereco"
                                           value="<?php echo htmlspecialchars($usuario['numero_endereco'] ?? ''); ?>"
                                           placeholder="123" required>
                                </div>

                                <div class="col-md-7">
                                    <label for="endereco" class="form-label">Logradouro</label>
                                    <input type="text" class="form-control" id="endereco" name="endereco"
                                           value="<?php echo htmlspecialchars($usuario['endereco'] ?? ''); ?>"
                                           placeholder="Será preenchido automaticamente" readonly required>
                                </div>

                                <div class="col-md-4">
                                    <label for="bairro" class="form-label">Bairro</label>
                                    <input type="text" class="form-control" id="bairro" name="bairro"
                                           value="<?php echo htmlspecialchars($usuario['bairro'] ?? ''); ?>"
                                           placeholder="Será preenchido automaticamente" readonly required>
                                </div>

                                <div class="col-md-6">
                                    <label for="cidade" class="form-label">Cidade</label>
                                    <input type="text" class="form-control" id="cidade" name="cidade"
                                           value="<?php echo htmlspecialchars($usuario['cidade'] ?? ''); ?>"
                                           placeholder="Será preenchido automaticamente" readonly required>
                                </div>

                                <div class="col-md-2">
                                    <label for="estado" class="form-label">Estado</label>
                                    <input type="text" class="form-control" id="estado" name="estado"
                                           value="<?php echo htmlspecialchars($usuario['estado'] ?? ''); ?>"
                                           placeholder="UF" readonly required>
                                </div>

                                <div class="col-12">
                                    <label for="ponto_referencia" class="form-label">Ponto de Referência</label>
                                    <input type="text" class="form-control" id="ponto_referencia" name="ponto_referencia"
                                           value="<?php echo htmlspecialchars($usuario['ponto_referencia'] ?? ''); ?>"
                                           placeholder="Ex: Próximo ao mercado">
                                </div>
                                
                                <div class="col-md-6">
                                    <label for="email_empresa" class="form-label">E-mail da Empresa</label>
                                    <input type="email" class="form-control" id="email_empresa" name="email_empresa" 
                                           value="<?php echo htmlspecialchars($usuario['email_empresa'] ?? ''); ?>">
                                </div>
                                
                                <div class="col-md-6">
                                    <label for="site" class="form-label">Site/Instagram</label>
                                    <input type="url" class="form-control" id="site" name="site" 
                                           value="<?php echo htmlspecialchars($usuario['site'] ?? ''); ?>"
                                           placeholder="https://...">
                                </div>
                                
                                <div class="col-12">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>
                                        Salvar Dados da Empresa
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
                
                <!-- Alterar Senha -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-lock me-2"></i>
                            Alterar Senha
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <input type="hidden" name="acao" value="alterar_senha">
                            
                            <div class="row g-3">
                                <div class="col-12">
                                    <label for="senha_atual" class="form-label">Senha Atual</label>
                                    <input type="password" class="form-control" id="senha_atual" name="senha_atual" required>
                                </div>
                                
                                <div class="col-md-6">
                                    <label for="nova_senha" class="form-label">Nova Senha</label>
                                    <input type="password" class="form-control" id="nova_senha" name="nova_senha" 
                                           minlength="6" required>
                                    <div class="form-text">Mínimo de 6 caracteres</div>
                                </div>
                                
                                <div class="col-md-6">
                                    <label for="confirmar_senha" class="form-label">Confirmar Nova Senha</label>
                                    <input type="password" class="form-control" id="confirmar_senha" name="confirmar_senha" 
                                           minlength="6" required>
                                </div>
                                
                                <div class="col-12">
                                    <button type="submit" class="btn btn-warning">
                                        <i class="fas fa-key me-2"></i>
                                        Alterar Senha
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </main>
</div>

<?php 
$extraJS = "
<script>
// Validação de confirmação de senha
document.getElementById('confirmar_senha').addEventListener('input', function() {
    const novaSenha = document.getElementById('nova_senha').value;
    const confirmarSenha = this.value;
    
    if (novaSenha !== confirmarSenha) {
        this.setCustomValidity('As senhas não conferem');
    } else {
        this.setCustomValidity('');
    }
});

// Máscara para telefone
function mascaraTelefone(input) {
    let valor = input.value.replace(/\D/g, '');
    valor = valor.replace(/^(\d{2})(\d)/g, '($1) $2');
    valor = valor.replace(/(\d)(\d{4})$/, '$1-$2');
    input.value = valor;
}

document.getElementById('telefone').addEventListener('input', function() {
    mascaraTelefone(this);
});

document.getElementById('telefone_empresa').addEventListener('input', function() {
    mascaraTelefone(this);
});

// Busca automática de CEP
document.getElementById('cep').addEventListener('blur', function() {
    const cep = this.value.replace(/\D/g, '');

    if (cep.length === 8) {
        // Buscar endereço
        fetch(`https://viacep.com.br/ws/${cep}/json/`)
            .then(response => response.json())
            .then(data => {
                if (data.erro) {
                    alert('CEP não encontrado');
                } else {
                    document.getElementById('endereco').value = data.logradouro || '';
                    document.getElementById('bairro').value = data.bairro || '';
                    document.getElementById('cidade').value = data.localidade || '';
                    document.getElementById('estado').value = data.uf || '';
                }
            })
            .catch(error => {
                console.error('Erro ao buscar CEP:', error);
            });
    }
});

// Máscara para CEP
document.getElementById('cep').addEventListener('input', function() {
    let valor = this.value.replace(/\D/g, '');
    valor = valor.replace(/(\d{5})(\d)/, '$1-$2');
    this.value = valor;
});
</script>
";

$layout->renderFooter($extraJS); 
?>
