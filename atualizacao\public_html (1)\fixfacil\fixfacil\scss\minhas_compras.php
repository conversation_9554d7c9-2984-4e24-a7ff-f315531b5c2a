<?php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\Exception;

require 'PHPMailer-master/src/Exception.php';
require 'PHPMailer-master/src/PHPMailer.php';
require 'PHPMailer-master/src/SMTP.php';

session_start();
require_once 'db.php';

try {
    // Verificar a sessão
    if (!isset($_SESSION['user_id']) || $_SESSION['nivel_acesso'] != 'lojista') {
        throw new Exception("Acesso negado");
    }

    // Logout
    if (isset($_GET['logout']) && $_GET['logout'] == 'true') {
        session_destroy();
        header("Location: login.php");
        exit();
    }

    // Definir o número de pedidos por página
    $pedidosPorPagina = 5;
    $paginaAtual = isset($_GET['page']) ? (int)$_GET['page'] : 1;
    $offset = ($paginaAtual - 1) * $pedidosPorPagina;

// Enviar mensagem para o fornecedor
    if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['mensagem'])) {
        $mensagem = $_POST['mensagem'];
        $fornecedor_id = $_POST['fornecedor_id'];
        $lojista_id = $_SESSION['user_id'];

        // Obter informações do cliente
        $queryCliente = "SELECT nome, telefone, email FROM usuarios WHERE id = :lojista_id LIMIT 1";
        $stmtCliente = $pdo->prepare($queryCliente);
        $stmtCliente->bindParam(':lojista_id', $lojista_id);
        $stmtCliente->execute();
        $cliente = $stmtCliente->fetch(PDO::FETCH_ASSOC);

        if ($cliente) {
            $nome_cliente = $cliente['nome'];
            $telefone_cliente = $cliente['telefone'];
            $email_cliente = $cliente['email'];

            $queryFornecedorEmail = "SELECT email FROM usuarios WHERE id = :fornecedor_id LIMIT 1";
            $stmtFornecedorEmail = $pdo->prepare($queryFornecedorEmail);
            $stmtFornecedorEmail->bindParam(':fornecedor_id', $fornecedor_id);
            $stmtFornecedorEmail->execute();
            $fornecedorEmail = $stmtFornecedorEmail->fetch(PDO::FETCH_ASSOC);

            if ($fornecedorEmail && isset($fornecedorEmail['email'])) {
                $fornecedor_email = $fornecedorEmail['email'];

                $mail = new PHPMailer(true);
                $mail->isSMTP();
                $mail->Host = 'email-ssl.com.br';
                $mail->SMTPAuth = true;
                $mail->Username = '<EMAIL>';
                $mail->Password = 'Sup@202323';
                $mail->SMTPSecure = 'ssl';
                $mail->Port = 465;

                $mail->setFrom('<EMAIL>', 'LOJISTA');
                $mail->addAddress($fornecedor_email);

                $mail->isHTML(true);
                $mail->Subject = 'Nova Mensagem de um Lojista';
                $mail->Body = "Nome do Cliente: {$nome_cliente}<br>Telefone: {$telefone_cliente}<br>Email: {$email_cliente}<br><br>Mensagem: {$mensagem}";

                if (!$mail->send()) {
                    throw new Exception("Erro ao enviar e-mail: " . $mail->ErrorInfo);
                } else {
                    echo "<script>alert('Mensagem enviada com sucesso!');</script>";
                }
            } else {
                throw new Exception("Fornecedor não encontrado");
            }
        } else {
            throw new Exception("Informações do cliente não encontradas");
        }
    }


    // Confirmar pedido
    if (isset($_GET['confirmar_compra'])) {
        $id = $_GET['confirmar_compra'];
        $lojista_id = $_SESSION['user_id'];

        // Verificar se o pedido existe
        $stmtCheck = $pdo->prepare("SELECT * FROM compras WHERE id = :id AND lojista_id = :lojista_id");
        $stmtCheck->bindParam(':id', $id, PDO::PARAM_INT);
        $stmtCheck->bindParam(':lojista_id', $lojista_id, PDO::PARAM_INT);
        $stmtCheck->execute();

        if ($stmtCheck->rowCount() > 0) {
            $stmt = $pdo->prepare("UPDATE compras SET status_pedido = 'confirmado' WHERE id = :id AND lojista_id = :lojista_id");
            $stmt->bindParam(':id', $id, PDO::PARAM_INT);
            $stmt->bindParam(':lojista_id', $lojista_id, PDO::PARAM_INT);
            $stmt->execute();
            
            echo "<script>alert('Pedido confirmado com sucesso!');</script>";
        } else {
            echo "<script>alert('Dados do pedido: ID: $id, Lojista ID: $lojista_id');</script>";
            echo "<script>alert('Pedido não encontrado ou já confirmado!');</script>";
        }
    }

    // Consulta para obter o número total de pedidos
    $lojista_id = $_SESSION['user_id'];
    $queryTotal = "SELECT COUNT(*) AS total FROM compras WHERE lojista_id = :lojista_id";
    $stmtTotal = $pdo->prepare($queryTotal);
    $stmtTotal->bindParam(':lojista_id', $lojista_id);
    $stmtTotal->execute();
    $totalPedidos = $stmtTotal->fetch(PDO::FETCH_ASSOC)['total'];

    // Consulta para obter os pedidos da página atual
    $queryPedidos = "SELECT compras.*, produtos.nome AS produto_nome, compras.valor_pedido AS produto_preco, usuarios.nome AS fornecedor_nome
              FROM compras
              JOIN produtos ON compras.produto_id = produtos.id
              JOIN usuarios ON compras.fornecedor_id = usuarios.id
              WHERE compras.lojista_id = :lojista_id
              ORDER BY compras.id DESC
              LIMIT :pedidosPorPagina OFFSET :offset";
    $stmtPedidos = $pdo->prepare($queryPedidos);
    $stmtPedidos->bindParam(':lojista_id', $lojista_id);
    $stmtPedidos->bindParam(':pedidosPorPagina', $pedidosPorPagina, PDO::PARAM_INT);
    $stmtPedidos->bindParam(':offset', $offset, PDO::PARAM_INT);
    $stmtPedidos->execute();
    $compras = $stmtPedidos->fetchAll(PDO::FETCH_ASSOC);

} catch (Exception $e) {
    echo "<script>alert('Erro: " . $e->getMessage() . "');</script>";
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <title>LOGISTA - Dashboard</title>
    <link href="vendor/fontawesome-free/css/all.min.css" rel="stylesheet" type="text/css">
    <link href="https://fonts.googleapis.com/css?family=Nunito:200,200i,300,300i,400,400i,600,600i,700,700i,800,800i,900,900i" rel="stylesheet">
    <link href="css/sb-admin-2.min.css" rel="stylesheet">
</head>

<body id="page-top">

    <!-- Page Wrapper -->
    <div id="wrapper">

        <!-- Sidebar -->
        <ul class="navbar-nav bg-gradient-primary sidebar sidebar-dark accordion" id="accordionSidebar">

            <!-- Sidebar - Brand -->
            <a class="sidebar-brand d-flex align-items-center justify-content-center" href="admin_dashboard.phpl">
                <div class="sidebar-brand-icon rotate-n-15">
                    <i class="fas fa-laugh-wink"></i>
                </div>
                <div class="sidebar-brand-text mx-3"> <sup>Avos Brasil</sup></div>
            </a>

            <!-- Divider -->
            <hr class="sidebar-divider my-0">

            <li class="nav-item active">
                <a class="nav-link" href="minhas_compras.php">
                    <i class="fas fa-fw fa-tachometer-alt"></i>
                    <span>Meus Pedidos</span></a>
            </li>
            <!-- Nav Item - Charts -->
            <li class="nav-item">
                <a class="nav-link" href="lojista_compra.php">
                    <i class="fas fa-fw fa-chart-area"></i>
                    <span>Nova Cotação</span></a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="lojista_favorito.php">
                    <i class="fas fa-fw fa-chart-area"></i>
                    <span>Favorito</span></a>
            </li>


            <!-- Divider -->
            <hr class="sidebar-divider d-none d-md-block">

            <!-- Sidebar Toggler (Sidebar) -->
            <div class="text-center d-none d-md-inline">
                <button class="rounded-circle border-0" id="sidebarToggle"></button>
            </div>

            <!-- Sidebar Message -->
            <div class="sidebar-card d-none d-lg-flex">
                <img class="sidebar-card-illustration mb-2" src="img/undraw_rocket.svg" alt="...">
                <p class="text-center mb-2"><strong>Avos Brasil Pro</strong>Controle LFM_Consultoria</p>
                
            </div>

        </ul>
        <!-- End of Sidebar -->

        <!-- Content Wrapper -->
        <div id="content-wrapper" class="d-flex flex-column">

            <!-- Main Content -->
            <div id="content">

                <!-- Topbar -->
                <nav class="navbar navbar-expand navbar-light bg-white topbar mb-4 static-top shadow">

                    <!-- Sidebar Toggle (Topbar) -->
                    <button id="sidebarToggleTop" class="btn btn-link d-md-none rounded-circle mr-3">
                        <i class="fa fa-bars"></i>
                    </button>

                    <!-- Topbar Search -->
                    <form
                        class="d-none d-sm-inline-block form-inline mr-auto ml-md-3 my-2 my-md-0 mw-100 navbar-search">
                        <div class="input-group">
                            <input type="text" class="form-control bg-light border-0 small" placeholder="Search for..."
                                aria-label="Search" aria-describedby="basic-addon2">
                            <div class="input-group-append">
                                <button class="btn btn-primary" type="button">
                                    <i class="fas fa-search fa-sm"></i>
                                </button>
                            </div>
                        </div>
                    </form>

                    <!-- Topbar Navbar -->
                    <ul class="navbar-nav ml-auto">

                        <!-- Nav Item - Search Dropdown (Visible Only XS) -->
                        <li class="nav-item dropdown no-arrow d-sm-none">
                            <a class="nav-link dropdown-toggle" href="#" id="searchDropdown" role="button"
                                data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                <i class="fas fa-search fa-fw"></i>
                            </a>
                            <!-- Dropdown - Messages -->
                            <div class="dropdown-menu dropdown-menu-right p-3 shadow animated--grow-in"
                                aria-labelledby="searchDropdown">
                                <form class="form-inline mr-auto w-100 navbar-search">
                                    <div class="input-group">
                                        <input type="text" class="form-control bg-light border-0 small"
                                            placeholder="Search for..." aria-label="Search"
                                            aria-describedby="basic-addon2">
                                        <div class="input-group-append">
                                            <button class="btn btn-primary" type="button">
                                                <i class="fas fa-search fa-sm"></i>
                                            </button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </li>

                        <!-- Nav Item - Alerts -->
                        <li class="nav-item dropdown no-arrow mx-1">
                            <a class="nav-link dropdown-toggle" href="#" id="alertsDropdown" role="button"
                                data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                <i class="fas fa-bell fa-fw"></i>
                                <!-- Counter - Alerts -->
                                <span class="badge badge-danger badge-counter">3+</span>
                            </a>
                            <!-- Dropdown - Alerts -->
                            
                        </li>

                        <!-- Nav Item - Messages -->
                        <li class="nav-item dropdown no-arrow mx-1">
                            <a class="nav-link dropdown-toggle" href="#" id="messagesDropdown" role="button"
                                data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                <i class="fas fa-envelope fa-fw"></i>
                                <!-- Counter - Messages -->
                                <span class="badge badge-danger badge-counter">7</span>
                            </a>
                            <!-- Dropdown - Messages -->
                            <div class="dropdown-list dropdown-menu dropdown-menu-right shadow animated--grow-in"
                                aria-labelledby="messagesDropdown">
                                <h6 class="dropdown-header">
                                    Message Center
                                </h6>
                                <a class="dropdown-item d-flex align-items-center" href="#">
                                    <div class="dropdown-list-image mr-3">
                                        <img class="rounded-circle" src="img/undraw_profile_1.svg"
                                            alt="...">
                                        <div class="status-indicator bg-success"></div>
                                    </div>
                                    <div class="font-weight-bold">
                                        <div class="text-truncate">Hi there! I am wondering if you can help me with a
                                            problem I've been having.</div>
                                        <div class="small text-gray-500">Emily Fowler · 58m</div>
                                    </div>
                                </a>
                                <a class="dropdown-item d-flex align-items-center" href="#">
                                    <div class="dropdown-list-image mr-3">
                                        <img class="rounded-circle" src="img/undraw_profile_2.svg"
                                            alt="...">
                                        <div class="status-indicator"></div>
                                    </div>
                                    <div>
                                        <div class="text-truncate">I have the photos that you ordered last month, how
                                            would you like them sent to you?</div>
                                        <div class="small text-gray-500">Jae Chun · 1d</div>
                                    </div>
                                </a>
                                <a class="dropdown-item d-flex align-items-center" href="#">
                                    <div class="dropdown-list-image mr-3">
                                        <img class="rounded-circle" src="img/undraw_profile_3.svg"
                                            alt="...">
                                        <div class="status-indicator bg-warning"></div>
                                    </div>
                                    <div>
                                        <div class="text-truncate">Last month's report looks great, I am very happy with
                                            the progress so far, keep up the good work!</div>
                                        <div class="small text-gray-500">Morgan Alvarez · 2d</div>
                                    </div>
                                </a>
                                <a class="dropdown-item d-flex align-items-center" href="#">
                                    <div class="dropdown-list-image mr-3">
                                        <img class="rounded-circle" src="https://source.unsplash.com/Mv9hjnEUHR4/60x60"
                                            alt="...">
                                        <div class="status-indicator bg-success"></div>
                                    </div>
                                    <div>
                                        <div class="text-truncate">Am I a good boy? The reason I ask is because someone
                                            told me that people say this to all dogs, even if they aren't good...</div>
                                        <div class="small text-gray-500">Chicken the Dog · 2w</div>
                                    </div>
                                </a>
                                <a class="dropdown-item text-center small text-gray-500" href="#">Read More Messages</a>
                            </div>
                        </li>

                        <div class="topbar-divider d-none d-sm-block"></div>

                        <!-- Nav Item - User Information -->
                        <li class="nav-item dropdown no-arrow">
                            <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button"
                                data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                <span class="mr-2 d-none d-lg-inline text-gray-600 small">LOJISTA</span>
                                <img class="img-profile rounded-circle"
                                    src="img/undraw_profile.svg">
                            </a>
                            <!-- Dropdown - User Information -->
                            <div class="dropdown-menu dropdown-menu-right shadow animated--grow-in"
                                aria-labelledby="userDropdown">
                                <a class="dropdown-item" href="#">
                                    <i class="fas fa-user fa-sm fa-fw mr-2 text-gray-400"></i>
                                    Profile
                                </a>
                                <a class="dropdown-item" href="#">
                                    <i class="fas fa-cogs fa-sm fa-fw mr-2 text-gray-400"></i>
                                    Settings
                                </a>
                                <a class="dropdown-item" href="#">
                                    <i class="fas fa-list fa-sm fa-fw mr-2 text-gray-400"></i>
                                    Activity Log
                                </a>
                                <div class="dropdown-divider"></div>
                                <a class="dropdown-item" href="#" data-toggle="modal" data-target="#logoutModal">
                                    <i class="fas fa-sign-out-alt fa-sm fa-fw mr-2 text-gray-400"></i>
                                    Logout
                                </a>
                            </div>
                        </li>

                    </ul>

                </nav>
                <!-- End of Topbar -->

                <!-- Begin Page Content -->
                <div class="container-fluid">
                    <!-- colocar estrutura -->
                    
<!-- Adicionando o slideshow -->
<div id="carouselExampleSlidesOnly" class="carousel slide" data-ride="carousel">
    <div class="carousel-inner">
        <!-- Adicione aqui seus slides -->
        <div class="carousel-item active">
            <img src="https://www.friboi.com.br/_next/image/?url=https%3A%2F%2Ffuture-brand-frib.s3.amazonaws.com%2F1953_friboi_simplismente_excepcional_hero_desktop_0bbcd07507.png&w=1440&q=85" class="d-block w-100" alt="Slide 1">
        </div>
        <div class="carousel-item">
            <img src="https://www.friboi.com.br/_next/image/?url=https%3A%2F%2Ffuture-brand-frib.s3.amazonaws.com%2F1953_friboi_simplismente_excepcional_hero_desktop_0bbcd07507.png&w=1440&q=85" class="d-block w-100" alt="Slide 2">
        </div>
        <!-- Adicione mais slides conforme necessário -->
    </div>
</div>

            <!-- End of Topbar -->
            <div class="container mt-5">
                <div class="jumbotron text-center">
                    <h1 class="display-4">Minhas Compras</h1>
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Produto</th>
                                    <th>Fornecedor</th>
                                    <th>Quantidade</th>
                                    <th>Preço Unitário</th>
                                    <th>Total</th>
                                    <th>Status</th>
                                    <th>Ações</th>
                                    <th>Enviar Mensagem</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($compras as $compra): ?>
                                    <tr>
                                        <td><?php echo $compra['produto_nome'] ?? 'N/A'; ?></td>
                                        <td><?php echo $compra['fornecedor_nome'] ?? 'N/A'; ?></td>
                                        <td><?php echo $compra['quantidade'] ?? 'N/A'; ?></td>
                                        <td><?php echo isset($compra['produto_preco']) ? 'R$ ' . number_format($compra['produto_preco'], 2, ',', '.') : 'N/A'; ?></td>
                                        <td><?php echo isset($compra['produto_preco']) && isset($compra['quantidade']) ? 'R$ ' . number_format($compra['produto_preco'] * $compra['quantidade'], 2, ',', '.') : 'N/A'; ?></td>
                                        <td><?php echo $compra['status_pedido'] ?? 'N/A'; ?></td>
                                        <td>
                                            <?php if ($compra['status_pedido'] == 'pendente'): ?>
                                                <a href="?confirmar_compra=<?php echo $compra['id']; ?>" class="btn btn-sm btn-success" onclick="return confirmarPedido()">Confirmar</a>
                                                <form method="post" action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>" class="d-inline">
                                                    <input type="hidden" name="compra_id" value="<?php echo $compra['id']; ?>">
                                                    <button type="submit" class="btn btn-sm btn-danger" name="excluir_pedido">Excluir</button>
                                                </form>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($compra['status_pedido'] == 'pendente' || $compra['status_pedido'] == 'confirmado'): ?>
                                                <button class="btn btn-sm btn-info" data-toggle="modal" data-target="#enviarMensagemModal_<?php echo $compra['id']; ?>">Enviar Mensagem</button>
                                            <?php endif; ?>
                                        </td>
                                        <!-- Modal para enviar mensagem -->
                                        <div class="modal fade" id="enviarMensagemModal_<?php echo $compra['id']; ?>" tabindex="-1" role="dialog" aria-labelledby="enviarMensagemModalLabel_<?php echo $compra['id']; ?>" aria-hidden="true">
                                            <div class="modal-dialog" role="document">
                                                <div class="modal-content">
                                                    <div class="modal-header">
                                                        <h5 class="modal-title" id="enviarMensagemModalLabel_<?php echo $compra['id']; ?>">Enviar Mensagem</h5>
                                                        <button type="button" class="close" data-dismiss="modal" aria-label="Fechar">
                                                            <span aria-hidden="true">&times;</span>
                                                        </button>
                                                    </div>
                                                    <div class="modal-body">
                                                        <!-- Formulário para enviar mensagem -->
                                                        <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>" method="post">
                                                            <input type="hidden" name="fornecedor_id" value="<?php echo $compra['fornecedor_id']; ?>">
                                                            <div class="form-group">
                                                                <label for="mensagem">Mensagem:</label>
                                                                <textarea class="form-control" id="mensagem" name="mensagem" rows="3" required></textarea>
                                                            </div>
                                                            <button type="submit" class="btn btn-primary">Enviar</button>
                                                        </form>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
<!-- Script de Inicialização do Tawk.to -->
    <script type="text/javascript">
        var Tawk_API = Tawk_API || {}, Tawk_LoadStart = new Date();
        (function () {
            var s1 = document.createElement("script"), s0 = document.getElementsByTagName("script")[0];
            s1.async = true;
            s1.src = 'https://embed.tawk.to/66269f961ec1082f04e59b7a/1hs3dupor';
            s1.charset = 'UTF-8';
            s1.setAttribute('crossorigin', '*');
            s0.parentNode.insertBefore(s1, s0);
        })();
    </script>
    <script src="vendor/jquery/jquery.min.js"></script>
    <script src="vendor/bootstrap/js/bootstrap.bundle.min.js"></script>
    <script src="vendor/jquery-easing/jquery.easing.min.js"></script>
    <script src="js/sb-admin-2.min.js"></script>
    <script>
        function confirmarPedido() {
            return confirm("Tem certeza que deseja confirmar este pedido?");
        }
    </script>
</body>
</html>
