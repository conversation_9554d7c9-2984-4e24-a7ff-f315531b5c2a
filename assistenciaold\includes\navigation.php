<?php
// Configuração de navegação para assistências
// Define quais páginas usar (novas ou antigas)

$navigation_config = [
    'use_new_design' => true, // Altere para false para usar design antigo
    
    'pages' => [
        'dashboard' => 'dashboard_new.php',
        'solicitacoes' => 'solicitacoes_new.php', 
        'propostas' => 'propostas_new.php',
        'reparos' => 'reparos_em_andamento.php', // Ainda não modernizado
        'marketplace' => 'meumarktplace.php', // Ainda não modernizado
        'carteira' => 'carteira.php', // Ainda não modernizado
        'perfil' => 'perfil.php', // Ainda não modernizado
        'pecas' => 'solicitar_pecas.php', // Ainda não modernizado
        'link_fix' => 'link_fix.php',
        'selo_fixfacil' => 'selo_fixfacil.php',
        'retirada_presencial' => 'retirada_presencial.php',
        'upgrade_plano' => 'upgrade_plano.php'
    ]
];

// Função para obter URL da página
function getPageUrl($page_key) {
    global $navigation_config;
    
    if (isset($navigation_config['pages'][$page_key])) {
        return $navigation_config['pages'][$page_key];
    }
    
    // Fallback para páginas não configuradas
    return $page_key . '.php';
}

// Função para verificar se está usando novo design
function isUsingNewDesign() {
    global $navigation_config;
    return $navigation_config['use_new_design'] ?? false;
}

// Menu items para mobile
function getMobileMenuItems($usuario_id, $taxaCalculator) {
    $items = [
        [
            'url' => getPageUrl('dashboard'),
            'icon' => 'fas fa-home',
            'label' => 'Início',
            'active' => true
        ],
        [
            'url' => getPageUrl('solicitacoes'),
            'icon' => 'fas fa-inbox', 
            'label' => 'Solicitações',
            'active' => false
        ],
        [
            'url' => getPageUrl('propostas'),
            'icon' => 'fas fa-paper-plane',
            'label' => 'Propostas', 
            'active' => false
        ]
    ];
    
    // Adicionar marketplace ou carteira baseado no plano
    if ($taxaCalculator->temAcesso($usuario_id, 'acesso_marketplace')) {
        $items[] = [
            'url' => getPageUrl('marketplace'),
            'icon' => 'fas fa-store',
            'label' => 'Marketplace',
            'active' => false
        ];
    } else {
        $items[] = [
            'url' => getPageUrl('carteira'),
            'icon' => 'fas fa-wallet', 
            'label' => 'Carteira',
            'active' => false
        ];
    }
    
    $items[] = [
        'url' => getPageUrl('perfil'),
        'icon' => 'fas fa-user',
        'label' => 'Perfil',
        'active' => false
    ];
    
    return $items;
}

// Header actions para desktop
function getHeaderActions($usuario_id, $taxaCalculator, $plano_info) {
    $actions = [];
    
    // Notificações
    $actions[] = [
        'type' => 'button',
        'icon' => 'fas fa-bell',
        'title' => 'Notificações',
        'class' => 'notification-btn'
    ];
    
    // Menu do usuário
    $actions[] = [
        'type' => 'dropdown',
        'items' => [
            [
                'url' => getPageUrl('perfil'),
                'icon' => 'fas fa-user',
                'label' => 'Meu Perfil'
            ],
            [
                'url' => getPageUrl('upgrade_plano'),
                'icon' => 'fas fa-crown', 
                'label' => 'Upgrade de Plano',
                'show' => $plano_info['nome'] !== 'Master'
            ],
            [
                'type' => 'divider'
            ],
            [
                'url' => '../logout.php',
                'icon' => 'fas fa-sign-out-alt',
                'label' => 'Sair'
            ]
        ]
    ];
    
    return $actions;
}

// Quick actions para dashboard
function getQuickActions($usuario_id, $taxaCalculator, $tem_link_ativo = false) {
    $actions = [
        [
            'url' => getPageUrl('solicitacoes'),
            'icon' => 'fas fa-inbox',
            'title' => 'Nova Solicitação',
            'description' => 'Verificar novas solicitações de reparo'
        ],
        [
            'url' => getPageUrl('propostas'),
            'icon' => 'fas fa-paper-plane',
            'title' => 'Enviar Proposta', 
            'description' => 'Criar e enviar propostas de orçamento'
        ]
    ];
    
    // Marketplace (Premium+)
    if ($taxaCalculator->temAcesso($usuario_id, 'acesso_marketplace')) {
        $actions[] = [
            'url' => getPageUrl('marketplace'),
            'icon' => 'fas fa-store',
            'title' => 'Marketplace',
            'description' => 'Gerenciar produtos e vendas'
        ];
    }
    
    // Link Fix (Master)
    if ($taxaCalculator->temAcesso($usuario_id, 'link_personalizado')) {
        $actions[] = [
            'url' => getPageUrl('link_fix'),
            'icon' => 'fas fa-link',
            'title' => 'Link Fix',
            'description' => ($tem_link_ativo ? 'Gerenciar' : 'Criar') . ' assistência virtual'
        ];
    }
    
    // Selo FixFácil (Master)
    if ($taxaCalculator->temAcesso($usuario_id, 'selo_fixfacil')) {
        $actions[] = [
            'url' => getPageUrl('selo_fixfacil'),
            'icon' => 'fas fa-certificate',
            'title' => 'Selo FixFácil',
            'description' => 'Visualizar certificação de qualidade'
        ];
    }
    
    // Retirada Presencial (Master)
    if ($taxaCalculator->temAcesso($usuario_id, 'retirada_presencial')) {
        $actions[] = [
            'url' => getPageUrl('retirada_presencial'),
            'icon' => 'fas fa-store-alt',
            'title' => 'Retirada Presencial',
            'description' => 'Gerenciar agendamentos presenciais'
        ];
    }
    
    // Ações básicas
    $actions = array_merge($actions, [
        [
            'url' => getPageUrl('carteira'),
            'icon' => 'fas fa-wallet',
            'title' => 'Carteira',
            'description' => 'Visualizar ganhos e transações'
        ],
        [
            'url' => getPageUrl('perfil'),
            'icon' => 'fas fa-user-cog',
            'title' => 'Atualizar Perfil',
            'description' => 'Manter informações atualizadas'
        ],
        [
            'url' => getPageUrl('pecas'),
            'icon' => 'fas fa-cogs',
            'title' => 'Solicitar Peças',
            'description' => 'Pedir peças para reparos'
        ]
    ]);
    
    return $actions;
}

// Função para renderizar mobile menu
function renderMobileMenu($current_page, $usuario_id, $taxaCalculator) {
    $items = getMobileMenuItems($usuario_id, $taxaCalculator);
    
    echo '<nav class="mobile-menu d-lg-none">';
    echo '<div class="mobile-menu-items">';
    
    foreach ($items as $item) {
        $active_class = (basename($_SERVER['PHP_SELF']) === basename($item['url'])) ? ' active' : '';
        echo '<a href="' . $item['url'] . '" class="mobile-menu-item' . $active_class . '">';
        echo '<i class="' . $item['icon'] . '"></i>';
        echo '<span>' . $item['label'] . '</span>';
        echo '</a>';
    }
    
    echo '</div>';
    echo '</nav>';
}

// CSS para novo design
function getNewDesignCSS() {
    return '
    <style>
        :root {
            --primary: #667eea;
            --primary-dark: #5a67d8;
            --secondary: #718096;
            --success: #48bb78;
            --warning: #ed8936;
            --danger: #f56565;
            --info: #4299e1;
            --light: #f7fafc;
            --dark: #2d3748;
            --white: #ffffff;
            --gray-50: #f9fafb;
            --gray-100: #f3f4f6;
            --gray-200: #e5e7eb;
            --gray-300: #d1d5db;
            --gray-400: #9ca3af;
            --gray-500: #6b7280;
            --gray-600: #4b5563;
            --gray-700: #374151;
            --gray-800: #1f2937;
            --gray-900: #111827;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: "Inter", sans-serif;
            background: linear-gradient(135deg, var(--gray-50) 0%, var(--gray-100) 100%);
            color: var(--gray-800);
            line-height: 1.6;
        }

        /* Mobile Menu */
        .mobile-menu {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: var(--white);
            box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
            padding: 0.75rem 0;
            z-index: 1000;
        }

        .mobile-menu-items {
            display: flex;
            justify-content: space-around;
            align-items: center;
        }

        .mobile-menu-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-decoration: none;
            color: var(--gray-600);
            font-size: 0.75rem;
            padding: 0.5rem;
            border-radius: 0.5rem;
            transition: all 0.2s;
        }

        .mobile-menu-item.active {
            color: var(--primary);
        }

        .mobile-menu-item i {
            font-size: 1.25rem;
            margin-bottom: 0.25rem;
        }

        @media (max-width: 768px) {
            body {
                margin-bottom: 5rem;
            }
        }
    </style>';
}
?>
