<?php
/**
 * Verificar Sistema de Planos
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>📋 Verificação do Sistema de Planos</h1>";
echo "<style>body { font-family: Arial, sans-serif; margin: 20px; } table { border-collapse: collapse; width: 100%; } th, td { border: 1px solid #ddd; padding: 8px; text-align: left; } th { background-color: #f2f2f2; }</style>";

try {
    require_once 'config/database.php';
    require_once 'config/auth.php';
    
    $db = getDatabase();
    $auth = getAuth();
    
    echo "<p>✅ Conexão estabelecida</p>";
    
    // Verificar tabelas
    echo "<h3>🗄️ Verificação de Tabelas:</h3>";
    
    $tabelas = ['planos', 'assinaturas_assistencias', 'assistencias_tecnicas', 'usuarios'];
    foreach ($tabelas as $tabela) {
        try {
            $result = $db->query("SELECT COUNT(*) as total FROM $tabela");
            $row = $result->fetch_assoc();
            echo "<p>✅ Tabela '$tabela': {$row['total']} registros</p>";
        } catch (Exception $e) {
            echo "<p>❌ Tabela '$tabela': " . $e->getMessage() . "</p>";
        }
    }
    
    // Verificar estrutura da tabela planos
    echo "<h3>📋 Estrutura da Tabela Planos:</h3>";
    try {
        $result = $db->query("DESCRIBE planos");
        echo "<table>";
        echo "<tr><th>Campo</th><th>Tipo</th><th>Null</th><th>Key</th><th>Default</th></tr>";
        while ($row = $result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>{$row['Field']}</td>";
            echo "<td>{$row['Type']}</td>";
            echo "<td>{$row['Null']}</td>";
            echo "<td>{$row['Key']}</td>";
            echo "<td>{$row['Default']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } catch (Exception $e) {
        echo "<p>❌ Erro ao verificar estrutura: " . $e->getMessage() . "</p>";
    }
    
    // Verificar planos existentes
    echo "<h3>📊 Planos Cadastrados:</h3>";
    try {
        $result = $db->query("SELECT * FROM planos ORDER BY id");
        if ($result->num_rows > 0) {
            echo "<table>";
            echo "<tr><th>ID</th><th>Nome</th><th>Preço</th><th>Taxa</th><th>Chat</th><th>Marketplace</th><th>Virtual</th></tr>";
            while ($row = $result->fetch_assoc()) {
                echo "<tr>";
                echo "<td>{$row['id']}</td>";
                echo "<td>{$row['nome']}</td>";
                echo "<td>R$ " . number_format($row['preco'], 2, ',', '.') . "</td>";
                echo "<td>{$row['taxa_servico']}%</td>";
                echo "<td>" . ($row['acesso_chat'] ? 'SIM' : 'NÃO') . "</td>";
                echo "<td>" . ($row['acesso_marketplace'] ? 'SIM' : 'NÃO') . "</td>";
                echo "<td>" . (isset($row['acesso_assistencia_virtual']) ? ($row['acesso_assistencia_virtual'] ? 'SIM' : 'NÃO') : 'N/A') . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p>⚠️ Nenhum plano cadastrado</p>";
            
            // Criar planos padrão
            if (isset($_GET['criar_planos']) && $_GET['criar_planos'] === 'sim') {
                echo "<h4>🛠️ Criando planos padrão...</h4>";
                
                $planos = [
                    [
                        'nome' => 'Free',
                        'preco' => 0.00,
                        'taxa_servico' => 25.00,
                        'acesso_chat' => 0,
                        'acesso_marketplace' => 0,
                        'acesso_assistencia_virtual' => 0
                    ],
                    [
                        'nome' => 'Premium',
                        'preco' => 89.90,
                        'taxa_servico' => 20.00,
                        'acesso_chat' => 1,
                        'acesso_marketplace' => 1,
                        'acesso_assistencia_virtual' => 0
                    ],
                    [
                        'nome' => 'Master',
                        'preco' => 159.90,
                        'taxa_servico' => 10.00,
                        'acesso_chat' => 1,
                        'acesso_marketplace' => 1,
                        'acesso_assistencia_virtual' => 1
                    ]
                ];
                
                foreach ($planos as $plano) {
                    try {
                        $sql = "
                            INSERT INTO planos 
                            (nome, preco, taxa_servico, acesso_chat, acesso_marketplace, acesso_assistencia_virtual)
                            VALUES (?, ?, ?, ?, ?, ?)
                        ";
                        $db->query($sql, [
                            $plano['nome'],
                            $plano['preco'],
                            $plano['taxa_servico'],
                            $plano['acesso_chat'],
                            $plano['acesso_marketplace'],
                            $plano['acesso_assistencia_virtual']
                        ]);
                        echo "<p>✅ Plano {$plano['nome']} criado</p>";
                    } catch (Exception $e) {
                        echo "<p>❌ Erro ao criar plano {$plano['nome']}: " . $e->getMessage() . "</p>";
                    }
                }
                
                echo "<p><a href='?'>Recarregar página</a></p>";
            } else {
                echo "<p><a href='?criar_planos=sim' style='background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🛠️ Criar Planos Padrão</a></p>";
            }
        }
    } catch (Exception $e) {
        echo "<p>❌ Erro ao verificar planos: " . $e->getMessage() . "</p>";
    }
    
    // Verificar assinaturas
    echo "<h3>📝 Assinaturas das Assistências:</h3>";
    try {
        $sql = "
            SELECT 
                aa.id,
                at.nome_empresa,
                p.nome as plano_nome,
                aa.status,
                aa.data_inicio,
                aa.data_fim
            FROM assinaturas_assistencias aa
            JOIN assistencias_tecnicas at ON aa.assistencia_id = at.id
            JOIN planos p ON aa.plano_id = p.id
            ORDER BY aa.id DESC
            LIMIT 10
        ";
        
        $result = $db->query($sql);
        if ($result->num_rows > 0) {
            echo "<table>";
            echo "<tr><th>ID</th><th>Empresa</th><th>Plano</th><th>Status</th><th>Início</th><th>Fim</th></tr>";
            while ($row = $result->fetch_assoc()) {
                echo "<tr>";
                echo "<td>{$row['id']}</td>";
                echo "<td>" . htmlspecialchars($row['nome_empresa']) . "</td>";
                echo "<td>{$row['plano_nome']}</td>";
                echo "<td>{$row['status']}</td>";
                echo "<td>" . date('d/m/Y', strtotime($row['data_inicio'])) . "</td>";
                echo "<td>" . ($row['data_fim'] ? date('d/m/Y', strtotime($row['data_fim'])) : 'Indefinido') . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p>⚠️ Nenhuma assinatura encontrada</p>";
        }
    } catch (Exception $e) {
        echo "<p>❌ Erro ao verificar assinaturas: " . $e->getMessage() . "</p>";
    }
    
    // Teste com usuário logado
    if (isset($_SESSION['usuario_id'])) {
        echo "<h3>👤 Teste com Usuário Logado:</h3>";
        echo "<p>Usuário ID: {$_SESSION['usuario_id']}</p>";
        echo "<p>Tipo: {$_SESSION['tipo_usuario']}</p>";
        
        $usuario = $auth->getUsuarioLogado();
        if ($usuario) {
            echo "<p>Nome: " . htmlspecialchars($usuario['nome']) . "</p>";
            echo "<p>Assistência ID: " . ($usuario['assistencia_id'] ?? 'Não definido') . "</p>";
            
            // Testar plano
            $plano = $auth->getPlanoInfo($usuario['id']);
            if ($plano) {
                echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
                echo "<h4>✅ Plano Identificado:</h4>";
                echo "<p><strong>Nome:</strong> {$plano['nome']}</p>";
                echo "<p><strong>Preço:</strong> R$ " . number_format($plano['preco'], 2, ',', '.') . "</p>";
                echo "<p><strong>Taxa:</strong> {$plano['taxa_servico']}%</p>";
                echo "<p><strong>Chat:</strong> " . ($plano['acesso_chat'] ? 'SIM' : 'NÃO') . "</p>";
                echo "<p><strong>Marketplace:</strong> " . ($plano['acesso_marketplace'] ? 'SIM' : 'NÃO') . "</p>";
                echo "<p><strong>Assistência Virtual:</strong> " . (isset($plano['acesso_assistencia_virtual']) ? ($plano['acesso_assistencia_virtual'] ? 'SIM' : 'NÃO') : 'N/A') . "</p>";
                echo "</div>";
                
                // Testar acessos
                echo "<h4>🔐 Teste de Acessos:</h4>";
                $funcionalidades = ['chat', 'marketplace', 'assistencia_virtual'];
                foreach ($funcionalidades as $func) {
                    $tem_acesso = $auth->hasAccess($func);
                    $status = $tem_acesso ? '✅ TEM ACESSO' : '❌ SEM ACESSO';
                    echo "<p><strong>$func:</strong> $status</p>";
                }
                
            } else {
                echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
                echo "<h4>❌ Plano NÃO Identificado</h4>";
                echo "<p>Usuário não tem plano ativo ou erro na consulta</p>";
                echo "</div>";
            }
        }
    } else {
        echo "<h3>⚠️ Usuário não está logado</h3>";
        echo "<p><a href='../login.php'>Fazer Login</a></p>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ Erro geral: " . $e->getMessage() . "</p>";
}

?>

<hr>

<h3>🔗 Links Úteis:</h3>
<ul>
    <li><a href="dashboard.php">Dashboard</a></li>
    <li><a href="upgrade_plano.php">Upgrade de Plano</a></li>
    <li><a href="../login.php">Login</a></li>
    <li><a href="assistencia_virtual.php">Assistência Virtual (Master)</a></li>
    <li><a href="marketplace.php">Marketplace (Premium/Master)</a></li>
</ul>

<p><small>Verificação executada em <?php echo date('d/m/Y H:i:s'); ?></small></p>
