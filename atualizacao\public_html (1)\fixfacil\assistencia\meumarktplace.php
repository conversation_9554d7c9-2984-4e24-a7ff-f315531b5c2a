<?php
session_start();

// Verificar se o usuário está logado e é uma assistência
if (!isset($_SESSION['usuario_id']) || $_SESSION['tipo_usuario'] != 'assistencia') {
    header('Location: ../login.php');
    exit();
}

$usuario_id = $_SESSION['usuario_id'];
$nome_assistencia = $_SESSION['nome']; // Nome da assistência

// Conexão com o banco de dados
$servername = "localhost";
$username_db = "u682219090_fixfacilnew";
$password_db = "T3cn0l0g1a@";
$dbname = "u682219090_fixfacilnew";

// Ativar exceções para erros do MySQLi
mysqli_report(MYSQLI_REPORT_ERROR | MYSQLI_REPORT_STRICT);

try {
    $conn = new mysqli($servername, $username_db, $password_db, $dbname);
    $conn->set_charset("utf8mb4");
} catch (mysqli_sql_exception $e) {
    die("Falha na conexão: " . $e->getMessage());
}

// Inicializar variáveis para mensagens
$mensagem = "";
$tipo_alerta = "";

// Verificar se a assistência técnica existe
try {
    $stmt_assistencia = $conn->prepare("SELECT id FROM assistencias_tecnicas WHERE usuario_id = ?");
    $stmt_assistencia->bind_param("i", $usuario_id);
    $stmt_assistencia->execute();
    $stmt_assistencia->bind_result($assistencia_id);
    if (!$stmt_assistencia->fetch()) {
        $stmt_assistencia->close();
        die("Assistência técnica não encontrada para o usuário logado.");
    }
    $stmt_assistencia->close();
} catch (mysqli_sql_exception $e) {
    die("Erro ao buscar assistência: " . $e->getMessage());
}

// Processamento do formulário de adicionar/editar/excluir produto
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    try {
        if (isset($_POST['adicionar_produto'])) {
            // Adicionar novo produto
            $nome_produto = trim($_POST['nome_produto']);
            $descricao = trim($_POST['descricao']);
            $preco = floatval($_POST['preco']);
            $estoque = intval($_POST['estoque']);
            $categoria = trim($_POST['categoria']);

            // Gerenciar upload de imagem
            if (isset($_FILES['imagem']) && $_FILES['imagem']['error'] == UPLOAD_ERR_OK) {
                $upload_dir = '../uploads/produtos/';
                if (!is_dir($upload_dir)) {
                    mkdir($upload_dir, 0777, true);
                }

                // Gerar um nome único para a imagem para evitar conflitos
                $nome_imagem = uniqid() . "_" . basename($_FILES['imagem']['name']);
                $imagem_path = $upload_dir . $nome_imagem;
                $imageFileType = strtolower(pathinfo($imagem_path, PATHINFO_EXTENSION));

                // Validar o tipo de arquivo
                $check = getimagesize($_FILES['imagem']['tmp_name']);
                if ($check !== false) {
                    // Permitir apenas certos formatos
                    if (in_array($imageFileType, ['jpg', 'jpeg', 'png', 'gif'])) {
                        move_uploaded_file($_FILES['imagem']['tmp_name'], $imagem_path);
                    } else {
                        throw new Exception("Formato de imagem inválido. Permissões: JPG, JPEG, PNG, GIF.");
                    }
                } else {
                    throw new Exception("O arquivo enviado não é uma imagem.");
                }
            } else {
                $nome_imagem = null; // Imagem não obrigatória
            }

            // Inserir no banco de dados
            $sql_inserir = "INSERT INTO produtos (usuario_id, nome_produto, descricao, imagem, preco, estoque, categoria, quantidade_disponivel)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
            $quantidade_disponivel = $estoque; // Inicialmente igual ao estoque
            $stmt_inserir = $conn->prepare($sql_inserir);
            $stmt_inserir->bind_param("isssdisi", $usuario_id, $nome_produto, $descricao, $nome_imagem, $preco, $estoque, $categoria, $quantidade_disponivel);
            $stmt_inserir->execute();
            $stmt_inserir->close();

            $mensagem = "Produto adicionado com sucesso!";
            $tipo_alerta = "success";
        }

        if (isset($_POST['editar_produto'])) {
            // Editar produto existente
            $produto_id = intval($_POST['produto_id']);
            $nome_produto = trim($_POST['nome_produto']);
            $descricao = trim($_POST['descricao']);
            $preco = floatval($_POST['preco']);
            $estoque = intval($_POST['estoque']);
            $categoria = trim($_POST['categoria']);

            // Gerenciar upload de imagem
            if (isset($_FILES['imagem']) && $_FILES['imagem']['error'] == UPLOAD_ERR_OK) {
                $upload_dir = '../uploads/produtos/';
                if (!is_dir($upload_dir)) {
                    mkdir($upload_dir, 0777, true);
                }

                // Gerar um nome único para a imagem para evitar conflitos
                $nome_imagem = uniqid() . "_" . basename($_FILES['imagem']['name']);
                $imagem_path = $upload_dir . $nome_imagem;
                $imageFileType = strtolower(pathinfo($imagem_path, PATHINFO_EXTENSION));

                // Validar o tipo de arquivo
                $check = getimagesize($_FILES['imagem']['tmp_name']);
                if ($check !== false) {
                    // Permitir apenas certos formatos
                    if (in_array($imageFileType, ['jpg', 'jpeg', 'png', 'gif'])) {
                        move_uploaded_file($_FILES['imagem']['tmp_name'], $imagem_path);
                    } else {
                        throw new Exception("Formato de imagem inválido. Permissões: JPG, JPEG, PNG, GIF.");
                    }
                } else {
                    throw new Exception("O arquivo enviado não é uma imagem.");
                }

                // Deletar a imagem antiga, se existir
                if (!empty($_POST['imagem_atual'])) {
                    $imagem_antiga = '../uploads/produtos/' . $_POST['imagem_atual'];
                    if (file_exists($imagem_antiga)) {
                        unlink($imagem_antiga);
                    }
                }
            } else {
                // Se não há nova imagem, manter a atual
                $nome_imagem = $_POST['imagem_atual'];
            }

            // Atualizar no banco de dados
            $sql_atualizar = "UPDATE produtos SET nome_produto = ?, descricao = ?, imagem = ?, preco = ?, estoque = ?, categoria = ?, quantidade_disponivel = ? WHERE id = ? AND usuario_id = ?";
            $quantidade_disponivel = $estoque; // Atualiza a quantidade disponível
            $stmt_atualizar = $conn->prepare($sql_atualizar);
            $stmt_atualizar->bind_param("sssdisii", $nome_produto, $descricao, $nome_imagem, $preco, $estoque, $categoria, $quantidade_disponivel, $produto_id, $usuario_id);
            $stmt_atualizar->execute();
            $stmt_atualizar->close();

            $mensagem = "Produto atualizado com sucesso!";
            $tipo_alerta = "success";
        }

        if (isset($_POST['excluir_produto'])) {
            // Excluir produto
            $produto_id = intval($_POST['produto_id']);

            // Obter o nome da imagem para deletar do servidor
            $sql_obter = "SELECT imagem FROM produtos WHERE id = ? AND usuario_id = ?";
            $stmt_obter = $conn->prepare($sql_obter);
            $stmt_obter->bind_param("ii", $produto_id, $usuario_id);
            $stmt_obter->execute();
            $result_obter = $stmt_obter->get_result();

            if ($result_obter->num_rows > 0) {
                $produto = $result_obter->fetch_assoc();
                $imagem = $produto['imagem'];

                // Deletar a imagem do servidor, se existir
                if (!empty($imagem)) {
                    $imagem_path = '../uploads/produtos/' . $imagem;
                    if (file_exists($imagem_path)) {
                        unlink($imagem_path);
                    }
                }

                // Deletar do banco de dados
                $sql_excluir = "DELETE FROM produtos WHERE id = ? AND usuario_id = ?";
                $stmt_excluir = $conn->prepare($sql_excluir);
                $stmt_excluir->bind_param("ii", $produto_id, $usuario_id);
                $stmt_excluir->execute();
                $stmt_excluir->close();

                $mensagem = "Produto excluído com sucesso!";
                $tipo_alerta = "success";
            } else {
                $mensagem = "Produto não encontrado ou você não tem permissão para excluí-lo.";
                $tipo_alerta = "danger";
            }
            $stmt_obter->close();
        }
    } catch (Exception $e) {
        $mensagem = "Erro: " . $e->getMessage();
        $tipo_alerta = "danger";
    }
}

// **Buscando a Lista de Produtos (fora do bloco POST)**
try {
    $sql_produtos = "SELECT * FROM produtos WHERE usuario_id = ? ORDER BY data_adicionado DESC";
    $stmt_produtos = $conn->prepare($sql_produtos);
    $stmt_produtos->bind_param("i", $usuario_id);
    $stmt_produtos->execute();
    $result_produtos = $stmt_produtos->get_result();
    $stmt_produtos->close();
} catch (mysqli_sql_exception $e) {
    die("Erro na consulta SQL: " . $e->getMessage());
}

// **Buscando a Lista de Pedidos (fora do bloco POST)**
try {
    $sql_pedidos = "SELECT p.id AS pedido_id, p.usuario_id, p.total, p.status, p.data_pedido,
                           ip.produto_id, ip.quantidade, ip.preco_unitario,
                           pf.nome_produto, pf.categoria
                    FROM pedidos p
                    INNER JOIN itens_pedido ip ON p.id = ip.pedido_id
                    INNER JOIN produtos pf ON ip.produto_id = pf.id
                    WHERE pf.usuario_id = ?
                    ORDER BY p.data_pedido DESC";
    $stmt_pedidos = $conn->prepare($sql_pedidos);
    $stmt_pedidos->bind_param("i", $usuario_id);
    $stmt_pedidos->execute();
    $result_pedidos = $stmt_pedidos->get_result();
    $stmt_pedidos->close();
} catch (mysqli_sql_exception $e) {
    die("Erro na consulta de pedidos: " . $e->getMessage());
}
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <title>Meu Marketplace - Assistência FixFácil</title>
    <!-- Meta Tags para Responsividade -->
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <!-- Incluindo Bootstrap CSS -->
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <!-- Font Awesome para ícones -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.1/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css?family=Poppins:300,400,500,600,700&display=swap" rel="stylesheet">
    <!-- Estilos personalizados -->
    <style>
        body {
            font-family: 'Poppins', sans-serif;
            background-color: #f7f9fc;
            margin-bottom: 60px; /* Espaço para a navbar inferior */
            color: #495057;
        }
        /* Navbar */
        .navbar {
            background-color: #fff;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .navbar-brand img {
            width: 150px;
        }
        .navbar-nav .nav-link {
            color: #495057 !important;
            font-weight: 500;
            margin-right: 15px;
        }
        .navbar-nav .nav-link.active {
            color: #007BFF !important;
        }
        .navbar-nav .nav-link:hover {
            color: #0056b3 !important;
        }
        /* Conteúdo Principal */
        .main-content {
            padding: 80px 20px 20px; /* Ajuste o padding-top para evitar sobreposição com a navbar fixa */
        }
        .welcome {
            margin-bottom: 40px;
        }
        .welcome h2 {
            font-weight: 600;
            color: #343a40;
        }
        .card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.05);
            background-color: #fff;
            padding: 30px;
            margin-bottom: 20px;	
        }
        /* Tabela */
        .table thead th {
            border-bottom: none;
            font-weight: 600;
            color: #343a40;
        }
        .table tbody td {
            vertical-align: middle;
        }
        /* Status Badges */
        .badge-pendente {
            background-color: #ffc107;
            color: #212529;
        }
        .badge-processando {
            background-color: #17a2b8;
            color: #fff;
        }
        .badge-enviado {
            background-color: #28a745;
            color: #fff;
        }
        .badge-entregue {
            background-color: #007bff;
            color: #fff;
        }
        .badge-cancelado {
            background-color: #dc3545;
            color: #fff;
        }
        /* Footer */
        footer.footer {
            background-color: #fff;
            padding: 20px 0;
            position: fixed;
            width: 100%;
            bottom: 0;
            box-shadow: 0 -2px 4px rgba(0,0,0,0.1);
        }
        footer.footer span {
            color: #6c757d;
        }
        /* Navbar Inferior (Mobile) */
        .footer-nav {
            position: fixed;
            bottom: 0;
            width: 100%;
            background-color: #fff;
            box-shadow: 0 -2px 5px rgba(0,0,0,0.1);
            z-index: 1000;
        }
        .footer-nav .nav-link {
            color: #6c757d;
            text-align: center;
            padding: 10px 0;
            font-size: 12px;
        }
        .footer-nav .nav-link.active {
            color: #007BFF;
        }
        .footer-nav .nav-link i {
            font-size: 20px;
        }
        @media (min-width: 768px) {
            .footer-nav {
                display: none;
            }
        }
        /* Estilos para Imagens */
        .produto-imagem {
            width: 100px;
            height: auto;
        }
    </style>
</head>
<body>
    <!-- Cabeçalho -->
    <nav class="navbar navbar-expand-lg navbar-light fixed-top">
        
        <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarNavAssistencia" 
                aria-controls="navbarNavAssistencia" aria-expanded="false" aria-label="Alternar navegação">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse justify-content-end" id="navbarNavAssistencia">
            <ul class="navbar-nav">
                <li class="nav-item">
                    <a class="nav-link " href="home.php"><i class="fas fa-home"></i> Painel</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link " href="solicitacoes.php"><i class="fas fa-envelope"></i> Solicitações</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link active" href="meumarktplace.php"><i class="fas fa-store"></i> Marketplace</a> <!-- Corrigido o link e ícone -->
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="solicitar_pecas.php"><i class="fas fa-plus"></i> Solicitação Peças</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="propostas_enviadas.php"><i class="fas fa-paper-plane"></i> Propostas Enviadas</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="reparos_em_andamento.php"><i class="fas fa-tools"></i> Reparos em Andamento</a>
                </li>
                 <li class="nav-item">
                    <a class="nav-link " href="carteira.php"><i class="fas fa-wallet"></i> Carteira</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="perfil.php"><i class="fas fa-user-circle"></i> Perfil</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="../logout.php"><i class="fas fa-sign-out-alt"></i> Sair</a>
                </li>
            </ul>
        </div>
    </nav>

    <!-- Conteúdo Principal -->
    <div class="container main-content">
        <div class="welcome text-center">
            <h2>Meu Marketplace</h2>
            <p class="text-muted">Cadastre seus produtos e acompanhe os pedidos recebidos no marketplace.</p>
        </div>

        <!-- Exibir Mensagens -->
        <?php if (!empty($mensagem)): ?>
            <div class="alert alert-<?php echo $tipo_alerta; ?>" role="alert">
                <?php echo htmlspecialchars($mensagem); ?>
            </div>
        <?php endif; ?>

        <!-- Formulário para Adicionar Produto -->
        <div class="card mb-4">
            <h5 class="card-header">Adicionar Novo Produto</h5>
            <div class="card-body">
                <form action="meumarktplace.php" method="POST" enctype="multipart/form-data">
                    <div class="form-group">
                        <label for="nome_produto">Nome do Produto <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="nome_produto" name="nome_produto" required>
                    </div>
                    <div class="form-group">
                        <label for="descricao">Descrição</label>
                        <textarea class="form-control" id="descricao" name="descricao" rows="3"></textarea>
                    </div>
                    <div class="form-group">
                        <label for="preco">Preço (R$) <span class="text-danger">*</span></label>
                        <input type="number" class="form-control" id="preco" name="preco" step="0.01" min="0" required>
                    </div>
                    <div class="form-group">
                        <label for="estoque">Estoque <span class="text-danger">*</span></label>
                        <input type="number" class="form-control" id="estoque" name="estoque" min="0" value="0" required>
                    </div>
                    <div class="form-group">
                        <label for="categoria">Categoria</label>
                        <input type="text" class="form-control" id="categoria" name="categoria">
                    </div>
                    <div class="form-group">
                        <label for="imagem">Imagem do Produto</label>
                        <input type="file" class="form-control-file" id="imagem" name="imagem" accept="image/*">
                        <small class="form-text text-muted">Formato: JPG, JPEG, PNG, GIF. Tamanho máximo: 2MB.</small>
                    </div>
                    <button type="submit" name="adicionar_produto" class="btn btn-success">Adicionar Produto</button>
                </form>
            </div>
        </div>

        <!-- Lista de Produtos -->
        <div class="card mb-4">
            <h5 class="card-header">Produtos Cadastrados</h5>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped" id="tabela-produtos">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Nome do Produto</th>
                                <th>Categoria</th>
                                <th>Preço (R$)</th>
                                <th>Estoque</th>
                                <th>Quantidade Disponível</th>
                                <th>Imagem</th>
                                <th>Data Adicionado</th>
                                <th>Ações</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (isset($result_produtos) && $result_produtos->num_rows > 0): ?>
                                <?php while ($produto = $result_produtos->fetch_assoc()): ?>
                                    <tr>
                                        <td><?php echo $produto['id']; ?></td>
                                        <td><?php echo htmlspecialchars($produto['nome_produto']); ?></td>
                                        <td><?php echo htmlspecialchars($produto['categoria'] ?? 'N/A'); ?></td>
                                        <td><?php echo number_format($produto['preco'], 2, ',', '.'); ?></td>
                                        <td><?php echo $produto['estoque']; ?></td>
                                        <td><?php echo $produto['quantidade_disponivel']; ?></td>
                                        <td>
                                            <?php if (!empty($produto['imagem'])): ?>
                                                <img src="../uploads/produtos/<?php echo htmlspecialchars($produto['imagem']); ?>" alt="Imagem do Produto" class="produto-imagem">
                                            <?php else: ?>
                                                <span class="text-muted">Sem Imagem</span>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo date('d/m/Y H:i', strtotime($produto['data_adicionado'])); ?></td>
                                        <td>
                                            <!-- Botão para abrir o modal de edição -->
                                            <button class="btn btn-primary btn-sm" data-toggle="modal" data-target="#modalEditar"
                                                    data-id="<?php echo $produto['id']; ?>"
                                                    data-nome="<?php echo htmlspecialchars($produto['nome_produto']); ?>"
                                                    data-descricao="<?php echo htmlspecialchars($produto['descricao']); ?>"
                                                    data-preco="<?php echo $produto['preco']; ?>"
                                                    data-estoque="<?php echo $produto['estoque']; ?>"
                                                    data-categoria="<?php echo htmlspecialchars($produto['categoria']); ?>"
                                                    data-imagem="<?php echo htmlspecialchars($produto['imagem']); ?>"
                                                    data-quantidade_disponivel="<?php echo $produto['quantidade_disponivel']; ?>">
                                                Editar
                                            </button>
                                            <!-- Botão para abrir o modal de exclusão -->
                                            <button class="btn btn-danger btn-sm" data-toggle="modal" data-target="#modalExcluir"
                                                    data-id="<?php echo $produto['id']; ?>"
                                                    data-nome="<?php echo htmlspecialchars($produto['nome_produto']); ?>">
                                                Excluir
                                            </button>
                                        </td>
                                    </tr>
                                <?php endwhile; ?>
                            <?php else: ?>
                                <tr>
                                    <td colspan="9" class="text-center">Nenhum produto cadastrado.</td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Lista de Pedidos -->
        <div class="card">
            <h5 class="card-header">Pedidos do Marketplace</h5>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped" id="tabela-pedidos">
                        <thead>
                            <tr>
                                <th>ID Pedido</th>
                                <th>Cliente ID</th>
                                <th>Total (R$)</th>
                                <th>Status</th>
                                <th>Data do Pedido</th>
                                <th>Produtos</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (isset($result_pedidos) && $result_pedidos->num_rows > 0): ?>
                                <?php 
                                // Agrupar pedidos para evitar repetição de informações
                                $pedidos = [];
                                while ($pedido = $result_pedidos->fetch_assoc()) {
                                    $pedido_id = $pedido['pedido_id'];
                                    if (!isset($pedidos[$pedido_id])) {
                                        $pedidos[$pedido_id] = [
                                            'usuario_id' => $pedido['usuario_id'],
                                            'total' => $pedido['total'],
                                            'status' => $pedido['status'],
                                            'data_pedido' => $pedido['data_pedido'],
                                            'produtos' => []
                                        ];
                                    }
                                    $pedidos[$pedido_id]['produtos'][] = [
                                        'nome_produto' => $pedido['nome_produto'],
                                        'categoria' => $pedido['categoria'],
                                        'quantidade' => $pedido['quantidade'],
                                        'preco_unitario' => $pedido['preco_unitario']
                                    ];
                                }
                                ?>

                                <?php foreach ($pedidos as $id => $pedido): ?>
                                    <tr>
                                        <td><?php echo $id; ?></td>
                                        <td><?php echo $pedido['usuario_id']; ?></td>
                                        <td><?php echo number_format($pedido['total'], 2, ',', '.'); ?></td>
                                        <td>
                                            <?php
                                            $status = $pedido['status'];
                                            switch ($status) {
                                                case 'pendente':
                                                    echo '<span class="badge badge-pendente">Pendente</span>';
                                                    break;
                                                case 'processando':
                                                    echo '<span class="badge badge-processando">Processando</span>';
                                                    break;
                                                case 'enviado':
                                                    echo '<span class="badge badge-enviado">Enviado</span>';
                                                    break;
                                                case 'entregue':
                                                    echo '<span class="badge badge-entregue">Entregue</span>';
                                                    break;
                                                case 'cancelado':
                                                    echo '<span class="badge badge-cancelado">Cancelado</span>';
                                                    break;
                                                default:
                                                    echo '<span class="badge badge-secondary">Indefinido</span>';
                                                    break;
                                            }
                                            ?>
                                        </td>
                                        <td><?php echo date('d/m/Y H:i', strtotime($pedido['data_pedido'])); ?></td>
                                        <td>
                                            <?php foreach ($pedido['produtos'] as $produto): ?>
                                                <div>
                                                    <strong><?php echo htmlspecialchars($produto['nome_produto']); ?></strong> 
                                                    (<?php echo htmlspecialchars($produto['categoria']); ?>) - 
                                                    Quantidade: <?php echo $produto['quantidade']; ?> - 
                                                    Preço Unitário: R$ <?php echo number_format($produto['preco_unitario'], 2, ',', '.'); ?>
                                                </div>
                                            <?php endforeach; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <tr>
                                    <td colspan="6" class="text-center">Nenhum pedido encontrado.</td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal para Editar Produto -->
    <div class="modal fade" id="modalEditar" tabindex="-1" aria-labelledby="modalEditarLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <form action="meumarktplace.php" method="POST" enctype="multipart/form-data">
                    <div class="modal-header">
                        <h5 class="modal-title" id="modalEditarLabel">Editar Produto</h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Fechar">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <input type="hidden" name="produto_id" id="editar_produto_id" value="">
                        <input type="hidden" name="imagem_atual" id="editar_imagem_atual" value="">
                        <div class="form-group">
                            <label for="editar_nome_produto">Nome do Produto <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="editar_nome_produto" name="nome_produto" required>
                        </div>
                        <div class="form-group">
                            <label for="editar_descricao">Descrição</label>
                            <textarea class="form-control" id="editar_descricao" name="descricao" rows="3"></textarea>
                        </div>
                        <div class="form-group">
                            <label for="editar_preco">Preço (R$) <span class="text-danger">*</span></label>
                            <input type="number" class="form-control" id="editar_preco" name="preco" step="0.01" min="0" required>
                        </div>
                        <div class="form-group">
                            <label for="editar_estoque">Estoque <span class="text-danger">*</span></label>
                            <input type="number" class="form-control" id="editar_estoque" name="estoque" min="0" required>
                        </div>
                        <div class="form-group">
                            <label for="editar_categoria">Categoria</label>
                            <input type="text" class="form-control" id="editar_categoria" name="categoria">
                        </div>
                        <div class="form-group">
                            <label for="editar_imagem">Imagem do Produto</label>
                            <input type="file" class="form-control-file" id="editar_imagem" name="imagem" accept="image/*">
                            <small class="form-text text-muted">Deixe em branco para manter a imagem atual.</small>
                            <img src="" alt="Imagem Atual" id="imagem_atual_visualizacao" class="produto-imagem mt-2 d-none">
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancelar</button>
                        <button type="submit" name="editar_produto" class="btn btn-primary">Salvar Alterações</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Modal para Excluir Produto -->
    <div class="modal fade" id="modalExcluir" tabindex="-1" aria-labelledby="modalExcluirLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <form action="meumarktplace.php" method="POST">
                    <div class="modal-header">
                        <h5 class="modal-title" id="modalExcluirLabel">Excluir Produto</h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Fechar">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <input type="hidden" name="produto_id" id="excluir_produto_id" value="">
                        <p>Tem certeza que deseja excluir o produto <strong id="excluir_nome_produto"></strong>?</p>
                        <p class="text-warning">Esta ação não poderá ser desfeita.</p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancelar</button>
                        <button type="submit" name="excluir_produto" class="btn btn-danger">Excluir</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Barra de Navegação Inferior (Mobile) -->
    <nav class="footer-nav d-md-none">
        <ul class="nav justify-content-around">
            <li class="nav-item">
                <a class="nav-link active" href="home.php">
                    <i class="fas fa-home"></i><br>Painel
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="solicitacoes.php">
                    <i class="fas fa-envelope"></i><br>Solicitações
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="propostas_enviadas.php">
                    <i class="fas fa-paper-plane"></i><br>Propostas
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="reparos_em_andamento.php">
                    <i class="fas fa-tools"></i><br>Reparos
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="solicitar_pecas.php">
                    <i class="fas fa-cogs"></i><br>Solicitar Peças
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="meus_pedidos.php">
                    <i class="fas fa-list"></i><br>Meus Pedidos
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="perfil.php">
                    <i class="fas fa-user-circle"></i><br>Perfil
                </a>
            </li>
        </ul>
    </nav>

    <!-- Rodapé -->
    <footer class="footer d-none d-md-block">
        <div class="container text-center">
            <span>&copy; <?php echo date("Y"); ?> FixFácil. Todos os direitos reservados.</span>
        </div>
    </footer>

    <!-- Incluindo Bootstrap JS e dependências -->
    <script src="https://code.jquery.com/jquery-3.5.1.min.js" ></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.5.2/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Script para preencher os modais -->
    <script>
        $(document).ready(function() {
            // Modal de Editar
            $('#modalEditar').on('show.bs.modal', function (event) {
                var button = $(event.relatedTarget); // Botão que acionou o modal
                var produtoId = button.data('id');
                var nomeProduto = button.data('nome');
                var descricao = button.data('descricao');
                var preco = button.data('preco');
                var estoque = button.data('estoque');
                var categoria = button.data('categoria');
                var imagem = button.data('imagem');
                var quantidade_disponivel = button.data('quantidade_disponivel');

                var modal = $(this);
                modal.find('#editar_produto_id').val(produtoId);
                modal.find('#editar_nome_produto').val(nomeProduto);
                modal.find('#editar_descricao').val(descricao);
                modal.find('#editar_preco').val(preco);
                modal.find('#editar_estoque').val(estoque);
                modal.find('#editar_categoria').val(categoria);
                modal.find('#editar_imagem_atual').val(imagem);
                // modal.find('#quantidade_disponivel').val(quantidade_disponivel); // Se necessário

                if(imagem) {
                    modal.find('#imagem_atual_visualizacao').attr('src', '../uploads/produtos/' + imagem).removeClass('d-none');
                } else {
                    modal.find('#imagem_atual_visualizacao').addClass('d-none');
                }
            });

            // Modal de Excluir
            $('#modalExcluir').on('show.bs.modal', function (event) {
                var button = $(event.relatedTarget); // Botão que acionou o modal
                var produtoId = button.data('id');
                var nomeProduto = button.data('nome');

                var modal = $(this);
                modal.find('#excluir_produto_id').val(produtoId);
                modal.find('#excluir_nome_produto').text(nomeProduto);
            });
        });
    </script>
</body>
</html>
