<?php
session_start();
require_once 'db.php';

$mensagem = '';

if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['fazer_lance'])) {
    $produto_id = $_POST['produto_id'];
    $valor_lance = $_POST['valor_lance'];

    try {
        $query = "INSERT INTO lances (produto_id, lojista_id, valor_lance, data_lance) VALUES (:produto_id, :lojista_id, :valor_lance, NOW())";
        $stmt = $pdo->prepare($query);
        $stmt->bindParam(':produto_id', $produto_id);
        $stmt->bindParam(':lojista_id', $_SESSION['user_id']);
        $stmt->bindParam(':valor_lance', $valor_lance);
        $stmt->execute();

        $mensagem = 'Lance realizado com sucesso!';
    } catch (PDOException $e) {
        $mensagem = 'Erro ao fazer lance: ' . $e->getMessage();
    }
}
?>

<!DOCTYPE html>
<html lang="pt-br">
<head>
    <!-- ... (cabeçalho HTML) ... -->
</head>
<body>
    <!-- ... (código HTML) ... -->
    <h2>Fazer Lance</h2>
    <?php if ($mensagem): ?>
        <div><?= $mensagem ?></div>
    <?php endif; ?>

    <form method="post" action="">
        <input type="hidden" name="produto_id" value="<?= $_GET['produto_id'] ?>">
        <input type="number" name="valor_lance" placeholder="Valor do Lance">
        <button type="submit" name="fazer_lance">Fazer Lance</button>
    </form>
</body>
</html>
