<?php
/**
 * Página de Propostas - Versão Funcional
 * FixFácil Assistências - Sistema Novo
 */

// Configurar relatório de erros para debug
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Iniciar sessão se não estiver iniciada
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Verificar se o usuário está logado
if (!isset($_SESSION['usuario_id']) || $_SESSION['tipo_usuario'] !== 'assistencia') {
    header('Location: ../login.php');
    exit();
}

// Configuração de banco de dados
$host = "localhost";
$username = "u680766645_fixfacilnew";
$password = "T3cn0l0g1a@";
$database = "u680766645_fixfacilnew";

$mysqli = new mysqli($host, $username, $password, $database);

if ($mysqli->connect_error) {
    die("Erro de conexão: " . $mysqli->connect_error);
}

$mysqli->set_charset("utf8");

// Obter dados do usuário logado
$usuario_id = $_SESSION['usuario_id'];
$usuario = null;

// Buscar dados do usuário de forma simples
$sql = "SELECT u.id, u.nome, u.email, u.telefone, u.plano_id, at.id as assistencia_id 
        FROM usuarios u 
        LEFT JOIN assistencias_tecnicas at ON u.id = at.usuario_id 
        WHERE u.id = $usuario_id";
$result = $mysqli->query($sql);
if ($result && $result->num_rows > 0) {
    $usuario = $result->fetch_assoc();
}

// Dados padrão se não encontrar
if (!$usuario) {
    $usuario = [
        'id' => $usuario_id,
        'nome' => 'Usuário',
        'email' => '',
        'assistencia_id' => 1
    ];
}

// Obter propostas
$propostas = [];
if ($usuario && isset($usuario['assistencia_id'])) {
    $sql = "
        SELECT 
            pa.*,
            sr.descricao_problema,
            sr.dispositivo,
            sr.marca,
            sr.modelo,
            u.nome as cliente_nome,
            u.telefone as cliente_telefone
        FROM propostas_assistencia pa
        JOIN solicitacoes_reparo sr ON pa.solicitacao_id = sr.id
        JOIN usuarios u ON sr.usuario_id = u.id
        WHERE pa.assistencia_id = " . (int)$usuario['assistencia_id'] . "
        ORDER BY pa.data_proposta DESC
        LIMIT 50
    ";
    
    $result = $mysqli->query($sql);
    if ($result) {
        while ($row = $result->fetch_assoc()) {
            $propostas[] = $row;
        }
    }
}

// Estatísticas
$stats = [
    'total_solicitacoes' => count($propostas),
    'aguardando_resposta' => 0,
    'em_andamento' => 0
];

// Contar por status
foreach ($propostas as $proposta) {
    if ($proposta['status'] === 'enviada') {
        $stats['aguardando_resposta']++;
    } elseif ($proposta['status'] === 'aceita' || $proposta['status'] === 'Em Andamento') {
        $stats['em_andamento']++;
    }
}

// Incluir template mobile
require_once __DIR__ . '/includes/mobile_template.php';
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Propostas - FixFácil Assistências</title>
    <style>
        <?php echo getMobileCSS(); ?>
        
        /* Estilos específicos para propostas */
        .filter-tabs {
            display: flex;
            background: white;
            border-radius: 12px;
            padding: 4px;
            margin-bottom: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .filter-tab {
            flex: 1;
            padding: 12px 16px;
            text-align: center;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            text-decoration: none;
            color: #64748b;
        }
        
        .filter-tab.active {
            background: #059669;
            color: white;
        }
        
        .proposta-card {
            background: white;
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
            transition: transform 0.2s ease;
        }
        
        .proposta-card:hover {
            transform: translateY(-2px);
        }
        
        .proposta-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 12px;
        }
        
        .device-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .device-icon {
            width: 48px;
            height: 48px;
            background: #f0fdf4;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            color: #059669;
        }
        
        .device-details h3 {
            font-size: 16px;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 4px;
        }
        
        .device-specs {
            font-size: 12px;
            color: #64748b;
        }
        
        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        .status-enviada {
            background: #fef3c7;
            color: #92400e;
        }
        
        .status-aceita {
            background: #d1fae5;
            color: #065f46;
        }
        
        .status-rejeitada {
            background: #fee2e2;
            color: #991b1b;
        }
        
        .proposta-valor {
            font-size: 18px;
            font-weight: 700;
            color: #059669;
            margin: 12px 0;
        }
        
        .cliente-info {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 12px;
            font-size: 14px;
            color: #64748b;
        }
        
        .action-buttons {
            display: flex;
            gap: 8px;
            margin-top: 12px;
        }
        
        .btn-chat {
            flex: 1;
            background: linear-gradient(135deg, #059669, #065f46);
            color: white;
            border: none;
            border-radius: 8px;
            padding: 12px 16px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            text-decoration: none;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }
        
        .btn-chat:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(5, 150, 105, 0.3);
        }
        
        .btn-detalhes {
            background: #f8fafc;
            color: #475569;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            padding: 12px 16px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            text-decoration: none;
        }
        
        .btn-detalhes:hover {
            border-color: #059669;
            color: #059669;
        }
        
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            background: white;
            border-radius: 16px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
        }
        
        .empty-icon {
            font-size: 48px;
            margin-bottom: 16px;
            opacity: 0.5;
        }
        
        .empty-title {
            font-size: 18px;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 8px;
        }
        
        .empty-subtitle {
            font-size: 14px;
            color: #64748b;
            line-height: 1.5;
        }
    </style>
</head>
<body>
    <div class="container">
        <?php echo renderMobileHeader('Propostas', $usuario['nome'], $stats); ?>
        
        <div class="content">
            <!-- Filtros -->
            <div class="filter-tabs">
                <a href="?status=todas" class="filter-tab <?php echo ($_GET['status'] ?? 'todas') === 'todas' ? 'active' : ''; ?>">
                    📋 Todas
                </a>
                <a href="?status=enviada" class="filter-tab <?php echo ($_GET['status'] ?? '') === 'enviada' ? 'active' : ''; ?>">
                    ⏳ Enviadas
                </a>
                <a href="?status=aceita" class="filter-tab <?php echo ($_GET['status'] ?? '') === 'aceita' ? 'active' : ''; ?>">
                    ✅ Aceitas
                </a>
            </div>
            
            <!-- Lista de Propostas -->
            <?php if (empty($propostas)): ?>
            <div class="empty-state">
                <div class="empty-icon">💼</div>
                <h3 class="empty-title">Nenhuma proposta encontrada</h3>
                <p class="empty-subtitle">
                    Você ainda não enviou nenhuma proposta.<br>
                    Acesse a página de solicitações para enviar suas primeiras propostas.
                </p>
            </div>
            <?php else: ?>
            <?php foreach ($propostas as $proposta): ?>
            <div class="proposta-card">
                <div class="proposta-header">
                    <div class="device-info">
                        <div class="device-icon">📱</div>
                        <div class="device-details">
                            <h3><?php echo htmlspecialchars($proposta['marca'] . ' ' . $proposta['modelo']); ?></h3>
                            <div class="device-specs">
                                <?php echo htmlspecialchars($proposta['dispositivo']); ?>
                            </div>
                        </div>
                    </div>
                    <div class="status-badge status-<?php echo strtolower($proposta['status']); ?>">
                        <?php echo htmlspecialchars($proposta['status']); ?>
                    </div>
                </div>
                
                <div class="cliente-info">
                    <span>👤</span>
                    <span><?php echo htmlspecialchars($proposta['cliente_nome']); ?></span>
                    <span>•</span>
                    <span>📞 <?php echo htmlspecialchars($proposta['cliente_telefone']); ?></span>
                </div>
                
                <div class="proposta-valor">
                    💰 R$ <?php echo number_format($proposta['preco'], 2, ',', '.'); ?>
                </div>
                
                <?php if ($proposta['status'] === 'aceita' || $proposta['status'] === 'Em Andamento'): ?>
                <div class="action-buttons">
                    <a href="chat.php?proposta_id=<?php echo $proposta['id']; ?>" class="btn-chat">
                        <span>💬</span>
                        Chat
                    </a>
                </div>
                <?php endif; ?>
                
                <?php if (isset($proposta['pago']) && $proposta['pago'] && $proposta['status'] === 'Concluída'): ?>
                <div style="background: #d1fae5; padding: 12px; border-radius: 8px; margin-top: 12px; text-align: center; color: #065f46; font-weight: 600;">
                    💰 Você recebeu: R$ <?php echo number_format($proposta['preco'] * 0.75, 2, ',', '.'); ?>
                </div>
                <?php endif; ?>
            </div>
            <?php endforeach; ?>
            <?php endif; ?>
        </div>
        
        <?php echo renderFloatingAction('solicitacoes.php', '+'); ?>
        <?php echo renderBottomNav('propostas'); ?>
    </div>

    <script>
        // Auto-refresh a cada 30 segundos
        setInterval(() => {
            if (document.visibilityState === 'visible') {
                location.reload();
            }
        }, 30000);
    </script>
</body>
</html>
