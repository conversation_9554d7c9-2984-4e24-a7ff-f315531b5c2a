<?php
// Move a chamada para session_start() para o início do arquivo, antes de qualquer outra saída
session_start();
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

require_once 'db.php';

if (!isset($_SESSION['user_id']) || $_SESSION['nivel_acesso'] != 'fornecedor') {
    header("Location: login.php");
    exit();
}

if(isset($_GET['logout']) && $_GET['logout'] == 'true') {
    session_destroy();
    header("Location: login.php");
    exit();
}

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    $fornecedor_id = $_SESSION['user_id'];

    // Total de pedidos do fornecedor
    $queryTotalPedidos = "SELECT COUNT(*) AS total_pedidos FROM compras WHERE fornecedor_id = :fornecedor_id";
    $stmtTotalPedidos = $pdo->prepare($queryTotalPedidos);
    $stmtTotalPedidos->bindParam(':fornecedor_id', $fornecedor_id);
    $stmtTotalPedidos->execute();
    $totalPedidos = $stmtTotalPedidos->fetch(PDO::FETCH_ASSOC);

    // Lucro financeiro total baseado nos pedidos finalizados
    $queryLucro = "SELECT SUM(valor_pedido) AS lucro_total FROM compras WHERE fornecedor_id = :fornecedor_id AND status_pedido = 'Finalizado'";
    $stmtLucro = $pdo->prepare($queryLucro);
    $stmtLucro->bindParam(':fornecedor_id', $fornecedor_id);
    $stmtLucro->execute();
    $lucro = $stmtLucro->fetch(PDO::FETCH_ASSOC);

    // Produtos mais vendidos pelo fornecedor
    $queryProdutosMaisVendidos = "SELECT produtos.nome AS nome_produto, SUM(compras.quantidade) AS total_vendido
                                  FROM compras
                                  JOIN produtos ON compras.produto_id = produtos.id
                                  WHERE produtos.fornecedor_id = :fornecedor_id
                                  GROUP BY produtos.nome
                                  ORDER BY total_vendido DESC
                                  LIMIT 5";
    $stmtProdutosMaisVendidos = $pdo->prepare($queryProdutosMaisVendidos);
    $stmtProdutosMaisVendidos->bindParam(':fornecedor_id', $fornecedor_id);
    $stmtProdutosMaisVendidos->execute();
    $produtosMaisVendidos = $stmtProdutosMaisVendidos->fetchAll(PDO::FETCH_ASSOC);

} catch (PDOException $e) {
    echo "Erro: " . $e->getMessage();
}
?>
<!DOCTYPE html>
<html lang="pt-br">

<head>

    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="description" content="">
    <meta name="author" content="">
    <bootstrap.min.css">
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <title>LOJISTA - Dashboard</title>

    <!-- Custom fonts for this template-->
    <link href="vendor/fontawesome-free/css/all.min.css" rel="stylesheet" type="text/css">
    <link href="https://fonts.googleapis.com/css?family=Nunito:200,200i,300,300i,400,400i,600,600i,700,700i,800,800i,900,900i"
        rel="stylesheet">

    <!-- Custom styles for this template-->
    <link href="css/sb-admin-2.min.css" rel="stylesheet">

</head>

<body id="page-top">

    <!-- Page Wrapper -->
    <div id="wrapper">

        <!-- Sidebar -->
        <ul class="navbar-nav bg-gradient-primary sidebar sidebar-dark accordion" id="accordionSidebar">

            <!-- Sidebar - Brand -->
            <a class="sidebar-brand d-flex align-items-center justify-content-center" href="fornecedor_dashboard.php">
                <div class="sidebar-brand-icon rotate-n-15">
                    <i class="fas fa-laugh-wink"></i>
                </div>
                <div class="sidebar-brand-text mx-3"> <sup>Avos Brasil</sup></div>
            </a>

            <!-- Divider -->
            <hr class="sidebar-divider my-0">

            <!-- Nav Item - Dashboard -->
            <li class="nav-item active">
                <a class="nav-link" href="fornecedor_pedidos.php">
                    <i class="fas fa-fw fa-tachometer-alt"></i>
                    <span>Minhas Cotacao</span></a>
            </li>
            <!-- Nav Item - Charts -->
            <li class="nav-item">
                <a class="nav-link" href="cadastro_produto.php">
                    <i class="fas fa-fw fa-chart-area"></i>
                    <span>Cadastrar Produto</span></a>
            </li>
             <li class="nav-item">
                <a class="nav-link" href="fornecedor_produto.php">
                    <i class="fas fa-fw fa-chart-area"></i>
                    <span>Meus Produto</span></a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="fornecedor_informacoes.php">
                    <i class="fas fa-fw fa-chart-area"></i>
                    <span>Dashboard Financeiro</span></a>
            </li>

            <!-- Divider -->
            <hr class="sidebar-divider d-none d-md-block">

            <!-- Sidebar Toggler (Sidebar) -->
            <div class="text-center d-none d-md-inline">
                <button class="rounded-circle border-0" id="sidebarToggle"></button>
            </div>

            <!-- Sidebar Message -->
            <div class="sidebar-card d-none d-lg-flex">
                <img class="sidebar-card-illustration mb-2" src="img/undraw_rocket.svg" alt="...">
                <p class="text-center mb-2"><strong>Avos Brasil Pro</strong>Controle LFM_Consultoria</p>
            </div>

        </ul>
        <!-- End of Sidebar -->

        <!-- Content Wrapper -->
        <div id="content-wrapper" class="d-flex flex-column">

            <!-- Main Content -->
            <div id="content">

                <!-- Topbar -->
                <nav class="navbar navbar-expand navbar-light bg-white topbar mb-4 static-top shadow">

                    <!-- Sidebar Toggle (Topbar) -->
                    <button id="sidebarToggleTop" class="btn btn-link d-md-none rounded-circle mr-3">
                        <i class="fa fa-bars"></i>
                    </button>

                    <!-- Topbar Search -->
                    <form class="d-none d-sm-inline-block form-inline mr-auto ml-md-3 my-2 my-md-0 mw-100 navbar-search">
                        <div class="input-group">
                            <input type="text" class="form-control bg-light border-0 small" placeholder="Search for..."
                                aria-label="Search" aria-describedby="basic-addon2">
                            <div class="input-group-append">
                                <button class="btn btn-primary" type="button">
                                    <i class="fas fa-search fa-sm"></i>
                                </button>
                            </div>
                        </div>
                    </form>

                    <!-- Topbar Navbar -->
                    <ul class="navbar-nav ml-auto">

                        <!-- Nav Item - User Information -->
                        <li class="nav-item dropdown no-arrow">
                            <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button"
                                data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                <span class="mr-2 d-none d-lg-inline text-gray-600 small">LOJISTA</span>
                                <img class="img-profile rounded-circle"
                                    src="img/undraw_profile.svg">
                            </a>
                            <!-- Dropdown - User Information -->
                            <div class="dropdown-menu dropdown-menu-right shadow animated--grow-in"
                                aria-labelledby="userDropdown">
                                <a class="dropdown-item" href="#">
                                    <i class="fas fa-user fa-sm fa-fw mr-2 text-gray-400"></i>
                                    Profile
                                </a>
                                <a class="dropdown-item" href="#">
                                    <i class="fas fa-cogs fa-sm fa-fw mr-2 text-gray-400"></i>
                                    Settings
                                </a>
                                <a class="dropdown-item" href="#">
                                    <i class="fas fa-list fa-sm fa-fw mr-2 text-gray-400"></i>
                                    Activity Log
                                </a>
                                <div class="dropdown-divider"></div>
                                <a class="dropdown-item" href="#" data-toggle="modal" data-target="#logoutModal">
                                    <i class="fas fa-sign-out-alt fa-sm fa-fw mr-2 text-gray-400"></i>
                                    Logout
                                </a>
                            </div>
                        </li>

                    </ul>

                </nav>
                <!-- End of Topbar -->

                <!-- Begin Page Content -->
                <div class="container-fluid">

                <div class="jumbotron">
        <h1 class="display-4">Dashboard - Financeiro</h1>
        <p class="lead">Bem-vindo ao seu painel de controle.</p>
    </div>

    <div class="row">
    <div class="container mt-5">
       
        
        <div class="row mt-5">
            <div class="col-md-4">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">Total de Pedidos</h5>
                        <p class="card-text"><?php echo $totalPedidos['total_pedidos']; ?></p>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">Lucro Financeiro</h5>
                        <p class="card-text">R$ <?php echo number_format($lucro['lucro_total'] ?? 0, 2, ',', '.'); ?></p>

                        
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">Produtos Mais Vendidos</h5>
                        <ul class="list-group">
                            <?php foreach ($produtosMaisVendidos as $produto): ?>
                                <li class="list-group-item"><?php echo $produto['nome_produto']; ?> - <?php echo $produto['total_vendido']; ?> unidades</li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Gráficos -->
        <div class="row mt-5">
            <div class="col-md-6">
                <canvas id="graficoPedidos" width="400" height="200"></canvas>
            </div>
            <div class="col-md-6">
                <canvas id="graficoProdutos" width="400" height="200"></canvas>
            </div>
        </div>
    </div>

    <script>
        // Gráfico de Pedidos
        var ctxPedidos = document.getElementById('graficoPedidos').getContext('2d');
        var chartPedidos = new Chart(ctxPedidos, {
            type: 'bar',
            data: {
                labels: ['Total de Pedidos'],
                datasets: [{
                    label: 'Número de Pedidos',
                    data: [<?php echo $totalPedidos['total_pedidos']; ?>],
                    backgroundColor: 'rgba(255, 99, 132, 0.2)',
                    borderColor: 'rgba(255, 99, 132, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });

        // Gráfico de Produtos Mais Vendidos
        var ctxProdutos = document.getElementById('graficoProdutos').getContext('2d');
        var labels = <?php echo json_encode(array_column($produtosMaisVendidos, 'nome_produto')); ?>;
        var data = <?php echo json_encode(array_column($produtosMaisVendidos, 'total_vendido')); ?>;
        var chartProdutos = new Chart(ctxProdutos, {
            type: 'pie',
            data: {
                labels: labels,
                datasets: [{
                    label: 'Produtos Mais Vendidos',
                    data: data,
                    backgroundColor: [
                        'rgba(255, 99, 132, 0.2)',
                        'rgba(54, 162, 235, 0.2)',
                        'rgba(255, 206, 86, 0.2)',
                        'rgba(75, 192, 192, 0.2)',
                        'rgba(153, 102, 255, 0.2)'
                    ],
                    borderColor: [
                        'rgba(255, 99, 132, 1)',
                        'rgba(54, 162, 235, 1)',
                        'rgba(255, 206, 86, 1)',
                        'rgba(75, 192, 192, 1)',
                        'rgba(153, 102, 255, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    </script>

    </div>
</div>


                </div>
                <!-- /.container-fluid -->

            </div>
            <!-- End of Main Content -->

            <!-- End of Footer -->

        </div>
        <!-- End of Content Wrapper -->

    </div>
    <!-- End of Page Wrapper -->

    <!-- Scroll to Top Button-->
    <a class="scroll-to-top rounded" href="#page-top">
        <i class="fas fa-angle-up"></i>
    </a>

    <!-- Logout Modal-->
    <div class="modal fade" id="logoutModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
        aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalLabel">Pronto para sair?</h5>
                    <button class="close" type="button" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">×</span>
                    </button>
                </div>
                <div class="modal-body">Selecione "Logout" abaixo se você estiver pronto para terminar sua sessão atual.</div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" type="button" data-dismiss="modal">Cancelar</button>
                    <a class="btn btn-primary" href="?logout=true">Logout</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap core JavaScript-->
    <script src="vendor/jquery/jquery.min.js"></script>
    <script src="vendor/bootstrap/js/bootstrap.bundle.min.js"></script>

    <!-- Core plugin JavaScript-->
    <script src="vendor/jquery-easing/jquery.easing.min.js"></script>

    <!-- Custom scripts for all pages-->
    <script src="js/sb-admin-2.min.js"></script>
    
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    // Gráfico de pedidos
    var ctxPedidos = document.getElementById('pedidosChart').getContext('2d');
    var pedidosChart = new Chart(ctxPedidos, {
        type: 'bar',
        data: {
            labels: ['Pedidos'],
            datasets: [{
                label: 'Total de Pedidos',
                data: [<?php echo $totalPedidos['total_pedidos']; ?>],
                backgroundColor: 'rgba(255, 99, 132, 0.2)',
                borderColor: 'rgba(255, 99, 132, 1)',
                borderWidth: 1
            }]
        },
        options: {
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });

</script>


</body>

</html>
