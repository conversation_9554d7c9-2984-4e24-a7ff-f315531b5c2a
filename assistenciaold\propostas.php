<?php
/**
 * Página de Propostas
 * FixFácil Assistências - Sistema Novo
 */

require_once 'config/auth.php';
require_once 'config/database.php';
require_once 'includes/layout.php';

// Verificar autenticação
$auth = getAuth();
$auth->checkAssistenciaAuth();

// Obter dados do usuário
$usuario = $auth->getUsuarioLogado();
$plano = $auth->getPlanoInfo($usuario['id']);
$db = getDatabase();

// Filtros
$status_filter = $_GET['status'] ?? 'todas';
$search = $_GET['search'] ?? '';

// Obter propostas
$propostas = [];
try {
    $where_conditions = ["pa.assistencia_id = ?"];
    $params = [$usuario['assistencia_id']];
    
    if ($status_filter !== 'todas') {
        $where_conditions[] = "pa.status = ?";
        $params[] = $status_filter;
    }
    
    if (!empty($search)) {
        $where_conditions[] = "(sr.descricao_problema LIKE ? OR sr.dispositivo LIKE ? OR sr.marca LIKE ? OR sr.modelo LIKE ? OR u.nome LIKE ?)";
        $search_param = "%$search%";
        $params = array_merge($params, [$search_param, $search_param, $search_param, $search_param, $search_param]);
    }
    
    $where_clause = implode(' AND ', $where_conditions);
    
    $sql = "
        SELECT 
            pa.*,
            sr.descricao_problema,
            sr.dispositivo,
            sr.marca,
            sr.modelo,
            sr.memoria,
            sr.metodo_entrega,
            sr.data_solicitacao,
            u.nome as cliente_nome,
            u.telefone as cliente_telefone,
            u.endereco as cliente_endereco
        FROM propostas_assistencia pa
        JOIN solicitacoes_reparo sr ON pa.solicitacao_id = sr.id
        JOIN usuarios u ON sr.usuario_id = u.id
        WHERE $where_clause
        ORDER BY pa.data_proposta DESC
        LIMIT 50
    ";
    
    $result = $db->query($sql, $params);
    
    while ($row = $result->fetch_assoc()) {
        $propostas[] = $row;
    }
    
} catch (Exception $e) {
    error_log("Erro ao obter propostas: " . $e->getMessage());
}

// Estatísticas das propostas
$stats = [];
try {
    $sql = "
        SELECT 
            COUNT(*) as total,
            COUNT(CASE WHEN status = 'enviada' THEN 1 END) as enviadas,
            COUNT(CASE WHEN status = 'aceita' THEN 1 END) as aceitas,
            COUNT(CASE WHEN status = 'Em Andamento' THEN 1 END) as em_andamento,
            COUNT(CASE WHEN status = 'Concluída' THEN 1 END) as concluidas,
            COUNT(CASE WHEN status = 'rejeitada' THEN 1 END) as rejeitadas,
            COALESCE(SUM(CASE WHEN status = 'Concluída' AND pago = 1 THEN preco * (1 - ?/100) END), 0) as receita_total
        FROM propostas_assistencia 
        WHERE assistencia_id = ?
    ";
    
    $result = $db->query($sql, [$plano['taxa_servico'], $usuario['assistencia_id']]);
    $stats = $result->fetch_assoc();
    
} catch (Exception $e) {
    error_log("Erro ao obter estatísticas: " . $e->getMessage());
    $stats = [
        'total' => 0, 'enviadas' => 0, 'aceitas' => 0, 
        'em_andamento' => 0, 'concluidas' => 0, 'rejeitadas' => 0, 'receita_total' => 0
    ];
}

// Inicializar layout
$layout = new Layout();
?>

<?php $layout->renderHead("Propostas - FixFácil Assistências"); ?>

<div class="main-wrapper">
    <?php $layout->renderSidebar('propostas'); ?>
    
    <main class="main-content">
        <!-- Header -->
        <div class="content-header">
            <h1 class="page-title">
                <i class="fas fa-paper-plane me-3"></i>
                Minhas Propostas
            </h1>
            <p class="page-subtitle">
                Gerencie todas as propostas que você enviou aos clientes
            </p>
        </div>
        
        <!-- Estatísticas -->
        <div class="row g-4 mb-4">
            <div class="col-lg-2 col-md-4 col-sm-6">
                <div class="card text-center">
                    <div class="card-body py-3">
                        <h4 class="text-primary mb-1"><?php echo $stats['total']; ?></h4>
                        <small class="text-muted">Total</small>
                    </div>
                </div>
            </div>
            <div class="col-lg-2 col-md-4 col-sm-6">
                <div class="card text-center">
                    <div class="card-body py-3">
                        <h4 class="text-warning mb-1"><?php echo $stats['enviadas']; ?></h4>
                        <small class="text-muted">Enviadas</small>
                    </div>
                </div>
            </div>
            <div class="col-lg-2 col-md-4 col-sm-6">
                <div class="card text-center">
                    <div class="card-body py-3">
                        <h4 class="text-success mb-1"><?php echo $stats['aceitas']; ?></h4>
                        <small class="text-muted">Aceitas</small>
                    </div>
                </div>
            </div>
            <div class="col-lg-2 col-md-4 col-sm-6">
                <div class="card text-center">
                    <div class="card-body py-3">
                        <h4 class="text-info mb-1"><?php echo $stats['em_andamento']; ?></h4>
                        <small class="text-muted">Em Andamento</small>
                    </div>
                </div>
            </div>
            <div class="col-lg-2 col-md-4 col-sm-6">
                <div class="card text-center">
                    <div class="card-body py-3">
                        <h4 class="text-primary mb-1"><?php echo $stats['concluidas']; ?></h4>
                        <small class="text-muted">Concluídas</small>
                    </div>
                </div>
            </div>
            <div class="col-lg-2 col-md-4 col-sm-6">
                <div class="card text-center">
                    <div class="card-body py-3">
                        <h4 class="text-success mb-1">R$ <?php echo number_format($stats['receita_total'], 0); ?></h4>
                        <small class="text-muted">Receita</small>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Filtros -->
        <div class="card mb-4">
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-3">
                        <label class="form-label">Status</label>
                        <select name="status" class="form-select">
                            <option value="todas" <?php echo $status_filter === 'todas' ? 'selected' : ''; ?>>
                                Todas
                            </option>
                            <option value="enviada" <?php echo $status_filter === 'enviada' ? 'selected' : ''; ?>>
                                Enviadas
                            </option>
                            <option value="aceita" <?php echo $status_filter === 'aceita' ? 'selected' : ''; ?>>
                                Aceitas
                            </option>
                            <option value="Em Andamento" <?php echo $status_filter === 'Em Andamento' ? 'selected' : ''; ?>>
                                Em Andamento
                            </option>
                            <option value="Concluída" <?php echo $status_filter === 'Concluída' ? 'selected' : ''; ?>>
                                Concluídas
                            </option>
                            <option value="rejeitada" <?php echo $status_filter === 'rejeitada' ? 'selected' : ''; ?>>
                                Rejeitadas
                            </option>
                        </select>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">Buscar</label>
                        <input type="text" name="search" class="form-control" 
                               placeholder="Buscar por dispositivo, marca, modelo ou cliente..."
                               value="<?php echo htmlspecialchars($search); ?>">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search me-2"></i>
                                Filtrar
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- Lista de Propostas -->
        <?php if (empty($propostas)): ?>
        <div class="card">
            <div class="card-body text-center py-5">
                <i class="fas fa-paper-plane fs-1 text-muted mb-3"></i>
                <h4 class="text-muted">Nenhuma proposta encontrada</h4>
                <p class="text-muted">
                    <?php if ($status_filter === 'todas'): ?>
                        Você ainda não enviou nenhuma proposta.
                    <?php else: ?>
                        Não há propostas com o status "<?php echo $status_filter; ?>".
                    <?php endif; ?>
                </p>
                <a href="solicitacoes.php" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>
                    Ver Solicitações
                </a>
            </div>
        </div>
        <?php else: ?>
        <div class="row g-4">
            <?php foreach ($propostas as $proposta): ?>
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-lg-8">
                                <div class="d-flex align-items-start mb-3">
                                    <div class="flex-shrink-0">
                                        <div class="bg-primary bg-opacity-10 p-3 rounded-circle">
                                            <i class="fas fa-mobile-alt text-primary fs-5"></i>
                                        </div>
                                    </div>
                                    <div class="flex-grow-1 ms-3">
                                        <h5 class="mb-1">
                                            <?php echo htmlspecialchars($proposta['marca'] . ' ' . $proposta['modelo']); ?>
                                            <span class="badge bg-secondary ms-2"><?php echo htmlspecialchars($proposta['memoria']); ?></span>
                                        </h5>
                                        <p class="text-muted mb-2">
                                            <i class="fas fa-user me-1"></i>
                                            <?php echo htmlspecialchars($proposta['cliente_nome']); ?>
                                            <i class="fas fa-phone ms-3 me-1"></i>
                                            <?php echo htmlspecialchars($proposta['cliente_telefone']); ?>
                                        </p>
                                        <p class="mb-2">
                                            <strong>Problema:</strong> 
                                            <?php echo htmlspecialchars($proposta['descricao_problema']); ?>
                                        </p>
                                        <?php if (!empty($proposta['observacoes'])): ?>
                                        <p class="mb-2">
                                            <strong>Sua Proposta:</strong> 
                                            <?php echo nl2br(htmlspecialchars($proposta['observacoes'])); ?>
                                        </p>
                                        <?php endif; ?>
                                        <div class="d-flex align-items-center text-muted">
                                            <small>
                                                <i class="fas fa-calendar me-1"></i>
                                                Enviada em <?php echo date('d/m/Y H:i', strtotime($proposta['data_proposta'])); ?>
                                            </small>
                                            <small class="ms-3">
                                                <i class="fas fa-truck me-1"></i>
                                                <?php echo htmlspecialchars($proposta['metodo_entrega']); ?>
                                            </small>
                                            <?php if ($proposta['retirada_expressa']): ?>
                                            <small class="ms-3">
                                                <i class="fas fa-motorcycle me-1"></i>
                                                Retirada Express
                                            </small>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-4">
                                <div class="text-lg-end">
                                    <div class="mb-3">
                                        <?php
                                        $status_class = [
                                            'enviada' => 'warning',
                                            'aceita' => 'success',
                                            'rejeitada' => 'danger',
                                            'Em Andamento' => 'info',
                                            'Concluída' => 'primary',
                                            'pagamento' => 'success'
                                        ];
                                        ?>
                                        <span class="badge bg-<?php echo $status_class[$proposta['status']] ?? 'secondary'; ?> fs-6">
                                            <?php echo htmlspecialchars($proposta['status']); ?>
                                        </span>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <div class="row g-2 text-center">
                                            <div class="col-6">
                                                <small class="text-muted d-block">Preço</small>
                                                <strong class="text-primary">R$ <?php echo number_format($proposta['preco'], 2, ',', '.'); ?></strong>
                                            </div>
                                            <div class="col-6">
                                                <small class="text-muted d-block">Prazo</small>
                                                <strong><?php echo $proposta['prazo']; ?> dia(s)</strong>
                                            </div>
                                        </div>
                                        
                                        <?php if ($proposta['status'] === 'Concluída' && $proposta['pago']): ?>
                                        <div class="mt-2">
                                            <small class="text-success d-block">Você recebeu</small>
                                            <strong class="text-success">
                                                R$ <?php echo number_format($proposta['preco'] * (1 - $plano['taxa_servico']/100), 2, ',', '.'); ?>
                                            </strong>
                                        </div>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <div class="d-grid gap-2">
                                        <a href="detalhes_solicitacao.php?id=<?php echo $proposta['solicitacao_id']; ?>" 
                                           class="btn btn-outline-primary btn-sm">
                                            <i class="fas fa-eye me-2"></i>
                                            Ver Solicitação
                                        </a>
                                        
                                        <?php if ($proposta['status'] === 'aceita' || $proposta['status'] === 'Em Andamento'): ?>
                                        <a href="gerenciar_reparo.php?proposta_id=<?php echo $proposta['id']; ?>" 
                                           class="btn btn-primary btn-sm">
                                            <i class="fas fa-tools me-2"></i>
                                            Gerenciar Reparo
                                        </a>
                                        <?php endif; ?>
                                        
                                        <?php if ($auth->hasAccess('chat') && ($proposta['status'] === 'aceita' || $proposta['status'] === 'Em Andamento')): ?>
                                        <a href="chat.php?proposta_id=<?php echo $proposta['id']; ?>" 
                                           class="btn btn-outline-info btn-sm">
                                            <i class="fas fa-comments me-2"></i>
                                            Chat
                                        </a>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
        <?php endif; ?>
    </main>
</div>

<?php $layout->renderFooter(); ?>
