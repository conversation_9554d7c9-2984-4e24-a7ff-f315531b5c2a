<?php
session_start();
require_once 'db.php';

if (!isset($_SESSION['user_id']) || $_SESSION['nivel_acesso'] != 'lojista') {
    header("Location: login.php");
    exit();
}

if (isset($_GET['logout']) && $_GET['logout'] == 'true') {
    session_destroy();
    header("Location: login.php");
    exit();
}

function refazerPedido($pdo, $lojista_id, $fornecedor_id, $produto_id, $quantidade, $observacao, $data_programada)
{
    try {
        $query = "INSERT INTO compras (lojista_id, fornecedor_id, produto_id, quantidade, observacao, data_programada) VALUES (:lojista_id, :fornecedor_id, :produto_id, :quantidade, :observacao, :data_programada)";
        $stmt = $pdo->prepare($query);
        $stmt->bindParam(':lojista_id', $lojista_id);
        $stmt->bindParam(':fornecedor_id', $fornecedor_id);
        $stmt->bindParam(':produto_id', $produto_id);
        $stmt->bindParam(':quantidade', $quantidade);
        $stmt->bindParam(':observacao', $observacao);
        $stmt->bindParam(':data_programada', $data_programada, PDO::PARAM_STR);
        $stmt->execute();

        return true;
    } catch (PDOException $e) {
        echo "Erro ao refazer o pedido: " . $e->getMessage();
        return false;
    }
}

$lojista_id = $_SESSION['user_id'];
$query = "SELECT c.id AS compra_id, p.id AS produto_id, p.nome AS produto_nome, c.quantidade, c.observacao, c.data_programada, c.fornecedor_id
          FROM compras c
          JOIN produtos p ON c.produto_id = p.id
          WHERE c.lojista_id = :lojista_id
          ORDER BY c.id DESC
          LIMIT 5";
$stmt = $pdo->prepare($query);
$stmt->bindParam(':lojista_id', $lojista_id);
$stmt->execute();
$ultimosPedidos = $stmt->fetchAll(PDO::FETCH_ASSOC);

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    if (isset($_POST['refazer_pedido'])) {
        $lojista_id = $_SESSION['user_id'];
        $produto_id = $_POST['produto_id'];
        $quantidade = $_POST['quantidade'];
        $observacao = $_POST['observacao'];
        $data_programada = $_POST['data_programada'];
        $compra_id = $_POST['compra_id'];  // Adicionado para obter o compra_id

        // Buscar fornecedor_id do pedido selecionado
        $query = "SELECT fornecedor_id FROM compras WHERE id = :compra_id";
        $stmt = $pdo->prepare($query);
        $stmt->bindParam(':compra_id', $compra_id);
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);

        // Verificar e exibir o valor de fornecedor_id
        if ($result && isset($result['fornecedor_id'])) {
            $fornecedor_id = $result['fornecedor_id'];
        } else {
            echo "Erro ao obter o fornecedor_id.";
            exit();
        }

        if (refazerPedido($pdo, $lojista_id, $fornecedor_id, $produto_id, $quantidade, $observacao, $data_programada)) {
            echo '';
        }
    }
}
?>
<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="description" content="">
    <meta name="author" content="">
    <title>LOJISTA - Favoritos</title>
    <link href="vendor/fontawesome-free/css/all.min.css" rel="stylesheet" type="text/css">
    <link href="https://fonts.googleapis.com/css?family=Nunito:200,200i,300,300i,400,400i,600,600i,700,700i,800,800i,900,900i" rel="stylesheet">
    <link href="css/sb-admin-2.min.css" rel="stylesheet">
</head>
<body id="page-top">
    
    <!-- Page Wrapper -->
    <div id="wrapper">

        <!-- Sidebar -->
        <ul class="navbar-nav bg-gradient-primary sidebar sidebar-dark accordion" id="accordionSidebar">

            <!-- Sidebar - Brand -->
            <a class="sidebar-brand d-flex align-items-center justify-content-center" href="admin_dashboard.php">
                <div class="sidebar-brand-icon rotate-n-15">
                    <i class="fas fa-laugh-wink"></i>
                </div>
                <div class="sidebar-brand-text mx-3"> <sup>Avos Brasil</sup></div>
            </a>

            <!-- Divider -->
            <hr class="sidebar-divider my-0">

            <!-- Nav Item - Dashboard -->
            <li class="nav-item active">
                <a class="nav-link" href="minhas_compras.php">
                    <i class="fas fa-fw fa-tachometer-alt"></i>
                    <span>Meus Pedidos</span></a>
            </li>
            <!-- Nav Item - Charts -->
            <li class="nav-item">
                <a class="nav-link" href="lojista_compra.php">
                    <i class="fas fa-fw fa-chart-area"></i>
                    <span>Nova Cotação</span></a>
            </li>
            <!-- Nav Item - Charts -->
            <li class="nav-item">
                <a class="nav-link" href="lojista_favorito.php">
                    <i class="fas fa-fw fa-chart-area"></i>
                    <span>Favorito</span></a>
            </li>

            <!-- Divider -->
            <hr class="sidebar-divider d-none d-md-block">

            <!-- Sidebar Toggler (Sidebar) -->
            <div class="text-center d-none d-md-inline">
                <button class="rounded-circle border-0" id="sidebarToggle"></button>
            </div>

            <!-- Sidebar Message -->
            <div class="sidebar-card d-none d-lg-flex">
                <img class="sidebar-card-illustration mb-2" src="img/undraw_rocket.svg" alt="...">
                <p class="text-center mb-2"><strong>Avos Brasil Pro</strong>Controle LFM_Consultoria</p>
            </div>

        </ul>
        <!-- End of Sidebar -->

        <!-- Content Wrapper -->
        <div id="content-wrapper" class="d-flex flex-column">

            <!-- Main Content -->
            <div id="content">

                <!-- Topbar -->
                <nav class="navbar navbar-expand navbar-light bg-white topbar mb-4 static-top shadow">

                    <!-- Sidebar Toggle (Topbar) -->
                    <button id="sidebarToggleTop" class="btn btn-link d-md-none rounded-circle mr-3">
                        <i class="fa fa-bars"></i>
                    </button>

                    <!-- Topbar Search -->
                    <form class="d-none d-sm-inline-block form-inline mr-auto ml-md-3 my-2 my-md-0 mw-100 navbar-search">
                        <div class="input-group">
                            <input type="text" class="form-control bg-light border-0 small" placeholder="Search for..."
                                aria-label="Search" aria-describedby="basic-addon2">
                            <div class="input-group-append">
                                <button class="btn btn-primary" type="button">
                                    <i class="fas fa-search fa-sm"></i>
                                </button>
                            </div>
                        </div>
                    </form>

                    <!-- Topbar Navbar -->
                    <ul class="navbar-nav ml-auto">

                        <!-- Nav Item - User Information -->
                        <li class="nav-item dropdown no-arrow">
                            <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button"
                                data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                <span class="mr-2 d-none d-lg-inline text-gray-600 small">LOJISTA</span>
                                <img class="img-profile rounded-circle"
                                    src="img/undraw_profile.svg">
                            </a>
                            <!-- Dropdown - User Information -->
                            <div class="dropdown-menu dropdown-menu-right shadow animated--grow-in"
                                aria-labelledby="userDropdown">
                                <a class="dropdown-item" href="#">
                                    <i class="fas fa-user fa-sm fa-fw mr-2 text-gray-400"></i>
                                    Profile
                                </a>
                                <a class="dropdown-item" href="#">
                                    <i class="fas fa-cogs fa-sm fa-fw mr-2 text-gray-400"></i>
                                    Settings
                                </a>
                                <a class="dropdown-item" href="#">
                                    <i class="fas fa-list fa-sm fa-fw mr-2 text-gray-400"></i>
                                    Activity Log
                                </a>
                                <div class="dropdown-divider"></div>
                                <a class="dropdown-item" href="#" data-toggle="modal" data-target="#logoutModal">
                                    <i class="fas fa-sign-out-alt fa-sm fa-fw mr-2 text-gray-400"></i>
                                    Logout
                                </a>
                            </div>
                        </li>

                    </ul>

                </nav>
                <!-- End of Topbar -->

     
        <div id="content-wrapper" class="d-flex flex-column">
            <div id="content">
                <nav class="navbar navbar-expand navbar-light bg-white topbar mb-4 static-top shadow">
                    <!-- ... (O código do navbar permanece o mesmo) ... -->
                </nav>
                <div class="container-fluid">
        <h1 class="h3 mb-4 text-gray-800">Favoritos</h1>
        <div class="row">
            <div class="col-lg-12">
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Últimos Pedidos</h6>
                    </div>
                    <div class="card-body">
                    <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                                            <thead>
                                                <tr>
                                                    <th>Produto</th>
                                                    <th>Quantidade</th>
                                                    <th>Observação</th>
                                                    <th>Data Programada</th>
                                                    <th>Ação</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($ultimosPedidos as $pedido) : ?>
                                                    <tr>
                                                        <td><?php echo $pedido['produto_nome']; ?></td>
                                                        <td><?php echo $pedido['quantidade']; ?></td>
                                                        <td><?php echo $pedido['observacao']; ?></td>
                                                        <td><?php echo $pedido['data_programada']; ?></td>
                                                        <td>
                                                            <form method="post" action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>">
                                                                <input type="hidden" name="compra_id" value="<?php echo $pedido['compra_id']; ?>">
                                                                <input type="hidden" name="produto_id" value="<?php echo $pedido['produto_id']; ?>">
                                                                <input type="hidden" name="quantidade" value="<?php echo $pedido['quantidade']; ?>">
                                                                <input type="hidden" name="observacao" value="<?php echo $pedido['observacao']; ?>">
                                                                <input type="hidden" name="data_programada" value="<?php echo $pedido['data_programada']; ?>">
                                                                <button type="submit" class="btn btn-warning" name="refazer_pedido">Refazer Pedido</button>
                                                            </form>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                </div>
            </div>
        </div>
    </div>
<!-- Script de Inicialização do Tawk.to -->
    <script type="text/javascript">
        var Tawk_API = Tawk_API || {}, Tawk_LoadStart = new Date();
        (function () {
            var s1 = document.createElement("script"), s0 = document.getElementsByTagName("script")[0];
            s1.async = true;
            s1.src = 'https://embed.tawk.to/66269f961ec1082f04e59b7a/1hs3dupor';
            s1.charset = 'UTF-8';
            s1.setAttribute('crossorigin', '*');
            s0.parentNode.insertBefore(s1, s0);
        })();
    </script>
            </div>
            <footer class="sticky-footer bg-white">
                <div class="container my-auto">
                    <div class="text-center my-auto">
                        <span>Desenvolvido por LFM_Consultoria</span>
                    </div>
                </div>
            </footer>
        </div>
    </div>
    <a class="scroll-to-top rounded" href="#page-top">
        <i class="fas fa-angle-up"></i>
    </a>
    <div class="modal fade" id="logoutModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
        <!-- ... (O código do modal permanece o mesmo) ... -->
    </div>
    <script src="vendor/jquery/jquery.min.js"></script>
    <script src="vendor/bootstrap/js/bootstrap.bundle.min.js"></script>
    <script src="vendor/jquery-easing/jquery.easing.min.js"></script>
    <script src="js/sb-admin-2.min.js"></script>
</body>
</html>