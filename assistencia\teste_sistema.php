<?php
/**
 * Teste do Sistema FixFácil
 * Verifica se todas as funcionalidades estão funcionando
 */

// Configurações
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🧪 Teste do Sistema FixFácil</h1>";
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; }
.success { color: green; }
.error { color: red; }
.warning { color: orange; }
.test { margin: 10px 0; padding: 10px; border-left: 4px solid #ccc; }
.test.success { border-color: green; background: #f0fff0; }
.test.error { border-color: red; background: #fff0f0; }
.test.warning { border-color: orange; background: #fff8f0; }
</style>";

$testes = [];

// Teste 1: Verificar PHP
$testes[] = [
    'nome' => 'Versão do PHP',
    'resultado' => version_compare(PHP_VERSION, '7.4.0', '>='),
    'mensagem' => 'PHP ' . PHP_VERSION . (version_compare(PHP_VERSION, '7.4.0', '>=') ? ' ✅' : ' ❌ (Requer 7.4+)')
];

// Teste 2: Extensões PHP
$extensoes = ['mysqli', 'json', 'session'];
foreach ($extensoes as $ext) {
    $testes[] = [
        'nome' => "Extensão $ext",
        'resultado' => extension_loaded($ext),
        'mensagem' => extension_loaded($ext) ? "✅ $ext carregada" : "❌ $ext não encontrada"
    ];
}

// Teste 3: Conexão com banco
try {
    require_once 'config/database.php';
    $db = getDatabase();
    $result = $db->query("SELECT 1");
    $testes[] = [
        'nome' => 'Conexão com Banco',
        'resultado' => true,
        'mensagem' => '✅ Conexão estabelecida'
    ];
} catch (Exception $e) {
    $testes[] = [
        'nome' => 'Conexão com Banco',
        'resultado' => false,
        'mensagem' => '❌ Erro: ' . $e->getMessage()
    ];
}

// Teste 4: Tabelas principais
$tabelas_principais = [
    'usuarios', 'assistencias_tecnicas', 'solicitacoes_reparo', 
    'propostas_assistencia', 'planos'
];

if (isset($db)) {
    foreach ($tabelas_principais as $tabela) {
        try {
            $result = $db->query("SELECT COUNT(*) FROM $tabela");
            $count = $result->fetch_row()[0];
            $testes[] = [
                'nome' => "Tabela $tabela",
                'resultado' => true,
                'mensagem' => "✅ $tabela ($count registros)"
            ];
        } catch (Exception $e) {
            $testes[] = [
                'nome' => "Tabela $tabela",
                'resultado' => false,
                'mensagem' => "❌ $tabela não encontrada"
            ];
        }
    }
}

// Teste 5: Tabelas avançadas
$tabelas_avancadas = [
    'produtos_marketplace', 'mensagens_chat', 'assistencias_virtuais',
    'logs_atividades', 'notificacoes'
];

if (isset($db)) {
    foreach ($tabelas_avancadas as $tabela) {
        try {
            $result = $db->query("SELECT COUNT(*) FROM $tabela");
            $count = $result->fetch_row()[0];
            $testes[] = [
                'nome' => "Tabela $tabela (Avançada)",
                'resultado' => true,
                'mensagem' => "✅ $tabela ($count registros)"
            ];
        } catch (Exception $e) {
            $testes[] = [
                'nome' => "Tabela $tabela (Avançada)",
                'resultado' => false,
                'mensagem' => "⚠️ $tabela não encontrada (Execute sql/tabelas_adicionais.sql)"
            ];
        }
    }
}

// Teste 6: Arquivos principais
$arquivos_principais = [
    'dashboard.php', 'solicitacoes.php', 'propostas.php', 'reparos.php',
    'carteira.php', 'perfil.php', 'config/auth.php', 'includes/layout.php'
];

foreach ($arquivos_principais as $arquivo) {
    $existe = file_exists($arquivo);
    $testes[] = [
        'nome' => "Arquivo $arquivo",
        'resultado' => $existe,
        'mensagem' => $existe ? "✅ $arquivo" : "❌ $arquivo não encontrado"
    ];
}

// Teste 7: Arquivos avançados
$arquivos_avancados = [
    'chat.php', 'marketplace.php', 'assistencia_virtual.php',
    'adicionar_produto.php', 'gerenciar_reparo.php'
];

foreach ($arquivos_avancados as $arquivo) {
    $existe = file_exists($arquivo);
    $testes[] = [
        'nome' => "Arquivo $arquivo (Avançado)",
        'resultado' => $existe,
        'mensagem' => $existe ? "✅ $arquivo" : "❌ $arquivo não encontrado"
    ];
}

// Teste 8: Diretórios
$diretorios = [
    'ajax', 'sistema', 'admin', 'sql', '../virtual'
];

foreach ($diretorios as $dir) {
    $existe = is_dir($dir);
    $testes[] = [
        'nome' => "Diretório $dir",
        'resultado' => $existe,
        'mensagem' => $existe ? "✅ $dir" : "❌ $dir não encontrado"
    ];
}

// Teste 9: Permissões
$diretorios_permissao = [
    '.' => 'Diretório principal',
    '../virtual' => 'Diretório virtual'
];

foreach ($diretorios_permissao as $dir => $nome) {
    if (is_dir($dir)) {
        $permissao = substr(sprintf('%o', fileperms($dir)), -4);
        $escrevivel = is_writable($dir);
        $testes[] = [
            'nome' => "Permissão $nome",
            'resultado' => $escrevivel,
            'mensagem' => "$nome: $permissao " . ($escrevivel ? "✅ Escrevível" : "⚠️ Não escrevível")
        ];
    }
}

// Teste 10: Configuração Apache
$mod_rewrite = function_exists('apache_get_modules') ? 
    in_array('mod_rewrite', apache_get_modules()) : 
    (isset($_SERVER['HTTP_MOD_REWRITE']) ? $_SERVER['HTTP_MOD_REWRITE'] === 'On' : null);

if ($mod_rewrite !== null) {
    $testes[] = [
        'nome' => 'mod_rewrite',
        'resultado' => $mod_rewrite,
        'mensagem' => $mod_rewrite ? '✅ mod_rewrite habilitado' : '⚠️ mod_rewrite não detectado'
    ];
}

// Exibir resultados
$total = count($testes);
$sucessos = 0;
$erros = 0;
$avisos = 0;

foreach ($testes as $teste) {
    $classe = $teste['resultado'] ? 'success' : (strpos($teste['mensagem'], '⚠️') !== false ? 'warning' : 'error');
    
    if ($classe === 'success') $sucessos++;
    elseif ($classe === 'error') $erros++;
    else $avisos++;
    
    echo "<div class='test $classe'>";
    echo "<strong>{$teste['nome']}:</strong> {$teste['mensagem']}";
    echo "</div>";
}

// Resumo
echo "<hr>";
echo "<h2>📊 Resumo dos Testes</h2>";
echo "<div class='test'>";
echo "<strong>Total:</strong> $total testes<br>";
echo "<strong class='success'>Sucessos:</strong> $sucessos<br>";
echo "<strong class='warning'>Avisos:</strong> $avisos<br>";
echo "<strong class='error'>Erros:</strong> $erros<br>";
echo "</div>";

// Recomendações
echo "<h2>💡 Recomendações</h2>";

if ($erros > 0) {
    echo "<div class='test error'>";
    echo "<strong>❌ Sistema com problemas!</strong><br>";
    echo "Corrija os erros antes de usar em produção.";
    echo "</div>";
} elseif ($avisos > 0) {
    echo "<div class='test warning'>";
    echo "<strong>⚠️ Sistema funcional com limitações!</strong><br>";
    echo "Algumas funcionalidades avançadas podem não funcionar.";
    echo "</div>";
} else {
    echo "<div class='test success'>";
    echo "<strong>✅ Sistema totalmente funcional!</strong><br>";
    echo "Todas as funcionalidades estão disponíveis.";
    echo "</div>";
}

// Próximos passos
echo "<h2>🚀 Próximos Passos</h2>";
echo "<ol>";
echo "<li>Se há erros de tabelas, execute: <code>sql/tabelas_adicionais.sql</code></li>";
echo "<li>Configure as credenciais do banco em: <code>config/database.php</code></li>";
echo "<li>Acesse o sistema: <a href='dashboard.php'>dashboard.php</a></li>";
echo "<li>Teste a assistência virtual: <a href='../virtual/'>../virtual/</a></li>";
echo "<li>Leia a documentação: <a href='INSTALACAO.md'>INSTALACAO.md</a></li>";
echo "</ol>";

echo "<hr>";
echo "<p><small>🔧 Teste executado em " . date('d/m/Y H:i:s') . "</small></p>";
?>
