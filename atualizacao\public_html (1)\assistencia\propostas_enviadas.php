<?php
// propostas_enviadas.php

// Ativar exibição de erros (Desative em produção)
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Iniciar a sessão
session_start();

// Verificar autenticação e autorização
if (!isset($_SESSION['usuario_id']) || $_SESSION['tipo_usuario'] != 'assistencia') {
    header('Location: ../login.php');
    exit();
}

$usuario_id = $_SESSION['usuario_id'];
$nome_usuario = $_SESSION['nome'];

// Conexão com o banco de dados usando mysqli com Prepared Statements
$servername = "localhost";
$username_db = "u680766645_fixfacilnew";
$password_db = "T3cn0l0g1a@";
$dbname = "u680766645_fixfacilnew";

// <PERSON><PERSON><PERSON> conex<PERSON>
$conn = new mysqli($servername, $username_db, $password_db, $dbname);

// Verificar conexão
if ($conn->connect_error) {
    die("Falha na conexão: " . $conn->connect_error);
}

$conn->set_charset("utf8");

// Obter o assistencia_id correspondente ao usuario_id
$sql_assistencia = "SELECT id FROM assistencias_tecnicas WHERE usuario_id = ?";
$stmt_assistencia = $conn->prepare($sql_assistencia);
$stmt_assistencia->bind_param("i", $usuario_id);
$stmt_assistencia->execute();
$result_assistencia = $stmt_assistencia->get_result();
if ($row_assistencia = $result_assistencia->fetch_assoc()) {
    $assistencia_id = $row_assistencia['id'];
} else {
    // Assistência técnica não encontrada
    die("Assistência técnica não encontrada.");
}
$stmt_assistencia->close();

// Inicializar variáveis para mensagens
$mensagem = "";
$tipo_alerta = "";

// Processar ações de mudança de status ou exclusão via AJAX
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['action'])) {
    header('Content-Type: application/json');
    $response = ['success' => false, 'message' => 'Ação inválida.'];

    // Obter e sanitizar dados
    $proposta_id = isset($_POST['proposta_id']) ? intval($_POST['proposta_id']) : 0;

    if ($proposta_id <= 0) {
        $response['message'] = 'ID da proposta inválido.';
        echo json_encode($response);
        exit;
    }

    switch ($_POST['action']) {
        case 'aceitar':
            // Código existente para aceitar a proposta
            $novo_status = 'aceita';
            $sql_update_status = "UPDATE propostas_assistencia SET status = ? WHERE id = ? AND assistencia_id = ?";
            $stmt_update = $conn->prepare($sql_update_status);
            $stmt_update->bind_param("sii", $novo_status, $proposta_id, $assistencia_id);
            if ($stmt_update->execute()) {
                if ($stmt_update->affected_rows > 0) {
                    $response['success'] = true;
                    $response['message'] = "Status da proposta atualizado para 'Aceita' com sucesso.";
                } else {
                    $response['message'] = "Nenhuma alteração realizada. Verifique se a proposta existe.";
                }
            } else {
                $response['message'] = "Erro ao atualizar o status da proposta.";
            }
            $stmt_update->close();
            break;

        case 'rejeitar':
            // Código existente para rejeitar a proposta
            $novo_status = 'rejeitada';
            $sql_update_status = "UPDATE propostas_assistencia SET status = ? WHERE id = ? AND assistencia_id = ?";
            $stmt_update = $conn->prepare($sql_update_status);
            $stmt_update->bind_param("sii", $novo_status, $proposta_id, $assistencia_id);
            if ($stmt_update->execute()) {
                if ($stmt_update->affected_rows > 0) {
                    $response['success'] = true;
                    $response['message'] = "Status da proposta atualizado para 'Rejeitada' com sucesso.";
                } else {
                    $response['message'] = "Nenhuma alteração realizada. Verifique se a proposta existe.";
                }
            } else {
                $response['message'] = "Erro ao atualizar o status da proposta.";
            }
            $stmt_update->close();
            break;

        case 'excluir':
            // Código existente para excluir a proposta
            $sql_delete_proposta = "DELETE FROM propostas_assistencia WHERE id = ? AND assistencia_id = ? AND status = 'enviada'";
            $stmt_delete = $conn->prepare($sql_delete_proposta);
            $stmt_delete->bind_param("ii", $proposta_id, $assistencia_id);
            if ($stmt_delete->execute()) {
                if ($stmt_delete->affected_rows > 0) {
                    $response['success'] = true;
                    $response['message'] = "Proposta excluída com sucesso.";
                } else {
                    $response['message'] = "Não é possível excluir esta proposta. Verifique se ela está no status 'Enviada'.";
                }
            } else {
                $response['message'] = "Erro ao excluir a proposta.";
            }
            $stmt_delete->close();
            break;

        case 'atualizar_status':
            // Novo código para atualizar o status da proposta
            $novo_status = isset($_POST['novo_status']) ? $_POST['novo_status'] : '';

            // Validar o novo status
            $status_permitidos = ['Em Andamento', 'concluída', 'rejeitada'];
            if (!in_array($novo_status, $status_permitidos)) {
                $response['message'] = 'Status inválido.';
                echo json_encode($response);
                exit;
            }

            // Atualizar o status no banco de dados
            $sql_update_status = "UPDATE propostas_assistencia SET status = ? WHERE id = ? AND assistencia_id = ?";
            $stmt_update = $conn->prepare($sql_update_status);
            $stmt_update->bind_param("sii", $novo_status, $proposta_id, $assistencia_id);
            if ($stmt_update->execute()) {
                if ($stmt_update->affected_rows > 0) {
                    $response['success'] = true;
                    $response['message'] = "Status da proposta atualizado para '$novo_status' com sucesso.";
                } else {
                    $response['message'] = "Nenhuma alteração realizada. Verifique se a proposta existe.";
                }
            } else {
                $response['message'] = "Erro ao atualizar o status da proposta.";
            }
            $stmt_update->close();
            break;

        default:
            $response['message'] = 'Ação desconhecida.';
            break;
    }

    echo json_encode($response);
    exit;
}

// Filtro de status (se existir)
$status_filtro = isset($_GET['status']) ? $_GET['status'] : '';
$where_status = "";
if (!empty($status_filtro)) {
    $where_status = " AND pa.status = '$status_filtro'";
}

// Obter as propostas enviadas pela assistência técnica, ordenando as aceitas no topo
$sql = "SELECT pa.id AS proposta_id, pa.preco, pa.prazo, pa.observacoes, pa.status, pa.data_proposta,
        sr.id AS solicitacao_id, sr.descricao_problema, sr.dispositivo, sr.marca, sr.modelo, u.nome AS nome_cliente
        FROM propostas_assistencia pa
        INNER JOIN solicitacoes_reparo sr ON pa.solicitacao_id = sr.id
        INNER JOIN usuarios u ON sr.usuario_id = u.id
        WHERE pa.assistencia_id = ? $where_status
        ORDER BY 
            CASE 
                WHEN pa.status = 'aceita' THEN 0 
                WHEN pa.status = 'Em Andamento' THEN 1 
                WHEN pa.status = 'rejeitada' THEN 2 
                ELSE 3 
            END, 
            pa.data_proposta DESC";
$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $assistencia_id);
$stmt->execute();
$result = $stmt->get_result();

// Contar propostas por status
$sql_count = "SELECT status, COUNT(*) as total FROM propostas_assistencia 
              WHERE assistencia_id = ? 
              GROUP BY status";
$stmt_count = $conn->prepare($sql_count);
$stmt_count->bind_param("i", $assistencia_id);
$stmt_count->execute();
$result_count = $stmt_count->get_result();

$propostas_por_status = [
    'enviada' => 0,
    'aceita' => 0,
    'Em Andamento' => 0,
    'rejeitada' => 0,
    'concluída' => 0
];

while ($row = $result_count->fetch_assoc()) {
    $propostas_por_status[$row['status']] = $row['total'];
}

$total_propostas = array_sum($propostas_por_status);
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <title>Propostas Enviadas - FixFácil</title>
    <!-- Meta Tags para Responsividade -->
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome 6 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- SweetAlert2 CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">
    <!-- DataTables CSS -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.4/css/dataTables.bootstrap5.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Estilos personalizados -->
    <style>
        :root {
            --primary-color: #2563eb;
            --primary-dark: #1d4ed8;
            --secondary-color: #475569;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --light-bg: #f8fafc;
            --card-bg: #ffffff;
            --text-color: #1e293b;
            --text-muted: #64748b;
            --border-radius: 12px;
            --shadow-sm: 0 1px 3px rgba(0,0,0,0.1);
            --shadow-md: 0 4px 6px -1px rgba(0,0,0,0.1), 0 2px 4px -1px rgba(0,0,0,0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0,0,0,0.1), 0 4px 6px -2px rgba(0,0,0,0.05);
        }
        
        body {
            font-family: 'Inter', sans-serif;
            background-color: var(--light-bg);
            color: var(--text-color);
            margin-bottom: 80px;
            padding-top: 70px;
        }
        
        /* Navbar */
        .navbar {
            background-color: var(--card-bg);
            box-shadow: var(--shadow-sm);
            padding: 12px 0;
        }
        
        .navbar-brand {
            font-weight: 700;
            color: var(--primary-color) !important;
        }
        
        .navbar .nav-link {
            color: var(--text-color) !important;
            font-weight: 500;
            padding: 8px 16px;
            border-radius: var(--border-radius);
            transition: all 0.3s ease;
        }
        
        .navbar .nav-link:hover {
            background-color: rgba(37, 99, 235, 0.1);
            color: var(--primary-color) !important;
        }
        
        .navbar .nav-link.active {
            background-color: var(--primary-color);
            color: white !important;
        }
        
        /* Conteúdo Principal */
        .main-content {
            padding: 20px 12px;
        }
        
        .header-section {
            margin-bottom: 24px;
        }
        
        .header-section h1 {
            font-weight: 700;
            font-size: 1.75rem;
            margin-bottom: 8px;
            color: var(--text-color);
        }
        
        .header-section p {
            color: var(--text-muted);
            font-size: 1rem;
            margin-bottom: 0;
        }
        
        /* Cards de resumo */
        .stats-card {
            background-color: var(--card-bg);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-md);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            padding: 20px;
            height: 100%;
            position: relative;
            overflow: hidden;
        }
        
        .stats-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-lg);
        }
        
        .stats-card .card-icon {
            position: absolute;
            top: 15px;
            right: 15px;
            font-size: 2.5rem;
            opacity: 0.2;
            color: inherit;
        }
        
        .stats-card .card-title {
            font-weight: 600;
            font-size: 0.9rem;
            margin-bottom: 10px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .stats-card .card-value {
            font-weight: 700;
            font-size: 2rem;
            margin-bottom: 0;
        }
        
        .status-enviada { color: var(--warning-color); }
        .status-aceita { color: var(--success-color); }
        .status-andamento { color: var(--primary-color); }
        .status-rejeitada { color: var(--danger-color); }
        .status-total { color: var(--secondary-color); }
        
        /* Filtros */
        .filtros-container {
            background-color: var(--card-bg);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-md);
            padding: 16px;
            margin-bottom: 20px;
        }
        
        .filtro-btn {
            border: 1px solid rgba(0, 0, 0, 0.1);
            border-radius: 30px;
            padding: 8px 16px;
            font-weight: 500;
            transition: all 0.3s ease;
            margin-right: 8px;
            margin-bottom: 8px;
            background-color: #f8f9fa;
        }
        
        .filtro-btn:hover {
            background-color: #e9ecef;
        }
        
        .filtro-btn.active {
            background-color: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }
        
        /* Tabela de propostas */
        .tabela-container {
            background-color: var(--card-bg);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-md);
            padding: 20px;
            margin-bottom: 30px;
            overflow: hidden;
        }
        
        .table-striped > tbody > tr:nth-of-type(odd) > * {
            background-color: rgba(0, 0, 0, 0.02);
        }
        
        .table > :not(caption) > * > * {
            padding: 12px 16px;
        }
        
        .status-badge {
            padding: 6px 12px;
            border-radius: 50px;
            font-weight: 600;
            font-size: 0.75rem;
            text-transform: uppercase;
        }
        
        .status-badge-enviada {
            background-color: rgba(245, 158, 11, 0.1);
            color: var(--warning-color);
        }
        
        .status-badge-aceita {
            background-color: rgba(16, 185, 129, 0.1);
            color: var(--success-color);
        }
        
        .status-badge-andamento {
            background-color: rgba(37, 99, 235, 0.1);
            color: var(--primary-color);
        }
        
        .status-badge-rejeitada {
            background-color: rgba(239, 68, 68, 0.1);
            color: var(--danger-color);
        }
        
        .status-badge-concluida {
            background-color: rgba(75, 85, 99, 0.1);
            color: var(--text-muted);
        }
        
        /* Botões de ação */
        .action-btn {
            padding: 6px 12px;
            border-radius: var(--border-radius);
            font-weight: 500;
            font-size: 0.8rem;
            margin-right: 5px;
            margin-bottom: 5px;
            display: inline-flex;
            align-items: center;
        }
        
        .action-btn i {
            margin-right: 5px;
        }
        
        /* Modal */
        .modal-content {
            border-radius: var(--border-radius);
            border: none;
            box-shadow: var(--shadow-lg);
        }
        
        .modal-header {
            background-color: var(--primary-color);
            color: white;
            border-top-left-radius: var(--border-radius);
            border-top-right-radius: var(--border-radius);
            border-bottom: none;
            padding: 20px;
        }
        
        .modal-title {
            font-weight: 600;
        }
        
        .modal-header .btn-close {
            color: white;
            background: transparent url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='white'%3e%3cpath d='M.293.293a1 1 0 011.414 0L8 6.586 14.293.293a1 1 0 111.414 1.414L9.414 8l6.293 6.293a1 1 0 01-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 01-1.414-1.414L6.586 8 .293 1.707a1 1 0 010-1.414z'/%3e%3c/svg%3e") center/1em auto no-repeat;
        }
        
        /* Mobile Menu */
        .mobile-menu {
            position: fixed;
            bottom: 0;
            width: 100%;
            background-color: var(--card-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            z-index: 1000;
            padding: 8px 0;
            display: flex;
            justify-content: space-around;
        }
        
        .mobile-menu .menu-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 8px 0;
            width: 20%;
            text-decoration: none;
        }
        
        .mobile-menu .menu-item i {
            font-size: 20px;
            margin-bottom: 4px;
            color: var(--text-muted);
            transition: color 0.3s ease;
        }
        
        .mobile-menu .menu-item span {
            font-size: 12px;
            color: var(--text-muted);
            transition: color 0.3s ease;
        }
        
        .mobile-menu .menu-item.active i,
        .mobile-menu .menu-item.active span {
            color: var(--primary-color);
            font-weight: 600;
        }
        
        .mobile-menu .menu-item:hover i,
        .mobile-menu .menu-item:hover span {
            color: var(--primary-dark);
        }
        
        /* Esconder menu mobile em desktop */
        @media (min-width: 992px) {
            .mobile-menu {
                display: none;
            }
        }
        
        /* Ajustes para mobile */
        @media (max-width: 767px) {
            .main-content {
                padding: 15px 10px;
            }
            
            .stats-card {
                margin-bottom: 15px;
            }
            
            .filtro-btn {
                width: 100%;
                margin-right: 0;
                margin-bottom: 10px;
                text-align: center;
            }
            
            .action-btn {
                width: 100%;
                margin-right: 0;
                margin-bottom: 8px;
                justify-content: center;
            }
            
            .tabela-container {
                padding: 15px 10px;
            }
            
            .table > :not(caption) > * > * {
                padding: 10px 12px;
            }
            
            .modal-dialog {
                margin: 10px;
            }
        }

        /* Botão de nova proposta flutuante */
        .btn-float {
            position: fixed;
            bottom: 80px;
            right: 20px;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
            z-index: 999;
        }
        
        /* Detalhes da proposta */
        .detalhe-proposta {
            padding: 15px;
            background-color: rgba(0, 0, 0, 0.02);
            border-radius: 8px;
            margin-top: 10px;
        }
        
        .detalhe-proposta p {
            margin-bottom: 8px;
        }
        
        .detalhe-item {
            display: flex;
            margin-bottom: 5px;
        }
        
        .detalhe-label {
            font-weight: 600;
            min-width: 120px;
        }
        
        .detalhe-valor {
            flex: 1;
        }
        
        /* Alerta personalizado */
        .custom-alert {
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 24px;
            border-left: 4px solid;
        }
        
        .alert-success {
            background-color: rgba(16, 185, 129, 0.1);
            border-color: var(--success-color);
            color: #065f46;
        }
        
        .alert-danger {
            background-color: rgba(239, 68, 68, 0.1);
            border-color: var(--danger-color);
            color: #b91c1c;
        }
        
        .alert-info {
            background-color: rgba(37, 99, 235, 0.1);
            border-color: var(--primary-color);
            color: #1e40af;
        }
        
        /* Placeholder para sem dados */
        .sem-dados {
            text-align: center;
            padding: 40px 20px;
        }
        
        .sem-dados i {
            font-size: 3rem;
            color: var(--text-muted);
            margin-bottom: 15px;
        }
        
        .sem-dados h4 {
            font-weight: 600;
            margin-bottom: 10px;
        }
        
        .sem-dados p {
            color: var(--text-muted);
            max-width: 500px;
            margin: 0 auto;
        }
    </style>
</head>
<body>
    <!-- Overlay de carregamento -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="spinner"></div>
    </div>

    <!-- Cabeçalho (Navbar) -->
    <nav class="navbar navbar-expand-lg fixed-top navbar-light">
        <div class="container">
            <a class="navbar-brand" href="#">FixFácil</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" 
                    aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="home.php">Painel</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="solicitacoes.php">Solicitações</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="propostas_enviadas.php">Propostas</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="reparos_em_andamento.php">Reparos</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="meumarktplace.php">Marketplace</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="solicitar_pecas.php">Peças</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="carteira.php">Carteira</a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-user-circle"></i>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="perfil.php">Meu Perfil</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../logout.php">Sair</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Conteúdo Principal -->
    <div class="container main-content">
        <!-- Cabeçalho da página -->
        <div class="header-section">
            <h1>Propostas Enviadas</h1>
            <p>Acompanhe e gerencie as propostas enviadas aos clientes.</p>
        </div>

        <!-- Exibir Mensagens -->
        <?php if (!empty($mensagem)): ?>
            <div class="custom-alert alert-<?php echo $tipo_alerta; ?>" role="alert">
                <?php echo htmlspecialchars($mensagem); ?>
            </div>
        <?php endif; ?>

        <!-- Cards de Resumo -->
        <div class="row mb-4">
            <div class="col-lg col-md-6 mb-3">
                <div class="stats-card">
                    <i class="fas fa-paper-plane card-icon status-enviada"></i>
                    <div class="card-title status-enviada">Enviadas</div>
                    <div class="card-value status-enviada"><?php echo $propostas_por_status['enviada']; ?></div>
                </div>
            </div>
            <div class="col-lg col-md-6 mb-3">
                <div class="stats-card">
                    <i class="fas fa-check-circle card-icon status-aceita"></i>
                    <div class="card-title status-aceita">Aceitas</div>
                    <div class="card-value status-aceita"><?php echo $propostas_por_status['aceita']; ?></div>
                </div>
            </div>
            <div class="col-lg col-md-6 mb-3">
                <div class="stats-card">
                    <i class="fas fa-tools card-icon status-andamento"></i>
                    <div class="card-title status-andamento">Em Andamento</div>
                    <div class="card-value status-andamento"><?php echo $propostas_por_status['Em Andamento']; ?></div>
                </div>
            </div>
            <div class="col-lg col-md-6 mb-3">
                <div class="stats-card">
                    <i class="fas fa-times-circle card-icon status-rejeitada"></i>
                    <div class="card-title status-rejeitada">Rejeitadas</div>
                    <div class="card-value status-rejeitada"><?php echo $propostas_por_status['rejeitada']; ?></div>
                </div>
            </div>
            <div class="col-lg col-md-12 mb-3">
                <div class="stats-card">
                    <i class="fas fa-chart-bar card-icon status-total"></i>
                    <div class="card-title status-total">Total</div>
                    <div class="card-value status-total"><?php echo $total_propostas; ?></div>
                </div>
            </div>
        </div>

        <!-- Filtros -->
        <div class="filtros-container mb-4">
            <div class="row">
                <div class="col-md-8">
                    <h5 class="mb-3">Filtrar por Status</h5>
                    <div class="d-flex flex-wrap">
                        <a href="propostas_enviadas.php" class="filtro-btn <?php echo empty($status_filtro) ? 'active' : ''; ?>">
                            Todas
                        </a>
                        <a href="propostas_enviadas.php?status=enviada" class="filtro-btn <?php echo $status_filtro == 'enviada' ? 'active' : ''; ?>">
                            <i class="fas fa-paper-plane me-1"></i> Enviadas
                        </a>
                        <a href="propostas_enviadas.php?status=aceita" class="filtro-btn <?php echo $status_filtro == 'aceita' ? 'active' : ''; ?>">
                            <i class="fas fa-check-circle me-1"></i> Aceitas
                        </a>
                        <a href="propostas_enviadas.php?status=Em Andamento" class="filtro-btn <?php echo $status_filtro == 'Em Andamento' ? 'active' : ''; ?>">
                            <i class="fas fa-tools me-1"></i> Em Andamento
                        </a>
                        <a href="propostas_enviadas.php?status=rejeitada" class="filtro-btn <?php echo $status_filtro == 'rejeitada' ? 'active' : ''; ?>">
                            <i class="fas fa-times-circle me-1"></i> Rejeitadas
                        </a>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="input-group mt-3 mt-md-4">
                        <input type="text" class="form-control" id="searchInput" placeholder="Buscar proposta...">
                        <button class="btn btn-outline-secondary" type="button">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Lista de Propostas -->
        <div class="tabela-container">
            <div class="table-responsive">
                <table class="table table-striped" id="tabela-propostas">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Cliente</th>
                            <th>Dispositivo</th>
                            <th>Preço</th>
                            <th>Prazo</th>
                            <th>Status</th>
                            <th>Data</th>
                            <th>Ações</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if ($result->num_rows > 0): ?>
                            <?php while ($proposta = $result->fetch_assoc()): ?>
                                <tr id="proposta-<?php echo $proposta['proposta_id']; ?>">
                                    <td>#<?php echo $proposta['proposta_id']; ?></td>
                                    <td><?php echo htmlspecialchars($proposta['nome_cliente']); ?></td>
                                    <td>
                                        <?php echo htmlspecialchars($proposta['dispositivo']); ?><br>
                                        <small class="text-muted"><?php echo htmlspecialchars($proposta['marca'] . ' ' . $proposta['modelo']); ?></small>
                                    </td>
                                    <td><strong>R$ <?php echo number_format($proposta['preco'], 2, ',', '.'); ?></strong></td>
                                    <td><?php echo htmlspecialchars($proposta['prazo']); ?> dias</td>
                                    <td>
                                        <?php
                                        $status = $proposta['status'];
                                        $status_class = '';
                                        $status_text = '';
                                        
                                        switch ($status) {
                                            case 'enviada':
                                                $status_class = 'status-badge-enviada';
                                                $status_text = 'Enviada';
                                                break;
                                            case 'aceita':
                                                $status_class = 'status-badge-aceita';
                                                $status_text = 'Aceita';
                                                break;
                                            case 'Em Andamento':
                                                $status_class = 'status-badge-andamento';
                                                $status_text = 'Em Andamento';
                                                break;
                                            case 'concluída':
                                                $status_class = 'status-badge-concluida';
                                                $status_text = 'Concluída';
                                                break;
                                            case 'rejeitada':
                                                $status_class = 'status-badge-rejeitada';
                                                $status_text = 'Rejeitada';
                                                break;
                                            default:
                                                $status_class = '';
                                                $status_text = $status;
                                                break;
                                        }
                                        ?>
                                        <span class="status-badge <?php echo $status_class; ?>"><?php echo $status_text; ?></span>
                                    </td>
                                    <td><?php echo date('d/m/Y H:i', strtotime($proposta['data_proposta'])); ?></td>
                                    <td>
                                        <button class="btn btn-info action-btn btn-detalhes" data-id="<?php echo $proposta['proposta_id']; ?>" data-bs-toggle="modal" data-bs-target="#modalDetalhes" data-solicitacao="<?php echo $proposta['solicitacao_id']; ?>">
                                            <i class="fas fa-eye"></i> Detalhes
                                        </button>
                                        
                                        <?php if ($status == 'enviada'): ?>
                                            <button class="btn btn-success action-btn btn-aceitar" data-id="<?php echo $proposta['proposta_id']; ?>">
                                                <i class="fas fa-check-circle"></i> Aceitar
                                            </button>
                                            <button class="btn btn-danger action-btn btn-rejeitar" data-id="<?php echo $proposta['proposta_id']; ?>">
                                                <i class="fas fa-times-circle"></i> Rejeitar
                                            </button>
                                            <button class="btn btn-secondary action-btn btn-excluir" data-id="<?php echo $proposta['proposta_id']; ?>">
                                                <i class="fas fa-trash-alt"></i> Excluir
                                            </button>
                                        <?php elseif ($status == 'aceita' || $status == 'Em Andamento'): ?>
                                            <button class="btn btn-primary action-btn btn-atualizar-status" data-id="<?php echo $proposta['proposta_id']; ?>">
                                                <i class="fas fa-edit"></i> Atualizar Status
                                            </button>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endwhile; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="8">
                                    <div class="sem-dados">
                                        <i class="fas fa-inbox"></i>
                                        <h4>Nenhuma proposta encontrada</h4>
                                        <p>Não há propostas que correspondam aos filtros selecionados. Tente outros filtros ou envie novas propostas.</p>
                                    </div>
                                </td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Modal para Detalhes da Proposta -->
    <div class="modal fade" id="modalDetalhes" tabindex="-1" aria-labelledby="modalDetalhesLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="modalDetalhesLabel">Detalhes da Proposta</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Fechar"></button>
                </div>
                <div class="modal-body">
                    <div class="text-center p-4" id="loading-detalhes">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Carregando...</span>
                        </div>
                        <p class="mt-2">Carregando detalhes...</p>
                    </div>
                    <div id="detalhes-conteudo" style="display: none;">
                        <!-- O conteúdo será preenchido via JavaScript -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fechar</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal para Atualizar Status -->
    <div class="modal fade" id="modalAtualizarStatus" tabindex="-1" aria-labelledby="modalAtualizarStatusLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <form id="formAtualizarStatus">
                    <div class="modal-header">
                        <h5 class="modal-title" id="modalAtualizarStatusLabel">Atualizar Status da Proposta</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Fechar"></button>
                    </div>
                    <div class="modal-body">
                        <input type="hidden" name="proposta_id" id="atualizar_proposta_id" value="">
                        <div class="mb-3">
                            <label for="novo_status" class="form-label">Novo Status</label>
                            <select class="form-select" id="novo_status" name="novo_status" required>
                                <option value="" disabled selected>Selecione o novo status</option>
                                <option value="Em Andamento">Em Andamento</option>
                                <option value="concluída">Concluída</option>
                                <option value="rejeitada">Rejeitada</option>
                            </select>
                        </div>
                        <div id="atualizar-feedback" class="text-danger"></div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                        <button type="submit" class="btn btn-primary">Atualizar</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Botão flutuante para nova proposta -->
    <a href="solicitacoes.php" class="btn btn-primary btn-float d-md-none">
        <i class="fas fa-plus"></i>
    </a>

    <!-- Menu Mobile (fixo na parte inferior) -->
    <div class="mobile-menu d-lg-none">
        <a href="home.php" class="menu-item">
            <i class="fas fa-home"></i>
            <span>Painel</span>
        </a>
        <a href="solicitacoes.php" class="menu-item">
            <i class="fas fa-inbox"></i>
            <span>Solicitações</span>
        </a>
        <a href="propostas_enviadas.php" class="menu-item active">
            <i class="fas fa-paper-plane"></i>
            <span>Propostas</span>
        </a>
        <a href="reparos_em_andamento.php" class="menu-item">
            <i class="fas fa-tools"></i>
            <span>Reparos</span>
        </a>
        <a href="perfil.php" class="menu-item">
            <i class="fas fa-user"></i>
            <span>Perfil</span>
        </a>
    </div>

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="https://cdn.datatables.net/1.13.4/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.4/js/dataTables.bootstrap5.min.js"></script>
    <script>
        $(document).ready(function() {
            // Inicializar DataTables
            const table = $('#tabela-propostas').DataTable({
                language: {
                    url: '//cdn.datatables.net/plug-ins/1.13.4/i18n/pt-BR.json',
                },
                responsive: true,
                paging: true,
                ordering: true,
                info: true,
                pageLength: 10,
                lengthMenu: [5, 10, 25, 50],
                dom: '<"top"f>rt<"bottom"ilp><"clear">',
                columnDefs: [
                    { orderable: false, targets: -1 } // Desativar ordenação na coluna de ações
                ]
            });
            
            // Busca na tabela
            $('#searchInput').on('keyup', function() {
                table.search(this.value).draw();
            });

            // Função para mostrar overlay de carregamento
            function showLoading() {
                $('#loadingOverlay').addClass('show');
            }

            // Função para esconder overlay de carregamento
            function hideLoading() {
                $('#loadingOverlay').removeClass('show');
            }

            // Função para aceitar proposta
            $('.btn-aceitar').on('click', function() {
                var propostaId = $(this).data('id');
                Swal.fire({
                    title: 'Tem certeza?',
                    text: "Você está prestes a aceitar esta proposta.",
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#3085d6',
                    cancelButtonColor: '#d33',
                    confirmButtonText: 'Sim, aceitar!',
                    cancelButtonText: 'Cancelar'
                }).then((result) => {
                    if (result.isConfirmed) {
                        atualizarStatusProposta(propostaId, 'aceitar');
                    }
                });
            });

            // Função para rejeitar proposta
            $('.btn-rejeitar').on('click', function() {
                var propostaId = $(this).data('id');
                Swal.fire({
                    title: 'Tem certeza?',
                    text: "Você está prestes a rejeitar esta proposta.",
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#d33',
                    cancelButtonColor: '#3085d6',
                    confirmButtonText: 'Sim, rejeitar!',
                    cancelButtonText: 'Cancelar'
                }).then((result) => {
                    if (result.isConfirmed) {
                        atualizarStatusProposta(propostaId, 'rejeitar');
                    }
                });
            });

            // Função para excluir proposta
            $('.btn-excluir').on('click', function() {
                var propostaId = $(this).data('id');
                Swal.fire({
                    title: 'Tem certeza?',
                    text: "Você está prestes a excluir esta proposta. Esta ação não pode ser desfeita.",
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#d33',
                    cancelButtonColor: '#3085d6',
                    confirmButtonText: 'Sim, excluir!',
                    cancelButtonText: 'Cancelar'
                }).then((result) => {
                    if (result.isConfirmed) {
                        excluirProposta(propostaId);
                    }
                });
            });

            // Função para atualizar status via AJAX
            function atualizarStatusProposta(propostaId, acao) {
                showLoading();
                $.ajax({
                    url: 'propostas_enviadas.php',
                    type: 'POST',
                    data: {
                        action: acao,
                        proposta_id: propostaId
                    },
                    dataType: 'json',
                    success: function(response) {
                        hideLoading();
                        if (response.success) {
                            Swal.fire({
                                icon: 'success',
                                title: 'Sucesso!',
                                text: response.message,
                                timer: 2000,
                                timerProgressBar: true
                            }).then(() => {
                                // Atualizar a página para mostrar as mudanças
                                location.reload();
                            });
                        } else {
                            Swal.fire({
                                icon: 'error',
                                title: 'Erro!',
                                text: response.message
                            });
                        }
                    },
                    error: function() {
                        hideLoading();
                        Swal.fire({
                            icon: 'error',
                            title: 'Erro!',
                            text: 'Ocorreu um erro ao processar a ação. Tente novamente mais tarde.'
                        });
                    }
                });
            }

            // Função para excluir proposta via AJAX
            function excluirProposta(propostaId) {
                showLoading();
                $.ajax({
                    url: 'propostas_enviadas.php',
                    type: 'POST',
                    data: {
                        action: 'excluir',
                        proposta_id: propostaId
                    },
                    dataType: 'json',
                    success: function(response) {
                        hideLoading();
                        if (response.success) {
                            Swal.fire({
                                icon: 'success',
                                title: 'Excluído!',
                                text: response.message,
                                timer: 2000,
                                timerProgressBar: true
                            }).then(() => {
                                // Remover a linha da tabela e atualizar os contadores
                                $('#proposta-' + propostaId).fadeOut(300, function() {
                                    table.row($(this)).remove().draw();
                                    location.reload(); // Recarregar para atualizar os contadores
                                });
                            });
                        } else {
                            Swal.fire({
                                icon: 'error',
                                title: 'Erro!',
                                text: response.message
                            });
                        }
                    },
                    error: function() {
                        hideLoading();
                        Swal.fire({
                            icon: 'error',
                            title: 'Erro!',
                            text: 'Ocorreu um erro ao excluir a proposta. Tente novamente mais tarde.'
                        });
                    }
                });
            }

            // Função para mostrar detalhes da proposta
            $('.btn-detalhes').on('click', function() {
                const propostaId = $(this).data('id');
                const solicitacaoId = $(this).data('solicitacao');
                
                // Mostrar loader e esconder conteúdo anterior
                $('#loading-detalhes').show();
                $('#detalhes-conteudo').hide();
                
                // Obter detalhes da proposta e da solicitação
                $.ajax({
                    url: 'get_detalhes_proposta.php',
                    type: 'POST',
                    data: {
                        proposta_id: propostaId,
                        solicitacao_id: solicitacaoId
                    },
                    dataType: 'json',
                    success: function(response) {
                        $('#loading-detalhes').hide();
                        
                        if (response.success) {
                            const dados = response.data;
                            let html = `
                            <div class="row">
                                <div class="col-md-6">
                                    <h5>Informações da Proposta</h5>
                                    <div class="detalhe-proposta">
                                        <div class="detalhe-item">
                                            <div class="detalhe-label">ID da Proposta:</div>
                                            <div class="detalhe-valor">#${dados.proposta_id}</div>
                                        </div>
                                        <div class="detalhe-item">
                                            <div class="detalhe-label">Preço:</div>
                                            <div class="detalhe-valor">R$ ${dados.preco}</div>
                                        </div>
                                        <div class="detalhe-item">
                                            <div class="detalhe-label">Prazo:</div>
                                            <div class="detalhe-valor">${dados.prazo} dias</div>
                                        </div>
                                        <div class="detalhe-item">
                                            <div class="detalhe-label">Status:</div>
                                            <div class="detalhe-valor">
                                                <span class="status-badge ${dados.status_class}">${dados.status_text}</span>
                                            </div>
                                        </div>
                                        <div class="detalhe-item">
                                            <div class="detalhe-label">Data de Envio:</div>
                                            <div class="detalhe-valor">${dados.data_proposta}</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <h5>Informações do Cliente e Dispositivo</h5>
                                    <div class="detalhe-proposta">
                                        <div class="detalhe-item">
                                            <div class="detalhe-label">Cliente:</div>
                                            <div class="detalhe-valor">${dados.nome_cliente}</div>
                                        </div>
                                        <div class="detalhe-item">
                                            <div class="detalhe-label">Dispositivo:</div>
                                            <div class="detalhe-valor">${dados.dispositivo}</div>
                                        </div>
                                        <div class="detalhe-item">
                                            <div class="detalhe-label">Marca/Modelo:</div>
                                            <div class="detalhe-valor">${dados.marca} ${dados.modelo}</div>
                                        </div>
                                        <div class="detalhe-item">
                                            <div class="detalhe-label">Método Entrega:</div>
                                            <div class="detalhe-valor">${dados.metodo_entrega}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row mt-3">
                                <div class="col-12">
                                    <h5>Observações</h5>
                                    <div class="detalhe-proposta">
                                        <p>${dados.observacoes}</p>
                                    </div>
                                </div>
                            </div>
                            <div class="row mt-3">
                                <div class="col-12">
                                    <h5>Problema Relatado</h5>
                                    <div class="detalhe-proposta">
                                        <p>${dados.descricao_problema}</p>
                                    </div>
                                </div>
                            </div>`;
                            
                            $('#detalhes-conteudo').html(html).fadeIn();
                        } else {
                            $('#detalhes-conteudo').html(`
                                <div class="alert alert-danger">
                                    <i class="fas fa-exclamation-circle me-2"></i> 
                                    ${response.message || 'Erro ao carregar detalhes da proposta.'}
                                </div>
                            `).fadeIn();
                        }
                    },
                    error: function() {
                        $('#loading-detalhes').hide();
                        $('#detalhes-conteudo').html(`
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-circle me-2"></i> 
                                Ocorreu um erro ao buscar os detalhes da proposta. Tente novamente mais tarde.
                            </div>
                        `).fadeIn();
                    }
                });
            });

            // Função para abrir modal de atualizar status
            $('.btn-atualizar-status').on('click', function() {
                const propostaId = $(this).data('id');
                $('#atualizar_proposta_id').val(propostaId);
                $('#novo_status').val('');
                $('#atualizar-feedback').text('');
                $('#modalAtualizarStatus').modal('show');
            });

            // Processar formulário de atualizar status
            $('#formAtualizarStatus').on('submit', function(e) {
                e.preventDefault();
                
                const propostaId = $('#atualizar_proposta_id').val();
                const novoStatus = $('#novo_status').val();

                if (!novoStatus) {
                    $('#atualizar-feedback').text('Por favor, selecione um novo status.');
                    return;
                }

                showLoading();
                $.ajax({
                    url: 'propostas_enviadas.php',
                    type: 'POST',
                    data: {
                        action: 'atualizar_status',
                        proposta_id: propostaId,
                        novo_status: novoStatus
                    },
                    dataType: 'json',
                    success: function(response) {
                        hideLoading();
                        if (response.success) {
                            $('#modalAtualizarStatus').modal('hide');
                            Swal.fire({
                                icon: 'success',
                                title: 'Sucesso!',
                                text: response.message,
                                timer: 2000,
                                timerProgressBar: true
                            }).then(() => {
                                location.reload();
                            });
                        } else {
                            $('#atualizar-feedback').text(response.message);
                        }
                    },
                    error: function() {
                        hideLoading();
                        $('#atualizar-feedback').text('Ocorreu um erro ao atualizar o status. Tente novamente mais tarde.');
                    }
                });
            });
        });
    </script>
</body>
</html>