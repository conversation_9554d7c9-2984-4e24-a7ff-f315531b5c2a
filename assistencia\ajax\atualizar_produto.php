<?php
/**
 * AJAX - Atualizar Produto do Marketplace
 * FixFácil Assistências - Sistema Novo
 */

header('Content-Type: application/json');

require_once '../config/auth.php';
require_once '../config/database.php';

// Verificar autenticação
$auth = getAuth();
try {
    $auth->checkAssistenciaAuth();
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'Não autorizado']);
    exit();
}

// Verificar acesso ao marketplace
if (!$auth->hasAccess('marketplace')) {
    echo json_encode(['success' => false, 'message' => 'Acesso negado ao marketplace']);
    exit();
}

// Obter dados do usuário
$usuario = $auth->getUsuarioLogado();
$db = getDatabase();

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Método não permitido']);
    exit();
}

$produto_id = $_POST['produto_id'] ?? 0;
$status = $_POST['status'] ?? '';

// Validações
if (!$produto_id || empty($status)) {
    echo json_encode(['success' => false, 'message' => 'Dados obrigatórios não fornecidos']);
    exit();
}

$status_permitidos = ['ativo', 'pausado', 'esgotado', 'excluido'];
if (!in_array($status, $status_permitidos)) {
    echo json_encode(['success' => false, 'message' => 'Status inválido']);
    exit();
}

try {
    // Verificar se o produto pertence à assistência
    $sql = "SELECT id, nome, status FROM produtos_marketplace WHERE id = ? AND assistencia_id = ?";
    $result = $db->query($sql, [$produto_id, $usuario['assistencia_id']]);
    $produto = $result->fetch_assoc();
    
    if (!$produto) {
        echo json_encode(['success' => false, 'message' => 'Produto não encontrado']);
        exit();
    }
    
    // Se for exclusão, verificar se há vendas pendentes
    if ($status === 'excluido') {
        $sql = "SELECT COUNT(*) as vendas_pendentes FROM vendas_marketplace WHERE produto_id = ? AND status IN ('pendente', 'processando')";
        $result = $db->query($sql, [$produto_id]);
        $vendas = $result->fetch_assoc();
        
        if ($vendas['vendas_pendentes'] > 0) {
            echo json_encode(['success' => false, 'message' => 'Não é possível excluir produto com vendas pendentes']);
            exit();
        }
    }
    
    // Atualizar status do produto
    if ($status === 'excluido') {
        // Exclusão lógica
        $sql = "UPDATE produtos_marketplace SET status = 'excluido', data_exclusao = NOW() WHERE id = ?";
        $db->query($sql, [$produto_id]);
        $acao = 'excluído';
    } else {
        $sql = "UPDATE produtos_marketplace SET status = ? WHERE id = ?";
        $db->query($sql, [$status, $produto_id]);
        $acao = $status === 'ativo' ? 'ativado' : ($status === 'pausado' ? 'pausado' : 'atualizado');
    }
    
    // Registrar atividade
    try {
        $sql = "
            INSERT INTO logs_atividades (usuario_id, tipo, descricao, data_atividade)
            VALUES (?, 'produto_marketplace', ?, NOW())
        ";
        $descricao = "Produto '{$produto['nome']}' foi {$acao}";
        $db->query($sql, [$usuario['id'], $descricao]);
    } catch (Exception $e) {
        // Log opcional
    }
    
    // Se produto foi pausado/excluído, notificar compradores com carrinho
    if (in_array($status, ['pausado', 'excluido'])) {
        try {
            notificarCompradores($produto_id, $status, $db);
        } catch (Exception $e) {
            // Notificação opcional
        }
    }
    
    echo json_encode([
        'success' => true,
        'message' => "Produto {$acao} com sucesso",
        'novo_status' => $status
    ]);
    
} catch (Exception $e) {
    error_log("Erro ao atualizar produto: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Erro interno do servidor']);
}

/**
 * Notificar compradores que têm o produto no carrinho
 */
function notificarCompradores($produto_id, $status, $db) {
    // Buscar usuários que têm o produto no carrinho
    $sql = "
        SELECT DISTINCT u.id, u.nome, p.nome as produto_nome
        FROM carrinho_marketplace c
        JOIN usuarios u ON c.usuario_id = u.id
        JOIN produtos_marketplace p ON c.produto_id = p.id
        WHERE c.produto_id = ?
    ";
    
    $result = $db->query($sql, [$produto_id]);
    
    while ($row = $result->fetch_assoc()) {
        $titulo = "Produto Indisponível";
        $mensagem = $status === 'excluido' 
            ? "O produto '{$row['produto_nome']}' foi removido do marketplace e foi retirado do seu carrinho."
            : "O produto '{$row['produto_nome']}' foi pausado temporariamente.";
        
        // Inserir notificação
        $sql_notif = "
            INSERT INTO notificacoes (usuario_id, tipo, titulo, mensagem, data_criacao)
            VALUES (?, 'produto_indisponivel', ?, ?, NOW())
        ";
        $db->query($sql_notif, [$row['id'], $titulo, $mensagem]);
    }
    
    // Remover produto do carrinho se foi excluído
    if ($status === 'excluido') {
        $sql = "DELETE FROM carrinho_marketplace WHERE produto_id = ?";
        $db->query($sql, [$produto_id]);
    }
}
?>
