<?php
session_start();
require_once 'db.php';

if (!isset($_SESSION['user_id']) || $_SESSION['nivel_acesso'] != 'fornecedor') {
    header("Location: login.php");
    exit();
}

if(isset($_GET['logout']) && $_GET['logout'] == 'true') {
    session_destroy();
    header("Location: login.php");
    exit();
}

// Excluir produto, se solicitado
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['excluir_produto'])) {
    $produto_id = $_POST['produto_id'];
    $query = "DELETE FROM produtos WHERE id = :produto_id";
    $stmt = $pdo->prepare($query);
    $stmt->bindParam(':produto_id', $produto_id);
    $stmt->execute();
    // Redirecionar de volta para a página do dashboard após excluir o produto
    header("Location: dashboard.php");
    exit();
}

// Finalizar pedido, se solicitado
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['finalizar_pedido'])) {
    $pedido_id = $_POST['pedido_id'];
    $query = "UPDATE compras SET status_pedido = 'Finalizado' WHERE id = :pedido_id";
    $stmt = $pdo->prepare($query);
    $stmt->bindParam(':pedido_id', $pedido_id);
    $stmt->execute();
    // Redirecionar de volta para a página do dashboard após finalizar o pedido
    header("Location: dashboard.php");
    exit();
}

// Recuperar os pedidos dos lojistas relacionados aos produtos do fornecedor logado
$pedidos = array(); // Inicializa a variável como um array vazio
$fornecedor_id = $_SESSION['user_id'];
$queryPedidos = "SELECT compras.*, produtos.nome AS produto_nome, produtos.preco AS produto_preco, usuarios.nome AS lojista_nome
          FROM compras
          JOIN produtos ON compras.produto_id = produtos.id
          JOIN usuarios ON compras.lojista_id = usuarios.id
          WHERE compras.fornecedor_id = :fornecedor_id";
$stmtPedidos = $pdo->prepare($queryPedidos);
$stmtPedidos->bindParam(':fornecedor_id', $fornecedor_id);
$stmtPedidos->execute();
$pedidos = $stmtPedidos->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <title>Dashboard - Fornecedor</title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
</head>
<style>
    body {
        background-image: url('https://img.freepik.com/vetores-gratis/vetor-de-fundo-de-padrao-geometrico-branco-e-cinza_53876-136510.jpg?size=626&ext=jpg&ga=GA1.1.735520172.1712102400&semt=sph');
        background-size: cover;
        background-position: center;
        background-attachment: fixed;
        background-repeat: no-repeat;
        opacity: 0.9; /* Ajuste a opacidade conforme necessário */
        filter: alpha(opacity=80); /* Para navegadores antigos */
    }
</style>
<body>
<nav class="navbar navbar-expand-lg navbar-light bg-light">
    <a class="navbar-brand" href="#">Administrador</a>
    <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarSupportedContent" aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation">
        <span class="navbar-toggler-icon"></span>
    </button>
    <div class="collapse navbar-collapse" id="navbarSupportedContent">
        <ul class="navbar-nav mr-auto">
            <li class="nav-item active">
                <a class="nav-link" href="fornecedor_dashboard.php">Home <span class="sr-only">(current)</span></a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="fornecedor_pedidos.php">Pedidos</a>
            </li>
            <li class="nav-item">
                    <a class="nav-link" href="cadastro_produto.php">Cadastro Produtos</a>
                </li>
            <ul class="navbar-nav ml-auto">
            <li class="nav-item">
                <a class="nav-link" href="?logout=true">Logout</a>
            </li>
        </ul>
        </ul>
    </div>
</nav>
    <div class="container">
        <h2 class="mt-5">Dashboard - Fornecedor</h2>
       
        <h4 class="mt-4">Pedidos dos Lojistas</h4>
        <div class="form-row">
            <div class="form-group col-md-6">
                <label for="filtro-pedidos">Filtrar pedidos dos lojistas</label>
                <input class="form-control mb-3" id="filtro-pedidos" type="text" placeholder="Pesquisar...">
            </div>
            <div class="form-group col-md-3">
                <label for="filtro-status">Filtrar por status</label>
                <select class="form-control" id="filtro-status">
                    <option value="">Todos</option>
                    <option value="Finalizado">Finalizado</option>
                    <option value="Não finalizado">Não finalizado</option>
                </select>
            </div>
            <div class="form-group col-md-3">
                <label for="filtro-data">Filtrar por data</label>
                <select class="form-control" id="filtro-data">
                    <option value="">Todos</option>
                    <option value="Agendado">Agendado</option>
                    <option value="Não agendado">Não agendado</option>
                </select>
            </div>
        </div>

        <div class="table-responsive">
            <table class="table table-bordered mt-3">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>Lojista</th>
                        <th>Produto</th>
                        <th>Quantidade</th>
                        <th>Valor Pedido</th>
                        <th>Status Pedido</th>
                        <th>Data Programada</th>
                        <th>Ações</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (!empty($pedidos)): ?>
                        <?php foreach ($pedidos as $key => $pedido): ?>
                            <tr>
                                <td><?php echo $key + 1; ?></td>
                                <td><?php echo $pedido['lojista_nome']; ?></td>
                                <td><?php echo $pedido['produto_nome']; ?></td>
                                <td><?php echo $pedido['quantidade']; ?></td>
                                <td>
                                    <form method="post" action="">
                                        <input type="hidden" name="pedido_id" value="<?php echo $pedido['id']; ?>">
                                        <input type="hidden" name="produto_id" value="<?php echo $pedido['produto_id']; ?>">
                                        <div class="form-group">
                                            <input type="text" name="valor_pedido" class="form-control" value="<?php echo $pedido['valor_pedido']; ?>">
                                        </div>
                                        <button type="submit" class="btn btn-sm btn-primary">Atualizar</button>
                                    </form>
                                </td>
                                <td><?php echo $pedido['status_pedido']; ?></td>
                                <td>
                                    <?php if ($pedido['data_programada']): ?>
                                        <?php echo date('d/m/Y', strtotime($pedido['data_programada'])); ?>
                                    <?php else: ?>
                                        Não agendado
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if ($pedido['status_pedido'] != 'Finalizado'): ?>
                                        <form method="post" action="">
                                            <input type="hidden" name="pedido_id" value="<?php echo $pedido['id']; ?>">
                                            <button type="submit" class="btn btn-sm btn-success" name="finalizar_pedido">Finalizar Pedido</button>
                                        </form>
                                    <?php endif; ?>
                                    <a href="informacoes_pedido.php?pedido_id=<?php echo $pedido['id']; ?>" class="btn btn-sm btn-info">Detalhes</a>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <tr>
                            <td colspan="8">Nenhum pedido encontrado.</td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
    <script>
        $(document).ready(function(){
            // Filtro para pedidos
            $("#filtro-pedidos").on("keyup", function() {
                var value = $(this).val().toLowerCase();
                $("#tabela-pedidos tbody tr").filter(function() {
                    $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1)
                });
            });

            // Filtro por status
            $("#filtro-status").on("change", function() {
                var value = $(this).val().toLowerCase();
                $("#tabela-pedidos tbody tr").filter(function() {
                    var status = $(this).find("td:eq(5)").text().toLowerCase();
                    $(this).toggle(status.indexOf(value) > -1)
                });
            });

            // Filtro por data
            $("#filtro-data").on("change", function() {
                var value = $(this).val().toLowerCase();
                $("#tabela-pedidos tbody tr").filter(function() {
                    var data = $(this).find("td:eq(6)").text().toLowerCase();
                    if (value === "agendado") {
                        $(this).toggle(data !== "não agendado");
                    } else if (value === "não agendado") {
                        $(this).toggle(data === "não agendado");
                    } else {
                        $(this).toggle(true);
                    }
                });
            });
        });
    </script>
</body>
</html>
