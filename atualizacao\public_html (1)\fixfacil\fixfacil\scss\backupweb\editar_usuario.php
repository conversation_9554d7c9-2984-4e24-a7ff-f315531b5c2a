<?php
session_start();
require_once 'db.php';

if (!isset($_SESSION['user_id']) || $_SESSION['nivel_acesso'] != 'administrador') {
    header("Location: login.php");
    exit();
}

if(isset($_GET['id'])) {
    $usuario_id = $_GET['id'];

    if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['editar_usuario'])) {
        $nome = $_POST['nome'];
        $email = $_POST['email'];
        $nivel_acesso = $_POST['nivel_acesso'];

        $query = "UPDATE usuarios SET nome = :nome, email = :email, nivel_acesso = :nivel_acesso WHERE id = :id";
        $stmt = $pdo->prepare($query);
        $stmt->bindParam(':nome', $nome);
        $stmt->bindParam(':email', $email);
        $stmt->bindParam(':nivel_acesso', $nivel_acesso);
        $stmt->bindParam(':id', $usuario_id);
        $stmt->execute();

        header("Location: dashboard_admin.php");
        exit();
    }

    $query = "SELECT * FROM usuarios WHERE id = :id";
    $stmt = $pdo->prepare($query);
    $stmt->bindParam(':id', $usuario_id);
    $stmt->execute();
    $usuario = $stmt->fetch(PDO::FETCH_ASSOC);
} else {
    header("Location: dashboard_admin.php");
    exit();
}
?>

<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <title>Editar Usuário</title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
</head>
<body>
    <div class="container mt-5">
        <h2>Editar Usuário</h2>
        <form method="post">
            <div class="form-group">
                <label for="nome">Nome:</label>
                <input type="text" class="form-control" id="nome" name="nome" value="<?php echo $usuario['nome']; ?>">
            </div>
            <div class="form-group">
                <label for="email">Email:</label>
                <input type="email" class="form-control" id="email" name="email" value="<?php echo $usuario['email']; ?>">
            </div>
            <div class="form-group">
                <label for="nivel_acesso">Nível de Acesso:</label>
                <select name="nivel_acesso" class="form-control">
                    <option value="administrador" <?php echo ($usuario['nivel_acesso'] == 'administrador') ? 'selected' : ''; ?>>Administrador</option>
                    <option value="fornecedor" <?php echo ($usuario['nivel_acesso'] == 'fornecedor') ? 'selected' : ''; ?>>Fornecedor</option>
                    <option value="lojista" <?php echo ($usuario['nivel_acesso'] == 'lojista') ? 'selected' : ''; ?>>Lojista</option>
                </select>
            </div>
            <button type="submit" class="btn btn-primary" name="editar_usuario">Salvar</button>
            <a href="dashboard_admin.php" class="btn btn-secondary">Cancelar</a>
        </form>
    </div>
</body>
</html>
