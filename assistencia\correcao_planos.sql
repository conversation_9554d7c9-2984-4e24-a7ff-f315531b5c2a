-- Co<PERSON><PERSON> da Tabela Planos - FixFácil
-- Adicionar coluna acesso_assistencia_virtual e inserir planos padrão

-- 1. Adicionar coluna acesso_assistencia_virtual se não existir
ALTER TABLE planos ADD COLUMN IF NOT EXISTS acesso_assistencia_virtual TINYINT(1) NOT NULL DEFAULT 0 AFTER retirada_express_prioritaria;

-- 2. Verificar se existem planos cadastrados
-- Se não existirem, inserir os planos padrão

-- Plano Free
INSERT IGNORE INTO planos (
    nome, 
    descricao, 
    preco_mensal, 
    taxa_servico, 
    acesso_chat, 
    acesso_marketplace, 
    retirada_presencial, 
    selo_fixfacil, 
    link_personalizado, 
    retirada_express_prioritaria, 
    acesso_assistencia_virtual,
    status
) VALUES (
    'Free',
    'Plano gratuito com funcionalidades básicas para começar',
    0.00,
    25.00,
    0,
    0,
    1,
    0,
    0,
    0,
    0,
    'ativo'
);

-- Plano Premium
INSERT IGNORE INTO planos (
    nome, 
    descricao, 
    preco_mensal, 
    taxa_servico, 
    acesso_chat, 
    acesso_marketplace, 
    retirada_presencial, 
    selo_fixfacil, 
    link_personalizado, 
    retirada_express_prioritaria, 
    acesso_assistencia_virtual,
    status
) VALUES (
    'Premium',
    'Plano premium com chat e marketplace para expandir seu negócio',
    89.90,
    20.00,
    1,
    1,
    1,
    0,
    0,
    0,
    0,
    'ativo'
);

-- Plano Master
INSERT IGNORE INTO planos (
    nome, 
    descricao, 
    preco_mensal, 
    taxa_servico, 
    acesso_chat, 
    acesso_marketplace, 
    retirada_presencial, 
    selo_fixfacil, 
    link_personalizado, 
    retirada_express_prioritaria, 
    acesso_assistencia_virtual,
    status
) VALUES (
    'Master',
    'Plano completo com assistência virtual e todas as funcionalidades',
    159.90,
    10.00,
    1,
    1,
    1,
    1,
    1,
    1,
    1,
    'ativo'
);

-- 3. Atualizar planos existentes para garantir que Master tenha acesso à assistência virtual
UPDATE planos SET acesso_assistencia_virtual = 1 WHERE nome = 'Master';

-- 4. Verificar se tabela assinaturas_assistencias existe
CREATE TABLE IF NOT EXISTS assinaturas_assistencias (
    id INT AUTO_INCREMENT PRIMARY KEY,
    assistencia_id INT NOT NULL,
    plano_id INT NOT NULL,
    status ENUM('ativa', 'cancelada', 'suspensa') DEFAULT 'ativa',
    data_inicio TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    data_fim TIMESTAMP NULL,
    data_proximo_pagamento TIMESTAMP NULL,
    valor_pago DECIMAL(10,2) DEFAULT 0.00,
    forma_pagamento VARCHAR(50) DEFAULT 'gratuito',
    FOREIGN KEY (assistencia_id) REFERENCES assistencias_tecnicas(id),
    FOREIGN KEY (plano_id) REFERENCES planos(id),
    INDEX idx_assistencia_status (assistencia_id, status),
    INDEX idx_data_inicio (data_inicio)
);

-- 5. Criar assinatura Free para assistências que não têm plano
INSERT IGNORE INTO assinaturas_assistencias (assistencia_id, plano_id, status, data_inicio)
SELECT 
    at.id,
    (SELECT id FROM planos WHERE nome = 'Free' LIMIT 1),
    'ativa',
    NOW()
FROM assistencias_tecnicas at
WHERE at.id NOT IN (
    SELECT DISTINCT assistencia_id 
    FROM assinaturas_assistencias 
    WHERE status = 'ativa'
);

-- 6. Verificar estrutura final
SELECT 'Estrutura da tabela planos:' as info;
DESCRIBE planos;

SELECT 'Planos cadastrados:' as info;
SELECT id, nome, preco_mensal, taxa_servico, acesso_chat, acesso_marketplace, acesso_assistencia_virtual, status FROM planos;

SELECT 'Assinaturas ativas:' as info;
SELECT 
    aa.id,
    at.nome_empresa,
    p.nome as plano,
    aa.status,
    aa.data_inicio
FROM assinaturas_assistencias aa
JOIN assistencias_tecnicas at ON aa.assistencia_id = at.id
JOIN planos p ON aa.plano_id = p.id
WHERE aa.status = 'ativa'
ORDER BY aa.data_inicio DESC;
