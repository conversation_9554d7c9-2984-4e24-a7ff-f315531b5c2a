<?php
/**
 * AJAX - Upgrade de Plano
 * FixFácil Assistências - Sistema Novo
 */

header('Content-Type: application/json');

require_once '../config/auth.php';
require_once '../config/database.php';

// Verificar autenticação
$auth = getAuth();
try {
    $auth->checkAssistenciaAuth();
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'Não autorizado']);
    exit();
}

// Obter dados do usuário
$usuario = $auth->getUsuarioLogado();
$plano_atual = $auth->getPlanoInfo($usuario['id']);
$db = getDatabase();

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Método não permitido']);
    exit();
}

$novo_plano_id = $_POST['plano_id'] ?? 0;

// Validações
if (!$novo_plano_id) {
    echo json_encode(['success' => false, 'message' => 'Plano não especificado']);
    exit();
}

if ($novo_plano_id <= $plano_atual['id']) {
    echo json_encode(['success' => false, 'message' => 'Só é possível fazer upgrade para planos superiores']);
    exit();
}

try {
    // Verificar se o plano existe
    $sql = "SELECT * FROM planos WHERE id = ?";
    $result = $db->query($sql, [$novo_plano_id]);
    $novo_plano = $result->fetch_assoc();
    
    if (!$novo_plano) {
        echo json_encode(['success' => false, 'message' => 'Plano não encontrado']);
        exit();
    }
    
    // Iniciar transação
    $db->getConnection()->begin_transaction();
    
    try {
        // Atualizar plano do usuário
        $sql = "UPDATE usuarios SET plano_id = ? WHERE id = ?";
        $db->query($sql, [$novo_plano_id, $usuario['id']]);
        
        // Atualizar assinatura da assistência
        $sql = "
            UPDATE assinaturas_assistencias 
            SET plano_id = ?, data_upgrade = NOW()
            WHERE assistencia_id = ? AND status = 'ativa'
        ";
        $db->query($sql, [$novo_plano_id, $usuario['assistencia_id']]);
        
        // Registrar histórico de upgrade
        $sql = "
            INSERT INTO historico_upgrades 
            (assistencia_id, plano_anterior_id, plano_novo_id, data_upgrade, valor_anterior, valor_novo)
            VALUES (?, ?, ?, NOW(), ?, ?)
        ";
        $db->query($sql, [
            $usuario['assistencia_id'],
            $plano_atual['id'],
            $novo_plano_id,
            $plano_atual['preco_mensal'],
            $novo_plano['preco_mensal']
        ]);
        
        // Registrar atividade
        $sql = "
            INSERT INTO logs_atividades (usuario_id, tipo, descricao, data_atividade)
            VALUES (?, 'upgrade_plano', ?, NOW())
        ";
        $descricao = "Upgrade realizado: {$plano_atual['nome']} → {$novo_plano['nome']}";
        $db->query($sql, [$usuario['id'], $descricao]);
        
        // Criar notificação de boas-vindas
        $titulo = "Upgrade Realizado com Sucesso!";
        $mensagem = "Parabéns! Seu plano foi atualizado para {$novo_plano['nome']}. Agora você tem acesso a todos os recursos do novo plano.";
        
        $sql = "
            INSERT INTO notificacoes (usuario_id, tipo, titulo, mensagem, data_criacao)
            VALUES (?, 'upgrade_sucesso', ?, ?, NOW())
        ";
        $db->query($sql, [$usuario['id'], $titulo, $mensagem]);
        
        // Se for upgrade para Master, criar link personalizado
        if ($novo_plano['nome'] === 'Master') {
            try {
                criarLinkPersonalizado($usuario['assistencia_id'], $db);
            } catch (Exception $e) {
                // Link personalizado é opcional
                error_log("Erro ao criar link personalizado: " . $e->getMessage());
            }
        }
        
        // Commit da transação
        $db->getConnection()->commit();
        
        // Enviar e-mail de confirmação (opcional)
        try {
            enviarEmailUpgrade($usuario, $plano_atual, $novo_plano);
        } catch (Exception $e) {
            // E-mail é opcional
        }
        
        echo json_encode([
            'success' => true,
            'message' => 'Upgrade realizado com sucesso!',
            'novo_plano' => $novo_plano['nome'],
            'nova_taxa' => $novo_plano['taxa_servico']
        ]);
        
    } catch (Exception $e) {
        // Rollback em caso de erro
        $db->getConnection()->rollback();
        throw $e;
    }
    
} catch (Exception $e) {
    error_log("Erro ao realizar upgrade: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Erro interno do servidor']);
}

/**
 * Criar link personalizado para plano Master
 */
function criarLinkPersonalizado($assistencia_id, $db) {
    // Obter dados da assistência
    $sql = "SELECT nome_empresa FROM assistencias_tecnicas WHERE id = ?";
    $result = $db->query($sql, [$assistencia_id]);
    $assistencia = $result->fetch_assoc();
    
    if (!$assistencia) return;
    
    // Gerar slug baseado no nome da empresa
    $slug = gerarSlug($assistencia['nome_empresa']);
    
    // Verificar se o slug já existe
    $contador = 1;
    $slug_original = $slug;
    
    while (true) {
        $sql = "SELECT id FROM links_personalizados WHERE slug = ?";
        $result = $db->query($sql, [$slug]);
        
        if (!$result->fetch_assoc()) {
            break; // Slug disponível
        }
        
        $slug = $slug_original . '-' . $contador;
        $contador++;
    }
    
    // Criar link personalizado
    $sql = "
        INSERT INTO links_personalizados (assistencia_id, slug, ativo, data_criacao)
        VALUES (?, ?, 1, NOW())
        ON DUPLICATE KEY UPDATE slug = VALUES(slug), ativo = 1
    ";
    $db->query($sql, [$assistencia_id, $slug]);
}

/**
 * Gerar slug a partir do nome da empresa
 */
function gerarSlug($texto) {
    // Converter para minúsculas
    $slug = strtolower($texto);
    
    // Remover acentos
    $slug = iconv('UTF-8', 'ASCII//TRANSLIT', $slug);
    
    // Remover caracteres especiais
    $slug = preg_replace('/[^a-z0-9\s-]/', '', $slug);
    
    // Substituir espaços por hífens
    $slug = preg_replace('/[\s-]+/', '-', $slug);
    
    // Remover hífens do início e fim
    $slug = trim($slug, '-');
    
    // Limitar tamanho
    $slug = substr($slug, 0, 50);
    
    return $slug ?: 'assistencia';
}

/**
 * Enviar e-mail de confirmação de upgrade
 */
function enviarEmailUpgrade($usuario, $plano_anterior, $plano_novo) {
    $to = $usuario['email'];
    $subject = "Upgrade Realizado - FixFácil Assistências";
    
    $message = "
    <html>
    <head>
        <title>Upgrade Realizado</title>
    </head>
    <body>
        <h2>Parabéns pelo seu upgrade!</h2>
        <p>Olá {$usuario['nome']},</p>
        
        <p>Seu plano foi atualizado com sucesso:</p>
        
        <ul>
            <li><strong>Plano Anterior:</strong> {$plano_anterior['nome']} (Taxa: {$plano_anterior['taxa_servico']}%)</li>
            <li><strong>Novo Plano:</strong> {$plano_novo['nome']} (Taxa: {$plano_novo['taxa_servico']}%)</li>
        </ul>
        
        <p>Agora você tem acesso a todos os recursos do plano {$plano_novo['nome']}!</p>
        
        <p>Atenciosamente,<br>Equipe FixFácil</p>
    </body>
    </html>
    ";
    
    $headers = "MIME-Version: 1.0" . "\r\n";
    $headers .= "Content-type:text/html;charset=UTF-8" . "\r\n";
    $headers .= "From: <EMAIL>" . "\r\n";
    
    mail($to, $subject, $message, $headers);
}
?>
