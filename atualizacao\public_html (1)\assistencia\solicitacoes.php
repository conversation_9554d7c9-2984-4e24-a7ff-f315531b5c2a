<?php
session_start();
if (!isset($_SESSION['usuario_id']) || $_SESSION['tipo_usuario'] != 'assistencia') {
    header('Location: ../login.php');
    exit();
}

// Ativar exibição de erros para depuração (remova em produção)
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

$usuario_id = $_SESSION['usuario_id'];
$nome_usuario = $_SESSION['nome'];

// Conexão com o banco de dados
$servername = "localhost";
$username_db = "u680766645_fixfacilnew";
$password_db = "T3cn0l0g1a@";
$dbname = "u680766645_fixfacilnew";

$conn = new mysqli($servername, $username_db, $password_db, $dbname);

// Verificar conexão
if ($conn->connect_error) {
    die("Falha na conexão: " . $conn->connect_error);
}

$conn->set_charset("utf8mb4");

// Obter o assistencia_id correspondente ao usuario_id
$sql_assistencia = "SELECT id FROM assistencias_tecnicas WHERE usuario_id = ?";
$stmt_assistencia = $conn->prepare($sql_assistencia);
$stmt_assistencia->bind_param("i", $usuario_id);
$stmt_assistencia->execute();
$result_assistencia = $stmt_assistencia->get_result();
if ($row_assistencia = $result_assistencia->fetch_assoc()) {
    $assistencia_id = $row_assistencia['id'];
} else {
    // Assistência técnica não encontrada
    die("Assistência técnica não encontrada.");
}
$stmt_assistencia->close();

// Inicializar variáveis para mensagens
$mensagem = "";
$tipo_alerta = "";

// Log para depuração - verificar se o POST está chegando
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    error_log("POST recebido: " . print_r($_POST, true));
}

// Processar o formulário de envio de proposta quando enviado
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['enviar_proposta'])) {
    error_log("Processando envio de proposta");
    
    // Obter dados do formulário e sanitizar
    $solicitacao_id = intval($_POST['solicitacao_id']);
    $preco = floatval(str_replace(',', '.', $_POST['preco']));
    $prazo = intval($_POST['prazo']);
    $observacoes = $conn->real_escape_string(trim($_POST['observacoes']));
    
    // Log dos valores recebidos
    error_log("Solicitação ID: " . $solicitacao_id);
    error_log("Preço: " . $preco);
    error_log("Prazo: " . $prazo);
    
    // Obter e processar os novos campos de Tipo de Peça
    $tipo_peca = isset($_POST['tipo_peca']) ? $_POST['tipo_peca'] : [];
    error_log("Tipo de peça: " . print_r($tipo_peca, true));

    // Verificar se pelo menos um Tipo de Peça foi selecionado e outros campos obrigatórios
    if ($solicitacao_id <= 0 || $preco <= 0 || $prazo <= 0) {
        $mensagem = "Por favor, preencha todos os campos obrigatórios corretamente.";
        $tipo_alerta = "danger";
        error_log("Erro de validação: campos obrigatórios");
    } elseif (empty($tipo_peca)) {
        $mensagem = "Por favor, selecione pelo menos um tipo de peça.";
        $tipo_alerta = "danger";
        error_log("Erro de validação: tipo de peça não selecionado");
    } else {
        // Concatenar os tipos de peça com as observações
        $tipo_peca_str = implode(", ", array_map('trim', $tipo_peca));
        $observacoes .= "\nTipo de Peça: " . $tipo_peca_str;

        // Inserir proposta no banco de dados
        $sql_insert = "INSERT INTO propostas_assistencia (assistencia_id, solicitacao_id, preco, prazo, observacoes, status, data_proposta)
                       VALUES (?, ?, ?, ?, ?, 'enviada', NOW())";
        $stmt = $conn->prepare($sql_insert);
        if ($stmt === false) {
            $mensagem = "Erro na preparação da consulta: " . $conn->error;
            $tipo_alerta = "danger";
            error_log("Erro na preparação da consulta: " . $conn->error);
        } else {
            $stmt->bind_param("iidis", $assistencia_id, $solicitacao_id, $preco, $prazo, $observacoes);

            if ($stmt->execute()) {
                $mensagem = "Proposta enviada com sucesso!";
                $tipo_alerta = "success";
                error_log("Proposta inserida com sucesso! ID: " . $conn->insert_id);
            } else {
                $mensagem = "Erro ao enviar a proposta: " . $stmt->error;
                $tipo_alerta = "danger";
                error_log("Erro ao executar a consulta: " . $stmt->error);
            }

            $stmt->close();
        }
    }
}

// Verificar se é uma requisição AJAX para detalhes
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'detalhes_solicitacao') {
    header('Content-Type: application/json');

    // Obter o ID da solicitação
    $solicitacao_id = isset($_POST['id']) ? intval($_POST['id']) : 0;

    if ($solicitacao_id <= 0) {
        echo json_encode(['success' => false, 'message' => 'ID da solicitação inválido.']);
        exit();
    }

    // Buscar detalhes da solicitação
    $sql_detalhes = "SELECT sr.*, u.nome AS nome_cliente 
                     FROM solicitacoes_reparo sr
                     INNER JOIN usuarios u ON sr.usuario_id = u.id
                     WHERE sr.id = ?";
    $stmt_detalhes = $conn->prepare($sql_detalhes);
    if ($stmt_detalhes === false) {
        echo json_encode(['success' => false, 'message' => 'Erro na preparação da consulta: ' . $conn->error]);
        exit();
    }
    $stmt_detalhes->bind_param("i", $solicitacao_id);
    $stmt_detalhes->execute();
    $result_detalhes = $stmt_detalhes->get_result();

    if ($solicitacao = $result_detalhes->fetch_assoc()) {
        // Ajustar o caminho do vídeo
        $video_path = '';
        if (!empty($solicitacao['video'])) {
            // Construir a URL absoluta para o vídeo
            $video_path = 'https://' . $_SERVER['HTTP_HOST'] . '/usuario/' . $solicitacao['video'];
        }

        // Preparar dados para o JSON
        $dados = [
            'id' => $solicitacao['id'],
            'nome_cliente' => htmlspecialchars($solicitacao['nome_cliente']),
            'descricao_problema' => nl2br(htmlspecialchars($solicitacao['descricao_problema'])),
            'descricao_detalhada' => nl2br(htmlspecialchars($solicitacao['descricao_detalhada'])),
            'video' => htmlspecialchars($video_path),
            'metodo_entrega' => htmlspecialchars($solicitacao['metodo_entrega']),
            'endereco' => htmlspecialchars($solicitacao['endereco']),
            'dispositivo' => htmlspecialchars($solicitacao['dispositivo']),
            'marca' => htmlspecialchars($solicitacao['marca']),
            'modelo' => htmlspecialchars($solicitacao['modelo']),
            'memoria' => htmlspecialchars($solicitacao['memoria']),
            'status' => htmlspecialchars($solicitacao['status']),
            'data_solicitacao' => htmlspecialchars($solicitacao['data_solicitacao']),
            'verificacoes' => nl2br(htmlspecialchars($solicitacao['verificacoes'])),
            'termos_concordo' => $solicitacao['termos_concordo'] ? 'Sim' : 'Não'
        ];

        // Verificar propostas existentes da assistência para esta solicitação
        $sql_propostas = "SELECT id, preco, prazo, observacoes, data_proposta 
                          FROM propostas_assistencia 
                          WHERE assistencia_id = ? AND solicitacao_id = ?
                          ORDER BY data_proposta DESC";
        $stmt_propostas = $conn->prepare($sql_propostas);
        $stmt_propostas->bind_param("ii", $assistencia_id, $solicitacao_id);
        $stmt_propostas->execute();
        $result_propostas = $stmt_propostas->get_result();
        
        $propostas = [];
        while ($proposta = $result_propostas->fetch_assoc()) {
            $propostas[] = [
                'id' => $proposta['id'],
                'preco' => 'R$ ' . number_format($proposta['preco'], 2, ',', '.'),
                'prazo' => $proposta['prazo'] . ' dias',
                'observacoes' => nl2br(htmlspecialchars($proposta['observacoes'])),
                'data_proposta' => htmlspecialchars($proposta['data_proposta'])
            ];
        }
        $stmt_propostas->close();
        
        $dados['propostas_existentes'] = $propostas;

        echo json_encode(['success' => true, 'data' => $dados]);
    } else {
        echo json_encode(['success' => false, 'message' => 'Solicitação não encontrada.']);
    }

    $stmt_detalhes->close();
    $conn->close();
    exit();
}

// Obter as solicitações de reparo com status 'enviado'
$sql = "SELECT sr.id, sr.descricao_problema, sr.dispositivo, sr.marca, sr.modelo, sr.memoria, sr.metodo_entrega, sr.endereco, sr.video, sr.descricao_detalhada, u.nome AS nome_usuario, sr.data_solicitacao
        FROM solicitacoes_reparo sr
        INNER JOIN usuarios u ON sr.usuario_id = u.id
        WHERE sr.status = 'enviado' ORDER BY sr.data_solicitacao DESC";
$result = $conn->query($sql);
if ($result === false) {
    die("Erro na consulta: " . $conn->error);
}
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <title>Solicitações de Reparo - FixFácil</title>
    <!-- Meta Tags para Responsividade -->
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <!-- Incluindo Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome para ícones -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- SweetAlert2 CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">
    <!-- Estilos personalizados -->
    <style>
        :root {
            --primary-color: #2563eb;
            --primary-dark: #1d4ed8;
            --secondary-color: #475569;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --light-bg: #f8fafc;
            --card-bg: #ffffff;
            --text-color: #1e293b;
            --text-muted: #64748b;
            --border-radius: 12px;
            --shadow-sm: 0 1px 3px rgba(0,0,0,0.1);
            --shadow-md: 0 4px 6px -1px rgba(0,0,0,0.1), 0 2px 4px -1px rgba(0,0,0,0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0,0,0,0.1), 0 4px 6px -2px rgba(0,0,0,0.05);
        }
        
        body {
            font-family: 'Inter', sans-serif;
            background-color: var(--light-bg);
            color: var(--text-color);
            margin-bottom: 80px;
            padding-top: 70px;
        }
        
        /* Navbar */
        .navbar {
            background-color: var(--card-bg);
            box-shadow: var(--shadow-sm);
            padding: 12px 0;
        }
        
        .navbar-brand {
            font-weight: 700;
            color: var(--primary-color) !important;
        }
        
        .navbar .nav-link {
            color: var(--text-color) !important;
            font-weight: 500;
            padding: 8px 16px;
            border-radius: var(--border-radius);
            transition: all 0.3s ease;
        }
        
        .navbar .nav-link:hover {
            background-color: rgba(37, 99, 235, 0.1);
            color: var(--primary-color) !important;
        }
        
        .navbar .nav-link.active {
            background-color: var(--primary-color);
            color: white !important;
        }
        
        /* Conteúdo Principal */
        .main-content {
            padding: 20px 12px;
        }
        
        .header-section {
            margin-bottom: 24px;
        }
        
        .header-section h1 {
            font-weight: 700;
            font-size: 1.75rem;
            margin-bottom: 8px;
            color: var(--text-color);
        }
        
        .header-section p {
            color: var(--text-muted);
            font-size: 1rem;
            margin-bottom: 0;
        }
        
        /* Cards de solicitações */
        .solicitacao-card {
            background-color: var(--card-bg);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-md);
            margin-bottom: 20px;
            overflow: hidden;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .solicitacao-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-lg);
        }
        
        .solicitacao-header {
            background-color: rgba(37, 99, 235, 0.1);
            padding: 16px;
            border-bottom: 1px solid rgba(37, 99, 235, 0.2);
        }
        
        .solicitacao-id {
            font-weight: 700;
            color: var(--primary-color);
            font-size: 1.1rem;
            margin-bottom: 4px;
        }
        
        .solicitacao-cliente {
            font-weight: 600;
            color: var(--text-color);
            margin-bottom: 4px;
        }
        
        .solicitacao-data {
            color: var(--text-muted);
            font-size: 0.9rem;
        }
        
        .solicitacao-body {
            padding: 16px;
        }
        
        .solicitacao-info {
            margin-bottom: 8px;
        }
        
        .solicitacao-info-label {
            font-weight: 600;
            color: var(--text-color);
            display: block;
            margin-bottom: 4px;
        }
        
        .solicitacao-info-value {
            color: var(--text-color);
            background-color: rgba(0, 0, 0, 0.03);
            padding: 6px 10px;
            border-radius: 6px;
            font-size: 0.95rem;
        }
        
        .solicitacao-actions {
            padding: 16px;
            background-color: rgba(0, 0, 0, 0.02);
            border-top: 1px solid rgba(0, 0, 0, 0.05);
            display: flex;
            gap: 10px;
        }
        
        .action-btn {
            font-weight: 600;
            padding: 10px 20px;
            border-radius: var(--border-radius);
            display: flex;
            align-items: center;
            gap: 8px;
            flex: 1;
            justify-content: center;
            transition: all 0.3s ease;
        }
        
        .search-container {
            margin-bottom: 24px;
        }
        
        .search-input {
            border-radius: var(--border-radius);
            padding: 12px 20px;
            border: 1px solid rgba(0, 0, 0, 0.1);
            background-color: var(--card-bg);
            box-shadow: var(--shadow-sm);
        }
        
        /* Tabela (visível apenas em telas grandes) */
        .table-container {
            background-color: var(--card-bg);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-md);
            overflow: hidden;
            margin-bottom: 30px;
        }
        
        .custom-table {
            margin-bottom: 0;
        }
        
        .custom-table th {
            background-color: rgba(37, 99, 235, 0.05);
            border-top: none;
            border-bottom: 2px solid rgba(37, 99, 235, 0.1);
            color: var(--text-color);
            font-weight: 600;
            padding: 16px;
        }
        
        .custom-table td {
            padding: 16px;
            vertical-align: middle;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }
        
        .custom-table tr:last-child td {
            border-bottom: none;
        }
        
        .custom-table tr:hover {
            background-color: rgba(37, 99, 235, 0.03);
        }
        
        /* Modal personalizado */
        .modal-content {
            border-radius: var(--border-radius);
            border: none;
            box-shadow: var(--shadow-lg);
        }
        
        .modal-header {
            background-color: var(--primary-color);
            color: white;
            border-top-left-radius: var(--border-radius);
            border-top-right-radius: var(--border-radius);
            border-bottom: none;
            padding: 20px;
        }
        
        .modal-title {
            font-weight: 600;
        }
        
        .modal-header .btn-close {
            color: white;
            background: transparent url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='white'%3e%3cpath d='M.293.293a1 1 0 011.414 0L8 6.586 14.293.293a1 1 0 111.414 1.414L9.414 8l6.293 6.293a1 1 0 01-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 01-1.414-1.414L6.586 8 .293 1.707a1 1 0 010-1.414z'/%3e%3c/svg%3e") center/1em auto no-repeat;
        }
        
        .modal-body {
            padding: 24px;
        }
        
        .modal-footer {
            padding: 16px 24px;
            border-top: 1px solid rgba(0, 0, 0, 0.1);
        }
        
        /* Formulário personalizado */
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-label {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--text-color);
        }
        
        .form-control {
            border-radius: 8px;
            padding: 12px 16px;
            border: 1px solid rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }
        
        textarea.form-control {
            min-height: 120px;
        }
        
        /* Checkboxes estilizados */
        .checkbox-container {
            background-color: rgba(0, 0, 0, 0.02);
            border-radius: 8px;
            padding: 16px;
            border: 1px solid rgba(0, 0, 0, 0.05);
            margin-bottom: 8px;
        }
        
        .checkbox-item {
            display: flex;
            align-items: center;
            padding: 10px;
            margin-bottom: 8px;
            background-color: white;
            border-radius: 6px;
            border: 1px solid rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .checkbox-item:last-child {
            margin-bottom: 0;
        }
        
        .checkbox-item:hover {
            background-color: rgba(37, 99, 235, 0.05);
            border-color: rgba(37, 99, 235, 0.2);
        }
        
        .checkbox-item input[type="checkbox"] {
            margin-right: 12px;
            width: 18px;
            height: 18px;
        }
        
        .checkbox-item label {
            margin-bottom: 0;
            font-weight: 500;
            flex: 1;
            cursor: pointer;
        }
        
        .checkbox-item input[type="checkbox"]:checked + label {
            color: var(--primary-color);
            font-weight: 600;
        }
        
        /* Detalhes da solicitação */
        .detail-section {
            margin-bottom: 24px;
            padding: 16px;
            background-color: rgba(0, 0, 0, 0.02);
            border-radius: 8px;
        }
        
        .detail-section h4 {
            font-weight: 600;
            font-size: 1.2rem;
            margin-bottom: 16px;
            color: var(--primary-color);
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            padding-bottom: 8px;
        }
        
        .detail-row {
            margin-bottom: 12px;
        }
        
        .detail-label {
            font-weight: 600;
            color: var(--text-color);
            margin-bottom: 4px;
        }
        
        .detail-value {
            color: var(--text-color);
            background-color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 0.95rem;
            border: 1px solid rgba(0, 0, 0, 0.05);
        }
        
        /* Propostas existentes */
        .propostas-existentes {
            background-color: rgba(37, 99, 235, 0.05);
            border-radius: 8px;
            padding: 16px;
            margin-top: 24px;
            border-left: 4px solid var(--primary-color);
        }
        
        .propostas-existentes h5 {
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: 16px;
        }
        
        .proposta-item {
            background-color: white;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 16px;
            box-shadow: var(--shadow-sm);
            border: 1px solid rgba(0, 0, 0, 0.05);
        }
        
        .proposta-item:last-child {
            margin-bottom: 0;
        }
        
        .proposta-info {
            margin-bottom: 8px;
        }
        
        .proposta-info strong {
            font-weight: 600;
            color: var(--text-color);
        }
        
        /* Mobile Menu */
        .mobile-menu {
            position: fixed;
            bottom: 0;
            width: 100%;
            background-color: var(--card-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            z-index: 1000;
            padding: 8px 0;
            display: flex;
            justify-content: space-around;
        }
        
        .mobile-menu .menu-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 8px 0;
            width: 20%;
            text-decoration: none;
        }
        
        .mobile-menu .menu-item i {
            font-size: 20px;
            margin-bottom: 4px;
            color: var(--text-muted);
            transition: color 0.3s ease;
        }
        
        .mobile-menu .menu-item span {
            font-size: 12px;
            color: var(--text-muted);
            transition: color 0.3s ease;
        }
        
        .mobile-menu .menu-item.active i,
        .mobile-menu .menu-item.active span {
            color: var(--primary-color);
            font-weight: 600;
        }
        
        .mobile-menu .menu-item:hover i,
        .mobile-menu .menu-item:hover span {
            color: var(--primary-dark);
        }
        
        /* Esconder menu mobile em desktop */
        @media (min-width: 992px) {
            .mobile-menu {
                display: none;
            }
            body {
                margin-bottom: 60px;
            }
        }
        
        /* Ajustes para mobile */
        @media (max-width: 767px) {
            .table-container {
                display: none; /* Esconder tabela em mobile */
            }
            .mobile-cards {
                display: block; /* Mostrar cards em mobile */
            }
            .header-section h1 {
                font-size: 1.5rem;
            }
            .search-input {
                padding: 10px 16px;
            }
            .solicitacao-actions {
                flex-direction: column;
                gap: 10px;
            }
            .main-content {
                padding: 15px 10px;
            }
            .modal-dialog {
                margin: 10px;
            }
        }
        
        /* Em desktop, mostrar tabela e esconder cards */
        @media (min-width: 768px) {
            .mobile-cards {
                display: none;
            }
            .table-container {
                display: block;
            }
        }
        
        /* Overlay de carregamento */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(255, 255, 255, 0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.3s ease, visibility 0.3s ease;
        }
        
        .loading-overlay.show {
            opacity: 1;
            visibility: visible;
        }
        
        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid rgba(37, 99, 235, 0.1);
            border-radius: 50%;
            border-top-color: var(--primary-color);
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* Badge de status */
        .status-badge {
            display: inline-block;
            padding: 6px 12px;
            border-radius: 50px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        .status-enviado {
            background-color: rgba(37, 99, 235, 0.1);
            color: var(--primary-color);
        }
        
        /* Alerta personalizado */
        .custom-alert {
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 24px;
            border-left: 4px solid;
        }
        
        .alert-success {
            background-color: rgba(16, 185, 129, 0.1);
            border-color: var(--success-color);
            color: #065f46;
        }
        
        .alert-danger {
            background-color: rgba(239, 68, 68, 0.1);
            border-color: var(--danger-color);
            color: #b91c1c;
        }
        
        .alert-info {
            background-color: rgba(37, 99, 235, 0.1);
            border-color: var(--primary-color);
            color: #1e40af;
        }
    </style>
</head>
<body>
    <!-- Overlay de carregamento -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="spinner"></div>
    </div>

    <!-- Cabeçalho (Navbar) -->
    <nav class="navbar navbar-expand-lg fixed-top navbar-light">
        <div class="container">
            <a class="navbar-brand" href="#">FixFácil</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" 
                    aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="home.php">Painel</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="solicitacoes.php">Solicitações</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="propostas_enviadas.php">Propostas</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="reparos_em_andamento.php">Reparos</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="meumarktplace.php">Marketplace</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="solicitar_pecas.php">Peças</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="carteira.php">Carteira</a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-user-circle"></i>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="perfil.php">Meu Perfil</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../logout.php">Sair</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Conteúdo Principal -->
    <div class="container main-content">
        <!-- Cabeçalho da página -->
        <div class="header-section">
            <h1>Solicitações de Reparo</h1>
            <p>Visualize e envie propostas para as solicitações de clientes.</p>
        </div>

        <!-- Exibir Mensagens -->
        <?php if (!empty($mensagem)): ?>
            <div class="custom-alert alert-<?php echo $tipo_alerta; ?>" role="alert">
                <?php echo htmlspecialchars($mensagem); ?>
            </div>
        <?php endif; ?>

        <!-- Campo de busca -->
        <div class="search-container">
            <input type="text" class="form-control search-input" id="searchInput" placeholder="Buscar por nome, dispositivo, marca ou modelo...">
        </div>

        <!-- Versão para desktop: Tabela -->
        <div class="table-container">
            <table class="table custom-table" id="tabela-solicitacoes">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Cliente</th>
                        <th>Dispositivo</th>
                        <th>Marca/Modelo</th>
                        <th>Método de Entrega</th>
                        <th>Data</th>
                        <th>Ações</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if ($result->num_rows > 0): ?>
                        <?php while ($solicitacao = $result->fetch_assoc()): ?>
                            <tr class="solicitacao-row">
                                <td>#<?php echo $solicitacao['id']; ?></td>
                                <td><?php echo htmlspecialchars($solicitacao['nome_usuario']); ?></td>
                                <td><?php echo htmlspecialchars($solicitacao['dispositivo']); ?></td>
                                <td><?php echo htmlspecialchars($solicitacao['marca']) . ' ' . htmlspecialchars($solicitacao['modelo']); ?></td>
                                <td><?php echo htmlspecialchars($solicitacao['metodo_entrega']); ?></td>
                                <td><?php echo date('d/m/Y', strtotime($solicitacao['data_solicitacao'])); ?></td>
                                <td>
                                    <div class="d-flex gap-2">
                                        <button class="btn btn-sm btn-primary btn-enviar-proposta" data-id="<?php echo $solicitacao['id']; ?>">
                                            <i class="fas fa-paper-plane me-1"></i> Proposta
                                        </button>
                                        <button class="btn btn-sm btn-info text-white btn-detalhes" data-id="<?php echo $solicitacao['id']; ?>">
                                            <i class="fas fa-eye me-1"></i> Detalhes
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <?php endwhile; ?>
                    <?php else: ?>
                        <tr>
                            <td colspan="7" class="text-center">Nenhuma solicitação pendente encontrada.</td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>

        <!-- Versão para mobile: Cards -->
        <div class="mobile-cards">
            <?php 
            // Redefine o ponteiro do resultado
            $result->data_seek(0);
            if ($result->num_rows > 0): 
            ?>
                <?php while ($solicitacao = $result->fetch_assoc()): ?>
                    <div class="solicitacao-card solicitacao-row">
                        <div class="solicitacao-header">
                            <div class="solicitacao-id">#<?php echo $solicitacao['id']; ?></div>
                            <div class="solicitacao-cliente"><?php echo htmlspecialchars($solicitacao['nome_usuario']); ?></div>
                            <div class="solicitacao-data">
                                <i class="far fa-calendar-alt me-1"></i> 
                                <?php echo date('d/m/Y', strtotime($solicitacao['data_solicitacao'])); ?>
                            </div>
                        </div>
                        <div class="solicitacao-body">
                            <div class="row">
                                <div class="col-6">
                                    <div class="solicitacao-info">
                                        <div class="solicitacao-info-label">Dispositivo</div>
                                        <div class="solicitacao-info-value">
                                            <?php echo htmlspecialchars($solicitacao['dispositivo']); ?>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="solicitacao-info">
                                        <div class="solicitacao-info-label">Marca/Modelo</div>
                                        <div class="solicitacao-info-value">
                                            <?php echo htmlspecialchars($solicitacao['marca']) . ' ' . htmlspecialchars($solicitacao['modelo']); ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row mt-2">
                                <div class="col-6">
                                    <div class="solicitacao-info">
                                        <div class="solicitacao-info-label">Memória</div>
                                        <div class="solicitacao-info-value">
                                            <?php echo htmlspecialchars($solicitacao['memoria']); ?>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="solicitacao-info">
                                        <div class="solicitacao-info-label">Entrega</div>
                                        <div class="solicitacao-info-value">
                                            <?php echo htmlspecialchars($solicitacao['metodo_entrega']); ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="solicitacao-actions">
                            <button class="btn btn-primary action-btn btn-enviar-proposta" data-id="<?php echo $solicitacao['id']; ?>">
                                <i class="fas fa-paper-plane"></i> Enviar Proposta
                            </button>
                            <button class="btn btn-info text-white action-btn btn-detalhes" data-id="<?php echo $solicitacao['id']; ?>">
                                <i class="fas fa-eye"></i> Ver Detalhes
                            </button>
                        </div>
                    </div>
                <?php endwhile; ?>
            <?php else: ?>
                <div class="text-center py-5">
                    <div class="mb-3">
                        <i class="fas fa-inbox fa-3x text-muted"></i>
                    </div>
                    <h4>Nenhuma solicitação pendente</h4>
                    <p class="text-muted">Não há solicitações de reparo pendentes no momento.</p>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Modal para Enviar Proposta -->
    <div class="modal fade" id="modalProposta" tabindex="-1" aria-labelledby="modalPropostaLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="modalPropostaLabel">Enviar Proposta</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Fechar"></button>
                </div>
                <form action="solicitacoes.php" method="POST" id="formProposta">
                    <div class="modal-body">
                        <input type="hidden" name="solicitacao_id" id="solicitacao_id_modal" value="">
                        
                        <!-- Aviso sobre propostas anteriores -->
                        <div class="alert alert-info" id="aviso-propostas-anteriores" style="display: none;">
                            <i class="fas fa-info-circle me-2"></i> Você já enviou propostas para esta solicitação. Uma nova proposta não substituirá as anteriores.
                        </div>
                        
                        <div class="form-group">
                            <label for="preco" class="form-label">Preço (R$) <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <span class="input-group-text">R$</span>
                                <input type="text" class="form-control" id="preco" name="preco" required pattern="^\d+(\.\d{1,2})?$" placeholder="150.00">
                            </div>
                            <div class="invalid-feedback">
                                Digite um preço válido.
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="prazo" class="form-label">Prazo (dias) <span class="text-danger">*</span></label>
                            <input type="number" class="form-control" id="prazo" name="prazo" min="1" required placeholder="Exemplo: 5">
                            <div class="invalid-feedback">
                                O prazo deve ser de pelo menos 1 dia.
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="observacoes" class="form-label">Observações <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="observacoes" name="observacoes" rows="4" placeholder="Descreva detalhes sobre o reparo, condições, etc." required></textarea>
                            <div class="invalid-feedback">
                                Por favor, forneça observações sobre o reparo.
                            </div>
                        </div>

                        <!-- Grupo de Campos: Tipo de Peça (com visual melhorado) -->
                        <div class="form-group">
                            <label class="form-label">Tipo de Peça <span class="text-danger">*</span></label>
                            <div id="tipo-peca-error" class="text-danger mb-2" style="display:none;">
                                <i class="fas fa-exclamation-circle me-1"></i> Selecione pelo menos um tipo de peça.
                            </div>
                            <div class="checkbox-container">
                                <div class="checkbox-item" id="item-brasil">
                                    <input class="form-check-input tipo-peca-checkbox" type="checkbox" value="Peça Original Brasil" id="peca_original_brasil" name="tipo_peca[]">
                                    <label for="peca_original_brasil">Peça Original Brasil</label>
                                </div>
                                <div class="checkbox-item" id="item-china">
                                    <input class="form-check-input tipo-peca-checkbox" type="checkbox" value="Peça Original China" id="peca_original_china" name="tipo_peca[]">
                                    <label for="peca_original_china">Peça Original China</label>
                                </div>
                                <div class="checkbox-item" id="item-paralela">
                                    <input class="form-check-input tipo-peca-checkbox" type="checkbox" value="Peça Paralela" id="peca_paralela" name="tipo_peca[]">
                                    <label for="peca_paralela">Peça Paralela</label>
                                </div>
                            </div>
                            <small class="text-muted">Selecione pelo menos um tipo de peça a ser utilizada no reparo.</small>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                        <button type="submit" name="enviar_proposta" class="btn btn-primary" id="btnEnviarProposta">
                            <i class="fas fa-paper-plane me-1"></i> Enviar Proposta
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Modal para Detalhes da Solicitação -->
    <div class="modal fade" id="modalDetalhes" tabindex="-1" aria-labelledby="modalDetalhesLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Detalhes da Solicitação</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Fechar"></button>
                </div>
                <div class="modal-body">
                    <div class="text-center p-4" id="loading-detalhes">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Carregando...</span>
                        </div>
                        <p class="mt-2">Carregando detalhes...</p>
                    </div>
                    <div id="detalhes-conteudo" style="display: none;"></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fechar</button>
                    <button type="button" class="btn btn-primary" id="btn-nova-proposta">
                        <i class="fas fa-paper-plane me-1"></i> Enviar Nova Proposta
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Menu Mobile (fixo na parte inferior) -->
    <div class="mobile-menu d-lg-none">
        <a href="home.php" class="menu-item">
            <i class="fas fa-home"></i>
            <span>Painel</span>
        </a>
        <a href="solicitacoes.php" class="menu-item active">
            <i class="fas fa-inbox"></i>
            <span>Solicitações</span>
        </a>
        <a href="reparos_em_andamento.php" class="menu-item">
            <i class="fas fa-tools"></i>
            <span>Reparos</span>
        </a>
        <a href="propostas_enviadas.php" class="menu-item">
            <i class="fas fa-paper-plane"></i>
            <span>Propostas</span>
        </a>
        <a href="perfil.php" class="menu-item">
            <i class="fas fa-user"></i>
            <span>Perfil</span>
        </a>
    </div>

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
    $(document).ready(function() {
    let currentSolicitacaoId = 0;

    // NOVO: Verificar se há mensagem de sucesso
    if ($('.custom-alert.alert-success').length > 0) {
        Swal.fire({
            icon: 'success',
            title: 'Proposta Enviada',
            text: $('.custom-alert.alert-success').text(),
            confirmButtonColor: '#2563eb',
            timer: 3000
        });
    }

    // Função para mostrar overlay de carregamento
    function showLoading() {
        $('#loadingOverlay').addClass('show');
    }

    // Função para esconder overlay de carregamento
    function hideLoading() {
        $('#loadingOverlay').removeClass('show');
    }

    // Busca em tempo real
    $("#searchInput").on("keyup", function() {
        var value = $(this).val().toLowerCase();
        $(".solicitacao-row").filter(function() {
            $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1)
        });
    });

    // ALTERADO: Usar delegação de eventos para os botões
    $(document).on('click', '.btn-enviar-proposta', function(e) {
        e.preventDefault();
        console.log('Botão enviar proposta clicado');
        
        var solicitacaoId = $(this).data('id');
        currentSolicitacaoId = solicitacaoId;
        $('#solicitacao_id_modal').val(solicitacaoId);
        
        // Resetar o formulário e feedback
        $('#formProposta')[0].reset();
        $('#tipo-peca-error').hide();
        $('.form-control').removeClass('is-invalid');
        
        // Verificar se já existem propostas para esta solicitação
        verificarPropostasExistentes(solicitacaoId);
        
        // Abrir o modal
        var modalProposta = new bootstrap.Modal(document.getElementById('modalProposta'));
        modalProposta.show();
    });

    // ALTERADO: Usar delegação de eventos para os botões de detalhes
    $(document).on('click', '.btn-detalhes', function(e) {
        e.preventDefault();
        console.log('Botão detalhes clicado');
        
        var solicitacaoId = $(this).data('id');
        currentSolicitacaoId = solicitacaoId;
        
        // Mostrar indicador de carregamento
        $('#loading-detalhes').show();
        $('#detalhes-conteudo').hide();
        
        // Abrir o modal
        var modalDetalhes = new bootstrap.Modal(document.getElementById('modalDetalhes'));
        modalDetalhes.show();
        
        // Carregar detalhes
        carregarDetalhes(solicitacaoId);
    });

    // Melhorar a experiência de seleção de checkbox
    $(document).on('click', '.checkbox-item', function(e) {
        if (e.target.type !== 'checkbox') {
            const checkbox = $(this).find('input[type="checkbox"]');
            checkbox.prop('checked', !checkbox.prop('checked')).trigger('change');
        }
    });

    // Função para verificar propostas existentes
    function verificarPropostasExistentes(solicitacaoId) {
        showLoading();
        $.ajax({
            url: 'solicitacoes.php',
            type: 'POST',
            data: {
                action: 'detalhes_solicitacao',
                id: solicitacaoId
            },
            dataType: 'json',
            success: function(response) {
                hideLoading();
                if (response.success && response.data.propostas_existentes && response.data.propostas_existentes.length > 0) {
                    // Mostrar aviso de propostas anteriores
                    $('#aviso-propostas-anteriores').show();
                } else {
                    // Esconder aviso se não houver propostas
                    $('#aviso-propostas-anteriores').hide();
                }
            },
            error: function(xhr, status, error) {
                console.error("Erro na verificação de propostas:", status, error);
                hideLoading();
                $('#aviso-propostas-anteriores').hide();
            }
        });
    }

    // Função para carregar detalhes da solicitação
    function carregarDetalhes(solicitacaoId) {
        $.ajax({
            url: 'solicitacoes.php',
            type: 'POST',
            data: {
                action: 'detalhes_solicitacao',
                id: solicitacaoId
            },
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    var dados = response.data;
                    var html = `
                    <div class="row">
                        <div class="col-md-6">
                            <div class="detail-section">
                                <h4>Informações do Cliente</h4>
                                <div class="detail-row">
                                    <div class="detail-label">Cliente</div>
                                    <div class="detail-value">${dados.nome_cliente}</div>
                                </div>
                                <div class="detail-row">
                                    <div class="detail-label">Solicitação #</div>
                                    <div class="detail-value">${dados.id}</div>
                                </div>
                                <div class="detail-row">
                                    <div class="detail-label">Data da Solicitação</div>
                                    <div class="detail-value">${formatarData(dados.data_solicitacao)}</div>
                                </div>
                                <div class="detail-row">
                                    <div class="detail-label">Método de Entrega</div>
                                    <div class="detail-value">${dados.metodo_entrega}</div>
                                </div>
                                <div class="detail-row">
                                    <div class="detail-label">Endereço</div>
                                    <div class="detail-value">${dados.endereco}</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="detail-section">
                                <h4>Informações do Dispositivo</h4>
                                <div class="detail-row">
                                    <div class="detail-label">Dispositivo</div>
                                    <div class="detail-value">${dados.dispositivo}</div>
                                </div>
                                <div class="detail-row">
                                    <div class="detail-label">Marca</div>
                                    <div class="detail-value">${dados.marca}</div>
                                </div>
                                <div class="detail-row">
                                    <div class="detail-label">Modelo</div>
                                    <div class="detail-value">${dados.modelo}</div>
                                </div>
                                <div class="detail-row">
                                    <div class="detail-label">Memória</div>
                                    <div class="detail-value">${dados.memoria}</div>
                                </div>
                                <div class="detail-row">
                                    <div class="detail-label">Status</div>
                                    <div class="detail-value">
                                        <span class="status-badge status-enviado">${dados.status}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="detail-section">
                        <h4>Descrição do Problema</h4>
                        <div class="detail-row">
                            <div class="detail-value">${dados.descricao_problema}</div>
                        </div>
                    </div>`;

                    if (dados.descricao_detalhada) {
                        html += `
                        <div class="detail-section">
                            <h4>Descrição Detalhada</h4>
                            <div class="detail-row">
                                <div class="detail-value">${dados.descricao_detalhada}</div>
                            </div>
                        </div>`;
                    }

                    html += `
                    <div class="detail-section">
                        <h4>Verificações</h4>
                        <div class="detail-row">
                            <div class="detail-value">${dados.verificacoes}</div>
                        </div>
                    </div>`;

                    // Verificar se há vídeo
                    if (dados.video && dados.video !== '') {
                        var extensao = dados.video.split('.').pop().toLowerCase();
                        var tipo_video = '';
                        var podeReproduzir = true;
                        
                        switch(extensao) {
                            case 'mp4': tipo_video = 'video/mp4'; break;
                            case 'mov': tipo_video = 'video/quicktime'; podeReproduzir = false; break;
                            case 'avi': tipo_video = 'video/x-msvideo'; podeReproduzir = false; break;
                            case 'webm': tipo_video = 'video/webm'; break;
                            default: tipo_video = 'video/mp4';
                        }
                        
                        html += `<div class="detail-section">
                            <h4>Vídeo</h4>`;
                            
                        if (podeReproduzir) {
                            html += `
                            <video controls style="width: 100%; max-height: 300px; border-radius: 8px;">
                                <source src="${dados.video}" type="${tipo_video}">
                                Seu navegador não suporta o elemento de vídeo.
                            </video>`;
                        } else {
                            html += `
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i> O formato do vídeo não é suportado para visualização direta.
                                <a href="${dados.video}" target="_blank" class="alert-link">Clique aqui</a> para baixar o vídeo.
                            </div>`;
                        }
                        
                        html += `</div>`;
                    }

                    // Verificar se há propostas anteriores desta assistência
                    if (dados.propostas_existentes && dados.propostas_existentes.length > 0) {
                        html += `
                        <div class="propostas-existentes">
                            <h5><i class="fas fa-history me-2"></i> Suas Propostas Anteriores</h5>
                            <p class="text-muted">Você já enviou ${dados.propostas_existentes.length} proposta(s) para esta solicitação:</p>
                            <div class="row">`;
                            
                        dados.propostas_existentes.forEach(function(proposta) {
                            html += `
                            <div class="col-md-6 mb-3">
                                <div class="proposta-item">
                                    <div class="proposta-info"><strong>Data:</strong> ${formatarData(proposta.data_proposta)}</div>
                                    <div class="proposta-info"><strong>Preço:</strong> ${proposta.preco}</div>
                                    <div class="proposta-info"><strong>Prazo:</strong> ${proposta.prazo}</div>
                                    <div class="proposta-info"><strong>Observações:</strong><br>${proposta.observacoes}</div>
                                </div>
                            </div>`;
                        });
                        
                        html += `</div></div>`;
                    }

                    // Esconder loader e mostrar conteúdo
                    $('#loading-detalhes').hide();
                    $('#detalhes-conteudo').html(html).show();
                } else {
                    // Mostrar mensagem de erro
                    $('#loading-detalhes').hide();
                    $('#detalhes-conteudo').html(`
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle me-2"></i> ${response.message || 'Erro ao carregar detalhes da solicitação.'}
                    </div>`).show();
                }
            },
            error: function(xhr, status, error) {
                console.error("Erro AJAX:", xhr, status, error);
                // Mostrar mensagem de erro
                $('#loading-detalhes').hide();
                $('#detalhes-conteudo').html(`
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle me-2"></i> Ocorreu um erro ao buscar os detalhes da solicitação.
                </div>`).show();
            }
        });
    }

    // Função para formatar a data
    function formatarData(dataString) {
        const data = new Date(dataString);
        return data.toLocaleDateString('pt-BR', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    // Botão para abrir o modal de nova proposta a partir da tela de detalhes
    $(document).on('click', '#btn-nova-proposta', function(e) {
        e.preventDefault();
        console.log('Botão nova proposta clicado');
        
        // Fechar o modal de detalhes
        var modalDetalhes = bootstrap.Modal.getInstance(document.getElementById('modalDetalhes'));
        modalDetalhes.hide();
        
        setTimeout(function() {
            // Preencher o ID da solicitação no modal de proposta
            $('#solicitacao_id_modal').val(currentSolicitacaoId);
            
            // Resetar o formulário
            $('#formProposta')[0].reset();
            
            // Verificar propostas existentes
            verificarPropostasExistentes(currentSolicitacaoId);
            
            // Abrir o modal de proposta
            var modalProposta = new bootstrap.Modal(document.getElementById('modalProposta'));
            modalProposta.show();
        }, 500);
    });

    // MODIFICADO: Validação do formulário de proposta
    $('#formProposta').on('submit', function(e) {
        // Não usar preventDefault() aqui, pois precisamos que o formulário seja enviado normalmente
        let isValid = true;
        
        // Verificar se pelo menos um checkbox de tipo de peça está selecionado
        const checkboxChecked = $('.tipo-peca-checkbox:checked').length > 0;
        if (!checkboxChecked) {
            e.preventDefault(); // Prevenir envio apenas se inválido
            $('#tipo-peca-error').show();
            isValid = false;
        } else {
            $('#tipo-peca-error').hide();
        }
        
        // Validar outros campos
        const preco = $('#preco').val();
        const prazo = $('#prazo').val();
        const observacoes = $('#observacoes').val();
        
        if (!preco || parseFloat(preco) <= 0) {
            e.preventDefault(); // Prevenir envio apenas se inválido
            $('#preco').addClass('is-invalid');
            isValid = false;
        } else {
            $('#preco').removeClass('is-invalid');
        }
        
        if (!prazo || parseInt(prazo) <= 0) {
            e.preventDefault(); // Prevenir envio apenas se inválido
            $('#prazo').addClass('is-invalid');
            isValid = false;
        } else {
            $('#prazo').removeClass('is-invalid');
        }
        
        if (!observacoes.trim()) {
            e.preventDefault(); // Prevenir envio apenas se inválido
            $('#observacoes').addClass('is-invalid');
            isValid = false;
        } else {
            $('#observacoes').removeClass('is-invalid');
        }
        
        if (!isValid) {
            e.preventDefault(); // Prevenir envio apenas se inválido
            Swal.fire({
                icon: 'error',
                title: 'Erro no formulário',
                text: 'Por favor, preencha todos os campos obrigatórios corretamente e selecione pelo menos um tipo de peça.',
                confirmButtonColor: '#2563eb'
            });
            return false;
        }
        
        // Se tudo estiver válido, mostrar loader
        showLoading();
        console.log('Formulário validado e sendo enviado');
        // O formulário será enviado naturalmente (sem chamada a this.submit())
        return true;
    });

    // Ocultar mensagens de erro quando o usuário começa a preencher
    $('.tipo-peca-checkbox').on('change', function() {
        if ($('.tipo-peca-checkbox:checked').length > 0) {
            $('#tipo-peca-error').hide();
        }
    });
    
    $('#preco, #prazo, #observacoes').on('input', function() {
        $(this).removeClass('is-invalid');
    });

    // Formatar o campo de preço para formato de moeda
    $('#preco').on('input', function() {
        let value = $(this).val().replace(/[^\d.]/g, '');
        if (value.split('.').length > 2) {
            value = value.replace(/\.+$/, '');
        }
        $(this).val(value);
    });
    
    // Adicione esta linha para testar se o jQuery está sendo carregado corretamente
    console.log('jQuery carregado:', typeof $ !== 'undefined');
    console.log('Script inicializado com sucesso');
});
    </script>
</body>
</html>