<?php
/**
 * Gerador de Relatórios em PDF
 * FixFácil Assistências - Sistema Novo
 */

require_once '../config/auth.php';
require_once '../config/database.php';

// Verificar autenticação
$auth = getAuth();
$auth->checkAssistenciaAuth();

// Obter dados do usuário
$usuario = $auth->getUsuarioLogado();
$plano = $auth->getPlanoInfo($usuario['id']);
$db = getDatabase();

// Parâmetros do relatório
$tipo = $_GET['tipo'] ?? 'financeiro';
$periodo = $_GET['periodo'] ?? 'mes_atual';
$formato = $_GET['formato'] ?? 'pdf';

// Calcular datas
$data_inicio = '';
$data_fim = '';

switch ($periodo) {
    case 'mes_atual':
        $data_inicio = date('Y-m-01');
        $data_fim = date('Y-m-t');
        $periodo_nome = 'Mês Atual (' . date('m/Y') . ')';
        break;
    case 'mes_anterior':
        $data_inicio = date('Y-m-01', strtotime('first day of last month'));
        $data_fim = date('Y-m-t', strtotime('last day of last month'));
        $periodo_nome = 'Mês Anterior (' . date('m/Y', strtotime('last month')) . ')';
        break;
    case 'trimestre':
        $data_inicio = date('Y-m-01', strtotime('-2 months'));
        $data_fim = date('Y-m-t');
        $periodo_nome = 'Último Trimestre';
        break;
    case 'ano':
        $data_inicio = date('Y-01-01');
        $data_fim = date('Y-12-31');
        $periodo_nome = 'Ano Atual (' . date('Y') . ')';
        break;
}

// Gerar relatório baseado no tipo
switch ($tipo) {
    case 'financeiro':
        $dados = gerarRelatorioFinanceiro($usuario, $plano, $data_inicio, $data_fim, $db);
        $titulo = 'Relatório Financeiro';
        break;
    case 'reparos':
        $dados = gerarRelatorioReparos($usuario, $data_inicio, $data_fim, $db);
        $titulo = 'Relatório de Reparos';
        break;
    case 'marketplace':
        if (!$auth->hasAccess('marketplace')) {
            die('Acesso negado ao marketplace');
        }
        $dados = gerarRelatorioMarketplace($usuario, $data_inicio, $data_fim, $db);
        $titulo = 'Relatório do Marketplace';
        break;
    default:
        die('Tipo de relatório inválido');
}

// Gerar PDF ou HTML
if ($formato === 'pdf') {
    gerarPDF($titulo, $periodo_nome, $dados, $usuario, $plano);
} else {
    gerarHTML($titulo, $periodo_nome, $dados, $usuario, $plano);
}

/**
 * Gerar relatório financeiro
 */
function gerarRelatorioFinanceiro($usuario, $plano, $data_inicio, $data_fim, $db) {
    $dados = [];
    
    // Resumo financeiro
    $sql = "
        SELECT 
            COUNT(*) as total_reparos,
            COALESCE(SUM(preco), 0) as receita_bruta,
            COALESCE(SUM(preco * (1 - ?/100)), 0) as receita_liquida,
            COALESCE(SUM(preco * (?/100)), 0) as taxa_fixfacil,
            COALESCE(AVG(preco), 0) as ticket_medio
        FROM propostas_assistencia 
        WHERE assistencia_id = ? 
        AND status = 'Concluída' 
        AND pago = 1
        AND DATE(data_conclusao) BETWEEN ? AND ?
    ";
    
    $result = $db->query($sql, [
        $plano['taxa_servico'], 
        $plano['taxa_servico'], 
        $usuario['assistencia_id'], 
        $data_inicio, 
        $data_fim
    ]);
    $dados['resumo'] = $result->fetch_assoc();
    
    // Reparos por mês
    $sql = "
        SELECT 
            DATE_FORMAT(data_conclusao, '%Y-%m') as mes,
            COUNT(*) as quantidade,
            SUM(preco * (1 - ?/100)) as receita
        FROM propostas_assistencia 
        WHERE assistencia_id = ? 
        AND status = 'Concluída' 
        AND pago = 1
        AND DATE(data_conclusao) BETWEEN ? AND ?
        GROUP BY DATE_FORMAT(data_conclusao, '%Y-%m')
        ORDER BY mes
    ";
    
    $result = $db->query($sql, [
        $plano['taxa_servico'], 
        $usuario['assistencia_id'], 
        $data_inicio, 
        $data_fim
    ]);
    
    $dados['por_mes'] = [];
    while ($row = $result->fetch_assoc()) {
        $dados['por_mes'][] = $row;
    }
    
    // Top dispositivos
    $sql = "
        SELECT 
            CONCAT(sr.marca, ' ', sr.modelo) as dispositivo,
            COUNT(*) as quantidade,
            SUM(pa.preco * (1 - ?/100)) as receita
        FROM propostas_assistencia pa
        JOIN solicitacoes_reparo sr ON pa.solicitacao_id = sr.id
        WHERE pa.assistencia_id = ? 
        AND pa.status = 'Concluída' 
        AND pa.pago = 1
        AND DATE(pa.data_conclusao) BETWEEN ? AND ?
        GROUP BY sr.marca, sr.modelo
        ORDER BY quantidade DESC
        LIMIT 10
    ";
    
    $result = $db->query($sql, [
        $plano['taxa_servico'], 
        $usuario['assistencia_id'], 
        $data_inicio, 
        $data_fim
    ]);
    
    $dados['top_dispositivos'] = [];
    while ($row = $result->fetch_assoc()) {
        $dados['top_dispositivos'][] = $row;
    }
    
    return $dados;
}

/**
 * Gerar relatório de reparos
 */
function gerarRelatorioReparos($usuario, $data_inicio, $data_fim, $db) {
    $dados = [];
    
    // Resumo de reparos
    $sql = "
        SELECT 
            COUNT(*) as total,
            COUNT(CASE WHEN status = 'aceita' THEN 1 END) as aceitas,
            COUNT(CASE WHEN status = 'Em Andamento' THEN 1 END) as em_andamento,
            COUNT(CASE WHEN status = 'Concluída' THEN 1 END) as concluidas,
            COALESCE(AVG(CASE WHEN status = 'Concluída' THEN DATEDIFF(data_conclusao, data_proposta) END), 0) as tempo_medio
        FROM propostas_assistencia 
        WHERE assistencia_id = ?
        AND DATE(data_proposta) BETWEEN ? AND ?
    ";
    
    $result = $db->query($sql, [$usuario['assistencia_id'], $data_inicio, $data_fim]);
    $dados['resumo'] = $result->fetch_assoc();
    
    // Reparos detalhados
    $sql = "
        SELECT 
            pa.id,
            pa.preco,
            pa.prazo,
            pa.status,
            pa.data_proposta,
            pa.data_conclusao,
            CONCAT(sr.marca, ' ', sr.modelo) as dispositivo,
            sr.descricao_problema,
            u.nome as cliente_nome
        FROM propostas_assistencia pa
        JOIN solicitacoes_reparo sr ON pa.solicitacao_id = sr.id
        JOIN usuarios u ON sr.usuario_id = u.id
        WHERE pa.assistencia_id = ?
        AND DATE(pa.data_proposta) BETWEEN ? AND ?
        ORDER BY pa.data_proposta DESC
    ";
    
    $result = $db->query($sql, [$usuario['assistencia_id'], $data_inicio, $data_fim]);
    
    $dados['detalhes'] = [];
    while ($row = $result->fetch_assoc()) {
        $dados['detalhes'][] = $row;
    }
    
    return $dados;
}

/**
 * Gerar relatório do marketplace
 */
function gerarRelatorioMarketplace($usuario, $data_inicio, $data_fim, $db) {
    $dados = [];
    
    // Resumo do marketplace
    $sql = "
        SELECT 
            COUNT(p.id) as total_produtos,
            COUNT(CASE WHEN p.status = 'ativo' THEN 1 END) as produtos_ativos,
            COUNT(v.id) as total_vendas,
            COALESCE(SUM(v.valor_total), 0) as receita_total
        FROM produtos_marketplace p
        LEFT JOIN vendas_marketplace v ON p.id = v.produto_id 
            AND v.status = 'concluida'
            AND DATE(v.data_venda) BETWEEN ? AND ?
        WHERE p.assistencia_id = ?
    ";
    
    $result = $db->query($sql, [$data_inicio, $data_fim, $usuario['assistencia_id']]);
    $dados['resumo'] = $result->fetch_assoc();
    
    // Produtos mais vendidos
    $sql = "
        SELECT 
            p.nome,
            p.categoria,
            COUNT(v.id) as vendas,
            SUM(v.quantidade) as quantidade_vendida,
            SUM(v.valor_total) as receita
        FROM produtos_marketplace p
        JOIN vendas_marketplace v ON p.id = v.produto_id
        WHERE p.assistencia_id = ?
        AND v.status = 'concluida'
        AND DATE(v.data_venda) BETWEEN ? AND ?
        GROUP BY p.id
        ORDER BY vendas DESC
        LIMIT 10
    ";
    
    $result = $db->query($sql, [$usuario['assistencia_id'], $data_inicio, $data_fim]);
    
    $dados['top_produtos'] = [];
    while ($row = $result->fetch_assoc()) {
        $dados['top_produtos'][] = $row;
    }
    
    return $dados;
}

/**
 * Gerar PDF usando HTML/CSS
 */
function gerarPDF($titulo, $periodo, $dados, $usuario, $plano) {
    // Headers para download
    header('Content-Type: application/pdf');
    header('Content-Disposition: attachment; filename="' . sanitizeFilename($titulo . '_' . $periodo) . '.pdf"');
    
    // Aqui você pode usar uma biblioteca como TCPDF, FPDF ou mPDF
    // Por simplicidade, vou gerar HTML que pode ser convertido para PDF
    
    $html = gerarHTMLRelatorio($titulo, $periodo, $dados, $usuario, $plano);
    
    // Se tiver mPDF instalado:
    /*
    require_once '../vendor/autoload.php';
    $mpdf = new \Mpdf\Mpdf();
    $mpdf->WriteHTML($html);
    $mpdf->Output();
    */
    
    // Por enquanto, retorna HTML
    echo $html;
}

/**
 * Gerar HTML do relatório
 */
function gerarHTML($titulo, $periodo, $dados, $usuario, $plano) {
    header('Content-Type: text/html; charset=utf-8');
    echo gerarHTMLRelatorio($titulo, $periodo, $dados, $usuario, $plano);
}

/**
 * Gerar HTML do relatório
 */
function gerarHTMLRelatorio($titulo, $periodo, $dados, $usuario, $plano) {
    ob_start();
    ?>
    <!DOCTYPE html>
    <html lang="pt-BR">
    <head>
        <meta charset="UTF-8">
        <title><?php echo $titulo; ?> - <?php echo $periodo; ?></title>
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #667eea; padding-bottom: 20px; }
            .logo { font-size: 24px; font-weight: bold; color: #667eea; }
            .info-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 30px; }
            .info-box { background: #f8f9fa; padding: 15px; border-radius: 8px; }
            .table { width: 100%; border-collapse: collapse; margin-bottom: 30px; }
            .table th, .table td { border: 1px solid #ddd; padding: 8px; text-align: left; }
            .table th { background: #667eea; color: white; }
            .summary-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 30px; }
            .summary-card { background: #f8f9fa; padding: 15px; border-radius: 8px; text-align: center; }
            .summary-value { font-size: 24px; font-weight: bold; color: #667eea; }
            .summary-label { color: #666; font-size: 14px; }
        </style>
    </head>
    <body>
        <div class="header">
            <div class="logo">FixFácil Assistências</div>
            <h1><?php echo $titulo; ?></h1>
            <p><strong>Período:</strong> <?php echo $periodo; ?></p>
            <p><strong>Gerado em:</strong> <?php echo date('d/m/Y H:i'); ?></p>
        </div>
        
        <div class="info-grid">
            <div class="info-box">
                <h3>Assistência Técnica</h3>
                <p><strong>Nome:</strong> <?php echo htmlspecialchars($usuario['nome_empresa'] ?? $usuario['nome']); ?></p>
                <p><strong>Responsável:</strong> <?php echo htmlspecialchars($usuario['nome']); ?></p>
                <p><strong>E-mail:</strong> <?php echo htmlspecialchars($usuario['email']); ?></p>
            </div>
            <div class="info-box">
                <h3>Plano Atual</h3>
                <p><strong>Plano:</strong> <?php echo $plano['nome']; ?></p>
                <p><strong>Taxa de Serviço:</strong> <?php echo number_format($plano['taxa_servico'], 1); ?>%</p>
                <p><strong>Valor Mensal:</strong> R$ <?php echo number_format($plano['preco_mensal'], 2, ',', '.'); ?></p>
            </div>
        </div>
        
        <?php if (isset($dados['resumo'])): ?>
        <div class="summary-grid">
            <?php foreach ($dados['resumo'] as $key => $value): ?>
            <div class="summary-card">
                <div class="summary-value">
                    <?php 
                    if (strpos($key, 'receita') !== false || strpos($key, 'taxa') !== false || strpos($key, 'ticket') !== false) {
                        echo 'R$ ' . number_format($value, 2, ',', '.');
                    } else {
                        echo number_format($value, 0, ',', '.');
                    }
                    ?>
                </div>
                <div class="summary-label"><?php echo ucwords(str_replace('_', ' ', $key)); ?></div>
            </div>
            <?php endforeach; ?>
        </div>
        <?php endif; ?>
        
        <?php if (isset($dados['detalhes']) && !empty($dados['detalhes'])): ?>
        <h3>Detalhes dos Reparos</h3>
        <table class="table">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>Cliente</th>
                    <th>Dispositivo</th>
                    <th>Problema</th>
                    <th>Valor</th>
                    <th>Status</th>
                    <th>Data</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($dados['detalhes'] as $item): ?>
                <tr>
                    <td><?php echo $item['id']; ?></td>
                    <td><?php echo htmlspecialchars($item['cliente_nome']); ?></td>
                    <td><?php echo htmlspecialchars($item['dispositivo']); ?></td>
                    <td><?php echo htmlspecialchars(substr($item['descricao_problema'], 0, 50)) . '...'; ?></td>
                    <td>R$ <?php echo number_format($item['preco'], 2, ',', '.'); ?></td>
                    <td><?php echo $item['status']; ?></td>
                    <td><?php echo date('d/m/Y', strtotime($item['data_proposta'])); ?></td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
        <?php endif; ?>
        
        <div style="margin-top: 50px; text-align: center; color: #666; font-size: 12px;">
            <p>Relatório gerado automaticamente pelo sistema FixFácil Assistências</p>
            <p>© <?php echo date('Y'); ?> FixFácil - Todos os direitos reservados</p>
        </div>
    </body>
    </html>
    <?php
    return ob_get_clean();
}

/**
 * Sanitizar nome do arquivo
 */
function sanitizeFilename($filename) {
    return preg_replace('/[^a-zA-Z0-9_-]/', '_', $filename);
}
?>
