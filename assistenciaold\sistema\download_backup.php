<?php
/**
 * Download de Backup
 * FixFácil Assistências - Sistema Novo
 */

require_once '../config/auth.php';

// Verificar autenticação e permissão de admin
$auth = getAuth();
try {
    $auth->checkAssistenciaAuth();
} catch (Exception $e) {
    die('Não autorizado');
}

$usuario = $auth->getUsuarioLogado();

// Verificar se é admin
if ($usuario['email'] !== '<EMAIL>') {
    die('Acesso negado');
}

$arquivo = $_GET['arquivo'] ?? '';

if (empty($arquivo)) {
    die('Arquivo não especificado');
}

// Validar nome do arquivo para segurança
if (!preg_match('/^backup_\d{4}-\d{2}-\d{2}_\d{2}-\d{2}-\d{2}\.sql\.gz$/', $arquivo)) {
    die('Nome de arquivo inválido');
}

$caminho_arquivo = __DIR__ . '/../../backups/' . $arquivo;

if (!file_exists($caminho_arquivo)) {
    die('Arquivo não encontrado');
}

// Headers para download
header('Content-Type: application/gzip');
header('Content-Disposition: attachment; filename="' . $arquivo . '"');
header('Content-Length: ' . filesize($caminho_arquivo));
header('Cache-Control: no-cache, must-revalidate');
header('Expires: 0');

// Enviar arquivo
readfile($caminho_arquivo);
exit();
?>
