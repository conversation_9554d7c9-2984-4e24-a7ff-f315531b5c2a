<?php
/**
 * Perfil - Mobile First
 * FixFácil Assistências - Design Moderno
 */

// Redirecionar para versão mobile final
header('Location: perfil_new.php?' . $_SERVER['QUERY_STRING']);
exit();
?>

// Configuração de banco de dados
$host = "localhost";
$username = "u680766645_fixfacilnew";
$password = "T3cn0l0g1a@";
$database = "u680766645_fixfacilnew";

$mysqli = new mysqli($host, $username, $password, $database);

if ($mysqli->connect_error) {
    die("Erro de conexão: " . $mysqli->connect_error);
}

// Obter dados do usuário logado
$usuario_id = $_SESSION['usuario_id'];
$usuario = null;
$plano = null;
$assistencia_data = null;

try {
    // Buscar dados do usuário
    $sql = "SELECT u.id, u.nome, u.email, u.telefone, u.plano_id, at.id as assistencia_id,
                   at.nome_empresa, at.endereco, at.cep, at.numero_endereco, at.bairro, 
                   at.cidade, at.estado, at.ponto_referencia, at.telefone_empresa, 
                   at.email_empresa, at.site
            FROM usuarios u 
            LEFT JOIN assistencias_tecnicas at ON u.id = at.usuario_id 
            WHERE u.id = ?";
    $stmt = $mysqli->prepare($sql);
    $stmt->bind_param("i", $usuario_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $usuario = $result->fetch_assoc();
    
    // Buscar informações do plano
    if ($usuario && $usuario['plano_id']) {
        $sql = "SELECT * FROM planos WHERE id = ?";
        $stmt = $mysqli->prepare($sql);
        $stmt->bind_param("i", $usuario['plano_id']);
        $stmt->execute();
        $result = $stmt->get_result();
        $plano = $result->fetch_assoc();
    }
    
    // Plano padrão se não encontrar
    if (!$plano) {
        $plano = [
            'nome' => 'Free',
            'taxa_servico' => 25.00,
            'descricao' => 'Plano gratuito'
        ];
    }
    
} catch (Exception $e) {
    error_log("Erro ao buscar dados do usuário: " . $e->getMessage());
    $usuario = ['nome' => 'Usuário', 'email' => '', 'assistencia_id' => null];
    $plano = ['nome' => 'Free', 'taxa_servico' => 25.00];
}

// Processar formulário
$erro = '';
$sucesso = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $acao = $_POST['acao'] ?? '';
    
    if ($acao === 'dados_pessoais') {
        $nome = trim($_POST['nome'] ?? '');
        $email = trim($_POST['email'] ?? '');
        $telefone = trim($_POST['telefone'] ?? '');
        
        // Validações
        if (empty($nome)) {
            $erro = 'Nome é obrigatório.';
        } elseif (empty($email) || !filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $erro = 'E-mail válido é obrigatório.';
        } elseif (empty($telefone)) {
            $erro = 'Telefone é obrigatório.';
        } else {
            try {
                // Verificar se email já existe (exceto o próprio)
                $sql = "SELECT id FROM usuarios WHERE email = ? AND id != ?";
                $stmt = $mysqli->prepare($sql);
                $stmt->bind_param("si", $email, $usuario['id']);
                $stmt->execute();
                $result = $stmt->get_result();
                
                if ($result->fetch_assoc()) {
                    $erro = 'Este e-mail já está sendo usado por outro usuário.';
                } else {
                    // Atualizar dados
                    $sql = "UPDATE usuarios SET nome = ?, email = ?, telefone = ? WHERE id = ?";
                    $stmt = $mysqli->prepare($sql);
                    $stmt->bind_param("sssi", $nome, $email, $telefone, $usuario['id']);
                    $stmt->execute();
                    
                    $sucesso = 'Dados pessoais atualizados com sucesso!';
                    
                    // Atualizar sessão
                    $_SESSION['nome'] = $nome;
                    
                    // Recarregar dados do usuário
                    $usuario['nome'] = $nome;
                    $usuario['email'] = $email;
                    $usuario['telefone'] = $telefone;
                }
            } catch (Exception $e) {
                error_log("Erro ao atualizar dados pessoais: " . $e->getMessage());
                $erro = 'Erro interno. Tente novamente.';
            }
        }
    } elseif ($acao === 'dados_empresa') {
        $nome_empresa = trim($_POST['nome_empresa'] ?? '');
        $endereco = trim($_POST['endereco'] ?? '');
        $cep = trim($_POST['cep'] ?? '');
        $numero_endereco = trim($_POST['numero_endereco'] ?? '');
        $bairro = trim($_POST['bairro'] ?? '');
        $cidade = trim($_POST['cidade'] ?? '');
        $estado = trim($_POST['estado'] ?? '');
        $ponto_referencia = trim($_POST['ponto_referencia'] ?? '');
        $telefone_empresa = trim($_POST['telefone_empresa'] ?? '');
        $email_empresa = trim($_POST['email_empresa'] ?? '');
        $site = trim($_POST['site'] ?? '');

        // Validações
        if (empty($nome_empresa)) {
            $erro = 'Nome da empresa é obrigatório.';
        } elseif (empty($cep) || strlen(preg_replace('/\D/', '', $cep)) !== 8) {
            $erro = 'CEP válido é obrigatório.';
        } elseif (empty($numero_endereco)) {
            $erro = 'Número do endereço é obrigatório.';
        } elseif (empty($bairro)) {
            $erro = 'Bairro é obrigatório.';
        } elseif (empty($cidade)) {
            $erro = 'Cidade é obrigatória.';
        } elseif (empty($estado)) {
            $erro = 'Estado é obrigatório.';
        } elseif (!empty($email_empresa) && !filter_var($email_empresa, FILTER_VALIDATE_EMAIL)) {
            $erro = 'E-mail da empresa deve ser válido.';
        } else {
            try {
                // Limpar CEP
                $cep = preg_replace('/\D/', '', $cep);
                
                // Verificar se assistência existe
                if ($usuario['assistencia_id']) {
                    // Atualizar
                    $sql = "UPDATE assistencias_tecnicas SET 
                               nome_empresa = ?, endereco = ?, cep = ?, numero_endereco = ?, 
                               bairro = ?, cidade = ?, estado = ?, ponto_referencia = ?, 
                               telefone_empresa = ?, email_empresa = ?, site = ? 
                            WHERE usuario_id = ?";
                    $stmt = $mysqli->prepare($sql);
                    $stmt->bind_param("sssssssssssi", $nome_empresa, $endereco, $cep, $numero_endereco,
                                     $bairro, $cidade, $estado, $ponto_referencia, $telefone_empresa,
                                     $email_empresa, $site, $usuario['id']);
                } else {
                    // Inserir
                    $sql = "INSERT INTO assistencias_tecnicas 
                               (usuario_id, nome_empresa, endereco, cep, numero_endereco, 
                                bairro, cidade, estado, ponto_referencia, telefone_empresa, 
                                email_empresa, site) 
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
                    $stmt = $mysqli->prepare($sql);
                    $stmt->bind_param("isssssssssss", $usuario['id'], $nome_empresa, $endereco, $cep,
                                     $numero_endereco, $bairro, $cidade, $estado, $ponto_referencia,
                                     $telefone_empresa, $email_empresa, $site);
                }
                
                $stmt->execute();
                $sucesso = 'Dados da empresa atualizados com sucesso!';
                
                // Atualizar dados locais
                $usuario['nome_empresa'] = $nome_empresa;
                $usuario['endereco'] = $endereco;
                $usuario['cep'] = $cep;
                $usuario['numero_endereco'] = $numero_endereco;
                $usuario['bairro'] = $bairro;
                $usuario['cidade'] = $cidade;
                $usuario['estado'] = $estado;
                $usuario['ponto_referencia'] = $ponto_referencia;
                $usuario['telefone_empresa'] = $telefone_empresa;
                $usuario['email_empresa'] = $email_empresa;
                $usuario['site'] = $site;
                
            } catch (Exception $e) {
                error_log("Erro ao atualizar dados da empresa: " . $e->getMessage());
                $erro = 'Erro interno. Tente novamente.';
            }
        }
    }
}

// Obter estatísticas do perfil
$stats = [
    'total_solicitacoes' => 0,
    'reparos_concluidos' => 0,
    'avaliacao_media' => 4.8,
    'dias_ativo' => 0
];

try {
    if ($usuario && isset($usuario['assistencia_id'])) {
        $assistencia_id = $usuario['assistencia_id'];
        
        // Total de solicitações
        $sql = "SELECT COUNT(*) as total FROM solicitacoes_reparo WHERE assistencia_id = ?";
        $stmt = $mysqli->prepare($sql);
        $stmt->bind_param("i", $assistencia_id);
        $stmt->execute();
        $result = $stmt->get_result();
        $stats['total_solicitacoes'] = $result->fetch_assoc()['total'];
        
        // Reparos concluídos
        $sql = "SELECT COUNT(*) as total FROM solicitacoes_reparo WHERE assistencia_id = ? AND status = 'concluido'";
        $stmt = $mysqli->prepare($sql);
        $stmt->bind_param("i", $assistencia_id);
        $stmt->execute();
        $result = $stmt->get_result();
        $stats['reparos_concluidos'] = $result->fetch_assoc()['total'];
        
        // Dias ativo (desde a criação do usuário)
        $sql = "SELECT DATEDIFF(NOW(), data_criacao) as dias FROM usuarios WHERE id = ?";
        $stmt = $mysqli->prepare($sql);
        $stmt->bind_param("i", $usuario['id']);
        $stmt->execute();
        $result = $stmt->get_result();
        $row = $result->fetch_assoc();
        $stats['dias_ativo'] = $row['dias'] ?? 0;
    }
} catch (Exception $e) {
    error_log("Erro ao calcular estatísticas: " . $e->getMessage());
}
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Perfil - FixFacil</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            min-height: 100vh;
            color: #1e293b;
        }

        .container {
            max-width: 414px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
            position: relative;
            overflow-x: hidden;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }

        .header {
            background: linear-gradient(135deg, #059669 0%, #065f46 100%);
            color: white;
            padding: 24px 20px 20px 20px;
            position: relative;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 20"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="0.5" fill="white" opacity="0.05"/></pattern></defs><rect width="100" height="20" fill="url(%23grain)"/></svg>');
        }

        .header-content {
            position: relative;
            z-index: 1;
        }

        .header-top {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 20px;
        }

        .back-btn {
            width: 40px;
            height: 40px;
            background: rgba(255,255,255,0.15);
            border: none;
            border-radius: 12px;
            color: white;
            font-size: 18px;
            cursor: pointer;
            backdrop-filter: blur(10px);
            transition: all 0.2s ease;
            text-decoration: none;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .back-btn:hover {
            background: rgba(255,255,255,0.25);
            color: white;
            text-decoration: none;
        }

        .page-title {
            font-size: 20px;
            font-weight: 700;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .header-actions {
            display: flex;
            gap: 8px;
        }

        .action-btn {
            width: 40px;
            height: 40px;
            background: rgba(255,255,255,0.15);
            border: none;
            border-radius: 12px;
            color: white;
            font-size: 18px;
            cursor: pointer;
            backdrop-filter: blur(10px);
            transition: all 0.2s ease;
            text-decoration: none;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .action-btn:hover {
            background: rgba(255,255,255,0.25);
            color: white;
            text-decoration: none;
        }

        .profile-summary {
            background: rgba(255,255,255,0.15);
            border-radius: 12px;
            padding: 16px;
            backdrop-filter: blur(10px);
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .profile-avatar {
            width: 60px;
            height: 60px;
            background: rgba(255,255,255,0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            font-weight: 700;
            backdrop-filter: blur(10px);
        }

        .profile-info {
            flex: 1;
        }

        .profile-name {
            font-size: 18px;
            font-weight: 700;
            margin-bottom: 4px;
        }

        .profile-plan {
            font-size: 12px;
            opacity: 0.9;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .plan-badge {
            background: rgba(255,255,255,0.2);
            padding: 2px 8px;
            border-radius: 8px;
            font-size: 10px;
            font-weight: 600;
        }

        .content {
            padding: 20px;
            padding-bottom: 100px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
            margin-bottom: 20px;
        }

        .stat-card {
            background: white;
            border-radius: 12px;
            padding: 16px;
            text-align: center;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
        }

        .stat-number {
            font-size: 24px;
            font-weight: 700;
            color: #059669;
            margin-bottom: 4px;
        }

        .stat-label {
            font-size: 12px;
            color: #64748b;
        }

        .section-card {
            background: white;
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
        }

        .section-title {
            font-size: 18px;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .form-group {
            margin-bottom: 16px;
        }

        .form-label {
            display: block;
            font-size: 14px;
            font-weight: 600;
            color: #374151;
            margin-bottom: 6px;
        }

        .form-input {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            font-size: 16px;
            transition: all 0.2s ease;
            background: white;
        }

        .form-input:focus {
            outline: none;
            border-color: #059669;
            box-shadow: 0 0 0 3px rgba(5, 150, 105, 0.1);
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
        }

        .btn-primary {
            width: 100%;
            padding: 12px 24px;
            background: linear-gradient(135deg, #059669, #065f46);
            color: white;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(5, 150, 105, 0.4);
        }

        .alert {
            padding: 12px 16px;
            border-radius: 12px;
            margin-bottom: 16px;
            font-size: 14px;
            font-weight: 500;
        }

        .alert-success {
            background: #dcfce7;
            color: #16a34a;
            border: 1px solid #bbf7d0;
        }

        .alert-error {
            background: #fecaca;
            color: #dc2626;
            border: 1px solid #fca5a5;
        }

        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 100%;
            max-width: 414px;
            background: white;
            border-top: 1px solid #e2e8f0;
            padding: 12px 20px;
            display: flex;
            justify-content: space-around;
            box-shadow: 0 -4px 20px rgba(0,0,0,0.1);
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
            cursor: pointer;
            padding: 8px;
            border-radius: 12px;
            transition: all 0.2s ease;
            position: relative;
            text-decoration: none;
            color: inherit;
        }

        .nav-item.active {
            background: #f0fdf4;
            color: #059669;
        }

        .nav-item:hover {
            color: #059669;
            text-decoration: none;
        }

        .nav-icon {
            font-size: 20px;
        }

        .nav-label {
            font-size: 10px;
            font-weight: 500;
        }

        .nav-badge {
            position: absolute;
            top: 4px;
            right: 4px;
            background: #ef4444;
            color: white;
            border-radius: 50%;
            width: 16px;
            height: 16px;
            font-size: 10px;
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .floating-action {
            position: fixed;
            bottom: 80px;
            right: 20px;
            width: 56px;
            height: 56px;
            background: linear-gradient(135deg, #059669, #065f46);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            cursor: pointer;
            box-shadow: 0 4px 16px rgba(5, 150, 105, 0.4);
            transition: all 0.3s ease;
            z-index: 100;
            text-decoration: none;
        }

        .floating-action:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 20px rgba(5, 150, 105, 0.5);
            color: white;
            text-decoration: none;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .content > * {
            animation: fadeIn 0.6s ease;
        }

        @media (max-width: 480px) {
            .container {
                max-width: 100vw;
                box-shadow: none;
            }
            
            .header {
                padding: 20px 16px 16px 16px;
            }
            
            .content {
                padding: 16px;
                padding-bottom: 100px;
            }
            
            .form-row {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="header-content">
                <div class="header-top">
                    <a href="dashboard_mobile.php" class="back-btn">←</a>
                    <div class="page-title">
                        👤 Perfil
                    </div>
                    <div class="header-actions">
                        <a href="logout.php" class="action-btn" title="Sair">🚪</a>
                    </div>
                </div>

                <div class="profile-summary">
                    <div class="profile-avatar">
                        <?php echo strtoupper(substr($usuario['nome'], 0, 2)); ?>
                    </div>
                    <div class="profile-info">
                        <div class="profile-name"><?php echo htmlspecialchars($usuario['nome']); ?></div>
                        <div class="profile-plan">
                            <span>Plano:</span>
                            <span class="plan-badge"><?php echo htmlspecialchars($plano['nome']); ?></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Content -->
        <div class="content">
            <!-- Statistics -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number"><?php echo $stats['total_solicitacoes']; ?></div>
                    <div class="stat-label">Total de Solicitações</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo $stats['reparos_concluidos']; ?></div>
                    <div class="stat-label">Reparos Concluídos</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo number_format($stats['avaliacao_media'], 1); ?>⭐</div>
                    <div class="stat-label">Avaliação Média</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo $stats['dias_ativo']; ?></div>
                    <div class="stat-label">Dias Ativo</div>
                </div>
            </div>

            <!-- Alerts -->
            <?php if ($sucesso): ?>
                <div class="alert alert-success">
                    ✅ <?php echo htmlspecialchars($sucesso); ?>
                </div>
            <?php endif; ?>

            <?php if ($erro): ?>
                <div class="alert alert-error">
                    ❌ <?php echo htmlspecialchars($erro); ?>
                </div>
            <?php endif; ?>

            <!-- Dados Pessoais -->
            <div class="section-card">
                <div class="section-title">
                    👤 Dados Pessoais
                </div>
                
                <form method="POST" action="">
                    <input type="hidden" name="acao" value="dados_pessoais">
                    
                    <div class="form-group">
                        <label class="form-label">Nome Completo</label>
                        <input type="text" name="nome" class="form-input" value="<?php echo htmlspecialchars($usuario['nome']); ?>" required>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">E-mail</label>
                        <input type="email" name="email" class="form-input" value="<?php echo htmlspecialchars($usuario['email']); ?>" required>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">Telefone</label>
                        <input type="tel" name="telefone" class="form-input" value="<?php echo htmlspecialchars($usuario['telefone']); ?>" required>
                    </div>
                    
                    <button type="submit" class="btn-primary">
                        Atualizar Dados Pessoais
                    </button>
                </form>
            </div>

            <!-- Dados da Empresa -->
            <div class="section-card">
                <div class="section-title">
                    🏢 Dados da Empresa
                </div>
                
                <form method="POST" action="">
                    <input type="hidden" name="acao" value="dados_empresa">
                    
                    <div class="form-group">
                        <label class="form-label">Nome da Empresa</label>
                        <input type="text" name="nome_empresa" class="form-input" value="<?php echo htmlspecialchars($usuario['nome_empresa'] ?? ''); ?>" required>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">Endereço</label>
                        <input type="text" name="endereco" class="form-input" value="<?php echo htmlspecialchars($usuario['endereco'] ?? ''); ?>" required>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">CEP</label>
                            <input type="text" name="cep" class="form-input" value="<?php echo htmlspecialchars($usuario['cep'] ?? ''); ?>" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Número</label>
                            <input type="text" name="numero_endereco" class="form-input" value="<?php echo htmlspecialchars($usuario['numero_endereco'] ?? ''); ?>" required>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">Bairro</label>
                            <input type="text" name="bairro" class="form-input" value="<?php echo htmlspecialchars($usuario['bairro'] ?? ''); ?>" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Cidade</label>
                            <input type="text" name="cidade" class="form-input" value="<?php echo htmlspecialchars($usuario['cidade'] ?? ''); ?>" required>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">Estado</label>
                        <input type="text" name="estado" class="form-input" value="<?php echo htmlspecialchars($usuario['estado'] ?? ''); ?>" required>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">Ponto de Referência</label>
                        <input type="text" name="ponto_referencia" class="form-input" value="<?php echo htmlspecialchars($usuario['ponto_referencia'] ?? ''); ?>">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">Telefone da Empresa</label>
                        <input type="tel" name="telefone_empresa" class="form-input" value="<?php echo htmlspecialchars($usuario['telefone_empresa'] ?? ''); ?>">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">E-mail da Empresa</label>
                        <input type="email" name="email_empresa" class="form-input" value="<?php echo htmlspecialchars($usuario['email_empresa'] ?? ''); ?>">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">Site</label>
                        <input type="url" name="site" class="form-input" value="<?php echo htmlspecialchars($usuario['site'] ?? ''); ?>">
                    </div>
                    
                    <button type="submit" class="btn-primary">
                        Atualizar Dados da Empresa
                    </button>
                </form>
            </div>

            <!-- Informações do Plano -->
            <div class="section-card">
                <div class="section-title">
                    💎 Plano Atual
                </div>
                
                <div style="text-align: center; padding: 20px;">
                    <div style="font-size: 24px; font-weight: 700; color: #059669; margin-bottom: 8px;">
                        <?php echo htmlspecialchars($plano['nome']); ?>
                    </div>
                    <div style="font-size: 14px; color: #64748b; margin-bottom: 16px;">
                        Taxa de serviço: <?php echo number_format($plano['taxa_servico'], 2); ?>%
                    </div>
                    <div style="font-size: 12px; color: #64748b;">
                        <?php echo htmlspecialchars($plano['descricao'] ?? 'Plano básico com recursos essenciais'); ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Bottom Navigation -->
        <div class="bottom-nav">
            <a href="dashboard_mobile.php" class="nav-item">
                <div class="nav-icon">🏠</div>
                <div class="nav-label">Início</div>
            </a>
            <a href="solicitacoes_updated.php" class="nav-item">
                <div class="nav-icon">📋</div>
                <div class="nav-label">Solicitações</div>
            </a>
            <a href="reparos.php" class="nav-item">
                <div class="nav-icon">🔧</div>
                <div class="nav-label">Reparos</div>
            </a>
            <a href="marketplace.php" class="nav-item">
                <div class="nav-icon">🛒</div>
                <div class="nav-label">Loja</div>
            </a>
            <a href="carteira.php" class="nav-item">
                <div class="nav-icon">💳</div>
                <div class="nav-label">Carteira</div>
            </a>
            <a href="perfil_updated.php" class="nav-item active">
                <div class="nav-icon">👤</div>
                <div class="nav-label">Perfil</div>
            </a>
        </div>

        <!-- Floating Action Button -->
        <a href="assistencia_virtual.php" class="floating-action" title="Assistência Virtual">
            🤖
        </a>
    </div>

    <script>
        // Função para mostrar notificações
        function showNotification(message) {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                left: 50%;
                transform: translateX(-50%);
                background: #059669;
                color: white;
                padding: 12px 20px;
                border-radius: 8px;
                font-size: 14px;
                font-weight: 600;
                z-index: 2000;
                box-shadow: 0 4px 12px rgba(5, 150, 105, 0.3);
                max-width: 90%;
                text-align: center;
                animation: slideDown 0.3s ease-out;
            `;
            notification.textContent = message;
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.style.opacity = '0';
                setTimeout(() => notification.remove(), 300);
            }, 4000);
        }

        // Máscara para CEP
        document.querySelector('input[name="cep"]').addEventListener('input', function(e) {
            let value = e.target.value.replace(/\D/g, '');
            if (value.length > 8) value = value.slice(0, 8);
            e.target.value = value.replace(/(\d{5})(\d)/, '$1-$2');
        });

        // Máscara para telefone
        document.querySelectorAll('input[type="tel"]').forEach(function(input) {
            input.addEventListener('input', function(e) {
                let value = e.target.value.replace(/\D/g, '');
                if (value.length > 11) value = value.slice(0, 11);
                
                if (value.length <= 10) {
                    e.target.value = value.replace(/(\d{2})(\d{4})(\d)/, '($1) $2-$3');
                } else {
                    e.target.value = value.replace(/(\d{2})(\d{5})(\d)/, '($1) $2-$3');
                }
            });
        });

        // Adicionar estilo para animação
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideDown {
                from {
                    opacity: 0;
                    transform: translateX(-50%) translateY(-20px);
                }
                to {
                    opacity: 1;
                    transform: translateX(-50%) translateY(0);
                }
            }
        `;
        document.head.appendChild(style);

        // Remover alertas após 5 segundos
        setTimeout(() => {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                alert.style.opacity = '0';
                setTimeout(() => alert.remove(), 300);
            });
        }, 5000);
    </script>
</body>
</html>
