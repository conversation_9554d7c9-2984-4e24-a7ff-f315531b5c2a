<?php
/**
 * Página de Propostas - Versão Funcional
 * FixFácil Assistências - Sistema Novo
 */

// Configurar relatório de erros para debug
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Iniciar sessão se não estiver iniciada
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Verificar se o usuário está logado
if (!isset($_SESSION['usuario_id']) || $_SESSION['tipo_usuario'] !== 'assistencia') {
    header('Location: ../login.php');
    exit();
}

// Configuração de banco de dados
$host = "localhost";
$username = "u680766645_fixfacilnew";
$password = "T3cn0l0g1a@";
$database = "u680766645_fixfacilnew";

$mysqli = new mysqli($host, $username, $password, $database);

if ($mysqli->connect_error) {
    die("Erro de conexão: " . $mysqli->connect_error);
}

$mysqli->set_charset("utf8");

// Obter ID do usuário logado
$usuario_id = $_SESSION['usuario_id'];

// Processar exclusão de proposta
$mensagem = '';
$tipo_mensagem = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['excluir_proposta'])) {
    $proposta_id = intval($_POST['proposta_id']);

    // Verificação direta e simples
    $sql_check = "SELECT pa.id, pa.status, pa.assistencia_id, at.usuario_id
                  FROM propostas_assistencia pa
                  JOIN assistencias_tecnicas at ON pa.assistencia_id = at.id
                  WHERE pa.id = ?";

    $stmt_check = $mysqli->prepare($sql_check);
    $stmt_check->bind_param("i", $proposta_id);
    $stmt_check->execute();
    $result_check = $stmt_check->get_result();

    if ($result_check->num_rows > 0) {
        $proposta_data = $result_check->fetch_assoc();

        // Verificar se pertence ao usuário logado
        if ($proposta_data['usuario_id'] == $usuario_id) {
            // Só permitir excluir se a proposta ainda estiver pendente
            if ($proposta_data['status'] === 'enviada') {
                $sql_delete = "DELETE FROM propostas_assistencia WHERE id = ?";
                $stmt_delete = $mysqli->prepare($sql_delete);
                $stmt_delete->bind_param("i", $proposta_id);

                if ($stmt_delete->execute()) {
                    $mensagem = 'Proposta excluída com sucesso!';
                    $tipo_mensagem = 'success';
                } else {
                    $mensagem = 'Erro ao excluir proposta. Tente novamente.';
                    $tipo_mensagem = 'error';
                }
            } else {
                $mensagem = 'Não é possível excluir esta proposta. Status: ' . $proposta_data['status'];
                $tipo_mensagem = 'error';
            }
        } else {
            $mensagem = 'Você não tem permissão para excluir esta proposta.';
            $tipo_mensagem = 'error';
        }
    } else {
        $mensagem = 'Proposta não encontrada.';
        $tipo_mensagem = 'error';
    }
}

// Obter dados do usuário logado
$usuario = null;

// Buscar dados do usuário de forma simples
$sql = "SELECT u.id, u.nome, u.email, u.telefone, u.plano_id, at.id as assistencia_id 
        FROM usuarios u 
        LEFT JOIN assistencias_tecnicas at ON u.id = at.usuario_id 
        WHERE u.id = $usuario_id";
$result = $mysqli->query($sql);
if ($result && $result->num_rows > 0) {
    $usuario = $result->fetch_assoc();
}

// Dados padrão se não encontrar
if (!$usuario) {
    $usuario = [
        'id' => $usuario_id,
        'nome' => 'Usuário',
        'email' => '',
        'assistencia_id' => 1
    ];
}

// Obter propostas
$propostas = [];
if ($usuario && isset($usuario['assistencia_id'])) {
    $sql = "
        SELECT 
            pa.*,
            sr.descricao_problema,
            sr.dispositivo,
            sr.marca,
            sr.modelo,
            u.nome as cliente_nome,
            u.telefone as cliente_telefone
        FROM propostas_assistencia pa
        JOIN solicitacoes_reparo sr ON pa.solicitacao_id = sr.id
        JOIN usuarios u ON sr.usuario_id = u.id
        WHERE pa.assistencia_id = " . (int)$usuario['assistencia_id'] . "
        ORDER BY pa.data_proposta DESC
        LIMIT 50
    ";
    
    $result = $mysqli->query($sql);
    if ($result) {
        while ($row = $result->fetch_assoc()) {
            $propostas[] = $row;
        }
    }
}

// Estatísticas
$stats = [
    'total_solicitacoes' => count($propostas),
    'aguardando_resposta' => 0,
    'em_andamento' => 0
];

// Contar por status
foreach ($propostas as $proposta) {
    if ($proposta['status'] === 'enviada') {
        $stats['aguardando_resposta']++;
    } elseif ($proposta['status'] === 'aceita' || $proposta['status'] === 'Em Andamento') {
        $stats['em_andamento']++;
    }
}

?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Propostas - FixFácil Assistências</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            background: #f8fafc;
            min-height: 100vh;
            color: #1e293b;
        }

        .container {
            max-width: 414px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
            position: relative;
            overflow-x: hidden;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }

        .header {
            background: linear-gradient(135deg, #059669 0%, #065f46 100%);
            color: white;
            padding: 24px 20px 20px 20px;
            position: relative;
        }

        .header-content {
            position: relative;
            z-index: 1;
        }

        .header-top {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 20px;
        }

        .company-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .company-logo {
            width: 48px;
            height: 48px;
            background: rgba(255,255,255,0.2);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            font-weight: 700;
            backdrop-filter: blur(10px);
        }

        .company-details h1 {
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 2px;
        }

        .company-status {
            font-size: 12px;
            opacity: 0.9;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .status-indicator {
            width: 8px;
            height: 8px;
            background: #10b981;
            border-radius: 50%;
        }

        .header-actions {
            display: flex;
            gap: 8px;
        }

        .action-btn {
            width: 40px;
            height: 40px;
            background: rgba(255,255,255,0.15);
            border: none;
            border-radius: 12px;
            color: white;
            font-size: 18px;
            cursor: pointer;
            backdrop-filter: blur(10px);
            transition: all 0.2s ease;
            position: relative;
            text-decoration: none;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 12px;
        }

        .stat-card {
            background: rgba(255,255,255,0.15);
            border-radius: 12px;
            padding: 16px 12px;
            text-align: center;
            backdrop-filter: blur(10px);
        }

        .stat-number {
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 4px;
        }

        .stat-label {
            font-size: 11px;
            opacity: 0.8;
        }

        .content {
            padding: 20px;
            padding-bottom: 100px;
        }

        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 100%;
            max-width: 414px;
            background: white;
            border-top: 1px solid #e2e8f0;
            padding: 12px 20px;
            display: flex;
            justify-content: space-around;
            box-shadow: 0 -4px 20px rgba(0,0,0,0.1);
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
            cursor: pointer;
            padding: 8px;
            border-radius: 12px;
            transition: all 0.2s ease;
            position: relative;
            text-decoration: none;
            color: #64748b;
        }

        .nav-item.active {
            background: #f0fdf4;
            color: #059669;
        }

        .nav-icon {
            font-size: 20px;
        }

        .nav-label {
            font-size: 10px;
            font-weight: 500;
        }

        .floating-action {
            position: fixed;
            bottom: 80px;
            right: 20px;
            width: 56px;
            height: 56px;
            background: linear-gradient(135deg, #059669, #065f46);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            cursor: pointer;
            box-shadow: 0 4px 16px rgba(5, 150, 105, 0.4);
            transition: all 0.3s ease;
            z-index: 100;
            text-decoration: none;
        }

        /* Manter sempre formato mobile */
        
        /* Estilos específicos para propostas */
        .filter-tabs {
            display: flex;
            background: white;
            border-radius: 12px;
            padding: 4px;
            margin-bottom: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .filter-tab {
            flex: 1;
            padding: 12px 16px;
            text-align: center;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            text-decoration: none;
            color: #64748b;
        }
        
        .filter-tab.active {
            background: #059669;
            color: white;
        }
        
        .proposta-card {
            background: white;
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
            transition: transform 0.2s ease;
        }
        
        .proposta-card:hover {
            transform: translateY(-2px);
        }
        
        .proposta-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 12px;
        }
        
        .device-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .device-icon {
            width: 48px;
            height: 48px;
            background: #f0fdf4;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            color: #059669;
        }
        
        .device-details h3 {
            font-size: 16px;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 4px;
        }
        
        .device-specs {
            font-size: 12px;
            color: #64748b;
        }
        
        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        .status-enviada {
            background: #fef3c7;
            color: #92400e;
        }
        
        .status-aceita {
            background: #d1fae5;
            color: #065f46;
        }
        
        .status-rejeitada {
            background: #fee2e2;
            color: #991b1b;
        }
        
        .proposta-valor {
            font-size: 18px;
            font-weight: 700;
            color: #059669;
            margin: 12px 0;
        }
        
        .cliente-info {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 12px;
            font-size: 14px;
            color: #64748b;
        }
        
        .action-buttons {
            display: flex;
            gap: 8px;
            margin-top: 12px;
        }
        
        .btn-chat {
            flex: 1;
            background: linear-gradient(135deg, #059669, #065f46);
            color: white;
            border: none;
            border-radius: 8px;
            padding: 12px 16px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            text-decoration: none;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }
        
        .btn-chat:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(5, 150, 105, 0.3);
        }

        .btn-delete {
            background: #ef4444;
            color: white;
            padding: 6px 12px;
            border-radius: 16px;
            border: none;
            font-size: 11px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .btn-delete:hover {
            background: #dc2626;
            transform: translateY(-1px);
        }

        .alert {
            padding: 12px 16px;
            border-radius: 12px;
            margin-bottom: 20px;
            font-size: 14px;
            font-weight: 500;
        }

        .alert-success {
            background: #dcfce7;
            color: #166534;
            border: 1px solid #bbf7d0;
        }

        .alert-error {
            background: #fef2f2;
            color: #dc2626;
            border: 1px solid #fecaca;
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            background: white;
            border-radius: 16px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
        }
        
        .empty-icon {
            font-size: 48px;
            margin-bottom: 16px;
            opacity: 0.5;
        }
        
        .empty-title {
            font-size: 18px;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 8px;
        }
        
        .empty-subtitle {
            font-size: 14px;
            color: #64748b;
            line-height: 1.5;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="header-content">
                <div class="header-top">
                    <div class="company-info">
                        <div class="company-logo">FF</div>
                        <div class="company-details">
                            <h1><?php echo htmlspecialchars(substr($usuario['nome'], 0, 20)); ?></h1>
                            <div class="company-status">
                                <div class="status-indicator"></div>
                                <span>Online • Verificado</span>
                            </div>
                        </div>
                    </div>
                    <div class="header-actions">
                        <a href="../" class="action-btn" title="Acessar área do cliente">👤</a>
                        <a href="logout.php" class="action-btn" title="Sair">🚪</a>
                        <button class="action-btn">🔔</button>
                    </div>
                </div>

                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number"><?php echo $stats['total_solicitacoes']; ?></div>
                        <div class="stat-label">Total</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number"><?php echo $stats['aguardando_resposta']; ?></div>
                        <div class="stat-label">Enviadas</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number"><?php echo $stats['em_andamento']; ?></div>
                        <div class="stat-label">Aceitas</div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="content">
            <!-- Mensagens -->
            <?php if (!empty($mensagem)): ?>
            <div class="alert alert-<?php echo $tipo_mensagem; ?>">
                <?php echo $tipo_mensagem === 'success' ? '✅' : '⚠️'; ?> <?php echo htmlspecialchars($mensagem); ?>
            </div>
            <?php endif; ?>

            <!-- Filtros -->
            <div class="filter-tabs">
                <a href="?status=todas" class="filter-tab <?php echo ($_GET['status'] ?? 'todas') === 'todas' ? 'active' : ''; ?>">
                    📋 Todas
                </a>
                <a href="?status=enviada" class="filter-tab <?php echo ($_GET['status'] ?? '') === 'enviada' ? 'active' : ''; ?>">
                    ⏳ Enviadas
                </a>
                <a href="?status=aceita" class="filter-tab <?php echo ($_GET['status'] ?? '') === 'aceita' ? 'active' : ''; ?>">
                    ✅ Aceitas
                </a>
            </div>
            
            <!-- Lista de Propostas -->
            <?php if (empty($propostas)): ?>
            <div class="empty-state">
                <div class="empty-icon">💼</div>
                <h3 class="empty-title">Nenhuma proposta encontrada</h3>
                <p class="empty-subtitle">
                    Você ainda não enviou nenhuma proposta.<br>
                    Acesse a página de solicitações para enviar suas primeiras propostas.
                </p>
            </div>
            <?php else: ?>
            <?php foreach ($propostas as $proposta): ?>
            <div class="proposta-card">
                <div class="proposta-header">
                    <div class="device-info">
                        <div class="device-icon">📱</div>
                        <div class="device-details">
                            <h3><?php echo htmlspecialchars($proposta['marca'] . ' ' . $proposta['modelo']); ?></h3>
                            <div class="device-specs">
                                <?php echo htmlspecialchars($proposta['dispositivo']); ?>
                            </div>
                        </div>
                    </div>
                    <div class="status-badge status-<?php echo strtolower($proposta['status']); ?>">
                        <?php echo htmlspecialchars($proposta['status']); ?>
                    </div>
                </div>
                
                <div class="cliente-info">
                    <span>👤</span>
                    <span><?php echo htmlspecialchars($proposta['cliente_nome']); ?></span>
                    <span>•</span>
                    <span>📞 <?php echo htmlspecialchars($proposta['cliente_telefone']); ?></span>
                </div>
                
                <div class="proposta-valor">
                    💰 R$ <?php echo number_format($proposta['preco'], 2, ',', '.'); ?>
                </div>
                
                <!-- Botões de Ação -->
                <div class="action-buttons">
                    <?php if ($proposta['status'] === 'aceita' || $proposta['status'] === 'Em Andamento'): ?>
                    <a href="chat.php?proposta_id=<?php echo $proposta['id']; ?>" class="btn-chat">
                        <span>💬</span>
                        Chat
                    </a>
                    <?php endif; ?>

                    <?php if ($proposta['status'] === 'enviada'): ?>
                    <form method="POST" style="display: inline;" onsubmit="return confirm('Tem certeza que deseja excluir esta proposta?');">
                        <input type="hidden" name="proposta_id" value="<?php echo $proposta['id']; ?>">
                        <button type="submit" name="excluir_proposta" class="btn-delete">
                            🗑️ Excluir
                        </button>
                    </form>
                    <?php endif; ?>
                </div>
                
                <?php if (isset($proposta['pago']) && $proposta['pago'] && $proposta['status'] === 'Concluída'): ?>
                <div style="background: #d1fae5; padding: 12px; border-radius: 8px; margin-top: 12px; text-align: center; color: #065f46; font-weight: 600;">
                    💰 Você recebeu: R$ <?php echo number_format($proposta['preco'] * 0.75, 2, ',', '.'); ?>
                </div>
                <?php endif; ?>
            </div>
            <?php endforeach; ?>
            <?php endif; ?>
        </div>
        
        <!-- Floating Action Button -->
        <a href="solicitacoes.php" class="floating-action" title="Nova Proposta">
            <span>+</span>
        </a>

        <!-- Menu de Navegação -->
        <div class="bottom-nav">
            <a href="dashboard_mobile_final.php" class="nav-item">
                <div class="nav-icon">🏠</div>
                <div class="nav-label">Início</div>
            </a>
            <a href="solicitacoes.php" class="nav-item">
                <div class="nav-icon">📋</div>
                <div class="nav-label">Solicitações</div>
            </a>
            <a href="reparos_new.php" class="nav-item">
                <div class="nav-icon">🔧</div>
                <div class="nav-label">Reparos</div>
            </a>
            <a href="propostas.php" class="nav-item active">
                <div class="nav-icon">💼</div>
                <div class="nav-label">Propostas</div>
            </a>
            <a href="marketplace.php" class="nav-item">
                <div class="nav-icon">🛒</div>
                <div class="nav-label">Loja</div>
            </a>
            <a href="carteira.php" class="nav-item">
                <div class="nav-icon">💳</div>
                <div class="nav-label">Carteira</div>
            </a>
        </div>
    </div>

    <script>
        // Auto-refresh a cada 30 segundos
        setInterval(() => {
            if (document.visibilityState === 'visible') {
                location.reload();
            }
        }, 30000);
    </script>
</body>
</html>
