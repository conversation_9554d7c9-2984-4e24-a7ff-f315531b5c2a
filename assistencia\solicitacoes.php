<?php
/**
 * Página de Solicitações - Mobile First
 * FixFácil Assistências - Padrão Mobile
 */

// Configurar relatório de erros para debug
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Iniciar sessão se não estiver iniciada
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Verificar se o usuário está logado
if (!isset($_SESSION['usuario_id']) || $_SESSION['tipo_usuario'] !== 'assistencia') {
    header('Location: ../login.php');
    exit();
}

// Configuração de banco de dados
$host = "localhost";
$username = "u680766645_fixfacilnew";
$password = "T3cn0l0g1a@";
$database = "u680766645_fixfacilnew";

$mysqli = new mysqli($host, $username, $password, $database);

if ($mysqli->connect_error) {
    die("Erro de conexão: " . $mysqli->connect_error);
}

$mysqli->set_charset("utf8");

// Obter dados do usuário logado
$usuario_id = $_SESSION['usuario_id'];
$usuario = null;

// Buscar dados do usuário de forma simples
$sql = "SELECT u.id, u.nome, u.email, u.telefone, u.plano_id, at.id as assistencia_id
        FROM usuarios u
        LEFT JOIN assistencias_tecnicas at ON u.id = at.usuario_id
        WHERE u.id = $usuario_id";
$result = $mysqli->query($sql);
if ($result && $result->num_rows > 0) {
    $usuario = $result->fetch_assoc();
}

// Dados padrão se não encontrar
if (!$usuario) {
    $usuario = [
        'id' => $usuario_id,
        'nome' => 'Assistência Técnica',
        'email' => '',
        'telefone' => '',
        'plano_id' => 1,
        'assistencia_id' => null
    ];
}

// Filtros
$status_filter = $_GET['status'] ?? 'enviado';
$search = $_GET['search'] ?? '';

// Obter solicitações disponíveis
$solicitacoes = [];
$where_conditions = ["sr.status = 'enviado'", "sr.visivel = 1"];
$params = [];

if (!empty($search)) {
    $search_escaped = $mysqli->real_escape_string($search);
    $where_conditions[] = "(sr.descricao_problema LIKE '%$search_escaped%' OR sr.dispositivo LIKE '%$search_escaped%' OR sr.marca LIKE '%$search_escaped%' OR sr.modelo LIKE '%$search_escaped%' OR u.nome LIKE '%$search_escaped%')";
}

$where_clause = implode(' AND ', $where_conditions);

$sql = "
    SELECT
        sr.*,
        u.nome as cliente_nome,
        u.telefone as cliente_telefone,
        u.endereco as cliente_endereco,
        u.email as cliente_email
    FROM solicitacoes_reparo sr
    JOIN usuarios u ON sr.usuario_id = u.id
    WHERE $where_clause
    ORDER BY sr.data_solicitacao DESC
    LIMIT 50
";

$result = $mysqli->query($sql);
if ($result) {
    while ($row = $result->fetch_assoc()) {
        $solicitacoes[] = $row;
    }
}

// Estatísticas
$stats = [
    'total_solicitacoes' => count($solicitacoes),
    'aguardando_resposta' => count($solicitacoes),
    'em_andamento' => 0
];

?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Solicitações - FixFácil Assistências</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            min-height: 100vh;
            color: #1e293b;
        }

        .container {
            max-width: 414px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
            position: relative;
            overflow-x: hidden;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }

        .header {
            background: linear-gradient(135deg, #059669 0%, #065f46 100%);
            color: white;
            padding: 24px 20px 20px 20px;
            position: relative;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 20"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="0.5" fill="white" opacity="0.05"/></pattern></defs><rect width="100" height="20" fill="url(%23grain)"/></svg>');
        }

        .header-content {
            position: relative;
            z-index: 1;
        }

        .header-top {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 20px;
        }

        .company-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .company-logo {
            width: 48px;
            height: 48px;
            background: rgba(255,255,255,0.2);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            font-weight: 700;
            backdrop-filter: blur(10px);
        }

        .company-details h1 {
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 2px;
        }

        .company-status {
            font-size: 12px;
            opacity: 0.9;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .status-indicator {
            width: 8px;
            height: 8px;
            background: #10b981;
            border-radius: 50%;
        }

        .header-actions {
            display: flex;
            gap: 8px;
        }

        .action-btn {
            width: 40px;
            height: 40px;
            background: rgba(255,255,255,0.15);
            border: none;
            border-radius: 12px;
            color: white;
            font-size: 18px;
            cursor: pointer;
            backdrop-filter: blur(10px);
            transition: all 0.2s ease;
            position: relative;
            text-decoration: none;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .action-btn:hover {
            background: rgba(255,255,255,0.25);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 12px;
        }

        .stat-card {
            background: rgba(255,255,255,0.15);
            border-radius: 12px;
            padding: 16px 12px;
            text-align: center;
            backdrop-filter: blur(10px);
        }

        .stat-number {
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 4px;
        }

        .stat-label {
            font-size: 11px;
            opacity: 0.8;
        }

        .content {
            padding: 20px;
            padding-bottom: 100px;
        }

        .section-title {
            font-size: 18px;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .filters {
            background: white;
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
        }

        .search-box {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            font-size: 14px;
            margin-bottom: 16px;
            background: #f8fafc;
        }

        .filter-tabs {
            display: flex;
            gap: 8px;
            margin-bottom: 16px;
            overflow-x: auto;
            padding-bottom: 4px;
        }

        .filter-tab {
            padding: 8px 16px;
            border: 1px solid #e2e8f0;
            border-radius: 20px;
            font-size: 12px;
            white-space: nowrap;
            cursor: pointer;
            transition: all 0.2s;
            text-decoration: none;
            color: #64748b;
            background: white;
        }

        .filter-tab.active,
        .filter-tab:hover {
            background: #059669;
            color: white;
            border-color: #059669;
        }

        .solicitacao-card {
            background: white;
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
            border: 1px solid #f1f5f9;
        }

        .solicitacao-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 16px;
        }

        .device-icon {
            width: 48px;
            height: 48px;
            background: linear-gradient(135deg, #059669, #065f46);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            color: white;
        }

        .solicitacao-info h3 {
            font-size: 16px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 4px;
        }

        .solicitacao-meta {
            font-size: 12px;
            color: #64748b;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .problema-desc {
            background: #f8fafc;
            border-radius: 12px;
            padding: 12px;
            margin-bottom: 16px;
            font-size: 14px;
            color: #475569;
            line-height: 1.4;
        }

        .solicitacao-actions {
            display: flex;
            gap: 8px;
        }

        .btn {
            padding: 10px 16px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
            text-decoration: none;
            text-align: center;
            cursor: pointer;
            border: none;
            transition: all 0.2s;
            flex: 1;
        }

        .btn-primary {
            background: #059669;
            color: white;
        }

        .btn-primary:hover {
            background: #047857;
        }

        .btn-outline {
            background: white;
            color: #059669;
            border: 1px solid #059669;
        }

        .btn-outline:hover {
            background: #f0fdf4;
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #64748b;
        }

        .empty-state h3 {
            font-size: 18px;
            margin-bottom: 8px;
            color: #475569;
        }

        .empty-state p {
            font-size: 14px;
            margin-bottom: 24px;
            line-height: 1.5;
        }

        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 100%;
            max-width: 414px;
            background: white;
            border-top: 1px solid #e2e8f0;
            padding: 12px 20px;
            display: flex;
            justify-content: space-around;
            box-shadow: 0 -4px 20px rgba(0,0,0,0.1);
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
            cursor: pointer;
            padding: 8px;
            border-radius: 12px;
            transition: all 0.2s ease;
            position: relative;
            text-decoration: none;
            color: #64748b;
        }

        .nav-item.active {
            background: #f0fdf4;
            color: #059669;
        }

        .nav-icon {
            font-size: 20px;
        }

        .nav-label {
            font-size: 10px;
            font-weight: 500;
        }

        .nav-badge {
            position: absolute;
            top: 4px;
            right: 4px;
            background: #ef4444;
            color: white;
            border-radius: 50%;
            width: 16px;
            height: 16px;
            font-size: 10px;
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="header-content">
                <div class="header-top">
                    <div class="company-info">
                        <div class="company-logo">FF</div>
                        <div class="company-details">
                            <h1><?php echo htmlspecialchars(substr($usuario['nome'], 0, 20)); ?></h1>
                            <div class="company-status">
                                <div class="status-indicator"></div>
                                <span>Online • Verificado</span>
                            </div>
                        </div>
                    </div>
                    <div class="header-actions">
                        <a href="../" class="action-btn" title="Acessar área do cliente">👤</a>
                        <a href="logout.php" class="action-btn" title="Sair">🚪</a>
                        <button class="action-btn">🔔</button>
                    </div>
                </div>

                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number"><?php echo count($solicitacoes); ?></div>
                        <div class="stat-label">Disponíveis</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number"><?php echo $stats['aguardando_resposta']; ?></div>
                        <div class="stat-label">Aguardando</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number"><?php echo $stats['em_andamento']; ?></div>
                        <div class="stat-label">Em Andamento</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="content">
            <!-- Filtros -->
            <div class="filters">
                <div class="section-title">
                    📋 Filtrar Solicitações
                </div>

                <form method="GET" action="">
                    <input type="text" name="search" class="search-box"
                           placeholder="Buscar por dispositivo, marca, modelo ou cliente..."
                           value="<?php echo htmlspecialchars($search); ?>">

                    <div class="filter-tabs">
                        <a href="?status=enviado&search=<?php echo urlencode($search); ?>"
                           class="filter-tab <?php echo $status_filter === 'enviado' ? 'active' : ''; ?>">
                            Pendentes
                        </a>
                        <a href="?status=aceita&search=<?php echo urlencode($search); ?>"
                           class="filter-tab <?php echo $status_filter === 'aceita' ? 'active' : ''; ?>">
                            Aceitas
                        </a>
                        <a href="?status=rejeitada&search=<?php echo urlencode($search); ?>"
                           class="filter-tab <?php echo $status_filter === 'rejeitada' ? 'active' : ''; ?>">
                            Rejeitadas
                        </a>
                        <a href="?status=concluido&search=<?php echo urlencode($search); ?>"
                           class="filter-tab <?php echo $status_filter === 'concluido' ? 'active' : ''; ?>">
                            Concluídas
                        </a>
                    </div>
                </form>
            </div>

            <!-- Lista de Solicitações -->
            <?php if (empty($solicitacoes)): ?>
            <div class="empty-state">
                <h3>📭 Nenhuma solicitação encontrada</h3>
                <p>
                    <?php if ($status_filter === 'enviado'): ?>
                        Não há solicitações pendentes no momento.<br>
                        Aguarde novas solicitações chegarem.
                    <?php else: ?>
                        Não há solicitações com o status "<?php echo $status_filter; ?>".<br>
                        Tente filtrar por outro status.
                    <?php endif; ?>
                </p>
            </div>
            <?php else: ?>
                <?php foreach ($solicitacoes as $solicitacao): ?>
                <div class="solicitacao-card">
                    <div class="solicitacao-header">
                        <div class="device-icon">📱</div>
                        <div class="solicitacao-info">
                            <h3><?php echo htmlspecialchars($solicitacao['marca'] . ' ' . $solicitacao['modelo']); ?></h3>
                            <div class="solicitacao-meta">
                                <span>👤 <?php echo htmlspecialchars($solicitacao['cliente_nome']); ?></span>
                                <span>📞 <?php echo htmlspecialchars($solicitacao['cliente_telefone']); ?></span>
                            </div>
                        </div>
                    </div>

                    <div class="problema-desc">
                        <strong>Problema:</strong> <?php echo htmlspecialchars($solicitacao['descricao_problema']); ?>
                        <?php if (!empty($solicitacao['descricao_detalhada'])): ?>
                        <br><br><strong>Detalhes:</strong> <?php echo htmlspecialchars($solicitacao['descricao_detalhada']); ?>
                        <?php endif; ?>
                    </div>

                    <div class="solicitacao-meta" style="margin-bottom: 16px;">
                        <span>📅 <?php echo date('d/m/Y H:i', strtotime($solicitacao['data_solicitacao'])); ?></span>
                        <span>🚚 <?php echo htmlspecialchars($solicitacao['metodo_entrega']); ?></span>
                        <?php if (!empty($solicitacao['memoria'])): ?>
                        <span>💾 <?php echo htmlspecialchars($solicitacao['memoria']); ?></span>
                        <?php endif; ?>
                    </div>

                    <div class="solicitacao-actions">
                        <a href="detalhes_solicitacao.php?id=<?php echo $solicitacao['id']; ?>" class="btn btn-outline">
                            👁️ Ver Detalhes
                        </a>
                        <a href="enviar_proposta.php?solicitacao_id=<?php echo $solicitacao['id']; ?>" class="btn btn-primary">
                            📤 Enviar Proposta
                        </a>
                        <?php if (!empty($solicitacao['video'])): ?>
                        <button type="button" class="btn btn-outline" onclick="verVideo('<?php echo htmlspecialchars($solicitacao['video']); ?>')">
                            ▶️ Vídeo
                        </button>
                        <?php endif; ?>

                        <?php if (!empty($solicitacao['fotos'])): ?>
                        <button type="button" class="btn btn-outline" onclick="verImagem('<?php echo htmlspecialchars(explode(',', $solicitacao['fotos'])[0]); ?>')">
                            📷 Foto
                        </button>
                        <?php endif; ?>
                    </div>
                </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>

        <!-- Bottom Navigation -->
        <div class="bottom-nav">
            <a href="dashboard_mobile_final.php" class="nav-item">
                <div class="nav-icon">🏠</div>
                <div class="nav-label">Início</div>
            </a>
            <a href="solicitacoes.php" class="nav-item active">
                <div class="nav-icon">📋</div>
                <div class="nav-label">Solicitações</div>
                <?php if ($stats['aguardando_resposta'] > 0): ?>
                    <div class="nav-badge"><?php echo $stats['aguardando_resposta']; ?></div>
                <?php endif; ?>
            </a>
            <a href="reparos_new.php" class="nav-item">
                <div class="nav-icon">🔧</div>
                <div class="nav-label">Reparos</div>
            </a>
            <a href="marketplace.php" class="nav-item">
                <div class="nav-icon">🛒</div>
                <div class="nav-label">Loja</div>
            </a>
            <a href="carteira.php" class="nav-item">
                <div class="nav-icon">💳</div>
                <div class="nav-label">Carteira</div>
            </a>
        </div>
    </div>

    <script>
        // Função para ver vídeo
        function verVideo(videoUrl) {
            // Extrair apenas o nome do arquivo
            const fileName = videoUrl.split('/').pop();
            window.open('view_media.php?file=' + encodeURIComponent(fileName), '_blank');
        }

        // Função para ver imagem
        function verImagem(imagemUrl) {
            // Extrair apenas o nome do arquivo
            const fileName = imagemUrl.split('/').pop();
            window.open('view_media.php?file=' + encodeURIComponent(fileName), '_blank');
        }

        // Auto-refresh a cada 30 segundos
        setInterval(() => {
            if (document.visibilityState === 'visible') {
                location.reload();
            }
        }, 30000);

        // Busca em tempo real
        const searchInput = document.querySelector('.search-box');
        let searchTimeout;

        if (searchInput) {
            searchInput.addEventListener('input', function() {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    this.form.submit();
                }, 500);
            });
        }
    </script>
</body>
</html>
