<?php
/**
 * <PERSON>ágina Principal - Sistema de Assistência
 * FixFácil Assistências
 */

// Inicializar sistema
require_once 'includes/bootstrap.php';

// Verificar se está logado
if (!isset($_SESSION['usuario_id']) || $_SESSION['tipo_usuario'] !== 'assistencia') {
    // Redirecionar para login principal
    safeRedirect('../login.php');
}

// Obter dados do usuário
$usuario = getUserFromSession();

// Verificar se tem dados necessários
if (!$usuario['assistencia_id']) {
    safeRedirect('erro.php?erro=Dados da assistência não encontrados&codigo=400');
}

// Detectar dispositivo e redirecionar apropriadamente
$userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
$isMobile = preg_match('/Mobile|Android|iPhone|iPad|iPod/', $userAgent);

// Forçar versão mobile se solicitado
if (isset($_GET['mobile']) || $isMobile) {
    safeRedirect('dashboard_new.php');
}

// Verificar se o layout tradicional está funcionando
try {
    if (file_exists('includes/layout.php')) {
        require_once 'includes/layout.php';
        if (class_exists('Layout')) {
            // Redirecionar para dashboard tradicional
            safeRedirect('dashboard_safe.php');
        }
    }
} catch (Exception $e) {
    error_log("Layout tradicional não disponível: " . $e->getMessage());
}

// Fallback para versão mobile
safeRedirect('dashboard_new.php');
?>
