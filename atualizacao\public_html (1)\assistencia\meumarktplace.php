<?php
session_start();

// Verificar se o usuário está logado e é uma assistência
if (!isset($_SESSION['usuario_id']) || $_SESSION['tipo_usuario'] != 'assistencia') {
    header('Location: ../login.php');
    exit();
}

$usuario_id = $_SESSION['usuario_id'];
$nome_assistencia = $_SESSION['nome']; // Nome da assistência

// Conexão com o banco de dados
$servername = "localhost";
$username_db = "u680766645_fixfacilnew";
$password_db = "T3cn0l0g1a@";
$dbname = "u680766645_fixfacilnew";

// Ativar exceções para erros do MySQLi
mysqli_report(MYSQLI_REPORT_ERROR | MYSQLI_REPORT_STRICT);

try {
    $conn = new mysqli($servername, $username_db, $password_db, $dbname);
    $conn->set_charset("utf8mb4");
} catch (mysqli_sql_exception $e) {
    die("Falha na conexão: " . $e->getMessage());
}

// Inicializar variáveis para mensagens
$mensagem = "";
$tipo_alerta = "";

// Verificar se a assistência técnica existe
try {
    $stmt_assistencia = $conn->prepare("SELECT id FROM assistencias_tecnicas WHERE usuario_id = ?");
    $stmt_assistencia->bind_param("i", $usuario_id);
    $stmt_assistencia->execute();
    $stmt_assistencia->bind_result($assistencia_id);
    if (!$stmt_assistencia->fetch()) {
        $stmt_assistencia->close();
        die("Assistência técnica não encontrada para o usuário logado.");
    }
    $stmt_assistencia->close();
} catch (mysqli_sql_exception $e) {
    die("Erro ao buscar assistência: " . $e->getMessage());
}

// Processamento do formulário de adicionar/editar/excluir produto
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    try {
        if (isset($_POST['adicionar_produto'])) {
            // Adicionar novo produto
            $nome_produto = trim($_POST['nome_produto']);
            $descricao = trim($_POST['descricao']);
            $preco = floatval($_POST['preco']);
            $estoque = intval($_POST['estoque']);
            $categoria = trim($_POST['categoria']);

            // Gerenciar upload de imagem
            if (isset($_FILES['imagem']) && $_FILES['imagem']['error'] == UPLOAD_ERR_OK) {
                $upload_dir = 'uploads/produtos/';
                if (!is_dir($upload_dir)) {
                    mkdir($upload_dir, 0777, true);
                }

                // Gerar um nome único para a imagem para evitar conflitos
                $nome_imagem = uniqid() . "_" . basename($_FILES['imagem']['name']);
                $imagem_path = $upload_dir . $nome_imagem;
                $imageFileType = strtolower(pathinfo($imagem_path, PATHINFO_EXTENSION));

                // Validar o tipo de arquivo
                $check = getimagesize($_FILES['imagem']['tmp_name']);
                if ($check !== false) {
                    // Permitir apenas certos formatos
                    if (in_array($imageFileType, ['jpg', 'jpeg', 'png', 'gif'])) {
                        move_uploaded_file($_FILES['imagem']['tmp_name'], $imagem_path);
                    } else {
                        throw new Exception("Formato de imagem inválido. Permissões: JPG, JPEG, PNG, GIF.");
                    }
                } else {
                    throw new Exception("O arquivo enviado não é uma imagem.");
                }
            } else {
                $nome_imagem = null; // Imagem não obrigatória
            }

            // Inserir no banco de dados
            $sql_inserir = "INSERT INTO produtos (usuario_id, nome_produto, descricao, imagem, preco, estoque, categoria, quantidade_disponivel)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
            $quantidade_disponivel = $estoque; // Inicialmente igual ao estoque
            $stmt_inserir = $conn->prepare($sql_inserir);
            $stmt_inserir->bind_param("isssdisi", $usuario_id, $nome_produto, $descricao, $nome_imagem, $preco, $estoque, $categoria, $quantidade_disponivel);
            $stmt_inserir->execute();
            $stmt_inserir->close();

            $mensagem = "Produto adicionado com sucesso!";
            $tipo_alerta = "success";
        }

        if (isset($_POST['editar_produto'])) {
            // Editar produto existente
            $produto_id = intval($_POST['produto_id']);
            $nome_produto = trim($_POST['nome_produto']);
            $descricao = trim($_POST['descricao']);
            $preco = floatval($_POST['preco']);
            $estoque = intval($_POST['estoque']);
            $categoria = trim($_POST['categoria']);

            // Gerenciar upload de imagem
            if (isset($_FILES['imagem']) && $_FILES['imagem']['error'] == UPLOAD_ERR_OK) {
                $upload_dir = '../uploads/produtos/';
                if (!is_dir($upload_dir)) {
                    mkdir($upload_dir, 0777, true);
                }

                // Gerar um nome único para a imagem para evitar conflitos
                $nome_imagem = uniqid() . "_" . basename($_FILES['imagem']['name']);
                $imagem_path = $upload_dir . $nome_imagem;
                $imageFileType = strtolower(pathinfo($imagem_path, PATHINFO_EXTENSION));

                // Validar o tipo de arquivo
                $check = getimagesize($_FILES['imagem']['tmp_name']);
                if ($check !== false) {
                    // Permitir apenas certos formatos
                    if (in_array($imageFileType, ['jpg', 'jpeg', 'png', 'gif'])) {
                        move_uploaded_file($_FILES['imagem']['tmp_name'], $imagem_path);
                    } else {
                        throw new Exception("Formato de imagem inválido. Permissões: JPG, JPEG, PNG, GIF.");
                    }
                } else {
                    throw new Exception("O arquivo enviado não é uma imagem.");
                }

                // Deletar a imagem antiga, se existir
                if (!empty($_POST['imagem_atual'])) {
                    $imagem_antiga = '../uploads/produtos/' . $_POST['imagem_atual'];
                    if (file_exists($imagem_antiga)) {
                        unlink($imagem_antiga);
                    }
                }
            } else {
                // Se não há nova imagem, manter a atual
                $nome_imagem = $_POST['imagem_atual'];
            }

            // Atualizar no banco de dados
            $sql_atualizar = "UPDATE produtos SET nome_produto = ?, descricao = ?, imagem = ?, preco = ?, estoque = ?, categoria = ?, quantidade_disponivel = ? WHERE id = ? AND usuario_id = ?";
            $quantidade_disponivel = $estoque; // Atualiza a quantidade disponível
            $stmt_atualizar = $conn->prepare($sql_atualizar);
            $stmt_atualizar->bind_param("sssdisii", $nome_produto, $descricao, $nome_imagem, $preco, $estoque, $categoria, $quantidade_disponivel, $produto_id, $usuario_id);
            $stmt_atualizar->execute();
            $stmt_atualizar->close();

            $mensagem = "Produto atualizado com sucesso!";
            $tipo_alerta = "success";
        }

        if (isset($_POST['excluir_produto'])) {
            // Excluir produto
            $produto_id = intval($_POST['produto_id']);

            // Obter o nome da imagem para deletar do servidor
            $sql_obter = "SELECT imagem FROM produtos WHERE id = ? AND usuario_id = ?";
            $stmt_obter = $conn->prepare($sql_obter);
            $stmt_obter->bind_param("ii", $produto_id, $usuario_id);
            $stmt_obter->execute();
            $result_obter = $stmt_obter->get_result();

            if ($result_obter->num_rows > 0) {
                $produto = $result_obter->fetch_assoc();
                $imagem = $produto['imagem'];

                // Deletar a imagem do servidor, se existir
                if (!empty($imagem)) {
                    $imagem_path = '../uploads/produtos/' . $imagem;
                    if (file_exists($imagem_path)) {
                        unlink($imagem_path);
                    }
                }

                // Deletar do banco de dados
                $sql_excluir = "DELETE FROM produtos WHERE id = ? AND usuario_id = ?";
                $stmt_excluir = $conn->prepare($sql_excluir);
                $stmt_excluir->bind_param("ii", $produto_id, $usuario_id);
                $stmt_excluir->execute();
                $stmt_excluir->close();

                $mensagem = "Produto excluído com sucesso!";
                $tipo_alerta = "success";
            } else {
                $mensagem = "Produto não encontrado ou você não tem permissão para excluí-lo.";
                $tipo_alerta = "danger";
            }
            $stmt_obter->close();
        }
    } catch (Exception $e) {
        $mensagem = "Erro: " . $e->getMessage();
        $tipo_alerta = "danger";
    }
}

// **Buscando a Lista de Produtos (fora do bloco POST)**
try {
    $sql_produtos = "SELECT * FROM produtos WHERE usuario_id = ? ORDER BY data_adicionado DESC";
    $stmt_produtos = $conn->prepare($sql_produtos);
    $stmt_produtos->bind_param("i", $usuario_id);
    $stmt_produtos->execute();
    $result_produtos = $stmt_produtos->get_result();
    $stmt_produtos->close();
    
    // Contagem de produtos
    $total_produtos = $result_produtos->num_rows;
    
    // Cálculo de estoque total
    $estoque_total = 0;
    $produtos_sem_estoque = 0;
    $result_produtos_temp = $result_produtos; // Copiar resultado para não afetar o original
    while ($produto = $result_produtos_temp->fetch_assoc()) {
        $estoque_total += $produto['estoque'];
        if ($produto['estoque'] == 0) {
            $produtos_sem_estoque++;
        }
    }
    $result_produtos->data_seek(0); // Resetar o ponteiro do resultado
    
} catch (mysqli_sql_exception $e) {
    die("Erro na consulta SQL: " . $e->getMessage());
}

// **Buscando a Lista de Pedidos (fora do bloco POST)**
try {
    $sql_pedidos = "SELECT p.id AS pedido_id, p.usuario_id, p.total, p.status, p.data_pedido,
                          ip.produto_id, ip.quantidade, ip.preco_unitario,
                          pf.nome_produto, pf.categoria
                    FROM pedidos p
                    INNER JOIN itens_pedido ip ON p.id = ip.pedido_id
                    INNER JOIN produtos pf ON ip.produto_id = pf.id
                    WHERE pf.usuario_id = ?
                    ORDER BY p.data_pedido DESC";
    $stmt_pedidos = $conn->prepare($sql_pedidos);
    $stmt_pedidos->bind_param("i", $usuario_id);
    $stmt_pedidos->execute();
    $result_pedidos = $stmt_pedidos->get_result();
    $stmt_pedidos->close();
    
    // Contagem de pedidos
    $total_pedidos = 0;
    $pedidos_pendentes = 0;
    $pedidos_concluidos = 0;
    
    // Array para armazenar pedidos únicos
    $pedidos_unicos = [];
    
    while ($pedido = $result_pedidos->fetch_assoc()) {
        if (!isset($pedidos_unicos[$pedido['pedido_id']])) {
            $pedidos_unicos[$pedido['pedido_id']] = $pedido;
            $total_pedidos++;
            
            if ($pedido['status'] == 'pendente' || $pedido['status'] == 'processando') {
                $pedidos_pendentes++;
            } elseif ($pedido['status'] == 'entregue') {
                $pedidos_concluidos++;
            }
        }
    }
    
    // Resetar o ponteiro do resultado
    $result_pedidos->data_seek(0);
    
} catch (mysqli_sql_exception $e) {
    die("Erro na consulta de pedidos: " . $e->getMessage());
}
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <title>Meu Marketplace - Assistência FixFácil</title>
    <!-- Meta Tags para Responsividade -->
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome 6 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- SweetAlert2 CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">
    <!-- DataTables CSS -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.4/css/dataTables.bootstrap5.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Estilos personalizados -->
    <style>
        :root {
            --primary-color: #2563eb;
            --primary-dark: #1d4ed8;
            --secondary-color: #475569;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --light-bg: #f8fafc;
            --card-bg: #ffffff;
            --text-color: #1e293b;
            --text-muted: #64748b;
            --border-radius: 12px;
            --shadow-sm: 0 1px 3px rgba(0,0,0,0.1);
            --shadow-md: 0 4px 6px -1px rgba(0,0,0,0.1), 0 2px 4px -1px rgba(0,0,0,0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0,0,0,0.1), 0 4px 6px -2px rgba(0,0,0,0.05);
        }
        
        body {
            font-family: 'Inter', sans-serif;
            background-color: var(--light-bg);
            color: var(--text-color);
            margin-bottom: 80px;
            padding-top: 70px;
        }
        
        /* Navbar */
        .navbar {
            background-color: var(--card-bg);
            box-shadow: var(--shadow-sm);
            padding: 12px 0;
        }
        
        .navbar-brand {
            font-weight: 700;
            color: var(--primary-color) !important;
        }
        
        .navbar .nav-link {
            color: var(--text-color) !important;
            font-weight: 500;
            padding: 8px 16px;
            border-radius: var(--border-radius);
            transition: all 0.3s ease;
        }
        
        .navbar .nav-link:hover {
            background-color: rgba(37, 99, 235, 0.1);
            color: var(--primary-color) !important;
        }
        
        .navbar .nav-link.active {
            background-color: var(--primary-color);
            color: white !important;
        }
        
        /* Conteúdo Principal */
        .main-content {
            padding: 20px 12px;
        }
        
        .header-section {
            margin-bottom: 24px;
        }
        
        .header-section h1 {
            font-weight: 700;
            font-size: 1.75rem;
            margin-bottom: 8px;
            color: var(--text-color);
        }
        
        .header-section p {
            color: var(--text-muted);
            font-size: 1rem;
            margin-bottom: 0;
        }
        
        /* Cards de resumo */
        .stats-card {
            background-color: var(--card-bg);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-md);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            padding: 20px;
            height: 100%;
            position: relative;
            overflow: hidden;
        }
        
        .stats-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-lg);
        }
        
        .stats-card .card-icon {
            position: absolute;
            top: 15px;
            right: 15px;
            font-size: 2.5rem;
            opacity: 0.2;
            color: inherit;
        }
        
        .stats-card .card-title {
            font-weight: 600;
            font-size: 0.9rem;
            margin-bottom: 10px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .stats-card .card-value {
            font-weight: 700;
            font-size: 2rem;
            margin-bottom: 0;
        }
        
        .produtos-total { color: var(--primary-color); }
        .estoque-total { color: var(--success-color); }
        .sem-estoque { color: var(--warning-color); }
        .pedidos-total { color: var(--secondary-color); }
        
        /* Contêiner de Conteúdo */
        .content-container {
            background-color: var(--card-bg);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-md);
            margin-bottom: 30px;
            overflow: hidden;
        }
        
        .content-header {
            padding: 20px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            background-color: rgba(0, 0, 0, 0.02);
        }
        
        .content-header h2 {
            margin: 0;
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--text-color);
        }
        
        .content-body {
            padding: 20px;
        }
        
        /* Formulários */
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-label {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--text-color);
        }
        
        .form-control {
            border-radius: 8px;
            padding: 12px 16px;
            border: 1px solid rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }
        
        textarea.form-control {
            min-height: 120px;
        }
        
        /* Tabela de produtos e pedidos */
        .tabela-container {
            overflow-x: auto;
        }
        
        .table-striped > tbody > tr:nth-of-type(odd) > * {
            background-color: rgba(0, 0, 0, 0.02);
        }
        
        .table > :not(caption) > * > * {
            padding: 12px 16px;
        }
        
        /* Status Badges */
        .status-badge {
            padding: 6px 12px;
            border-radius: 50px;
            font-weight: 600;
            font-size: 0.75rem;
            text-transform: uppercase;
        }
        
        .status-badge-pendente {
            background-color: rgba(245, 158, 11, 0.1);
            color: var(--warning-color);
        }
        
        .status-badge-processando {
            background-color: rgba(79, 70, 229, 0.1);
            color: #4f46e5;
        }
        
        .status-badge-enviado {
            background-color: rgba(16, 185, 129, 0.1);
            color: var(--success-color);
        }
        
        .status-badge-entregue {
            background-color: rgba(37, 99, 235, 0.1);
            color: var(--primary-color);
        }
        
        .status-badge-cancelado {
            background-color: rgba(239, 68, 68, 0.1);
            color: var(--danger-color);
        }
        
        /* Modal */
        .modal-content {
            border-radius: var(--border-radius);
            border: none;
            box-shadow: var(--shadow-lg);
        }
        
        .modal-header {
            background-color: var(--primary-color);
            color: white;
            border-top-left-radius: var(--border-radius);
            border-top-right-radius: var(--border-radius);
            border-bottom: none;
            padding: 20px;
        }
        
        .modal-title {
            font-weight: 600;
        }
        
        .modal-header .btn-close {
            color: white;
            background: transparent url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='white'%3e%3cpath d='M.293.293a1 1 0 011.414 0L8 6.586 14.293.293a1 1 0 111.414 1.414L9.414 8l6.293 6.293a1 1 0 01-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 01-1.414-1.414L6.586 8 .293 1.707a1 1 0 010-1.414z'/%3e%3c/svg%3e") center/1em auto no-repeat;
        }
        
        .modal-body {
            padding: 24px;
        }
        
        .modal-footer {
            padding: 16px 24px;
            border-top: 1px solid rgba(0, 0, 0, 0.1);
        }
        
        /* Imagens de produtos */
        .produto-imagem {
            width: 80px;
            height: 80px;
            object-fit: cover;
            border-radius: 8px;
            border: 1px solid rgba(0, 0, 0, 0.1);
        }
        
        .produto-imagem-modal {
            width: 120px;
            height: 120px;
            object-fit: cover;
            border-radius: 8px;
            border: 1px solid rgba(0, 0, 0, 0.1);
        }
        
        /* Botões de ação */
        .action-btn {
            padding: 6px 12px;
            border-radius: var(--border-radius);
            font-weight: 500;
            font-size: 0.8rem;
            margin-right: 5px;
            margin-bottom: 5px;
            display: inline-flex;
            align-items: center;
        }
        
        .action-btn i {
            margin-right: 5px;
        }
        
        /* Mobile Menu */
        .mobile-menu {
            position: fixed;
            bottom: 0;
            width: 100%;
            background-color: var(--card-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            z-index: 1000;
            padding: 8px 0;
            display: flex;
            justify-content: space-around;
        }
        
        .mobile-menu .menu-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 8px 0;
            width: 20%;
            text-decoration: none;
        }
        
        .mobile-menu .menu-item i {
            font-size: 20px;
            margin-bottom: 4px;
            color: var(--text-muted);
            transition: color 0.3s ease;
        }
        
        .mobile-menu .menu-item span {
            font-size: 12px;
            color: var(--text-muted);
            transition: color 0.3s ease;
        }
        
        .mobile-menu .menu-item.active i,
        .mobile-menu .menu-item.active span {
            color: var(--primary-color);
            font-weight: 600;
        }
        
        .mobile-menu .menu-item:hover i,
        .mobile-menu .menu-item:hover span {
            color: var(--primary-dark);
        }
        
        /* Esconder menu mobile em desktop */
        @media (min-width: 992px) {
            .mobile-menu {
                display: none;
            }
        }
        
        /* Ajustes para mobile */
        @media (max-width: 767px) {
            .main-content {
                padding: 15px 10px;
            }
            
            .stats-card {
                margin-bottom: 15px;
            }
            
            .content-container {
                margin-bottom: 20px;
            }
            
            .content-header,
            .content-body {
                padding: 15px;
            }
            
            .action-btn {
                width: 100%;
                margin-right: 0;
                margin-bottom: 8px;
                justify-content: center;
            }
            
            .table > :not(caption) > * > * {
                padding: 10px 12px;
            }
            
            .modal-dialog {
                margin: 10px;
            }
        }
        
        /* Customização de tabs */
        .nav-tabs {
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        
        .nav-tabs .nav-link {
            color: var(--text-muted);
            border: none;
            padding: 12px 20px;
            font-weight: 500;
            position: relative;
            transition: all 0.3s ease;
        }
        
        .nav-tabs .nav-link:hover {
            color: var(--primary-color);
            background-color: rgba(37, 99, 235, 0.05);
        }
        
        .nav-tabs .nav-link.active {
            color: var(--primary-color);
            background-color: transparent;
        }
        
        .nav-tabs .nav-link.active::after {
            content: '';
            position: absolute;
            bottom: -1px;
            left: 0;
            width: 100%;
            height: 2px;
            background-color: var(--primary-color);
        }
        
        /* Alerta personalizado */
        .custom-alert {
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 24px;
            border-left: 4px solid;
        }
        
        .alert-success {
            background-color: rgba(16, 185, 129, 0.1);
            border-color: var(--success-color);
            color: #065f46;
        }
        
        .alert-danger {
            background-color: rgba(239, 68, 68, 0.1);
            border-color: var(--danger-color);
            color: #b91c1c;
        }
        
        .alert-warning {
            background-color: rgba(245, 158, 11, 0.1);
            border-color: var(--warning-color);
            color: #92400e;
        }
        
        .alert-info {
            background-color: rgba(37, 99, 235, 0.1);
            border-color: var(--primary-color);
            color: #1e40af;
        }
        
        /* Loading overlay */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(255, 255, 255, 0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.3s ease, visibility 0.3s ease;
        }
        
        .loading-overlay.show {
            opacity: 1;
            visibility: visible;
        }
        
        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid rgba(37, 99, 235, 0.1);
            border-radius: 50%;
            border-top-color: var(--primary-color);
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* Sem dados placeholder */
        .sem-dados {
            text-align: center;
            padding: 40px 20px;
        }
        
        .sem-dados i {
            font-size: 3rem;
            color: varcolor: var(--text-muted);
            margin-bottom: 15px;
        }
        
        .sem-dados h4 {
            font-weight: 600;
            margin-bottom: 10px;
        }
        
        .sem-dados p {
            color: var(--text-muted);
            max-width: 500px;
            margin: 0 auto;
        }
    </style>
</head>
<body>
    <!-- Overlay de carregamento -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="spinner"></div>
    </div>

    <!-- Cabeçalho (Navbar) -->
    <nav class="navbar navbar-expand-lg fixed-top navbar-light">
        <div class="container">
            <a class="navbar-brand" href="#">FixFácil</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" 
                    aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="home.php">Painel</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="solicitacoes.php">Solicitações</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="propostas_enviadas.php">Propostas</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="reparos_em_andamento.php">Reparos</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="meumarktplace.php">Marketplace</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="solicitar_pecas.php">Peças</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="carteira.php">Carteira</a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-user-circle"></i>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="perfil.php">Meu Perfil</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../logout.php">Sair</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Conteúdo Principal -->
    <div class="container main-content">
        <!-- Cabeçalho da página -->
        <div class="header-section">
            <h1>Meu Marketplace</h1>
            <p>Cadastre seus produtos e acompanhe os pedidos recebidos para venda online.</p>
        </div>

        <!-- Exibir Mensagens -->
        <?php if (!empty($mensagem)): ?>
            <div class="custom-alert alert-<?php echo $tipo_alerta; ?>" role="alert">
                <?php echo htmlspecialchars($mensagem); ?>
            </div>
        <?php endif; ?>

        <!-- Cards de Resumo -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stats-card">
                    <i class="fas fa-box-open card-icon produtos-total"></i>
                    <div class="card-title produtos-total">Total de Produtos</div>
                    <div class="card-value produtos-total"><?php echo $total_produtos; ?></div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stats-card">
                    <i class="fas fa-cubes card-icon estoque-total"></i>
                    <div class="card-title estoque-total">Estoque Total</div>
                    <div class="card-value estoque-total"><?php echo $estoque_total; ?></div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stats-card">
                    <i class="fas fa-exclamation-triangle card-icon sem-estoque"></i>
                    <div class="card-title sem-estoque">Sem Estoque</div>
                    <div class="card-value sem-estoque"><?php echo $produtos_sem_estoque; ?></div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stats-card">
                    <i class="fas fa-shopping-cart card-icon pedidos-total"></i>
                    <div class="card-title pedidos-total">Total de Pedidos</div>
                    <div class="card-value pedidos-total"><?php echo $total_pedidos; ?></div>
                </div>
            </div>
        </div>

        <!-- Tabs de Navegação -->
        <ul class="nav nav-tabs" id="myTab" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="produtos-tab" data-bs-toggle="tab" data-bs-target="#produtos" type="button" role="tab" aria-controls="produtos" aria-selected="true">
                    <i class="fas fa-boxes me-2"></i>Meus Produtos
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="pedidos-tab" data-bs-toggle="tab" data-bs-target="#pedidos" type="button" role="tab" aria-controls="pedidos" aria-selected="false">
                    <i class="fas fa-shopping-bag me-2"></i>Pedidos Recebidos
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="adicionar-tab" data-bs-toggle="tab" data-bs-target="#adicionar" type="button" role="tab" aria-controls="adicionar" aria-selected="false">
                    <i class="fas fa-plus-circle me-2"></i>Adicionar Produto
                </button>
            </li>
        </ul>

        <!-- Conteúdo das Tabs -->
        <div class="tab-content" id="myTabContent">
            <!-- Tab de Produtos -->
            <div class="tab-pane fade show active" id="produtos" role="tabpanel" aria-labelledby="produtos-tab">
                <div class="content-container">
                    <div class="content-header d-flex justify-content-between align-items-center">
                        <h2><i class="fas fa-boxes me-2"></i>Produtos Cadastrados</h2>
                        <button class="btn btn-primary" id="btn-add-produto" data-bs-toggle="tab" data-bs-target="#adicionar">
                            <i class="fas fa-plus me-2"></i>Novo Produto
                        </button>
                    </div>
                    <div class="content-body">
                        <div class="tabela-container">
                            <table class="table table-striped" id="tabela-produtos">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Imagem</th>
                                        <th>Nome do Produto</th>
                                        <th>Categoria</th>
                                        <th>Preço</th>
                                        <th>Estoque</th>
                                        <th>Ações</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (isset($result_produtos) && $result_produtos->num_rows > 0): ?>
                                        <?php while ($produto = $result_produtos->fetch_assoc()): ?>
                                            <tr>
                                                <td>#<?php echo $produto['id']; ?></td>
                                                <td>
                                                    <?php if (!empty($produto['imagem'])): ?>
                                                        <img src="../uploads/produtos/<?php echo htmlspecialchars($produto['imagem']); ?>" alt="Imagem do Produto" class="produto-imagem">
                                                    <?php else: ?>
                                                        <div class="produto-imagem d-flex align-items-center justify-content-center bg-light">
                                                            <i class="fas fa-image text-muted fa-2x"></i>
                                                        </div>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <strong><?php echo htmlspecialchars($produto['nome_produto']); ?></strong>
                                                    <?php if (!empty($produto['descricao'])): ?>
                                                        <p class="text-muted mb-0 small"><?php echo mb_substr(htmlspecialchars($produto['descricao']), 0, 50); ?><?php echo (mb_strlen($produto['descricao']) > 50) ? '...' : ''; ?></p>
                                                    <?php endif; ?>
                                                </td>
                                                <td><?php echo htmlspecialchars($produto['categoria'] ?? 'N/A'); ?></td>
                                                <td><strong class="text-success">R$ <?php echo number_format($produto['preco'], 2, ',', '.'); ?></strong></td>
                                                <td>
                                                    <?php if ($produto['estoque'] > 0): ?>
                                                        <span class="badge bg-success"><?php echo $produto['estoque']; ?> unid.</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-danger">Sem estoque</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <div class="d-flex flex-column flex-md-row">
                                                        <button class="btn btn-primary btn-sm action-btn mb-2 mb-md-0 me-md-2 btn-editar" 
                                                                data-bs-toggle="modal" data-bs-target="#modalEditar"
                                                                data-id="<?php echo $produto['id']; ?>"
                                                                data-nome="<?php echo htmlspecialchars($produto['nome_produto']); ?>"
                                                                data-descricao="<?php echo htmlspecialchars($produto['descricao']); ?>"
                                                                data-preco="<?php echo $produto['preco']; ?>"
                                                                data-estoque="<?php echo $produto['estoque']; ?>"
                                                                data-categoria="<?php echo htmlspecialchars($produto['categoria']); ?>"
                                                                data-imagem="<?php echo htmlspecialchars($produto['imagem']); ?>">
                                                            <i class="fas fa-edit"></i> Editar
                                                        </button>
                                                        <button class="btn btn-danger btn-sm action-btn btn-excluir" 
                                                                data-bs-toggle="modal" data-bs-target="#modalExcluir"
                                                                data-id="<?php echo $produto['id']; ?>"
                                                                data-nome="<?php echo htmlspecialchars($produto['nome_produto']); ?>">
                                                            <i class="fas fa-trash-alt"></i> Excluir
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endwhile; ?>
                                    <?php else: ?>
                                        <tr>
                                            <td colspan="7">
                                                <div class="sem-dados">
                                                    <i class="fas fa-box-open"></i>
                                                    <h4>Nenhum produto cadastrado</h4>
                                                    <p>Você ainda não possui produtos em seu marketplace. Clique em "Adicionar Produto" para começar a vender.</p>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tab de Pedidos -->
            <div class="tab-pane fade" id="pedidos" role="tabpanel" aria-labelledby="pedidos-tab">
                <div class="content-container">
                    <div class="content-header">
                        <h2><i class="fas fa-shopping-bag me-2"></i>Pedidos Recebidos</h2>
                    </div>
                    <div class="content-body">
                        <div class="tabela-container">
                            <table class="table table-striped" id="tabela-pedidos">
                                <thead>
                                    <tr>
                                        <th>Pedido #</th>
                                        <th>Cliente</th>
                                        <th>Data</th>
                                        <th>Total</th>
                                        <th>Status</th>
                                        <th>Produtos</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (isset($result_pedidos) && $result_pedidos->num_rows > 0): ?>
                                        <?php 
                                        // Agrupar pedidos para evitar repetição de informações
                                        $pedidos = [];
                                        while ($pedido = $result_pedidos->fetch_assoc()) {
                                            $pedido_id = $pedido['pedido_id'];
                                            if (!isset($pedidos[$pedido_id])) {
                                                $pedidos[$pedido_id] = [
                                                    'usuario_id' => $pedido['usuario_id'],
                                                    'total' => $pedido['total'],
                                                    'status' => $pedido['status'],
                                                    'data_pedido' => $pedido['data_pedido'],
                                                    'produtos' => []
                                                ];
                                            }
                                            $pedidos[$pedido_id]['produtos'][] = [
                                                'nome_produto' => $pedido['nome_produto'],
                                                'categoria' => $pedido['categoria'],
                                                'quantidade' => $pedido['quantidade'],
                                                'preco_unitario' => $pedido['preco_unitario']
                                            ];
                                        }
                                        ?>

                                        <?php foreach ($pedidos as $id => $pedido): ?>
                                            <tr>
                                                <td>#<?php echo $id; ?></td>
                                                <td>Cliente #<?php echo $pedido['usuario_id']; ?></td>
                                                <td><?php echo date('d/m/Y H:i', strtotime($pedido['data_pedido'])); ?></td>
                                                <td><strong class="text-success">R$ <?php echo number_format($pedido['total'], 2, ',', '.'); ?></strong></td>
                                                <td>
                                                    <?php
                                                    $status = $pedido['status'];
                                                    switch ($status) {
                                                        case 'pendente':
                                                            echo '<span class="status-badge status-badge-pendente">Pendente</span>';
                                                            break;
                                                        case 'processando':
                                                            echo '<span class="status-badge status-badge-processando">Processando</span>';
                                                            break;
                                                        case 'enviado':
                                                            echo '<span class="status-badge status-badge-enviado">Enviado</span>';
                                                            break;
                                                        case 'entregue':
                                                            echo '<span class="status-badge status-badge-entregue">Entregue</span>';
                                                            break;
                                                        case 'cancelado':
                                                            echo '<span class="status-badge status-badge-cancelado">Cancelado</span>';
                                                            break;
                                                        default:
                                                            echo '<span class="badge bg-secondary">Indefinido</span>';
                                                            break;
                                                    }
                                                    ?>
                                                </td>
                                                <td>
                                                    <button class="btn btn-info btn-sm action-btn" data-bs-toggle="modal" data-bs-target="#modalDetalhesPedido" data-pedido-id="<?php echo $id; ?>">
                                                        <i class="fas fa-eye"></i> Detalhes
                                                    </button>
                                                    <!-- Armazenar os dados do pedido para uso no modal -->
                                                    <div class="d-none" id="detalhes-pedido-<?php echo $id; ?>" data-pedido='<?php echo json_encode($pedido); ?>'></div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    <?php else: ?>
                                        <tr>
                                            <td colspan="6">
                                                <div class="sem-dados">
                                                    <i class="fas fa-shopping-cart"></i>
                                                    <h4>Nenhum pedido recebido</h4>
                                                    <p>Você ainda não recebeu pedidos no marketplace. Quando os clientes comprarem seus produtos, os pedidos aparecerão aqui.</p>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tab de Adicionar Produto -->
            <div class="tab-pane fade" id="adicionar" role="tabpanel" aria-labelledby="adicionar-tab">
                <div class="content-container">
                    <div class="content-header">
                        <h2><i class="fas fa-plus-circle me-2"></i>Adicionar Novo Produto</h2>
                    </div>
                    <div class="content-body">
                        <form action="meumarktplace.php" method="POST" enctype="multipart/form-data" id="form-adicionar-produto">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="nome_produto" class="form-label">Nome do Produto <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" id="nome_produto" name="nome_produto" required>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label for="categoria" class="form-label">Categoria</label>
                                        <input type="text" class="form-control" id="categoria" name="categoria" placeholder="Ex: Acessórios, Peças, etc">
                                    </div>
                                    
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="preco" class="form-label">Preço (R$) <span class="text-danger">*</span></label>
                                                <input type="number" class="form-control" id="preco" name="preco" step="0.01" min="0" required placeholder="0.00">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="estoque" class="form-label">Estoque <span class="text-danger">*</span></label>
                                                <input type="number" class="form-control" id="estoque" name="estoque" min="0" value="1" required>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label for="imagem" class="form-label">Imagem do Produto</label>
                                        <input type="file" class="form-control" id="imagem" name="imagem" accept="image/*">
                                        <small class="form-text text-muted">Formatos aceitos: JPG, JPEG, PNG, GIF. Tamanho máximo: 2MB.</small>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="descricao" class="form-label">Descrição do Produto</label>
                                        <textarea class="form-control" id="descricao" name="descricao" rows="10" placeholder="Descreva detalhes do produto, especificações, benefícios, etc."></textarea>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="form-group text-end mt-4">
                                <button type="button" class="btn btn-secondary me-2" data-bs-toggle="tab" data-bs-target="#produtos">Cancelar</button>
                                <button type="submit" name="adicionar_produto" class="btn btn-success">
                                    <i class="fas fa-plus-circle me-2"></i>Adicionar Produto
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal para Editar Produto -->
    <div class="modal fade" id="modalEditar" tabindex="-1" aria-labelledby="modalEditarLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="modalEditarLabel">Editar Produto</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Fechar"></button>
                </div>
                <form action="meumarktplace.php" method="POST" enctype="multipart/form-data">
                    <div class="modal-body">
                        <input type="hidden" name="produto_id" id="editar_produto_id" value="">
                        <input type="hidden" name="imagem_atual" id="editar_imagem_atual" value="">
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="editar_nome_produto" class="form-label">Nome do Produto <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="editar_nome_produto" name="nome_produto" required>
                                </div>
                                
                                <div class="form-group mb-3">
                                    <label for="editar_categoria" class="form-label">Categoria</label>
                                    <input type="text" class="form-control" id="editar_categoria" name="categoria">
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <label for="editar_preco" class="form-label">Preço (R$) <span class="text-danger">*</span></label>
                                            <input type="number" class="form-control" id="editar_preco" name="preco" step="0.01" min="0" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <label for="editar_estoque" class="form-label">Estoque <span class="text-danger">*</span></label>
                                            <input type="number" class="form-control" id="editar_estoque" name="estoque" min="0" required>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="form-group mb-3">
                                    <label for="editar_imagem" class="form-label">Imagem do Produto</label>
                                    <input type="file" class="form-control" id="editar_imagem" name="imagem" accept="image/*">
                                    <small class="form-text text-muted">Deixe em branco para manter a imagem atual.</small>
                                </div>
                                
                                <div class="mt-3">
                                    <label class="form-label">Imagem Atual</label>
                                    <div id="imagem-atual-container" class="text-center p-3 bg-light rounded">
                                        <img src="" alt="Imagem Atual" id="imagem_atual_visualizacao" class="produto-imagem-modal d-none">
                                        <div id="sem-imagem-msg" class="text-muted">
                                            <i class="fas fa-image fa-3x"></i>
                                            <p class="mt-2">Sem imagem</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="editar_descricao" class="form-label">Descrição do Produto</label>
                                    <textarea class="form-control" id="editar_descricao" name="descricao" rows="15"></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                        <button type="submit" name="editar_produto" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Salvar Alterações
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Modal para Excluir Produto -->
    <div class="modal fade" id="modalExcluir" tabindex="-1" aria-labelledby="modalExcluirLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title" id="modalExcluirLabel">Excluir Produto</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Fechar"></button>
                </div>
                <form action="meumarktplace.php" method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="produto_id" id="excluir_produto_id" value="">
                        <div class="text-center mb-4">
                            <i class="fas fa-exclamation-triangle text-danger fa-4x mb-3"></i>
                            <h4>Tem certeza?</h4>
                        </div>
                        <p>Você está prestes a excluir o produto <strong id="excluir_nome_produto"></strong>.</p>
                        <p class="text-danger">Esta ação não pode ser desfeita e todos os dados do produto serão perdidos permanentemente.</p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                        <button type="submit" name="excluir_produto" class="btn btn-danger">
                            <i class="fas fa-trash-alt me-2"></i>Excluir Produto
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Modal para Detalhes do Pedido -->
    <div class="modal fade" id="modalDetalhesPedido" tabindex="-1" aria-labelledby="modalDetalhesPedidoLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="modalDetalhesPedidoLabel">Detalhes do Pedido #<span id="detalhe-pedido-id"></span></h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Fechar"></button>
                </div>
                <div class="modal-body">
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h5 class="mb-3">Informações do Pedido</h5>
                            <div class="p-3 bg-light rounded">
                                <p><strong>Data do Pedido:</strong> <span id="detalhe-data"></span></p>
                                <p><strong>Cliente ID:</strong> <span id="detalhe-cliente"></span></p>
                                <p><strong>Status:</strong> <span id="detalhe-status"></span></p>
                                <p class="mb-0"><strong>Total:</strong> <span id="detalhe-total" class="text-success"></span></p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h5 class="mb-3">Ações</h5>
                            <div class="p-3 bg-light rounded">
                                <p class="text-muted">O status do pedido é atualizado automaticamente conforme o progresso do mesmo.</p>
                                <p class="text-muted mb-0">Para mais detalhes sobre o pedido, entre em contato com o suporte FixFácil.</p>
                            </div>
                        </div>
                    </div>
                    
                    <h5 class="mb-3">Produtos no Pedido</h5>
                    <div class="table-responsive">
                        <table class="table table-striped" id="tabela-itens-pedido">
                            <thead>
                                <tr>
                                    <th>Produto</th>
                                    <th>Categoria</th>
                                    <th>Quantidade</th>
                                    <th>Preço Unit.</th>
                                    <th>Subtotal</th>
                                </tr>
                            </thead>
                            <tbody id="detalhe-produtos">
                                <!-- Conteúdo será preenchido via JavaScript -->
                            </tbody>
                            <tfoot>
                                <tr>
                                    <td colspan="4" class="text-end"><strong>Total:</strong></td>
                                    <td><strong class="text-success" id="detalhe-total-2"></strong></td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fechar</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Menu Mobile (fixo na parte inferior) -->
    <div class="mobile-menu d-lg-none">
        <a href="home.php" class="menu-item">
            <i class="fas fa-home"></i>
            <span>Painel</span>
        </a>
        <a href="solicitacoes.php" class="menu-item">
            <i class="fas fa-inbox"></i>
            <span>Solicitações</span>
        </a>
        <a href="propostas_enviadas.php" class="menu-item">
            <i class="fas fa-paper-plane"></i>
            <span>Propostas</span>
        </a>
        <a href="reparos_em_andamento.php" class="menu-item">
            <i class="fas fa-tools"></i>
            <span>Reparos</span>
        </a>
        <a href="perfil.php" class="menu-item">
            <i class="fas fa-user"></i>
            <span>Perfil</span>
        </a>
    </div>

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="https://cdn.datatables.net/1.13.4/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.4/js/dataTables.bootstrap5.min.js"></script>
    <script>
        $(document).ready(function() {
            // Inicializar DataTables
            $.fn.dataTable.ext.errMode = 'none'; // Desativar mensagens de erro
            
            $('#tabela-produtos').DataTable({
                language: {
                    url: '//cdn.datatables.net/plug-ins/1.13.4/i18n/pt-BR.json',
                },
                responsive: true,
                ordering: true,
                pageLength: 10,
                lengthMenu: [5, 10, 25, 50],
                columnDefs: [
                    { orderable: false, targets: [1, 6] } // Desativar ordenação nas colunas de imagem e ações
                ]
            });
            
            $('#tabela-pedidos').DataTable({
                language: {
                    url: '//cdn.datatables.net/plug-ins/1.13.4/i18n/pt-BR.json',
                },
                responsive: true,
                ordering: true,
                pageLength: 10,
                lengthMenu: [5, 10, 25, 50],
                columnDefs: [
                    { orderable: false, targets: [5] } // Desativar ordenação na coluna de ações
                ]
            });
            
            // Função para mostrar overlay de carregamento
            function showLoading() {
                $('#loadingOverlay').addClass('show');
            }

            // Função para esconder overlay de carregamento
            function hideLoading() {
                $('#loadingOverlay').removeClass('show');
            }
            
            // Navegação entre tabs
            $('#btn-add-produto').on('click', function() {
                $('#myTab a[href="#adicionar"]').tab('show');
            });
            
            // Modal de Editar Produto
            $('#modalEditar').on('show.bs.modal', function(event) {
                const button = $(event.relatedTarget);
                const produtoId = button.data('id');
                const nomeProduto = button.data('nome');
                const descricao = button.data('descricao');
                const preco = button.data('preco');
                const estoque = button.data('estoque');
                const categoria = button.data('categoria');
                const imagem = button.data('imagem');
                
                const modal = $(this);
                modal.find('#editar_produto_id').val(produtoId);
                modal.find('#editar_nome_produto').val(nomeProduto);
                modal.find('#editar_descricao').val(descricao);
                modal.find('#editar_preco').val(preco);
                modal.find('#editar_estoque').val(estoque);
                modal.find('#editar_categoria').val(categoria);
                modal.find('#editar_imagem_atual').val(imagem);
                
                if (imagem) {
                    modal.find('#imagem_atual_visualizacao').attr('src', '../uploads/produtos/' + imagem).removeClass('d-none');
                    modal.find('#sem-imagem-msg').addClass('d-none');
                } else {
                    modal.find('#imagem_atual_visualizacao').addClass('d-none');
                    modal.find('#sem-imagem-msg').removeClass('d-none');
                }
            });
            
            // Modal de Excluir Produto
            $('#modalExcluir').on('show.bs.modal', function(event) {
                const button = $(event.relatedTarget);
                const produtoId = button.data('id');
                const nomeProduto = button.data('nome');
                
                const modal = $(this);
                modal.find('#excluir_produto_id').val(produtoId);
                modal.find('#excluir_nome_produto').text(nomeProduto);
            });
            
            // Modal de Detalhes do Pedido
            $('#modalDetalhesPedido').on('show.bs.modal', function(event) {
                const button = $(event.relatedTarget);
                const pedidoId = button.data('pedido-id');
                
                // Obter os dados do pedido armazenados no elemento oculto
                const pedidoData = JSON.parse($('#detalhes-pedido-' + pedidoId).data('pedido'));
                
                // Preencher os dados básicos do pedido
                $('#detalhe-pedido-id').text(pedidoId);
                $('#detalhe-data').text(formatarData(pedidoData.data_pedido));
                $('#detalhe-cliente').text(pedidoData.usuario_id);
                $('#detalhe-total').text('R$ ' + formatarMoeda(pedidoData.total));
                $('#detalhe-total-2').text('R$ ' + formatarMoeda(pedidoData.total));
                
                // Configurar o status
                let statusHtml = '';
                switch (pedidoData.status) {
                    case 'pendente':
                        statusHtml = '<span class="status-badge status-badge-pendente">Pendente</span>';
                        break;
                    case 'processando':
                        statusHtml = '<span class="status-badge status-badge-processando">Processando</span>';
                        break;
                    case 'enviado':
                        statusHtml = '<span class="status-badge status-badge-enviado">Enviado</span>';
                        break;
                    case 'entregue':
                        statusHtml = '<span class="status-badge status-badge-entregue">Entregue</span>';
                        break;
                    case 'cancelado':
                        statusHtml = '<span class="status-badge status-badge-cancelado">Cancelado</span>';
                        break;
                    default:
                        statusHtml = '<span class="badge bg-secondary">Indefinido</span>';
                        break;
                }
                $('#detalhe-status').html(statusHtml);
                
                // Preencher a tabela de produtos
                let produtosHtml = '';
                pedidoData.produtos.forEach(function(produto) {
                    const subtotal = produto.quantidade * produto.preco_unitario;
                    produtosHtml += `
                    <tr>
                        <td><strong>${produto.nome_produto}</strong></td>
                        <td>${produto.categoria || 'N/A'}</td>
                        <td>${produto.quantidade}</td>
                        <td>R$ ${formatarMoeda(produto.preco_unitario)}</td>
                        <td>R$ ${formatarMoeda(subtotal)}</td>
                    </tr>
                    `;
                });
                
                $('#detalhe-produtos').html(produtosHtml);
            });
            
            // Função para formatar data
            function formatarData(dataString) {
                const data = new Date(dataString);
                return data.toLocaleDateString('pt-BR', {
                    day: '2-digit',
                    month: '2-digit',
                    year: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                });
            }
            
            // Função para formatar valor monetário
            function formatarMoeda(valor) {
                return parseFloat(valor).toFixed(2).replace('.', ',');
            }
            
            // Exibir mensagens de sucesso com SweetAlert2
            <?php if ($tipo_alerta === 'success' && !empty($mensagem)): ?>
            Swal.fire({
                icon: 'success',
                title: 'Sucesso!',
                text: '<?php echo addslashes($mensagem); ?>',
                timer: 3000,
                timerProgressBar: true
            });
            <?php endif; ?>
            
            // Enviar formulário de adicionar produto com loading
            $('#form-adicionar-produto').on('submit', function() {
                showLoading();
            });
            
            // Inicialização concluída
            console.log('Scripts inicializados com sucesso!');
        });
    </script>
</body>
</html>