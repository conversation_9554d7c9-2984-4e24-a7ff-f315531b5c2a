<?php
session_start();

// Verificar se o usuário está logado e é do tipo 'assistencia'
if (!isset($_SESSION['usuario_id']) || $_SESSION['tipo_usuario'] != 'assistencia') {
    header('Location: ../login.php');
    exit();
}

$usuario_id = $_SESSION['usuario_id'];
$nome_assistencia = $_SESSION['nome'];

// Configurações do banco de dados
$servername = "localhost";
$username_db = "u680766645_fixfacilnew";
$password_db = "T3cn0l0g1a@";
$dbname = "u680766645_fixfacilnew";

// Ativar exceções para erros do MySQLi
mysqli_report(MYSQLI_REPORT_ERROR | MYSQLI_REPORT_STRICT);

try {
    // Conectar ao banco de dados
    $conn = new mysqli($servername, $username_db, $password_db, $dbname);
    $conn->set_charset("utf8mb4");
} catch (mysqli_sql_exception $e) {
    die("Falha na conexão: " . $e->getMessage());
}

// Obter o `id` da assistência técnica a partir do `usuario_id`
try {
    $sql_assistencia = "SELECT id FROM assistencias_tecnicas WHERE usuario_id = ?";
    $stmt_assistencia = $conn->prepare($sql_assistencia);
    $stmt_assistencia->bind_param("i", $usuario_id);
    $stmt_assistencia->execute();
    $result_assistencia = $stmt_assistencia->get_result();
    $stmt_assistencia->close();

    if ($result_assistencia->num_rows > 0) {
        $row_assistencia = $result_assistencia->fetch_assoc();
        $assistencia_id = $row_assistencia['id'];
    } else {
        die("Assistência técnica não encontrada.");
    }
} catch (mysqli_sql_exception $e) {
    die("Erro ao obter dados da assistência técnica: " . $e->getMessage());
}

// Inicializar variáveis para mensagens
$mensagem = "";
$tipo_alerta = "";

// Processar ações 
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Marcar Pagamento como Recebido
    if (isset($_POST['marcar_recebido'])) {
        $proposta_id = intval($_POST['proposta_id']);

        try {
            // Atualizar o status da proposta para 'pagamento' e marcar como pago
            $sql_receber = "UPDATE propostas_assistencia SET status = 'pagamento', pago = 1 WHERE id = ? AND assistencia_id = ?";
            $stmt_receber = $conn->prepare($sql_receber);
            $stmt_receber->bind_param("ii", $proposta_id, $assistencia_id);
            $stmt_receber->execute();
            $stmt_receber->close();

            $mensagem = "Pagamento marcado como recebido com sucesso!";
            $tipo_alerta = "success";
        } catch (mysqli_sql_exception $e) {
            $mensagem = "Erro ao atualizar pagamento: " . $e->getMessage();
            $tipo_alerta = "danger";
        }
    }
}

// Recuperar propostas em status 'aceita' (pagamentos pendentes)
try {
    $sql_pendentes = "
        SELECT 
            pa.id AS proposta_id, 
            sr.id AS solicitacao_id,
            sr.descricao_problema, 
            sr.dispositivo, 
            sr.marca, 
            sr.modelo, 
            TRIM(pa.status) AS status, 
            sr.data_solicitacao, 
            pa.preco
        FROM solicitacoes_reparo sr
        INNER JOIN propostas_assistencia pa ON sr.id = pa.solicitacao_id
        WHERE pa.assistencia_id = ? AND LOWER(TRIM(pa.status)) = LOWER('aceita')
        ORDER BY sr.data_solicitacao DESC
    ";
    $stmt_pendentes = $conn->prepare($sql_pendentes);
    $stmt_pendentes->bind_param("i", $assistencia_id);
    $stmt_pendentes->execute();
    $result_pendentes = $stmt_pendentes->get_result();
    $stmt_pendentes->close();
    
    $total_pendentes = 0;
    $count_pendentes = 0;
    $pendentes_array = [];
    
    while ($pendente = $result_pendentes->fetch_assoc()) {
        $total_pendentes += $pendente['preco'];
        $count_pendentes++;
        $pendentes_array[] = $pendente;
    }
    
} catch (mysqli_sql_exception $e) {
    die("Erro ao recuperar pagamentos pendentes: " . $e->getMessage());
}

// Recuperar propostas com status 'Em Andamento' e 'concluída' (valores a receber)
try {
    $sql_recebidos = "
        SELECT 
            pa.id AS proposta_id, 
            sr.id AS solicitacao_id,
            sr.descricao_problema, 
            sr.dispositivo, 
            sr.marca, 
            sr.modelo, 
            TRIM(pa.status) AS status, 
            sr.data_solicitacao, 
            pa.preco,
            pa.pago
        FROM solicitacoes_reparo sr
        INNER JOIN propostas_assistencia pa ON sr.id = pa.solicitacao_id
        WHERE pa.assistencia_id = ? AND (LOWER(TRIM(pa.status)) = LOWER('Em Andamento') OR LOWER(TRIM(pa.status)) = LOWER('concluída'))
        ORDER BY sr.data_solicitacao DESC
    ";
    $stmt_recebidos = $conn->prepare($sql_recebidos);
    $stmt_recebidos->bind_param("i", $assistencia_id);
    $stmt_recebidos->execute();
    $result_recebidos = $stmt_recebidos->get_result();
    $stmt_recebidos->close();
    
    $total_receber = 0;
    $count_recebidos = 0;
    $recebidos_array = [];
    
    while ($recebido = $result_recebidos->fetch_assoc()) {
        $total_receber += $recebido['preco'];
        $count_recebidos++;
        $recebidos_array[] = $recebido;
    }
    
} catch (mysqli_sql_exception $e) {
    die("Erro ao recuperar pagamentos a receber: " . $e->getMessage());
}

// Recuperar propostas com status 'pagamento' (pagamentos concluídos)
try {
    $sql_concluidos = "
        SELECT 
            pa.id AS proposta_id, 
            sr.id AS solicitacao_id,
            sr.descricao_problema, 
            sr.dispositivo, 
            sr.marca, 
            sr.modelo, 
            TRIM(pa.status) AS status, 
            sr.data_solicitacao, 
            pa.preco,
            pa.pago
        FROM solicitacoes_reparo sr
        INNER JOIN propostas_assistencia pa ON sr.id = pa.solicitacao_id
        WHERE pa.assistencia_id = ? AND LOWER(TRIM(pa.status)) = LOWER('pagamento')
        ORDER BY sr.data_solicitacao DESC
    ";
    $stmt_concluidos = $conn->prepare($sql_concluidos);
    $stmt_concluidos->bind_param("i", $assistencia_id);
    $stmt_concluidos->execute();
    $result_concluidos = $stmt_concluidos->get_result();
    $stmt_concluidos->close();
    
    $total_concluido = 0;
    $count_concluidos = 0;
    $concluidos_array = [];
    
    while ($concluido = $result_concluidos->fetch_assoc()) {
        $total_concluido += $concluido['preco'];
        $count_concluidos++;
        $concluidos_array[] = $concluido;
    }
    
} catch (mysqli_sql_exception $e) {
    die("Erro ao recuperar pagamentos concluídos: " . $e->getMessage());
}

// Fechar conexão
$conn->close();
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <title>Carteira - FixFácil</title>
    <!-- Meta Tags para Responsividade -->
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome 6 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- SweetAlert2 CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">
    <!-- DataTables CSS -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.4/css/dataTables.bootstrap5.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Estilos personalizados -->
    <style>
        :root {
            --primary-color: #2563eb;
            --primary-dark: #1d4ed8;
            --secondary-color: #475569;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --light-bg: #f8fafc;
            --card-bg: #ffffff;
            --text-color: #1e293b;
            --text-muted: #64748b;
            --border-radius: 12px;
            --shadow-sm: 0 1px 3px rgba(0,0,0,0.1);
            --shadow-md: 0 4px 6px -1px rgba(0,0,0,0.1), 0 2px 4px -1px rgba(0,0,0,0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0,0,0,0.1), 0 4px 6px -2px rgba(0,0,0,0.05);
        }
        
        body {
            font-family: 'Inter', sans-serif;
            background-color: var(--light-bg);
            color: var(--text-color);
            margin-bottom: 80px;
            padding-top: 70px;
        }
        
        /* Navbar */
        .navbar {
            background-color: var(--card-bg);
            box-shadow: var(--shadow-sm);
            padding: 12px 0;
        }
        
        .navbar-brand {
            font-weight: 700;
            color: var(--primary-color) !important;
        }
        
        .navbar .nav-link {
            color: var(--text-color) !important;
            font-weight: 500;
            padding: 8px 16px;
            border-radius: var(--border-radius);
            transition: all 0.3s ease;
        }
        
        .navbar .nav-link:hover {
            background-color: rgba(37, 99, 235, 0.1);
            color: var(--primary-color) !important;
        }
        
        .navbar .nav-link.active {
            background-color: var(--primary-color);
            color: white !important;
        }
        
        /* Conteúdo Principal */
        .main-content {
            padding: 20px 12px;
        }
        
        .header-section {
            margin-bottom: 24px;
        }
        
        .header-section h1 {
            font-weight: 700;
            font-size: 1.75rem;
            margin-bottom: 8px;
            color: var(--text-color);
        }
        
        .header-section p {
            color: var(--text-muted);
            font-size: 1rem;
            margin-bottom: 0;
        }
        
        /* Cards de resumo */
        .stats-card {
            background-color: var(--card-bg);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-md);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            padding: 20px;
            height: 100%;
            position: relative;
            overflow: hidden;
        }
        
        .stats-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-lg);
        }
        
        .stats-card .card-icon {
            position: absolute;
            top: 15px;
            right: 15px;
            font-size: 2.5rem;
            opacity: 0.2;
            color: inherit;
        }
        
        .stats-card .card-title {
            font-weight: 600;
            font-size: 0.9rem;
            margin-bottom: 10px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .stats-card .card-value {
            font-weight: 700;
            font-size: 2rem;
            margin-bottom: 0;
        }
        
        .stats-card .card-subtitle {
            color: var(--text-muted);
            font-size: 0.85rem;
            margin-top: 5px;
        }
        
        .pendentes-total { color: var(--warning-color); }
        .recebidos-total { color: var(--success-color); }
        .concluidos-total { color: var(--primary-color); }
        .taxa-info { color: var(--secondary-color); }
        
        /* Contêiner de Conteúdo */
        .content-container {
            background-color: var(--card-bg);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-md);
            margin-bottom: 30px;
            overflow: hidden;
        }
        
        .content-header {
            padding: 20px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            background-color: rgba(0, 0, 0, 0.02);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .content-header h2 {
            margin: 0;
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--text-color);
        }
        
        .content-body {
            padding: 20px;
        }
        
        /* Tabela */
        .tabela-container {
            overflow-x: auto;
        }
        
        .table-striped > tbody > tr:nth-of-type(odd) > * {
            background-color: rgba(0, 0, 0, 0.02);
        }
        
        .table > :not(caption) > * > * {
            padding: 12px 16px;
        }
        
        /* Status Badges */
        .status-badge {
            padding: 6px 12px;
            border-radius: 50px;
            font-weight: 600;
            font-size: 0.75rem;
            text-transform: uppercase;
        }
        
        .status-badge-pendente {
            background-color: rgba(245, 158, 11, 0.1);
            color: var(--warning-color);
        }
        
        .status-badge-recebido {
            background-color: rgba(16, 185, 129, 0.1);
            color: var(--success-color);
        }
        
        .status-badge-concluido {
            background-color: rgba(37, 99, 235, 0.1);
            color: var(--primary-color);
        }
        
        /* Botões de ação */
        .action-btn {
            padding: 6px 12px;
            border-radius: var(--border-radius);
            font-weight: 500;
            font-size: 0.8rem;
            margin-right: 5px;
            margin-bottom: 5px;
            display: inline-flex;
            align-items: center;
        }
        
        .action-btn i {
            margin-right: 5px;
        }
        
        /* Customização de tabs */
        .nav-tabs {
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        
        .nav-tabs .nav-link {
            color: var(--text-muted);
            border: none;
            padding: 12px 20px;
            font-weight: 500;
            position: relative;
            transition: all 0.3s ease;
        }
        
        .nav-tabs .nav-link:hover {
            color: var(--primary-color);
            background-color: rgba(37, 99, 235, 0.05);
        }
        
        .nav-tabs .nav-link.active {
            color: var(--primary-color);
            background-color: transparent;
        }
        
        .nav-tabs .nav-link.active::after {
            content: '';
            position: absolute;
            bottom: -1px;
            left: 0;
            width: 100%;
            height: 2px;
            background-color: var(--primary-color);
        }
        
        /* Mobile Menu */
        .mobile-menu {
            position: fixed;
            bottom: 0;
            width: 100%;
            background-color: var(--card-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            z-index: 1000;
            padding: 8px 0;
            display: flex;
            justify-content: space-around;
        }
        
        .mobile-menu .menu-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 8px 0;
            width: 20%;
            text-decoration: none;
        }
        
        .mobile-menu .menu-item i {
            font-size: 20px;
            margin-bottom: 4px;
            color: var(--text-muted);
            transition: color 0.3s ease;
        }
        
        .mobile-menu .menu-item span {
            font-size: 12px;
            color: var(--text-muted);
            transition: color 0.3s ease;
        }
        
        .mobile-menu .menu-item.active i,
        .mobile-menu .menu-item.active span {
            color: var(--primary-color);
            font-weight: 600;
        }
        
        .mobile-menu .menu-item:hover i,
        .mobile-menu .menu-item:hover span {
            color: var(--primary-dark);
        }
        
        /* Esconder menu mobile em desktop */
        @media (min-width: 992px) {
            .mobile-menu {
                display: none;
            }
        }
        
        /* Ajustes para mobile */
        @media (max-width: 767px) {
            .main-content {
                padding: 15px 10px;
            }
            
            .stats-card {
                margin-bottom: 15px;
            }
            
            .content-container {
                margin-bottom: 20px;
            }
            
            .content-header,
            .content-body {
                padding: 15px;
            }
            
            .action-btn {
                width: 100%;
                margin-right: 0;
                margin-bottom: 8px;
                justify-content: center;
            }
            
            .table > :not(caption) > * > * {
                padding: 10px 12px;
            }
        }
        
        /* Alerta personalizado */
        .custom-alert {
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 24px;
            border-left: 4px solid;
        }
        
        .alert-success {
            background-color: rgba(16, 185, 129, 0.1);
            border-color: var(--success-color);
            color: #065f46;
        }
        
        .alert-danger {
            background-color: rgba(239, 68, 68, 0.1);
            border-color: var(--danger-color);
            color: #b91c1c;
        }
        
        .alert-warning {
            background-color: rgba(245, 158, 11, 0.1);
            border-color: var(--warning-color);
            color: #92400e;
        }
        
        .alert-info {
            background-color: rgba(37, 99, 235, 0.1);
            border-color: var(--primary-color);
            color: #1e40af;
        }
        
        /* Loading overlay */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(255, 255, 255, 0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.3s ease, visibility 0.3s ease;
        }
        
        .loading-overlay.show {
            opacity: 1;
            visibility: visible;
        }
        
        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid rgba(37, 99, 235, 0.1);
            border-radius: 50%;
            border-top-color: var(--primary-color);
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* Sem dados placeholder */
        .sem-dados {
            text-align: center;
            padding: 40px 20px;
        }
        
        .sem-dados i {
            font-size: 3rem;
            color: var(--text-muted);
            margin-bottom: 15px;
        }
        
        .sem-dados h4 {
            font-weight: 600;
            margin-bottom: 10px;
        }
        
        .sem-dados p {
            color: var(--text-muted);
            max-width: 500px;
            margin: 0 auto;
        }
        
        /* Taxa de serviço */
        .taxa-info-card {
            background-color: rgba(37, 99, 235, 0.05);
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
            border-left: 4px solid var(--primary-color);
        }
        
        .taxa-info-card h5 {
            color: var(--primary-color);
            font-weight: 600;
            margin-bottom: 10px;
        }
        
        .total-valores {
            background-color: rgba(16, 185, 129, 0.05);
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
            text-align: right;
            border-right: 4px solid var(--success-color);
        }
        
        .total-valores h3 {
            color: var(--success-color);
            font-weight: 700;
            margin-bottom: 5px;
        }
        
        .total-valores p {
            color: var(--text-muted);
            margin-bottom: 0;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <!-- Overlay de carregamento -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="spinner"></div>
    </div>

    <!-- Cabeçalho (Navbar) -->
    <nav class="navbar navbar-expand-lg fixed-top navbar-light">
        <div class="container">
            <a class="navbar-brand" href="#">FixFácil</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" 
                    aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="home.php">Painel</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="solicitacoes.php">Solicitações</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="propostas_enviadas.php">Propostas</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="reparos_em_andamento.php">Reparos</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="meumarktplace.php">Marketplace</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="solicitar_pecas.php">Peças</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="carteira.php">Carteira</a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-user-circle"></i>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="perfil.php">Meu Perfil</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../logout.php">Sair</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Conteúdo Principal -->
    <div class="container main-content">
        <!-- Cabeçalho da página -->
        <div class="header-section">
            <h1>Minha Carteira</h1>
            <p>Acompanhe e gerencie seus pagamentos e recebimentos.</p>
        </div>

        <!-- Exibir Mensagens -->
        <?php if (!empty($mensagem)): ?>
            <div class="custom-alert alert-<?php echo $tipo_alerta; ?>" role="alert">
                <?php echo htmlspecialchars($mensagem); ?>
            </div>
        <?php endif; ?>

        <!-- Cards de Resumo -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stats-card">
                    <i class="fas fa-hourglass-half card-icon pendentes-total"></i>
                    <div class="card-title pendentes-total">Pagamentos Pendentes</div>
                    <div class="card-value pendentes-total"><?php echo $count_pendentes; ?></div>
                    <div class="card-subtitle">R$ <?php echo number_format($total_pendentes, 2, ',', '.'); ?></div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stats-card">
                    <i class="fas fa-wallet card-icon recebidos-total"></i>
                    <div class="card-title recebidos-total">Valores a Receber</div>
                    <div class="card-value recebidos-total"><?php echo $count_recebidos; ?></div>
                    <div class="card-subtitle">R$ <?php echo number_format($total_receber, 2, ',', '.'); ?></div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stats-card">
                    <i class="fas fa-check-circle card-icon concluidos-total"></i>
                    <div class="card-title concluidos-total">Pagamentos Concluídos</div>
                    <div class="card-value concluidos-total"><?php echo $count_concluidos; ?></div>
                    <div class="card-subtitle">R$ <?php echo number_format($total_concluido, 2, ',', '.'); ?></div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stats-card">
                    <i class="fas fa-percentage card-icon taxa-info"></i>
                    <div class="card-title taxa-info">Taxa FixFácil</div>
                    <div class="card-value taxa-info">0%</div>
                    <div class="card-subtitle">Aplicada em todos os serviços</div>
                </div>
            </div>
        </div>

        <!-- Tabs de Navegação -->
        <ul class="nav nav-tabs" id="myTab" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="pendentes-tab" data-bs-toggle="tab" data-bs-target="#pendentes" type="button" role="tab" aria-controls="pendentes" aria-selected="true">
                    <i class="fas fa-hourglass-half me-2"></i>Pagamentos Pendentes
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="recebidos-tab" data-bs-toggle="tab" data-bs-target="#recebidos" type="button" role="tab" aria-controls="recebidos" aria-selected="false">
                    <i class="fas fa-wallet me-2"></i>Valores a Receber
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="concluidos-tab" data-bs-toggle="tab" data-bs-target="#concluidos" type="button" role="tab" aria-controls="concluidos" aria-selected="false">
                    <i class="fas fa-check-circle me-2"></i>Pagamentos Concluídos
                </button>
            </li>
        </ul>

        <!-- Conteúdo das Tabs -->
        <div class="tab-content" id="myTabContent">
            <!-- Tab de Pagamentos Pendentes -->
            <div class="tab-pane fade show active" id="pendentes" role="tabpanel" aria-labelledby="pendentes-tab">
                <div class="content-container">
                    <div class="content-header">
                        <h2><i class="fas fa-hourglass-half me-2"></i>Pagamentos Pendentes</h2>
                        <div class="header-info">
                            <span class="badge bg-warning text-dark rounded-pill"><?php echo $count_pendentes; ?> pendentes</span>
                        </div>
                    </div>
                    <div class="content-body">
                        <?php if (!empty($pendentes_array)): ?>
                            <div class="tabela-container">
                                <table class="table table-striped" id="tabela-pendentes">
                                    <thead>
                                        <tr>
                                            <th>ID Proposta</th>
                                            <th>Dispositivo</th>
                                            <th>Marca/Modelo</th>
                                            <th>Valor Total</th>
                                            <th>Data</th>
                                            <th>Status</th>
                                            <th>Ações</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($pendentes_array as $pendente): ?>
                                            <tr id="pendente-<?php echo $pendente['proposta_id']; ?>">
                                                <td>#<?php echo $pendente['proposta_id']; ?></td>
                                                <td>
                                                    <?php echo htmlspecialchars($pendente['dispositivo']); ?>
                                                    <div class="small text-muted"><?php echo mb_substr(htmlspecialchars($pendente['descricao_problema']), 0, 40); ?><?php echo (mb_strlen($pendente['descricao_problema']) > 40) ? '...' : ''; ?></div>
                                                </td>
                                                <td><?php echo htmlspecialchars($pendente['marca'] . ' ' . $pendente['modelo']); ?></td>
                                                <td><strong class="text-success">R$ <?php echo number_format($pendente['preco'], 2, ',', '.'); ?></strong></td>
                                                <td><?php echo date('d/m/Y', strtotime($pendente['data_solicitacao'])); ?></td>
                                                <td><span class="status-badge status-badge-pendente">Pendente</span></td>
                                                <td>
                                                    <form action="carteira.php" method="POST" onsubmit="return showLoading()">
                                                        <input type="hidden" name="proposta_id" value="<?php echo $pendente['proposta_id']; ?>">
                                                        <button type="submit" name="marcar_recebido" class="btn btn-success btn-sm action-btn">
                                                            <i class="fas fa-check-circle"></i> Confirmar Pagamento
                                                        </button>
                                                    </form>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <div class="sem-dados">
                                <i class="fas fa-hourglass-half"></i>
                                <h4>Nenhum pagamento pendente</h4>
                                <p>Você não possui pagamentos pendentes no momento. Quando um cliente aceitar sua proposta, o pagamento aparecerá aqui.</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Tab de Valores a Receber -->
            <div class="tab-pane fade" id="recebidos" role="tabpanel" aria-labelledby="recebidos-tab">
                <div class="content-container">
                    <div class="content-header">
                        <h2><i class="fas fa-wallet me-2"></i>Valores a Receber</h2>
                        <div class="header-info">
                            <span class="badge bg-success text-white rounded-pill"><?php echo $count_recebidos; ?> em andamento</span>
                        </div>
                    </div>
                    <div class="content-body">
                        <?php if (!empty($recebidos_array)): ?>
                            <div class="tabela-container">
                                <table class="table table-striped" id="tabela-recebidos">
                                    <thead>
                                        <tr>
                                            <th>ID Proposta</th>
                                            <th>Dispositivo</th>
                                            <th>Marca/Modelo</th>
                                            <th>Valor Total</th>
                                            <th>Valor a Receber</th>
                                            <th>Data</th>
                                            <th>Status</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($recebidos_array as $recebido): ?>
                                            <tr>
                                                <td>#<?php echo $recebido['proposta_id']; ?></td>
                                                <td>
                                                    <?php echo htmlspecialchars($recebido['dispositivo']); ?>
                                                    <div class="small text-muted"><?php echo mb_substr(htmlspecialchars($recebido['descricao_problema']), 0, 40); ?><?php echo (mb_strlen($recebido['descricao_problema']) > 40) ? '...' : ''; ?></div>
                                                </td>
                                                <td><?php echo htmlspecialchars($recebido['marca'] . ' ' . $recebido['modelo']); ?></td>
                                                <td>R$ <?php echo number_format($recebido['preco'], 2, ',', '.'); ?></td>
                                                <td><strong class="text-success">R$ <?php echo number_format($recebido['preco'] * 0.85, 2, ',', '.'); ?></strong></td>
                                                <td><?php echo date('d/m/Y', strtotime($recebido['data_solicitacao'])); ?></td>
                                                <td>
                                                    <?php if (isset($recebido['pago']) && $recebido['pago'] == 1): ?>
                                                        <span class="status-badge status-badge-recebido">Pago</span>
                                                    <?php else: ?>
                                                        <span class="status-badge status-badge-pendente">Pendente</span>
                                                    <?php endif; ?>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                            
                            <!-- Informações sobre Taxa e Total -->
                            <div class="row mt-4">
                                <div class="col-md-6">
                                    <div class="taxa-info-card">
                                        <h5><i class="fas fa-info-circle me-2"></i>Informações sobre Taxa</h5>
                                        <p>A plataforma FixFácil cobra uma taxa de 15% sobre o valor dos serviços. Este valor é deduzido automaticamente do montante final a ser recebido.</p>
                                        <p class="mb-0">Os valores mostrados já consideram a dedução da taxa.</p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="total-valores">
                                        <h3>Total a Receber: R$ <?php echo number_format($total_receber, 2, ',', '.'); ?></h3>
                                        <p>Valor após dedução da taxa de 0% da plataforma FixFácil </p>
                                    </div>
                                </div>
                            </div>
                        <?php else: ?>
                            <div class="sem-dados">
                                <i class="fas fa-wallet"></i>
                                <h4>Nenhum valor a receber</h4>
                                <p>Você não possui valores a receber no momento. Quando seus serviços estiverem em andamento, os valores a receber aparecerão aqui.</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Tab de Pagamentos Concluídos -->
            <div class="tab-pane fade" id="concluidos" role="tabpanel" aria-labelledby="concluidos-tab">
                <div class="content-container">
                    <div class="content-header">
                        <h2><i class="fas fa-check-circle me-2"></i>Pagamentos Concluídos</h2>
                        <div class="header-info">
                            <span class="badge bg-primary text-white rounded-pill"><?php echo $count_concluidos; ?> concluídos</span>
                        </div>
                    </div>
                    <div class="content-body">
                        <?php if (!empty($concluidos_array)): ?>
                            <div class="tabela-container">
                                <table class="table table-striped" id="tabela-concluidos">
                                    <thead>
                                        <tr>
                                            <th>ID Proposta</th>
                                            <th>Dispositivo</th>
                                            <th>Marca/Modelo</th>
                                            <th>Valor Total</th>
                                            <th>Valor Recebido</th>
                                            <th>Data</th>
                                            <th>Status</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($concluidos_array as $concluido): ?>
                                            <tr>
                                                <td>#<?php echo $concluido['proposta_id']; ?></td>
                                                <td>
                                                    <?php echo htmlspecialchars($concluido['dispositivo']); ?>
                                                    <div class="small text-muted"><?php echo mb_substr(htmlspecialchars($concluido['descricao_problema']), 0, 40); ?><?php echo (mb_strlen($concluido['descricao_problema']) > 40) ? '...' : ''; ?></div>
                                                </td>
                                                <td><?php echo htmlspecialchars($concluido['marca'] . ' ' . $concluido['modelo']); ?></td>
                                                <td>R$ <?php echo number_format($concluido['preco'], 2, ',', '.'); ?></td>
                                                <td><strong class="text-success">R$ <?php echo number_format($concluido['preco'] * 0.85, 2, ',', '.'); ?></strong></td>
                                                <td><?php echo date('d/m/Y', strtotime($concluido['data_solicitacao'])); ?></td>
                                                <td><span class="status-badge status-badge-concluido">Concluído</span></td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                            
                            <!-- Total de Pagamentos Concluídos -->
                            <div class="total-valores mt-4 text-end">
                                <h3>Total Recebido: R$ <?php echo number_format($total_concluido, 2, ',', '.'); ?></h3>
                                <p>Valor total de pagamentos concluídos após dedução de taxas</p>
                            </div>
                        <?php else: ?>
                            <div class="sem-dados">
                                <i class="fas fa-check-circle"></i>
                                <h4>Nenhum pagamento concluído</h4>
                                <p>Você não possui pagamentos concluídos no momento. Quando seus serviços forem finalizados e os pagamentos processados, eles aparecerão aqui.</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Menu Mobile (fixo na parte inferior) -->
    <div class="mobile-menu d-lg-none">
        <a href="home.php" class="menu-item">
            <i class="fas fa-home"></i>
            <span>Painel</span>
        </a>
        <a href="solicitacoes.php" class="menu-item">
            <i class="fas fa-inbox"></i>
            <span>Solicitações</span>
        </a>
        <a href="propostas_enviadas.php" class="menu-item">
            <i class="fas fa-paper-plane"></i>
            <span>Propostas</span>
        </a>
        <a href="reparos_em_andamento.php" class="menu-item">
            <i class="fas fa-tools"></i>
            <span>Reparos</span>
        </a>
        <a href="carteira.php" class="menu-item active">
            <i class="fas fa-wallet"></i>
            <span>Carteira</span>
        </a>
    </div>

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="https://cdn.datatables.net/1.13.4/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.4/js/dataTables.bootstrap5.min.js"></script>
    <script>
        $(document).ready(function() {
            // Inicializar DataTables
            $.fn.dataTable.ext.errMode = 'none'; // Desativar mensagens de erro
            
            $('#tabela-pendentes').DataTable({
                language: {
                    url: '//cdn.datatables.net/plug-ins/1.13.4/i18n/pt-BR.json',
                },
                responsive: true,
                ordering: true,
                pageLength: 10,
                lengthMenu: [5, 10, 25, 50]
            });
            
            $('#tabela-recebidos').DataTable({
                language: {
                    url: '//cdn.datatables.net/plug-ins/1.13.4/i18n/pt-BR.json',
                },
                responsive: true,
                ordering: true,
                pageLength: 10,
                lengthMenu: [5, 10, 25, 50]
            });
            
            $('#tabela-concluidos').DataTable({
                language: {
                    url: '//cdn.datatables.net/plug-ins/1.13.4/i18n/pt-BR.json',
                },
                responsive: true,
                ordering: true,
                pageLength: 10,
                lengthMenu: [5, 10, 25, 50]
            });
            
            // Função para mostrar overlay de carregamento
            window.showLoading = function() {
                $('#loadingOverlay').addClass('show');
                return true;
            };

            // Função para esconder overlay de carregamento
            function hideLoading() {
                $('#loadingOverlay').removeClass('show');
            }
            
            // Exibir mensagens de sucesso com SweetAlert2
            <?php if ($tipo_alerta === 'success' && !empty($mensagem)): ?>
            Swal.fire({
                icon: 'success',
                title: 'Sucesso!',
                text: '<?php echo addslashes($mensagem); ?>',
                timer: 3000,
                timerProgressBar: true
            });
            <?php endif; ?>
            
            // Inicialização concluída
            console.log('Scripts inicializados com sucesso!');
        });
    </script>
</body>
</html>