<?php
// admin/reports.php

session_start();

// Verificar se o administrador está logado
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: ../admin_login.php');
    exit();
}

$admin_nome = $_SESSION['admin_nome'];

// Conexão com o banco de dados
$servername = "localhost";
$username_db = "u680766645_fixfacilnew";
$password_db = "T3cn0l0g1a@";
$dbname = "u680766645_fixfacilnew"; 

$conn = new mysqli($servername, $username_db, $password_db, $dbname);
if ($conn->connect_error) {
    die("Conexão falhou: " . $conn->connect_error);
}

$conn->set_charset("utf8mb4");

// Processar filtros (se houver)
$where = "";
$params = [];
$types = "";

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $data_inicio = $_POST['data_inicio'];
    $data_fim = $_POST['data_fim'];

    if (!empty($data_inicio)) {
        $where .= " AND data_solicitacao >= ?";
        $params[] = $data_inicio;
        $types .= "s";
    }
    if (!empty($data_fim)) {
        $where .= " AND data_solicitacao <= ?";
        $params[] = $data_fim;
        $types .= "s";
    }
}

// Obter dados para o relatório
$sql = "SELECT sr.*, u.nome AS nome_usuario, u.email
        FROM solicitacoes_reparo sr
        JOIN usuarios u ON sr.usuario_id = u.id
        WHERE 1=1 " . $where . "
        ORDER BY data_solicitacao DESC";
$stmt = $conn->prepare($sql);

if (!$stmt) {
    die("Erro na preparação da consulta: " . $conn->error);
}

if (!empty($params)) {
    $bind_params = array_merge([$types], $params);
    $tmp = array();
    foreach ($bind_params as $key => $value) {
        $tmp[$key] = &$bind_params[$key];
    }
    call_user_func_array(array($stmt, 'bind_param'), $tmp);
}

if (!$stmt->execute()) {
    die("Erro na execução da consulta: " . $stmt->error);
}

$result = $stmt->get_result();
if ($result === false) {
    die("Erro ao obter o resultado: " . $stmt->error);
}

$solicitacoes = $result->fetch_all(MYSQLI_ASSOC);
$stmt->close();

// Fechar conexão
$conn->close();
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <title>Relatórios - FixFácil</title>
    <!-- Meta Tags para Responsividade -->
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <!-- Incluindo Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome para ícones -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Estilos personalizados -->
    <style>
        body {
            font-family: 'Poppins', sans-serif;
            background-color: #f8f9fa;
            color: #495057;
        }
        /* Navbar */
        .navbar {
            background-color: #343a40;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .navbar-brand {
            font-weight: 600;
            color: #fff;
        }
        .navbar-nav .nav-link {
            color: #fff !important;
            font-weight: 500;
            margin-right: 15px;
        }
        .navbar-nav .nav-link.active {
            color: #17a2b8 !important;
        }
        .navbar-nav .nav-link:hover {
            color: #f8f9fa !important;
        }
        .main-content {
            padding: 100px 20px 20px;
        }
        .page-title {
            margin-bottom: 40px;
        }
        .page-title h2 {
            font-weight: 600;
            color: #343a40;
        }
        .footer {
            background-color: #343a40;
            padding: 20px 0;
            position: fixed;
            width: 100%;
            bottom: 0;
            box-shadow: 0 -2px 4px rgba(0,0,0,0.1);
        }
        .footer span {
            color: #6c757d;
        }
        .card-body {
            background-color: #fff;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand" href="dashboard.php">FixFácil - Painel Administrativo</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" 
                    aria-controls="navbarNav" aria-expanded="false" aria-label="Alternar navegação">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse justify-content-end" id="navbarNav">
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="admin_agendamentos.php"><i class="fas fa-calendar-alt"></i> Agendamentos</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="manage_users.php"><i class="fas fa-users"></i> Usuários</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="reports.php"><i class="fas fa-chart-bar"></i> Relatórios</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="logout.php"><i class="fas fa-sign-out-alt"></i> Sair</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Conteúdo Principal -->
    <div class="container main-content">
        <div class="page-title text-center">
            <h2>Relatórios</h2>
            <p class="text-muted">Visualize e exporte dados do sistema.</p>
        </div>

        <!-- Filtros -->
        <div class="card mb-4">
            <div class="card-body">
                <form action="reports.php" method="POST" class="row g-3">
                    <div class="col-md-4">
                        <label for="data_inicio" class="form-label">Data Início:</label>
                        <input type="date" name="data_inicio" id="data_inicio" class="form-control" value="<?php echo isset($_POST['data_inicio']) ? htmlspecialchars($_POST['data_inicio']) : ''; ?>">
                    </div>
                    <div class="col-md-4">
                        <label for="data_fim" class="form-label">Data Fim:</label>
                        <input type="date" name="data_fim" id="data_fim" class="form-control" value="<?php echo isset($_POST['data_fim']) ? htmlspecialchars($_POST['data_fim']) : ''; ?>">
                    </div>
                    <div class="col-md-4 align-self-end">
                        <button type="submit" class="btn btn-primary"><i class="fas fa-filter"></i> Aplicar Filtros</button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Tabela de Resultados -->
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Usuário</th>
                        <th>E-mail</th>
                        <th>Dispositivo</th>
                        <th>Marca</th>
                        <th>Modelo</th>
                        <th>Problema</th>
                        <th>Data</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (!empty($solicitacoes)): ?>
                        <?php foreach ($solicitacoes as $solicitacao): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($solicitacao['id']); ?></td>
                                <td><?php echo htmlspecialchars($solicitacao['nome_usuario']); ?></td>
                                <td><?php echo htmlspecialchars($solicitacao['email']); ?></td>
                                <td><?php echo htmlspecialchars($solicitacao['dispositivo']); ?></td>
                                <td><?php echo htmlspecialchars($solicitacao['marca']); ?></td>
                                <td><?php echo htmlspecialchars($solicitacao['modelo']); ?></td>
                                <td><?php echo htmlspecialchars($solicitacao['descricao_problema']); ?></td>
                                <td><?php echo date('d/m/Y H:i', strtotime($solicitacao['data_solicitacao'])); ?></td>
                            </tr>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <tr>
                            <td colspan="8" class="text-center">Nenhum registro encontrado.</td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>

    <!-- Rodapé -->
    <footer class="footer">
        <div class="container text-center">
            <span>&copy; <?php echo date("Y"); ?> FixFácil. Todos os direitos reservados.</span>
        </div>
    </footer>

    <!-- Incluindo Bootstrap JS e dependências -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
