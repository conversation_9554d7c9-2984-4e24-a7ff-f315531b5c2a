<?php
session_start();

// Verificar se o usuário está logado e é uma assistência
if (!isset($_SESSION['usuario_id']) || $_SESSION['tipo_usuario'] != 'assistencia') {
    header('Location: ../login.php');
    exit();
}

$usuario_id = $_SESSION['usuario_id'];
$nome_usuario = $_SESSION['nome'];

// Conexão com o banco de dados
$servername = "localhost";
$username_db = "u682219090_fixfacilnew";
$password_db = "T3cn0l0g1a@";
$dbname = "u682219090_fixfacilnew";

$conn = new mysqli($servername, $username_db, $password_db, $dbname);

if ($conn->connect_error) {
    die("Falha na conexão: " . $conn->connect_error);
}

$conn->set_charset("utf8");

// Inicializar variáveis para mensagens
$mensagem = "";
$tipo_alerta = "";

// Obter o assistencia_id correspondente ao usuario_id
$sql_assistencia = "SELECT id FROM assistencias WHERE usuario_id = ?";
$stmt_assistencia = $conn->prepare($sql_assistencia);
$stmt_assistencia->bind_param("i", $usuario_id);
$stmt_assistencia->execute();
$result_assistencia = $stmt_assistencia->get_result();
if ($row_assistencia = $result_assistencia->fetch_assoc()) {
    $assistencia_id = $row_assistencia['id'];
} else {
    // Assistência não encontrada
    die("Assistência não encontrada.");
}
$stmt_assistencia->close();

// Obter a lista de solicitações feitas pela assistência
$sql_solicitacoes = "SELECT s.id AS solicitacao_id, s.quantidade, s.status, s.data_solicitacao,
                     pr.nome_produto, pr.descricao, pr.preco,
                     f.nome_empresa AS nome_fornecedor
                     FROM solicitacoes s
                     INNER JOIN produtos pr ON s.produto_id = pr.id
                     INNER JOIN fornecedores f ON pr.fornecedor_id = f.id
                     WHERE s.assistencia_id = ?
                     ORDER BY s.data_solicitacao DESC";
$stmt_solicitacoes = $conn->prepare($sql_solicitacoes);
$stmt_solicitacoes->bind_param("i", $assistencia_id);
$stmt_solicitacoes->execute();
$result_solicitacoes = $stmt_solicitacoes->get_result();
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <title>Meus Pedidos - FixFácil</title>
    <!-- Meta Tags para Responsividade -->
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <!-- Incluindo Bootstrap CSS -->
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <!-- Font Awesome para ícones -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.1/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css?family=Poppins:300,400,500,600,700&display=swap" rel="stylesheet">
    <!-- Estilos personalizados -->
    <style>
        body {
            font-family: 'Poppins', sans-serif;
            background-color: #f7f9fc;
            margin-bottom: 60px; /* Espaço para a navbar inferior */
            color: #495057;
        }
        /* Navbar */
        .navbar {
            background-color: #fff;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .navbar-brand img {
            width: 150px;
        }
        .navbar-nav .nav-link {
            color: #495057 !important;
            font-weight: 500;
            margin-right: 15px;
        }
        .navbar-nav .nav-link.active {
            color: #007BFF !important;
        }
        .navbar-nav .nav-link:hover {
            color: #0056b3 !important;
        }
        /* Conteúdo Principal */
        .main-content {
            padding: 80px 20px 20px;
        }
        .welcome {
            margin-bottom: 40px;
        }
        .welcome h2 {
            font-weight: 600;
            color: #343a40;
        }
        .card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.05);
            background-color: #fff;
            padding: 30px;
            margin-bottom: 20px;
        }
        /* Footer */
        footer.footer {
            background-color: #fff;
            padding: 20px 0;
            position: fixed;
            width: 100%;
            bottom: 0;
            box-shadow: 0 -2px 4px rgba(0,0,0,0.1);
        }
        footer.footer span {
            color: #6c757d;
        }
        /* Navbar Inferior (Mobile) */
        .footer-nav {
            position: fixed;
            bottom: 0;
            width: 100%;
            background-color: #fff;
            box-shadow: 0 -2px 5px rgba(0,0,0,0.1);
            z-index: 1000;
        }
        .footer-nav .nav-link {
            color: #6c757d;
            text-align: center;
            padding: 10px 0;
            font-size: 12px;
        }
        .footer-nav .nav-link.active {
            color: #007BFF;
        }
        .footer-nav .nav-link i {
            font-size: 20px;
        }
        @media (min-width: 768px) {
            .footer-nav {
                display: none;
            }
        }
    </style>
</head>
<body>
    <!-- Cabeçalho -->
    <nav class="navbar navbar-expand-lg navbar-light fixed-top">
        <!-- Navbar com o item "Meus Pedidos" ativo -->
       
        <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarNavAssistencia" 
                aria-controls="navbarNavAssistencia" aria-expanded="false" aria-label="Alternar navegação">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse justify-content-end" id="navbarNavAssistencia">
            <ul class="navbar-nav">
                <li class="nav-item">
                    <a class="nav-link" href="home.php"><i class="fas fa-home"></i> Painel</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="solicitar_pecas.php"><i class="fas fa-cogs"></i> Solicitar Peças</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link active" href="meus_pedidos.php"><i class="fas fa-list"></i> Meus Pedidos</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="perfil.php"><i class="fas fa-user-circle"></i> Perfil</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="../logout.php"><i class="fas fa-sign-out-alt"></i> Sair</a>
                </li>
            </ul>
        </div>
    </nav>

    <!-- Conteúdo Principal -->
    <div class="container main-content">
        <div class="welcome text-center">
            <h2>Meus Pedidos</h2>
            <p class="text-muted">Acompanhe o status das suas solicitações de peças.</p>
        </div>

        <!-- Exibir Mensagens -->
        <?php if (!empty($mensagem)): ?>
            <div class="alert alert-<?php echo $tipo_alerta; ?>" role="alert">
                <?php echo htmlspecialchars($mensagem); ?>
            </div>
        <?php endif; ?>

        <!-- Lista de Solicitações -->
        <div class="card">
            <div class="table-responsive">
                <table class="table table-striped" id="tabela-solicitacoes">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Produto</th>
                            <th>Descrição</th>
                            <th>Quantidade</th>
                            <th>Preço Unitário (R$)</th>
                            <th>Total (R$)</th>
                            <th>Fornecedor</th>
                            <th>Status</th>
                            <th>Data da Solicitação</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if ($result_solicitacoes->num_rows > 0): ?>
                            <?php while ($solicitacao = $result_solicitacoes->fetch_assoc()): ?>
                                <tr>
                                    <td><?php echo $solicitacao['solicitacao_id']; ?></td>
                                    <td><?php echo htmlspecialchars($solicitacao['nome_produto']); ?></td>
                                    <td><?php echo htmlspecialchars($solicitacao['descricao']); ?></td>
                                    <td><?php echo $solicitacao['quantidade']; ?></td>
                                    <td><?php echo number_format($solicitacao['preco'], 2, ',', '.'); ?></td>
                                    <td>
                                        <?php
                                        $total = $solicitacao['quantidade'] * $solicitacao['preco'];
                                        echo number_format($total, 2, ',', '.');
                                        ?>
                                    </td>
                                    <td><?php echo htmlspecialchars($solicitacao['nome_fornecedor']); ?></td>
                                    <td>
                                        <?php
                                        $status = $solicitacao['status'];
                                        if ($status == 'pendente') {
                                            echo '<span class="badge badge-warning">Pendente</span>';
                                        } elseif ($status == 'aprovado') {
                                            echo '<span class="badge badge-info">Aprovado</span>';
                                        } elseif ($status == 'recusado') {
                                            echo '<span class="badge badge-danger">Recusado</span>';
                                        } elseif ($status == 'enviado') {
                                            echo '<span class="badge badge-primary">Enviado</span>';
                                        } elseif ($status == 'entregue') {
                                            echo '<span class="badge badge-success">Entregue</span>';
                                        }
                                        ?>
                                    </td>
                                    <td><?php echo date('d/m/Y H:i', strtotime($solicitacao['data_solicitacao'])); ?></td>
                                </tr>
                            <?php endwhile; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="9" class="text-center">Nenhuma solicitação encontrada.</td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Barra de Navegação Inferior (Mobile) -->
    <nav class="footer-nav d-md-none">
        <ul class="nav justify-content-around">
            <li class="nav-item">
                <a class="nav-link" href="home.php">
                    <i class="fas fa-home"></i><br>Painel
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="solicitar_pecas.php">
                    <i class="fas fa-cogs"></i><br>Solicitar Peças
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link active" href="meus_pedidos.php">
                    <i class="fas fa-list"></i><br>Meus Pedidos
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="perfil.php">
                    <i class="fas fa-user-circle"></i><br>Perfil
                </a>
            </li>
        </ul>
    </nav>

    <!-- Rodapé -->
    <footer class="footer d-none d-md-block">
        <div class="container text-center">
            <span>&copy; <?php echo date("Y"); ?> FixFácil. Todos os direitos reservados.</span>
        </div>
    </footer>

    <!-- Incluindo Bootstrap JS e dependências -->
    <script src="https://code.jquery.com/jquery-3.5.1.min.js" ></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.5.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
