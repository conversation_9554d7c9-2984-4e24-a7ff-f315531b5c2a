<?php
/**
 * Página de Solicitações
 * FixFácil Assistências - Sistema Novo
 */

// Configurar relatório de erros para debug
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Iniciar sessão se não estiver iniciada
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

try {
    require_once 'config/auth.php';
    require_once 'config/database.php';
    require_once 'includes/layout.php';

    // Verificar autenticação
    $auth = getAuth();
    $auth->checkAssistenciaAuth();

    // Obter dados do usuário
    $usuario = $auth->getUsuarioLogado();
    if (!$usuario) {
        throw new Exception("Usuário não encontrado");
    }

    $plano = $auth->getPlanoInfo($usuario['id']);
    if (!$plano) {
        throw new Exception("Plano não encontrado");
    }

    $db = getDatabase();
    if (!$db) {
        throw new Exception("Erro na conexão com banco de dados");
    }

} catch (Exception $e) {
    error_log("Erro na página de solicitações: " . $e->getMessage());
    die("Erro interno do servidor. Verifique os logs para mais detalhes.");
}

// Filtros
$status_filter = $_GET['status'] ?? 'enviado';
$search = $_GET['search'] ?? '';

// Obter solicitações
$solicitacoes = [];
try {
    $assistencia_id = $usuario['assistencia_id'] ?? null;
    if (!$assistencia_id) {
        throw new Exception("ID da assistência não encontrado");
    }

    $where_conditions = ["sr.status = ?", "sr.visivel = 1"];
    $params = [$status_filter];

    if (!empty($search)) {
        $where_conditions[] = "(sr.descricao_problema LIKE ? OR sr.dispositivo LIKE ? OR sr.marca LIKE ? OR sr.modelo LIKE ? OR u.nome LIKE ?)";
        $search_param = "%$search%";
        $params = array_merge($params, [$search_param, $search_param, $search_param, $search_param, $search_param]);
    }

    $where_clause = implode(' AND ', $where_conditions);

    $sql = "
        SELECT
            sr.id,
            sr.usuario_id,
            sr.celular_id,
            sr.tipo_solicitacao,
            sr.descricao_problema,
            sr.descricao_detalhada,
            sr.video,
            sr.metodo_entrega,
            sr.endereco,
            sr.dispositivo,
            sr.marca,
            sr.modelo,
            sr.memoria,
            sr.status,
            sr.data_solicitacao,
            sr.verificacoes,
            sr.origem,
            u.nome as cliente_nome,
            u.telefone as cliente_telefone,
            u.endereco as cliente_endereco,
            u.cep as cliente_cep,
            u.cidade as cliente_cidade,
            u.estado as cliente_estado,
            COALESCE(COUNT(pa.id), 0) as total_propostas,
            COALESCE(COUNT(CASE WHEN pa.assistencia_id = ? THEN 1 END), 0) as minhas_propostas
        FROM solicitacoes_reparo sr
        JOIN usuarios u ON sr.usuario_id = u.id
        LEFT JOIN propostas_assistencia pa ON sr.id = pa.solicitacao_id
        WHERE $where_clause
        GROUP BY sr.id, sr.usuario_id, sr.celular_id, sr.tipo_solicitacao, sr.descricao_problema,
                 sr.descricao_detalhada, sr.video, sr.metodo_entrega, sr.endereco, sr.dispositivo,
                 sr.marca, sr.modelo, sr.memoria, sr.status, sr.data_solicitacao, sr.verificacoes,
                 sr.origem, u.nome, u.telefone, u.endereco, u.cep, u.cidade, u.estado
        ORDER BY sr.data_solicitacao DESC
        LIMIT 50
    ";

    $all_params = array_merge([$assistencia_id], $params);
    $result = $db->query($sql, $all_params);

    if ($result) {
        while ($row = $result->fetch_assoc()) {
            $solicitacoes[] = $row;
        }
    }

} catch (Exception $e) {
    error_log("Erro ao obter solicitações: " . $e->getMessage());
    $solicitacoes = [];
}

// Inicializar layout
$layout = new Layout();
?>

<?php $layout->renderHead("Solicitações - FixFácil Assistências"); ?>

<div class="main-wrapper">
    <?php $layout->renderSidebar('solicitacoes'); ?>
    
    <main class="main-content">
        <!-- Header -->
        <div class="content-header">
            <h1 class="page-title">
                <i class="fas fa-inbox me-3"></i>
                Solicitações de Reparo
            </h1>
            <p class="page-subtitle">
                Visualize e responda às solicitações de reparo dos clientes
            </p>
        </div>
        
        <!-- Filtros -->
        <div class="card mb-4">
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-3">
                        <label class="form-label">Status</label>
                        <select name="status" class="form-select">
                            <option value="enviado" <?php echo $status_filter === 'enviado' ? 'selected' : ''; ?>>
                                Pendentes
                            </option>
                            <option value="aceita" <?php echo $status_filter === 'aceita' ? 'selected' : ''; ?>>
                                Aceitas
                            </option>
                            <option value="rejeitada" <?php echo $status_filter === 'rejeitada' ? 'selected' : ''; ?>>
                                Rejeitadas
                            </option>
                            <option value="concluido" <?php echo $status_filter === 'concluido' ? 'selected' : ''; ?>>
                                Concluídas
                            </option>
                        </select>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">Buscar</label>
                        <input type="text" name="search" class="form-control" 
                               placeholder="Buscar por dispositivo, marca, modelo ou cliente..."
                               value="<?php echo htmlspecialchars($search); ?>">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search me-2"></i>
                                Filtrar
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- Lista de Solicitações -->
        <?php if (empty($solicitacoes)): ?>
        <div class="card">
            <div class="card-body text-center py-5">
                <i class="fas fa-inbox fs-1 text-muted mb-3"></i>
                <h4 class="text-muted">Nenhuma solicitação encontrada</h4>
                <p class="text-muted">
                    <?php if ($status_filter === 'enviado'): ?>
                        Não há solicitações pendentes no momento.
                    <?php else: ?>
                        Não há solicitações com o status "<?php echo $status_filter; ?>".
                    <?php endif; ?>
                </p>
            </div>
        </div>
        <?php else: ?>
        <div class="row g-4">
            <?php foreach ($solicitacoes as $solicitacao): ?>
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-lg-8">
                                <div class="d-flex align-items-start mb-3">
                                    <div class="flex-shrink-0">
                                        <div class="bg-primary bg-opacity-10 p-3 rounded-circle">
                                            <i class="fas fa-mobile-alt text-primary fs-5"></i>
                                        </div>
                                    </div>
                                    <div class="flex-grow-1 ms-3">
                                        <h5 class="mb-1">
                                            <?php echo htmlspecialchars($solicitacao['marca'] . ' ' . $solicitacao['modelo']); ?>
                                            <span class="badge bg-secondary ms-2"><?php echo htmlspecialchars($solicitacao['memoria']); ?></span>
                                        </h5>
                                        <p class="text-muted mb-2">
                                            <i class="fas fa-user me-1"></i>
                                            <?php echo htmlspecialchars($solicitacao['cliente_nome']); ?>
                                            <i class="fas fa-phone ms-3 me-1"></i>
                                            <?php echo htmlspecialchars($solicitacao['cliente_telefone']); ?>
                                        </p>
                                        <p class="mb-2">
                                            <strong>Problema:</strong> 
                                            <?php echo htmlspecialchars($solicitacao['descricao_problema']); ?>
                                        </p>
                                        <?php if (!empty($solicitacao['descricao_detalhada'])): ?>
                                        <p class="mb-2">
                                            <strong>Detalhes:</strong> 
                                            <?php echo htmlspecialchars($solicitacao['descricao_detalhada']); ?>
                                        </p>
                                        <?php endif; ?>
                                        <div class="d-flex align-items-center text-muted">
                                            <small>
                                                <i class="fas fa-calendar me-1"></i>
                                                <?php echo date('d/m/Y H:i', strtotime($solicitacao['data_solicitacao'])); ?>
                                            </small>
                                            <small class="ms-3">
                                                <i class="fas fa-truck me-1"></i>
                                                <?php echo htmlspecialchars($solicitacao['metodo_entrega']); ?>
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-4">
                                <div class="text-lg-end">
                                    <div class="mb-3">
                                        <?php
                                        $status_class = [
                                            'enviado' => 'warning',
                                            'aceita' => 'success',
                                            'rejeitada' => 'danger',
                                            'concluido' => 'primary'
                                        ];
                                        $status_text = [
                                            'enviado' => 'Pendente',
                                            'aceita' => 'Aceita',
                                            'rejeitada' => 'Rejeitada',
                                            'concluido' => 'Concluída'
                                        ];
                                        ?>
                                        <span class="badge bg-<?php echo $status_class[$solicitacao['status']]; ?> fs-6">
                                            <?php echo $status_text[$solicitacao['status']]; ?>
                                        </span>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <small class="text-muted d-block">Propostas Totais</small>
                                        <strong><?php echo $solicitacao['total_propostas']; ?></strong>
                                        
                                        <?php if ($solicitacao['minhas_propostas'] > 0): ?>
                                        <small class="text-success d-block">
                                            <i class="fas fa-check me-1"></i>
                                            Você já enviou proposta
                                        </small>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <div class="d-grid gap-2">
                                        <a href="detalhes_solicitacao.php?id=<?php echo $solicitacao['id']; ?>" 
                                           class="btn btn-outline-primary btn-sm">
                                            <i class="fas fa-eye me-2"></i>
                                            Ver Detalhes
                                        </a>
                                        
                                        <?php if ($solicitacao['status'] === 'enviado' && $solicitacao['minhas_propostas'] == 0): ?>
                                        <a href="enviar_proposta.php?solicitacao_id=<?php echo $solicitacao['id']; ?>" 
                                           class="btn btn-primary btn-sm">
                                            <i class="fas fa-paper-plane me-2"></i>
                                            Enviar Proposta
                                        </a>
                                        <?php endif; ?>
                                        
                                        <?php if (!empty($solicitacao['video'])): ?>
                                        <button type="button" class="btn btn-outline-info btn-sm" 
                                                onclick="verVideo('<?php echo htmlspecialchars($solicitacao['video']); ?>')">
                                            <i class="fas fa-play me-2"></i>
                                            Ver Vídeo
                                        </button>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
        <?php endif; ?>
    </main>
</div>

<!-- Modal para vídeo -->
<div class="modal fade" id="videoModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Vídeo do Problema</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <video id="videoPlayer" class="w-100" controls>
                    Seu navegador não suporta vídeos.
                </video>
            </div>
        </div>
    </div>
</div>

<?php 
$extraJS = "
<script>
function verVideo(videoUrl) {
    const modal = new bootstrap.Modal(document.getElementById('videoModal'));
    const video = document.getElementById('videoPlayer');
    video.src = videoUrl;
    modal.show();
    
    // Pausar vídeo quando modal fechar
    document.getElementById('videoModal').addEventListener('hidden.bs.modal', function() {
        video.pause();
        video.currentTime = 0;
    });
}
</script>
";

$layout->renderFooter($extraJS); 
?>
