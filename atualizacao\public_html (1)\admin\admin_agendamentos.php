<?php
// admin/admin_agendamentos.php

session_start();

// Verificar se o administrador está logado
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: ../admin_login.php');
    exit();
}

$admin_nome = $_SESSION['admin_nome'];

// Configurações do banco de dados
$servername = "localhost";
$username_db = "u680766645_fixfacilnew";
$password_db = "T3cn0l0g1a@";
$dbname = "u680766645_fixfacilnew";

$conn = new mysqli($servername, $username_db, $password_db, $dbname);
if ($conn->connect_error) {
    die("Conexão falhou: " . $conn->connect_error);
}

$conn->set_charset("utf8mb4");

// Processar ações de confirmação ou cancelamento
$mensagem = '';

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Obter dados do formulário
    $agendamento_id = intval($_POST['agendamento_id']);
    $acao = $_POST['acao']; // 'confirmar' ou 'cancelar'

    if ($acao == 'confirmar') {
        $motoboy_nome = trim($_POST['motoboy_nome']);
        $motoboy_placa = trim($_POST['motoboy_placa']);
        $motoboy_contato = trim($_POST['motoboy_contato']);
        $observacoes = trim($_POST['observacoes']);

        // Atualizar o agendamento para Confirmado
        $stmt = $conn->prepare("
            UPDATE agendamentos_retirada
            SET status = 'Confirmado', motoboy_nome = ?, motoboy_placa = ?, motoboy_contato = ?, observacoes = ?
            WHERE id = ?
        ");
        $stmt->bind_param("ssssi", $motoboy_nome, $motoboy_placa, $motoboy_contato, $observacoes, $agendamento_id);

        if ($stmt->execute()) {
            $mensagem = '<div class="alert alert-success">Agendamento confirmado com sucesso!</div>';
            // Aqui você pode implementar o envio de notificação ao usuário (por e-mail, por exemplo)
        } else {
            $mensagem = '<div class="alert alert-danger">Erro ao confirmar o agendamento. Por favor, tente novamente.</div>';
        }
        $stmt->close();
    } elseif ($acao == 'cancelar') {
        // Atualizar o agendamento para Cancelado
        $stmt = $conn->prepare("
            UPDATE agendamentos_retirada
            SET status = 'Cancelado'
            WHERE id = ?
        ");
        $stmt->bind_param("i", $agendamento_id);

        if ($stmt->execute()) {
            $mensagem = '<div class="alert alert-success">Agendamento cancelado com sucesso!</div>';
            // Aqui você pode implementar o envio de notificação ao usuário (por e-mail, por exemplo)
        } else {
            $mensagem = '<div class="alert alert-danger">Erro ao cancelar o agendamento. Por favor, tente novamente.</div>';
        }
        $stmt->close();
    }
}

// Obter todos os agendamentos
$stmt = $conn->prepare("
    SELECT ar.*, pa.id AS proposta_id, sr.dispositivo, sr.marca, sr.modelo, u.nome AS nome_usuario, u.email
    FROM agendamentos_retirada ar
    JOIN propostas_assistencia pa ON ar.proposta_id = pa.id
    JOIN solicitacoes_reparo sr ON pa.solicitacao_id = sr.id
    JOIN usuarios u ON ar.usuario_id = u.id
    ORDER BY ar.data_agendada DESC, ar.horario_agendado DESC
");
$stmt->execute();
$result = $stmt->get_result();
$agendamentos = $result->fetch_all(MYSQLI_ASSOC);
$stmt->close();

$conn->close();
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <title>Administração de Agendamentos - FixFácil</title>
    <!-- Meta Tags para Responsividade -->
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <!-- Incluindo Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome para ícones -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Estilos personalizados -->
    <style>
        body {
            font-family: 'Poppins', sans-serif;
            background-color: #f7f9fc;
            color: #495057;
            margin-bottom: 60px;
        }
        /* Navbar */
        .navbar {
            background-color: #fff;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .navbar-brand {
            font-weight: 600;
            color: #007BFF;
        }
        .navbar-nav .nav-link {
            color: #495057 !important;
            font-weight: 500;
            margin-right: 15px;
        }
        .navbar-nav .nav-link.active {
            color: #007BFF !important;
        }
        .navbar-nav .nav-link:hover {
            color: #0056b3 !important;
        }
        /* Conteúdo Principal */
        .main-content {
            padding: 80px 20px 20px;
        }
        .page-title {
            margin-bottom: 40px;
        }
        .page-title h2 {
            font-weight: 600;
            color: #343a40;
        }
        /* Tabela */
        .table thead th {
            vertical-align: middle;
            text-align: center;
        }
        .table tbody td {
            vertical-align: middle;
            text-align: center;
        }
        /* Status Badges */
        .badge-pendente {
            background-color: #ffc107;
            color: #212529;
        }
        .badge-confirmado {
            background-color: #28a745;
            color: #fff;
        }
        .badge-concluido {
            background-color: #17a2b8;
            color: #fff;
        }
        .badge-cancelado {
            background-color: #dc3545;
            color: #fff;
        }
        /* Botões */
        .btn-action {
            border-radius: 5px;
            font-weight: 500;
            margin: 2px;
        }
        /* Footer */
        footer.footer {
            background-color: #fff;
            padding: 20px 0;
            position: fixed;
            width: 100%;
            bottom: 0;
            box-shadow: 0 -2px 4px rgba(0,0,0,0.1);
        }
        footer.footer span {
            color: #6c757d;
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-light fixed-top">
        <div class="container-fluid">
            <!-- Nome da Empresa -->
            <a class="navbar-brand" href="dashboard.php">Painel Administrativo - FixFácil</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" 
                    aria-controls="navbarNav" aria-expanded="false" aria-label="Alternar navegação">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse justify-content-end" id="navbarNav">
                <ul class="navbar-nav">
                    <!-- Links de navegação -->
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="admin_agendamentos.php"><i class="fas fa-calendar-alt"></i> Agendamentos</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="manage_users.php"><i class="fas fa-users"></i> Usuários</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="reports.php"><i class="fas fa-chart-bar"></i> Relatórios</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="logout.php"><i class="fas fa-sign-out-alt"></i> Sair</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Conteúdo Principal -->
    <div class="container main-content">
        <div class="page-title text-center">
            <h2>Administração de Agendamentos</h2>
            <p class="text-muted">Visualize e gerencie todos os agendamentos.</p>
        </div>

        <!-- Exibir Mensagens -->
        <?php if ($mensagem) echo $mensagem; ?>

        <?php if (!empty($agendamentos)): ?>
            <div class="table-responsive">
                <table class="table table-bordered table-hover align-middle">
                    <thead class="table-light">
                        <tr>
                            <th>ID</th>
                            <th>Usuário</th>
                            <th>Dispositivo</th>
                            <th>Data</th>
                            <th>Horário</th>
                            <th>Status</th>
                            <th>Ações</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($agendamentos as $agendamento): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($agendamento['id']); ?></td>
                                <td>
                                    <?php echo htmlspecialchars($agendamento['nome_usuario']); ?><br>
                                    <small><?php echo htmlspecialchars($agendamento['email']); ?></small>
                                </td>
                                <td>
                                    <?php echo htmlspecialchars($agendamento['dispositivo'] . " " . $agendamento['marca'] . " " . $agendamento['modelo']); ?>
                                </td>
                                <td><?php echo date('d/m/Y', strtotime($agendamento['data_agendada'])); ?></td>
                                <td><?php echo date('H:i', strtotime($agendamento['horario_agendado'])); ?></td>
                                <td>
                                    <?php
                                        switch ($agendamento['status']) {
                                            case 'Pendente':
                                                echo '<span class="badge badge-pendente">Pendente</span>';
                                                break;
                                            case 'Confirmado':
                                                echo '<span class="badge badge-confirmado">Confirmado</span>';
                                                break;
                                            case 'Concluído':
                                                echo '<span class="badge badge-concluido">Concluído</span>';
                                                break;
                                            case 'Cancelado':
                                                echo '<span class="badge badge-cancelado">Cancelado</span>';
                                                break;
                                            default:
                                                echo '<span class="badge bg-secondary">Desconhecido</span>';
                                        }
                                    ?>
                                </td>
                                <td>
                                    <!-- Botão para abrir o modal de detalhes -->
                                    <button type="button" class="btn btn-info btn-sm" data-bs-toggle="modal" data-bs-target="#detalhesModal<?php echo $agendamento['id']; ?>">
                                        <i class="fas fa-eye"></i> Detalhes
                                    </button>
                                </td>
                            </tr>

                            <!-- Modal de Detalhes -->
                            <div class="modal fade" id="detalhesModal<?php echo $agendamento['id']; ?>" tabindex="-1" aria-labelledby="detalhesModalLabel<?php echo $agendamento['id']; ?>" aria-hidden="true">
                                <div class="modal-dialog modal-lg">
                                    <div class="modal-content">
                                        <form action="admin_agendamentos.php" method="POST">
                                            <div class="modal-header">
                                                <h5 class="modal-title" id="detalhesModalLabel<?php echo $agendamento['id']; ?>">Detalhes do Agendamento #<?php echo $agendamento['id']; ?></h5>
                                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Fechar"></button>
                                            </div>
                                            <div class="modal-body">
                                                <!-- Detalhes do Agendamento -->
                                                <div class="row">
                                                    <div class="col-md-6">
                                                        <p><strong>Usuário:</strong> <?php echo htmlspecialchars($agendamento['nome_usuario']); ?></p>
                                                        <p><strong>Email:</strong> <?php echo htmlspecialchars($agendamento['email']); ?></p>
                                                        <p><strong>Dispositivo:</strong> <?php echo htmlspecialchars($agendamento['dispositivo'] . " " . $agendamento['marca'] . " " . $agendamento['modelo']); ?></p>
                                                        <p><strong>Data Agendada:</strong> <?php echo date('d/m/Y', strtotime($agendamento['data_agendada'])); ?></p>
                                                        <p><strong>Horário Agendado:</strong> <?php echo date('H:i', strtotime($agendamento['horario_agendado'])); ?></p>
                                                        <p><strong>Status:</strong> 
                                                            <?php
                                                                switch ($agendamento['status']) {
                                                                    case 'Pendente':
                                                                        echo '<span class="badge badge-pendente">Pendente</span>';
                                                                        break;
                                                                    case 'Confirmado':
                                                                        echo '<span class="badge badge-confirmado">Confirmado</span>';
                                                                        break;
                                                                    case 'Concluído':
                                                                        echo '<span class="badge badge-concluido">Concluído</span>';
                                                                        break;
                                                                    case 'Cancelado':
                                                                        echo '<span class="badge badge-cancelado">Cancelado</span>';
                                                                        break;
                                                                    default:
                                                                        echo '<span class="badge bg-secondary">Desconhecido</span>';
                                                                }
                                                            ?>
                                                        </p>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <?php if ($agendamento['status'] == 'Confirmado'): ?>
                                                            <p><strong>Nome do Motoboy:</strong> <?php echo htmlspecialchars($agendamento['motoboy_nome']); ?></p>
                                                            <p><strong>Placa da Moto:</strong> <?php echo htmlspecialchars($agendamento['motoboy_placa']); ?></p>
                                                            <p><strong>Contato do Motoboy:</strong> <?php echo htmlspecialchars($agendamento['motoboy_contato']); ?></p>
                                                            <p><strong>Observações:</strong> <?php echo htmlspecialchars($agendamento['observacoes']); ?></p>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>

                                                <!-- Formulário de Ação (Confirmar ou Cancelar) -->
                                                <?php if ($agendamento['status'] == 'Pendente'): ?>
                                                    <hr>
                                                    <h5>Confirmar Agendamento</h5>
                                                    <input type="hidden" name="agendamento_id" value="<?php echo htmlspecialchars($agendamento['id']); ?>">
                                                    <input type="hidden" name="acao" value="confirmar">
                                                    <div class="mb-3">
                                                        <label for="motoboy_nome_<?php echo $agendamento['id']; ?>" class="form-label">Nome do Motoboy:</label>
                                                        <input type="text" name="motoboy_nome" id="motoboy_nome_<?php echo $agendamento['id']; ?>" class="form-control" required>
                                                    </div>
                                                    <div class="mb-3">
                                                        <label for="motoboy_placa_<?php echo $agendamento['id']; ?>" class="form-label">Placa da Moto:</label>
                                                        <input type="text" name="motoboy_placa" id="motoboy_placa_<?php echo $agendamento['id']; ?>" class="form-control" required>
                                                    </div>
                                                    <div class="mb-3">
                                                        <label for="motoboy_contato_<?php echo $agendamento['id']; ?>" class="form-label">Contato do Motoboy:</label>
                                                        <input type="text" name="motoboy_contato" id="motoboy_contato_<?php echo $agendamento['id']; ?>" class="form-control" required>
                                                    </div>
                                                    <div class="mb-3">
                                                        <label for="observacoes_<?php echo $agendamento['id']; ?>" class="form-label">Observações:</label>
                                                        <textarea name="observacoes" id="observacoes_<?php echo $agendamento['id']; ?>" class="form-control"></textarea>
                                                    </div>
                                                <?php endif; ?>

                                                <?php if ($agendamento['status'] != 'Cancelado'): ?>
                                                    <hr>
                                                    <h5>Ações</h5>
                                                    <input type="hidden" name="agendamento_id" value="<?php echo htmlspecialchars($agendamento['id']); ?>">
                                                    <div class="d-flex justify-content-end">
                                                        <?php if ($agendamento['status'] == 'Pendente'): ?>
                                                            <button type="submit" name="acao" value="confirmar" class="btn btn-success me-2">
                                                                <i class="fas fa-check"></i> Confirmar
                                                            </button>
                                                        <?php endif; ?>
                                                        <button type="submit" name="acao" value="cancelar" class="btn btn-danger">
                                                            <i class="fas fa-times"></i> Cancelar
                                                        </button>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                            <!-- Fim do Modal -->
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php else: ?>
            <div class="alert alert-info">Não há agendamentos para exibir.</div>
        <?php endif; ?>
    </div>

    <!-- Rodapé -->
    <footer class="footer">
        <div class="container text-center">
            <span>&copy; <?php echo date("Y"); ?> FixFácil. Todos os direitos reservados.</span>
        </div>
    </footer>

    <!-- Incluindo Bootstrap JS e dependências -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
