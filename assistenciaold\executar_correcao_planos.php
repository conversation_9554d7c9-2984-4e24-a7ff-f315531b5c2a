<?php
/**
 * Executar Correção Automática dos Planos
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔧 Execução Automática da Correção de Planos</h1>";
echo "<style>body { font-family: Arial, sans-serif; margin: 20px; } .success { background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0; } .error { background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0; } .info { background: #d1ecf1; padding: 15px; border-radius: 5px; margin: 10px 0; }</style>";

try {
    require_once 'config/database.php';
    $db = getDatabase();
    
    echo "<p>✅ Conexão com banco estabelecida</p>";
    
    if (isset($_GET['executar']) && $_GET['executar'] === 'sim') {
        echo "<h3>🚀 Executando correções...</h3>";
        
        // 1. Adicionar coluna acesso_assistencia_virtual
        echo "<h4>1. Adicionando coluna acesso_assistencia_virtual</h4>";
        try {
            $sql = "ALTER TABLE planos ADD COLUMN acesso_assistencia_virtual TINYINT(1) NOT NULL DEFAULT 0 AFTER retirada_express_prioritaria";
            $db->query($sql);
            echo "<div class='success'>✅ Coluna acesso_assistencia_virtual adicionada</div>";
        } catch (Exception $e) {
            if (strpos($e->getMessage(), 'Duplicate column name') !== false) {
                echo "<div class='info'>ℹ️ Coluna acesso_assistencia_virtual já existe</div>";
            } else {
                echo "<div class='error'>❌ Erro ao adicionar coluna: " . $e->getMessage() . "</div>";
            }
        }
        
        // 2. Verificar e criar planos padrão
        echo "<h4>2. Verificando planos padrão</h4>";
        
        $planos_padrao = [
            [
                'nome' => 'Free',
                'descricao' => 'Plano gratuito com funcionalidades básicas para começar',
                'preco_mensal' => 0.00,
                'taxa_servico' => 25.00,
                'acesso_chat' => 0,
                'acesso_marketplace' => 0,
                'retirada_presencial' => 1,
                'selo_fixfacil' => 0,
                'link_personalizado' => 0,
                'retirada_express_prioritaria' => 0,
                'acesso_assistencia_virtual' => 0
            ],
            [
                'nome' => 'Premium',
                'descricao' => 'Plano premium com chat e marketplace para expandir seu negócio',
                'preco_mensal' => 89.90,
                'taxa_servico' => 20.00,
                'acesso_chat' => 1,
                'acesso_marketplace' => 1,
                'retirada_presencial' => 1,
                'selo_fixfacil' => 0,
                'link_personalizado' => 0,
                'retirada_express_prioritaria' => 0,
                'acesso_assistencia_virtual' => 0
            ],
            [
                'nome' => 'Master',
                'descricao' => 'Plano completo com assistência virtual e todas as funcionalidades',
                'preco_mensal' => 159.90,
                'taxa_servico' => 10.00,
                'acesso_chat' => 1,
                'acesso_marketplace' => 1,
                'retirada_presencial' => 1,
                'selo_fixfacil' => 1,
                'link_personalizado' => 1,
                'retirada_express_prioritaria' => 1,
                'acesso_assistencia_virtual' => 1
            ]
        ];
        
        foreach ($planos_padrao as $plano) {
            try {
                // Verificar se plano já existe
                $sql = "SELECT id FROM planos WHERE nome = ?";
                $result = $db->query($sql, [$plano['nome']]);
                
                if ($result->fetch_assoc()) {
                    // Atualizar plano existente
                    $sql = "
                        UPDATE planos SET 
                            descricao = ?,
                            preco_mensal = ?,
                            taxa_servico = ?,
                            acesso_chat = ?,
                            acesso_marketplace = ?,
                            retirada_presencial = ?,
                            selo_fixfacil = ?,
                            link_personalizado = ?,
                            retirada_express_prioritaria = ?,
                            acesso_assistencia_virtual = ?
                        WHERE nome = ?
                    ";
                    $db->query($sql, [
                        $plano['descricao'],
                        $plano['preco_mensal'],
                        $plano['taxa_servico'],
                        $plano['acesso_chat'],
                        $plano['acesso_marketplace'],
                        $plano['retirada_presencial'],
                        $plano['selo_fixfacil'],
                        $plano['link_personalizado'],
                        $plano['retirada_express_prioritaria'],
                        $plano['acesso_assistencia_virtual'],
                        $plano['nome']
                    ]);
                    echo "<div class='success'>✅ Plano {$plano['nome']} atualizado</div>";
                } else {
                    // Criar novo plano
                    $sql = "
                        INSERT INTO planos 
                        (nome, descricao, preco_mensal, taxa_servico, acesso_chat, acesso_marketplace, 
                         retirada_presencial, selo_fixfacil, link_personalizado, retirada_express_prioritaria, 
                         acesso_assistencia_virtual, status)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'ativo')
                    ";
                    $db->query($sql, [
                        $plano['nome'],
                        $plano['descricao'],
                        $plano['preco_mensal'],
                        $plano['taxa_servico'],
                        $plano['acesso_chat'],
                        $plano['acesso_marketplace'],
                        $plano['retirada_presencial'],
                        $plano['selo_fixfacil'],
                        $plano['link_personalizado'],
                        $plano['retirada_express_prioritaria'],
                        $plano['acesso_assistencia_virtual']
                    ]);
                    echo "<div class='success'>✅ Plano {$plano['nome']} criado</div>";
                }
            } catch (Exception $e) {
                echo "<div class='error'>❌ Erro ao processar plano {$plano['nome']}: " . $e->getMessage() . "</div>";
            }
        }
        
        // 3. Criar assinaturas Free para assistências sem plano
        echo "<h4>3. Criando assinaturas Free para assistências sem plano</h4>";
        try {
            $sql = "
                INSERT IGNORE INTO assinaturas_assistencias (assistencia_id, plano_id, status, data_inicio)
                SELECT 
                    at.id,
                    (SELECT id FROM planos WHERE nome = 'Free' LIMIT 1),
                    'ativa',
                    NOW()
                FROM assistencias_tecnicas at
                WHERE at.id NOT IN (
                    SELECT DISTINCT assistencia_id 
                    FROM assinaturas_assistencias 
                    WHERE status = 'ativa'
                )
            ";
            $db->query($sql);
            echo "<div class='success'>✅ Assinaturas Free criadas para assistências sem plano</div>";
        } catch (Exception $e) {
            echo "<div class='error'>❌ Erro ao criar assinaturas: " . $e->getMessage() . "</div>";
        }
        
        echo "<div class='success'>";
        echo "<h4>🎉 Correção concluída com sucesso!</h4>";
        echo "<p>O sistema de planos foi corrigido e está funcionando.</p>";
        echo "<p><a href='verificar_planos.php'>Verificar Sistema de Planos</a></p>";
        echo "<p><a href='dashboard.php'>Ir para Dashboard</a></p>";
        echo "</div>";
        
    } else {
        // Mostrar informações antes da execução
        echo "<h3>📋 O que será executado:</h3>";
        echo "<ul>";
        echo "<li>✅ Adicionar coluna 'acesso_assistencia_virtual' na tabela planos</li>";
        echo "<li>✅ Criar/atualizar planos padrão (Free, Premium, Master)</li>";
        echo "<li>✅ Configurar permissões corretas para cada plano</li>";
        echo "<li>✅ Criar assinaturas Free para assistências sem plano</li>";
        echo "</ul>";
        
        echo "<div class='info'>";
        echo "<h4>⚠️ Importante:</h4>";
        echo "<p>Esta operação irá modificar a estrutura do banco de dados.</p>";
        echo "<p>Certifique-se de ter um backup antes de continuar.</p>";
        echo "</div>";
        
        echo "<p><a href='?executar=sim' style='background: #007cba; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-size: 16px;'>🚀 Executar Correção</a></p>";
    }
    
    // Mostrar status atual
    echo "<hr>";
    echo "<h3>📊 Status Atual do Sistema:</h3>";
    
    // Verificar se coluna existe
    try {
        $result = $db->query("SHOW COLUMNS FROM planos LIKE 'acesso_assistencia_virtual'");
        if ($result->num_rows > 0) {
            echo "<p>✅ Coluna 'acesso_assistencia_virtual' existe</p>";
        } else {
            echo "<p>❌ Coluna 'acesso_assistencia_virtual' não existe</p>";
        }
    } catch (Exception $e) {
        echo "<p>❌ Erro ao verificar coluna: " . $e->getMessage() . "</p>";
    }
    
    // Verificar planos
    try {
        $result = $db->query("SELECT COUNT(*) as total FROM planos");
        $row = $result->fetch_assoc();
        echo "<p>📋 Total de planos cadastrados: {$row['total']}</p>";
        
        if ($row['total'] > 0) {
            $result = $db->query("SELECT nome, preco_mensal, acesso_assistencia_virtual FROM planos ORDER BY id");
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
            echo "<tr><th>Plano</th><th>Preço</th><th>Assistência Virtual</th></tr>";
            while ($plano = $result->fetch_assoc()) {
                $virtual = isset($plano['acesso_assistencia_virtual']) ? ($plano['acesso_assistencia_virtual'] ? 'SIM' : 'NÃO') : 'N/A';
                echo "<tr>";
                echo "<td>{$plano['nome']}</td>";
                echo "<td>R$ " . number_format($plano['preco_mensal'], 2, ',', '.') . "</td>";
                echo "<td>$virtual</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
    } catch (Exception $e) {
        echo "<p>❌ Erro ao verificar planos: " . $e->getMessage() . "</p>";
    }
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Erro de conexão: " . $e->getMessage() . "</div>";
}

?>

<hr>

<h3>🔗 Links Úteis:</h3>
<ul>
    <li><a href="verificar_planos.php">Verificar Sistema de Planos</a></li>
    <li><a href="dashboard.php">Dashboard</a></li>
    <li><a href="assistencia_virtual.php">Assistência Virtual</a></li>
    <li><a href="../login.php">Login</a></li>
</ul>

<p><small>Executado em <?php echo date('d/m/Y H:i:s'); ?></small></p>
