<?php
session_start();
require_once 'db.php';

$mensagem = '';

if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['fazer_lance'])) {
    $produto_id = $_POST['produto_id'];
    $leilao_id = $_POST['leilao_id'];
    $valor_lance = $_POST['valor_lance'];
    $lojista_id = $_SESSION['user_id'];
    $data_lance = date("Y-m-d H:i:s");

    try {
        $query = "INSERT INTO lances (produto_id, leilao_id, lojista_id, valor_lance, data_lance) VALUES (:produto_id, :leilao_id, :lojista_id, :valor_lance, :data_lance)";
        $stmt = $pdo->prepare($query);
        $stmt->bindParam(':produto_id', $produto_id, PDO::PARAM_INT);
        $stmt->bindParam(':leilao_id', $leilao_id, PDO::PARAM_INT);
        $stmt->bindParam(':lojista_id', $lojista_id, PDO::PARAM_INT);
        $stmt->bindParam(':valor_lance', $valor_lance, PDO::PARAM_STR);
        $stmt->bindParam(':data_lance', $data_lance);
        $stmt->execute();

        $mensagem = 'Lance feito com sucesso!';
    } catch (PDOException $e) {
        $mensagem = 'Erro ao fazer o lance: ' . $e->getMessage();
    }
}

// Buscar leilões
$queryLeiloes = "SELECT l.*, p.nome as nome_produto FROM leilao l
                 INNER JOIN produtos p ON l.produto_id = p.id
                 WHERE l.status_leilao = 'ativo'";
$stmtLeiloes = $pdo->prepare($queryLeiloes);
$stmtLeiloes->execute();
$leiloes = $stmtLeiloes->fetchAll(PDO::FETCH_ASSOC);

?>

<!DOCTYPE html>
<html lang="pt-br">
<head>
    <!-- ... (cabeçalho HTML) ... -->
</head>
<body>
    <!-- ... (código HTML) ... -->

    <?php if ($mensagem): ?>
        <div><?= $mensagem ?></div>
    <?php endif; ?>

    <h1>Leilões Disponíveis</h1>

    <table>
        <thead>
            <tr>
                <th>Produto</th>
                <th>Data de Início</th>
                <th>Data de Término</th>
                <th>Valor Atual</th>
                <th>Fazer Lance</th>
            </tr>
        </thead>
        <tbody>
            <?php foreach ($leiloes as $leilao): ?>
                <tr>
                    <td><?= $leilao['nome_produto'] ?></td>
                    <td><?= $leilao['data_inicio'] ?></td>
                    <td><?= $leilao['data_termino'] ?></td>
                    <td><?= $leilao['valor_lance_atual'] ?></td>
                    <td>
                        <form method="post" action="">
                            <input type="hidden" name="produto_id" value="<?= $leilao['produto_id'] ?>">
                            <input type="hidden" name="leilao_id" value="<?= $leilao['id'] ?>">
                            <input type="number" name="valor_lance" placeholder="Valor do Lance">
                            <button type="submit" name="fazer_lance">Fazer Lance</button>
                        </form>
                    </td>
                </tr>
            <?php endforeach; ?>
        </tbody>
    </table>

    <!-- ... (código HTML) ... -->
</body>
</html>
