<?php
/**
 * Página de Solicitações - Nova Versão Mobile-First
 * FixFácil Assistências - Sistema Novo
 */

// Redirecionar para versão mobile final
header('Location: solicitacoes.php?' . $_SERVER['QUERY_STRING']);
exit();
?>

// Incluir configurações básicas
require_once 'config/database.php';

// Configuração de banco de dados
$db = getDatabase();
$mysqli = $db->getConnection();

// Obter dados do usuário logado
$usuario_id = $_SESSION['usuario_id'];

// Filtros
$status_filter = $_GET['status'] ?? 'enviado';
$search = $_GET['search'] ?? '';

// Obter estatísticas das solicitações
$stats = [
    'total' => 0,
    'enviado' => 0,
    'aceita' => 0,
    'rejeitada' => 0,
    'concluido' => 0
];

try {
    // Buscar estatísticas gerais
    $sql_stats = "
        SELECT 
            COUNT(*) as total,
            SUM(CASE WHEN status = 'enviado' THEN 1 ELSE 0 END) as enviado,
            SUM(CASE WHEN status = 'aceita' THEN 1 ELSE 0 END) as aceita,
            SUM(CASE WHEN status = 'rejeitada' THEN 1 ELSE 0 END) as rejeitada,
            SUM(CASE WHEN status = 'concluido' THEN 1 ELSE 0 END) as concluido
        FROM solicitacoes_reparo 
        WHERE visivel = 1
    ";
    
    $result = $mysqli->query($sql_stats);
    if ($result) {
        $stats = $result->fetch_assoc();
    }

} catch (Exception $e) {
    error_log("Erro ao obter estatísticas: " . $e->getMessage());
}

// Obter solicitações
$solicitacoes = [];
try {
    $where_conditions = ["sr.status = ?", "sr.visivel = 1"];
    $params = [$status_filter];
    $types = "s";

    if (!empty($search)) {
        $where_conditions[] = "(sr.descricao_problema LIKE ? OR sr.dispositivo LIKE ? OR sr.marca LIKE ? OR sr.modelo LIKE ? OR u.nome LIKE ?)";
        $search_param = "%$search%";
        $params = array_merge($params, [$search_param, $search_param, $search_param, $search_param, $search_param]);
        $types .= "sssss";
    }

    $where_clause = implode(' AND ', $where_conditions);

    $sql = "
        SELECT
            sr.id,
            sr.usuario_id,
            sr.tipo_solicitacao,
            sr.descricao_problema,
            sr.descricao_detalhada,
            sr.video,
            sr.metodo_entrega,
            sr.endereco,
            sr.dispositivo,
            sr.marca,
            sr.modelo,
            sr.memoria,
            sr.status,
            sr.data_solicitacao,
            sr.verificacoes,
            sr.origem,
            u.nome as cliente_nome,
            u.telefone as cliente_telefone,
            u.endereco as cliente_endereco,
            u.cep as cliente_cep,
            u.cidade as cliente_cidade,
            u.estado as cliente_estado
        FROM solicitacoes_reparo sr
        JOIN usuarios u ON sr.usuario_id = u.id
        WHERE $where_clause
        ORDER BY sr.data_solicitacao DESC
        LIMIT 50
    ";

    $stmt = $mysqli->prepare($sql);
    $stmt->bind_param($types, ...$params);
    $stmt->execute();
    $result = $stmt->get_result();
    
    while ($row = $result->fetch_assoc()) {
        $solicitacoes[] = $row;
    }

} catch (Exception $e) {
    error_log("Erro ao obter solicitações: " . $e->getMessage());
    $solicitacoes = [];
}

// Função para obter cor do status
function getStatusColor($status) {
    switch ($status) {
        case 'enviado': return 'warning';
        case 'aceita': return 'success';
        case 'rejeitada': return 'danger';
        case 'concluido': return 'primary';
        default: return 'secondary';
    }
}

// Função para obter texto do status
function getStatusText($status) {
    switch ($status) {
        case 'enviado': return 'Pendente';
        case 'aceita': return 'Aceita';
        case 'rejeitada': return 'Rejeitada';
        case 'concluido': return 'Concluída';
        default: return 'Desconhecido';
    }
}

// Função para calcular tempo relativo
function tempoRelativo($data) {
    $tempo = time() - strtotime($data);
    if ($tempo < 3600) {
        return intval($tempo/60) . ' min';
    } elseif ($tempo < 86400) {
        return intval($tempo/3600) . 'h';
    } else {
        return intval($tempo/86400) . 'd';
    }
}
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, shrink-to-fit=no">
    <title>Solicitações - FixFácil</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            min-height: 100vh;
            color: #1e293b;
        }

        .container {
            max-width: 414px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
            position: relative;
            overflow-x: hidden;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }

        .header {
            background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
            color: white;
            padding: 24px 20px 20px 20px;
            position: relative;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 20"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="0.5" fill="white" opacity="0.05"/></pattern></defs><rect width="100" height="20" fill="url(%23grain)"/></svg>');
        }

        .header-content {
            position: relative;
            z-index: 1;
        }

        .header-top {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 16px;
        }

        .back-btn {
            width: 36px;
            height: 36px;
            background: rgba(255,255,255,0.15);
            border: none;
            border-radius: 10px;
            color: white;
            font-size: 16px;
            cursor: pointer;
            backdrop-filter: blur(10px);
            transition: all 0.2s ease;
            text-decoration: none;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .back-btn:hover {
            background: rgba(255,255,255,0.25);
            color: white;
        }

        .header h1 {
            font-size: 20px;
            font-weight: 700;
            letter-spacing: -0.025em;
            margin-bottom: 4px;
        }

        .header-subtitle {
            font-size: 14px;
            opacity: 0.8;
        }

        .header-stats {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 12px;
            margin-top: 16px;
        }

        .stat-item {
            text-align: center;
            background: rgba(255,255,255,0.15);
            border-radius: 12px;
            padding: 12px 8px;
            backdrop-filter: blur(10px);
        }

        .stat-number {
            font-size: 18px;
            font-weight: 700;
            margin-bottom: 4px;
        }

        .stat-label {
            font-size: 10px;
            opacity: 0.8;
            line-height: 1.2;
        }

        .tabs {
            background: white;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            padding: 0 20px;
            overflow-x: auto;
        }

        .tab {
            flex: 1;
            padding: 16px 8px;
            text-align: center;
            font-size: 13px;
            font-weight: 600;
            color: #64748b;
            cursor: pointer;
            border-bottom: 3px solid transparent;
            transition: all 0.3s ease;
            white-space: nowrap;
            text-decoration: none;
            min-width: 80px;
        }

        .tab.active {
            color: #3b82f6;
            border-bottom-color: #3b82f6;
        }

        .content {
            padding: 16px;
            padding-bottom: 100px;
            min-height: calc(100vh - 200px);
        }

        .search-box {
            background: white;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            padding: 12px 16px;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .search-box input {
            border: none;
            outline: none;
            flex: 1;
            font-size: 14px;
            background: transparent;
        }

        .search-box input::placeholder {
            color: #94a3b8;
        }

        .search-icon {
            color: #64748b;
            font-size: 16px;
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #64748b;
        }

        .empty-icon {
            font-size: 64px;
            margin-bottom: 16px;
            opacity: 0.6;
        }

        .empty-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 8px;
            color: #1e293b;
        }

        .empty-subtitle {
            font-size: 14px;
            line-height: 1.5;
        }

        .request-card {
            background: white;
            border: 2px solid #e2e8f0;
            border-radius: 16px;
            margin-bottom: 16px;
            overflow: hidden;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .request-card:hover {
            border-color: #3b82f6;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
        }

        .request-header {
            padding: 16px 20px;
            border-bottom: 1px solid #f1f5f9;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .device-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .device-icon {
            width: 48px;
            height: 48px;
            background: linear-gradient(135deg, #3b82f6, #1e40af);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
        }

        .device-details h3 {
            font-size: 16px;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 2px;
        }

        .device-memory {
            font-size: 12px;
            color: #64748b;
        }

        .request-time {
            font-size: 12px;
            color: #64748b;
            text-align: right;
        }

        .request-body {
            padding: 16px 20px;
        }

        .customer-info {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 12px;
            padding: 8px 12px;
            background: #f8fafc;
            border-radius: 8px;
        }

        .customer-name {
            font-weight: 600;
            color: #1e293b;
            font-size: 14px;
        }

        .customer-phone {
            color: #64748b;
            font-size: 12px;
        }

        .problem-description {
            color: #374151;
            font-size: 14px;
            line-height: 1.5;
            margin-bottom: 12px;
        }

        .request-meta {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            align-items: center;
            margin-bottom: 16px;
        }

        .meta-tag {
            background: #f1f5f9;
            color: #64748b;
            border-radius: 6px;
            padding: 4px 8px;
            font-size: 11px;
            font-weight: 500;
        }

        .meta-tag.delivery {
            background: #ecfdf5;
            color: #059669;
        }

        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-enviado {
            background: #fef3c7;
            color: #92400e;
        }

        .status-aceita {
            background: #d1fae5;
            color: #065f46;
        }

        .status-rejeitada {
            background: #fee2e2;
            color: #991b1b;
        }

        .status-concluido {
            background: #dbeafe;
            color: #1e40af;
        }

        .request-actions {
            display: flex;
            gap: 8px;
            padding-top: 12px;
            border-top: 1px solid #f1f5f9;
        }

        .btn {
            padding: 8px 16px;
            border-radius: 8px;
            font-size: 12px;
            font-weight: 600;
            text-decoration: none;
            border: none;
            cursor: pointer;
            transition: all 0.2s ease;
            display: inline-flex;
            align-items: center;
            gap: 6px;
            flex: 1;
            justify-content: center;
        }

        .btn-primary {
            background: #3b82f6;
            color: white;
        }

        .btn-primary:hover {
            background: #2563eb;
            color: white;
            text-decoration: none;
        }

        .btn-outline {
            background: transparent;
            color: #64748b;
            border: 1px solid #e2e8f0;
        }

        .btn-outline:hover {
            background: #f8fafc;
            color: #374151;
            text-decoration: none;
        }

        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 100%;
            max-width: 414px;
            background: white;
            border-top: 1px solid #e2e8f0;
            padding: 12px 20px;
            display: flex;
            justify-content: space-around;
            box-shadow: 0 -4px 20px rgba(0,0,0,0.1);
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
            cursor: pointer;
            padding: 8px;
            border-radius: 12px;
            transition: all 0.2s ease;
            position: relative;
            text-decoration: none;
            color: #64748b;
        }

        .nav-item.active {
            background: #eff6ff;
            color: #3b82f6;
        }

        .nav-item:hover {
            color: #3b82f6;
            text-decoration: none;
        }

        .nav-icon {
            font-size: 20px;
        }

        .nav-label {
            font-size: 10px;
            font-weight: 500;
        }

        .nav-badge {
            position: absolute;
            top: 4px;
            right: 4px;
            background: #ef4444;
            color: white;
            border-radius: 50%;
            width: 16px;
            height: 16px;
            font-size: 10px;
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .floating-action {
            position: fixed;
            bottom: 80px;
            right: 20px;
            width: 56px;
            height: 56px;
            background: linear-gradient(135deg, #3b82f6, #1e40af);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            cursor: pointer;
            box-shadow: 0 4px 16px rgba(59, 130, 246, 0.4);
            transition: all 0.3s ease;
            z-index: 100;
            text-decoration: none;
        }

        .floating-action:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 20px rgba(59, 130, 246, 0.5);
            color: white;
            text-decoration: none;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .request-card {
            animation: fadeIn 0.6s ease;
        }

        @media (max-width: 480px) {
            .container {
                max-width: 100%;
            }
            
            .header {
                padding: 20px 15px 16px 15px;
            }
            
            .content {
                padding: 12px;
                padding-bottom: 100px;
            }
            
            .request-card {
                margin-bottom: 12px;
            }
            
            .request-header {
                padding: 12px 16px;
            }
            
            .request-body {
                padding: 12px 16px;
            }
            
            .device-icon {
                width: 40px;
                height: 40px;
                font-size: 18px;
            }
            
            .device-details h3 {
                font-size: 14px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="header-content">
                <div class="header-top">
                    <a href="dashboard.php" class="back-btn">
                        ←
                    </a>
                    <div class="text-center flex-1">
                        <h1>Solicitações</h1>
                        <div class="header-subtitle">Gerencie solicitações de reparo</div>
                    </div>
                    <div style="width: 36px;"></div> <!-- Spacer -->
                </div>

                <div class="header-stats">
                    <div class="stat-item">
                        <div class="stat-number"><?php echo $stats['total']; ?></div>
                        <div class="stat-label">Total</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number"><?php echo $stats['enviado']; ?></div>
                        <div class="stat-label">Pendentes</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number"><?php echo $stats['aceita']; ?></div>
                        <div class="stat-label">Aceitas</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number"><?php echo $stats['concluido']; ?></div>
                        <div class="stat-label">Finalizadas</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tabs -->
        <div class="tabs">
            <a href="?status=enviado" class="tab <?php echo $status_filter === 'enviado' ? 'active' : ''; ?>">
                Pendentes
            </a>
            <a href="?status=aceita" class="tab <?php echo $status_filter === 'aceita' ? 'active' : ''; ?>">
                Aceitas
            </a>
            <a href="?status=rejeitada" class="tab <?php echo $status_filter === 'rejeitada' ? 'active' : ''; ?>">
                Rejeitadas
            </a>
            <a href="?status=concluido" class="tab <?php echo $status_filter === 'concluido' ? 'active' : ''; ?>">
                Concluídas
            </a>
        </div>

        <!-- Content -->
        <div class="content">
            <!-- Search Box -->
            <form method="GET" class="search-box">
                <input type="hidden" name="status" value="<?php echo htmlspecialchars($status_filter); ?>">
                <i class="fas fa-search search-icon"></i>
                <input type="text" name="search" placeholder="Buscar dispositivo, marca ou cliente..." 
                       value="<?php echo htmlspecialchars($search); ?>">
            </form>

            <!-- Lista de Solicitações -->
            <?php if (empty($solicitacoes)): ?>
                <div class="empty-state">
                    <div class="empty-icon">📋</div>
                    <div class="empty-title">Nenhuma solicitação encontrada</div>
                    <div class="empty-subtitle">
                        <?php if ($status_filter === 'enviado'): ?>
                            Não há solicitações pendentes no momento.
                        <?php else: ?>
                            Não há solicitações com o status "<?php echo getStatusText($status_filter); ?>".
                        <?php endif; ?>
                    </div>
                </div>
            <?php else: ?>
                <?php foreach ($solicitacoes as $solicitacao): ?>
                    <div class="request-card" onclick="viewRequest(<?php echo $solicitacao['id']; ?>)">
                        <div class="request-header">
                            <div class="device-info">
                                <div class="device-icon">
                                    📱
                                </div>
                                <div class="device-details">
                                    <h3><?php echo htmlspecialchars($solicitacao['marca'] . ' ' . $solicitacao['modelo']); ?></h3>
                                    <div class="device-memory"><?php echo htmlspecialchars($solicitacao['memoria'] ?? 'N/A'); ?></div>
                                </div>
                            </div>
                            <div class="request-time">
                                <?php echo tempoRelativo($solicitacao['data_solicitacao']); ?>
                            </div>
                        </div>

                        <div class="request-body">
                            <div class="customer-info">
                                <div class="customer-name">👤 <?php echo htmlspecialchars($solicitacao['cliente_nome']); ?></div>
                                <div class="customer-phone">📞 <?php echo htmlspecialchars($solicitacao['cliente_telefone']); ?></div>
                            </div>

                            <div class="problem-description">
                                <?php echo htmlspecialchars($solicitacao['descricao_problema']); ?>
                            </div>

                            <div class="request-meta">
                                <div class="meta-tag delivery">
                                    🚚 <?php echo htmlspecialchars($solicitacao['metodo_entrega']); ?>
                                </div>
                                <div class="meta-tag">
                                    📅 <?php echo date('d/m/Y', strtotime($solicitacao['data_solicitacao'])); ?>
                                </div>
                                <div class="status-badge status-<?php echo $solicitacao['status']; ?>">
                                    <?php echo getStatusText($solicitacao['status']); ?>
                                </div>
                            </div>

                            <div class="request-actions">
                                <a href="detalhes_solicitacao.php?id=<?php echo $solicitacao['id']; ?>" class="btn btn-outline">
                                    <i class="fas fa-eye"></i>
                                    Ver Detalhes
                                </a>
                                
                                <?php if ($solicitacao['status'] === 'enviado'): ?>
                                    <a href="enviar_proposta.php?solicitacao_id=<?php echo $solicitacao['id']; ?>" class="btn btn-primary">
                                        <i class="fas fa-paper-plane"></i>
                                        Proposta
                                    </a>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>

        <!-- Bottom Navigation -->
        <div class="bottom-nav">
            <a href="dashboard.php" class="nav-item">
                <div class="nav-icon">🏠</div>
                <div class="nav-label">Início</div>
            </a>
            <a href="solicitacoes_new.php" class="nav-item active">
                <div class="nav-icon">📋</div>
                <div class="nav-label">Solicitações</div>
                <?php if ($stats['enviado'] > 0): ?>
                    <div class="nav-badge"><?php echo $stats['enviado']; ?></div>
                <?php endif; ?>
            </a>
            <a href="reparos.php" class="nav-item">
                <div class="nav-icon">🔧</div>
                <div class="nav-label">Reparos</div>
            </a>
            <a href="marketplace.php" class="nav-item">
                <div class="nav-icon">🛒</div>
                <div class="nav-label">Loja</div>
            </a>
            <a href="perfil.php" class="nav-item">
                <div class="nav-icon">👤</div>
                <div class="nav-label">Perfil</div>
            </a>
        </div>

        <!-- Floating Action Button -->
        <a href="#" class="floating-action" onclick="refreshPage()" title="Atualizar">
            🔄
        </a>
    </div>

    <script>
        function viewRequest(id) {
            window.location.href = 'detalhes_solicitacao.php?id=' + id;
        }

        function refreshPage() {
            window.location.reload();
        }

        // Auto-submit search form on input
        document.querySelector('input[name="search"]').addEventListener('input', function() {
            clearTimeout(this.searchTimeout);
            this.searchTimeout = setTimeout(() => {
                this.form.submit();
            }, 1000);
        });

        // Adicionar animação de loading nos cartões
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.request-card');
            cards.forEach((card, index) => {
                card.style.animationDelay = (index * 0.1) + 's';
            });
        });
    </script>
</body>
</html>
