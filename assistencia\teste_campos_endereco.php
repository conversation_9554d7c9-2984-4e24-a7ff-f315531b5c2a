<?php
/**
 * Teste para verificar se os campos de endereço estão sendo carregados
 */

session_start();

// Verificar se está logado
if (!isset($_SESSION['usuario_id']) || $_SESSION['tipo_usuario'] !== 'assistencia') {
    echo "<h2>❌ Erro de Autenticação</h2>";
    echo "<p>Você precisa estar logado como assistência para acessar esta página.</p>";
    echo "<p><a href='../login.php'>Fazer <PERSON>gin</a></p>";
    exit();
}

require_once 'config/auth.php';
require_once 'config/database.php';

echo "<h2>🔍 Teste dos Campos de Endereço</h2>";

try {
    // Verificar autenticação
    $auth = getAuth();
    $usuario = $auth->getUsuarioLogado();
    
    echo "<h3>📋 Dados do Usuário Carregados:</h3>";
    
    if ($usuario) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>Campo</th><th>Valor</th><th>Status</th></tr>";
        
        $campos_endereco = [
            'endereco' => 'Endereço',
            'cep' => 'CEP',
            'numero_endereco' => 'Número',
            'complemento' => 'Complemento',
            'bairro' => 'Bairro',
            'cidade' => 'Cidade',
            'estado' => 'Estado',
            'ponto_referencia' => 'Ponto de Referência',
            'latitude' => 'Latitude',
            'longitude' => 'Longitude'
        ];
        
        foreach ($campos_endereco as $campo => $label) {
            $valor = $usuario[$campo] ?? null;
            $status = $valor ? '✅ Preenchido' : '⚠️ Vazio';
            $valor_exibido = $valor ? htmlspecialchars($valor) : '<em>Não informado</em>';
            
            echo "<tr>";
            echo "<td><strong>$label</strong></td>";
            echo "<td>$valor_exibido</td>";
            echo "<td>$status</td>";
            echo "</tr>";
        }
        
        echo "</table>";
        
        // Verificar outros campos importantes
        echo "<h3>📋 Outros Dados:</h3>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>Campo</th><th>Valor</th></tr>";
        
        $outros_campos = [
            'id' => 'ID do Usuário',
            'nome' => 'Nome',
            'email' => 'Email',
            'assistencia_id' => 'ID da Assistência',
            'nome_empresa' => 'Nome da Empresa',
            'telefone_empresa' => 'Telefone da Empresa',
            'email_empresa' => 'Email da Empresa',
            'site' => 'Site'
        ];
        
        foreach ($outros_campos as $campo => $label) {
            $valor = $usuario[$campo] ?? null;
            $valor_exibido = $valor ? htmlspecialchars($valor) : '<em>Não informado</em>';
            
            echo "<tr>";
            echo "<td><strong>$label</strong></td>";
            echo "<td>$valor_exibido</td>";
            echo "</tr>";
        }
        
        echo "</table>";
        
    } else {
        echo "<p style='color: red;'>❌ Erro: Não foi possível carregar os dados do usuário.</p>";
    }
    
    // Verificar estrutura da tabela
    echo "<h3>🗄️ Estrutura da Tabela assistencias_tecnicas:</h3>";
    
    $db = getDatabase();
    $result = $db->query("DESCRIBE assistencias_tecnicas");
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>Campo</th><th>Tipo</th><th>Nulo</th><th>Padrão</th><th>Comentário</th></tr>";
    
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>{$row['Field']}</td>";
        echo "<td>{$row['Type']}</td>";
        echo "<td>{$row['Null']}</td>";
        echo "<td>" . ($row['Default'] ?? 'NULL') . "</td>";
        echo "<td>" . ($row['Comment'] ?? '') . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
    
    // Verificar dados diretamente na tabela
    echo "<h3>🔍 Dados Diretos da Tabela:</h3>";
    
    $assistencia_id = $usuario['assistencia_id'] ?? null;
    if ($assistencia_id) {
        $sql = "SELECT * FROM assistencias_tecnicas WHERE id = ?";
        $result = $db->query($sql, [$assistencia_id]);
        $dados_diretos = $result->fetch_assoc();
        
        if ($dados_diretos) {
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr><th>Campo</th><th>Valor</th></tr>";
            
            foreach ($dados_diretos as $campo => $valor) {
                $valor_exibido = $valor ? htmlspecialchars($valor) : '<em>NULL</em>';
                echo "<tr>";
                echo "<td><strong>$campo</strong></td>";
                echo "<td>$valor_exibido</td>";
                echo "</tr>";
            }
            
            echo "</table>";
        } else {
            echo "<p style='color: red;'>❌ Nenhum registro encontrado na tabela assistencias_tecnicas para ID: $assistencia_id</p>";
        }
    } else {
        echo "<p style='color: orange;'>⚠️ ID da assistência não encontrado</p>";
    }
    
} catch (Exception $e) {
    echo "<h3 style='color: red;'>❌ Erro:</h3>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}

echo "<br><hr>";
echo "<p><a href='perfil.php'>← Voltar ao Perfil</a></p>";
echo "<p><a href='dashboard.php'>← Voltar ao Dashboard</a></p>";
?>
