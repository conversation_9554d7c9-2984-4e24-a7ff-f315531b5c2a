<?php
// Inclui o arquivo de configuração do banco de dados
require_once 'db.php';

header('Content-Type: application/json');

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=$charset", $user, $password, [
        PDO::ATTR_ERRMODE            => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES   => false,
    ]);

    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $mensagem = $_POST['mensagem'] ?? '';
        $fornecedor_id = $_POST['fornecedor_id'] ?? '';

        // Verificar se os campos estão preenchidos
        if (!empty($mensagem) && !empty($fornecedor_id)) {
            $stmt = $pdo->prepare("INSERT INTO mensagens (mensagem, fornecedor_id) VALUES (?, ?)");
            $stmt->execute([$mensagem, $fornecedor_id]);

            echo json_encode(['success' => true]);
            exit();
        } else {
            echo json_encode(['success' => false, 'message' => 'Dados incompletos']);
            exit();
        }
    }

} catch (\PDOException $e) {
    echo json_encode(['success' => false, 'message' => 'Erro interno do servidor: ' . $e->getMessage()]);
    exit();
}
?>
