<?php
// Iniciar sessão
session_start();

// Verificar se o usuário está logado e se é do tipo 'assistencia'
if (!isset($_SESSION['usuario_id']) || $_SESSION['tipo_usuario'] != 'assistencia') {
    header('Location: ../login.php');
    exit();
}

// Obter informações do usuário
$usuario_id = $_SESSION['usuario_id'];
$nome_usuario = $_SESSION['nome'];

// Conexão com o banco de dados
$servername = "localhost";
$username_db = "u682219090_fixfacilnew";
$password_db = "T3cn0l0g1a@";
$dbname = "u682219090_fixfacilnew";

$conn = new mysqli($servername, $username_db, $password_db, $dbname);

// Verificar conexão
if ($conn->connect_error) {
    die("Falha na conexão: " . $conn->connect_error);
}

// Definir charset
$conn->set_charset("utf8");

// Fechar conex<PERSON> (se não for mais necessária nesta página)
// $conn->close();
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <title>Painel de Assistência - FixFácil</title>
    <!-- Meta Tags para Responsividade -->
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <!-- Incluindo Bootstrap CSS (Versão 4.5.2) -->
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <!-- Font Awesome para ícones -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.1/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css?family=Poppins:300,400,500,600,700&display=swap" rel="stylesheet">
    <!-- Estilos personalizados -->
    <style>
        body {
            font-family: 'Poppins', sans-serif;
            background-color: #f7f9fc;
            margin-bottom: 60px; /* Espaço para a navbar inferior */
            color: #495057;
        }
        /* Navbar */
        .navbar {
            background-color: #fff;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .navbar-brand img {
            width: 150px;
        }
        .navbar-nav .nav-link {
            color: #495057 !important;
            font-weight: 500;
            margin-right: 15px;
        }
        .navbar-nav .nav-link.active {
            color: #007BFF !important;
        }
        .navbar-nav .nav-link:hover {
            color: #0056b3 !important;
        }
        /* Conteúdo Principal */
        .main-content {
            padding: 80px 20px 20px; /* Ajuste o padding-top para evitar sobreposição com a navbar fixa */
        }
        .welcome {
            margin-bottom: 40px;
        }
        .welcome h2 {
            font-weight: 600;
            color: #343a40;
        }
        .card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.05);
            background-color: #fff;
            padding: 30px;
            margin-bottom: 20px;
        }
        /* Footer */
        footer.footer {
            background-color: #fff;
            padding: 20px 0;
            position: fixed;
            width: 100%;
            bottom: 0;
            box-shadow: 0 -2px 4px rgba(0,0,0,0.1);
        }
        footer.footer span {
            color: #6c757d;
        }
        /* Navbar Inferior (Mobile) */
        .footer-nav {
            position: fixed;
            bottom: 0;
            width: 100%;
            background-color: #fff;
            box-shadow: 0 -2px 5px rgba(0,0,0,0.1);
            z-index: 1000;
        }
        .footer-nav .nav-link {
            color: #6c757d;
            text-align: center;
            padding: 10px 0;
            font-size: 12px;
        }
        .footer-nav .nav-link.active {
            color: #007BFF;
        }
        .footer-nav .nav-link i {
            font-size: 20px;
        }
        @media (min-width: 768px) {
            .footer-nav {
                display: none;
            }
        }
    </style>
</head>
<body>
    <!-- Cabeçalho -->
    <nav class="navbar navbar-expand-lg navbar-light fixed-top">
       
        <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarNavAssistencia" 
                aria-controls="navbarNavAssistencia" aria-expanded="false" aria-label="Alternar navegação">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse justify-content-end" id="navbarNavAssistencia">
           <ul class="navbar-nav">
                <li class="nav-item">
                    <a class="nav-link active" href="home.php"><i class="fas fa-home"></i> Painel</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="solicitacoes.php"><i class="fas fa-envelope"></i> Solicitações</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="meumarktplace.php"><i class="fas fa-store"></i> Marketplace</a> <!-- Corrigido o link e ícone -->
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="solicitar_pecas.php"><i class="fas fa-plus"></i> Solicitação Peças</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="propostas_enviadas.php"><i class="fas fa-paper-plane"></i> Propostas Enviadas</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="reparos_em_andamento.php"><i class="fas fa-tools"></i> Reparos em Andamento</a>
                </li>
                 <li class="nav-item">
                    <a class="nav-link " href="carteira.php"><i class="fas fa-wallet"></i> Carteira</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="perfil.php"><i class="fas fa-user-circle"></i> Perfil</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="../logout.php"><i class="fas fa-sign-out-alt"></i> Sair</a>
                </li>
            </ul>
        </div>
    </nav>
    <!-- Conteúdo Principal -->
    <div class="container main-content">
        <div class="welcome text-center">
            <h2>Bem-vindo, <?php echo htmlspecialchars($nome_usuario); ?>!</h2>
            <p class="text-muted">Este é o seu painel de assistência técnica.</p>
        </div>

        <!-- Cards com Informações e Ações -->
        <div class="row">
            <!-- Card 1: Solicitações Pendentes -->
            <div class="col-md-4">
                <div class="card text-center">
                    <i class="fas fa-envelope fa-3x mb-3 text-primary"></i>
                    <h5>Solicitações Pendentes</h5>
                    <p class="text-muted">Verifique as novas solicitações de reparo.</p>
                    <a href="solicitacoes.php" class="btn btn-primary btn-block">Ver Solicitações</a>
                </div>
            </div>
            <!-- Card 2: Propostas Enviadas -->
            <div class="col-md-4">
                <div class="card text-center">
                    <i class="fas fa-paper-plane fa-3x mb-3 text-success"></i>
                    <h5>Propostas Enviadas</h5>
                    <p class="text-muted">Acompanhe as propostas enviadas aos clientes.</p>
                    <a href="propostas_enviadas.php" class="btn btn-success btn-block">Ver Propostas</a>
                </div>
            </div>
            <!-- Card 3: Reparos em Andamento -->
            <div class="col-md-4">
                <div class="card text-center">
                    <i class="fas fa-tools fa-3x mb-3 text-warning"></i>
                    <h5>Reparos em Andamento</h5>
                    <p class="text-muted">Gerencie os reparos que estão em andamento.</p>
                    <a href="reparos_em_andamento.php" class="btn btn-warning btn-block">Ver Reparos</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Barra de Navegação Inferior (Mobile) -->
    <nav class="footer-nav d-md-none">
        <ul class="nav justify-content-around">
            <li class="nav-item">
                <a class="nav-link active" href="home.php">
                    <i class="fas fa-home"></i><br>Painel
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="solicitacoes.php">
                    <i class="fas fa-envelope"></i><br>Solicitações
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="propostas_enviadas.php">
                    <i class="fas fa-paper-plane"></i><br>Propostas
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="reparos_em_andamento.php">
                    <i class="fas fa-tools"></i><br>Reparos
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="solicitar_pecas.php">
                    <i class="fas fa-cogs"></i><br>Solicitar Peças
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="meus_pedidos.php">
                    <i class="fas fa-list"></i><br>Meus Pedidos
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="perfil.php">
                    <i class="fas fa-user-circle"></i><br>Perfil
                </a>
            </li>
        </ul>
    </nav>

    <!-- Rodapé -->
    <footer class="footer d-none d-md-block">
        <div class="container text-center">
            <span>&copy; <?php echo date("Y"); ?> FixFácil. Todos os direitos reservados.</span>
        </div>
    </footer>

    <!-- Incluindo Bootstrap JS e dependências -->
    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js" ></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.5.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
