<?php
/**
 * Dashboard Seguro - Sistema de Assistência
 * FixFácil Assistências
 */

// Redirecionar para a versão mobile final
header('Location: dashboard_mobile_final.php?' . $_SERVER['QUERY_STRING']);
exit();
?>

// Obter estatísticas
$stats = [];
try {
    $assistencia_id = $usuario['assistencia_id'];
    
    // Solicitações pendentes
    $result = $db->query("SELECT COUNT(*) as count FROM solicitacoes_reparo WHERE status = 'enviado' AND visivel = 1");
    $row = $result->fetch_assoc();
    $stats['solicitacoes_pendentes'] = $row ? (int)$row['count'] : 0;
    
    // Propostas enviadas
    $result = $db->query("SELECT COUNT(*) as count FROM propostas_assistencia WHERE assistencia_id = ? AND status = 'enviada'", [$assistencia_id]);
    $row = $result->fetch_assoc();
    $stats['propostas_enviadas'] = $row ? (int)$row['count'] : 0;
    
    // Reparos em andamento
    $result = $db->query("SELECT COUNT(*) as count FROM propostas_assistencia WHERE assistencia_id = ? AND status = 'Em Andamento'", [$assistencia_id]);
    $row = $result->fetch_assoc();
    $stats['reparos_andamento'] = $row ? (int)$row['count'] : 0;
    
    // Reparos concluídos este mês
    $result = $db->query("SELECT COUNT(*) as count FROM propostas_assistencia WHERE assistencia_id = ? AND status = 'Concluída' AND MONTH(data_proposta) = MONTH(CURRENT_DATE()) AND YEAR(data_proposta) = YEAR(CURRENT_DATE())", [$assistencia_id]);
    $row = $result->fetch_assoc();
    $stats['reparos_concluidos'] = $row ? (int)$row['count'] : 0;
    
    // Receita este mês
    $result = $db->query("SELECT COALESCE(SUM(preco * 0.85), 0) as receita FROM propostas_assistencia WHERE assistencia_id = ? AND status = 'Concluída' AND pago = 1 AND MONTH(data_proposta) = MONTH(CURRENT_DATE()) AND YEAR(data_proposta) = YEAR(CURRENT_DATE())", [$assistencia_id]);
    $row = $result->fetch_assoc();
    $stats['receita_mes'] = $row ? (float)$row['receita'] : 0;
    
    // Propostas aceitas
    $result = $db->query("SELECT COUNT(*) as count FROM propostas_assistencia WHERE assistencia_id = ? AND status = 'aceita'", [$assistencia_id]);
    $row = $result->fetch_assoc();
    $stats['propostas_aceitas'] = $row ? (int)$row['count'] : 0;
    
} catch (Exception $e) {
    error_log("Erro ao obter estatísticas: " . $e->getMessage());
    $stats = [
        'solicitacoes_pendentes' => 0,
        'propostas_enviadas' => 0,
        'reparos_andamento' => 0,
        'reparos_concluidos' => 0,
        'receita_mes' => 0,
        'propostas_aceitas' => 0
    ];
}

// Obter atividades recentes
$atividades = [];
try {
    $sql = "
        SELECT 
            'solicitacao' as tipo,
            id,
            CONCAT('Nova solicitação: ', marca, ' ', modelo) as descricao,
            data_solicitacao as data,
            descricao_problema as detalhes
        FROM solicitacoes_reparo 
        WHERE status = 'enviado' AND visivel = 1 
        ORDER BY data_solicitacao DESC 
        LIMIT 5
    ";
    
    $result = $db->query($sql);
    while ($row = $result->fetch_assoc()) {
        $atividades[] = $row;
    }
    
} catch (Exception $e) {
    error_log("Erro ao obter atividades: " . $e->getMessage());
    $atividades = [];
}

// Verificar se deve usar layout responsivo
$useResponsive = isset($_GET['mobile']) || 
                (isset($_SERVER['HTTP_USER_AGENT']) && 
                 preg_match('/Mobile|Android|iPhone|iPad/', $_SERVER['HTTP_USER_AGENT']));

if ($useResponsive) {
    safeRedirect('dashboard_new.php');
}
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - FixFácil Assistências</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Inter', sans-serif;
        }
        .navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: transform 0.2s;
        }
        .card:hover {
            transform: translateY(-5px);
        }
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
        }
        .activity-item {
            padding: 15px;
            border-left: 4px solid #667eea;
            margin-bottom: 10px;
            background: white;
            border-radius: 0 10px 10px 0;
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
        }
        .nav-link {
            color: rgba(255,255,255,0.8) !important;
            font-weight: 500;
        }
        .nav-link:hover {
            color: white !important;
        }
        .mobile-redirect {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand fw-bold" href="#">
                <i class="fas fa-tools me-2"></i>FixFácil Assistências
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="dashboard_new.php">
                    <i class="fas fa-mobile-alt me-1"></i>Versão Mobile
                </a>
                <a class="nav-link" href="perfil_new.php">
                    <i class="fas fa-user me-1"></i><?php echo e($usuario['nome']); ?>
                </a>
                <a class="nav-link" href="logout.php">
                    <i class="fas fa-sign-out-alt me-1"></i>Sair
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Estatísticas -->
        <div class="row mb-4">
            <div class="col-md-2">
                <div class="card stat-card">
                    <div class="card-body text-center">
                        <i class="fas fa-clock fa-2x mb-2"></i>
                        <div class="stat-number"><?php echo $stats['solicitacoes_pendentes']; ?></div>
                        <div>Pendentes</div>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card stat-card">
                    <div class="card-body text-center">
                        <i class="fas fa-paper-plane fa-2x mb-2"></i>
                        <div class="stat-number"><?php echo $stats['propostas_enviadas']; ?></div>
                        <div>Propostas</div>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card stat-card">
                    <div class="card-body text-center">
                        <i class="fas fa-cog fa-2x mb-2"></i>
                        <div class="stat-number"><?php echo $stats['reparos_andamento']; ?></div>
                        <div>Em Andamento</div>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card stat-card">
                    <div class="card-body text-center">
                        <i class="fas fa-check fa-2x mb-2"></i>
                        <div class="stat-number"><?php echo $stats['reparos_concluidos']; ?></div>
                        <div>Concluídos</div>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card stat-card">
                    <div class="card-body text-center">
                        <i class="fas fa-dollar-sign fa-2x mb-2"></i>
                        <div class="stat-number"><?php echo formatCurrency($stats['receita_mes']); ?></div>
                        <div>Receita</div>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card stat-card">
                    <div class="card-body text-center">
                        <i class="fas fa-handshake fa-2x mb-2"></i>
                        <div class="stat-number"><?php echo $stats['propostas_aceitas']; ?></div>
                        <div>Aceitas</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Ações Rápidas -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body text-center">
                        <i class="fas fa-list fa-3x text-primary mb-3"></i>
                        <h5>Solicitações</h5>
                        <p class="text-muted">Gerenciar solicitações de reparo</p>
                        <a href="solicitacoes_new.php" class="btn btn-primary">
                            <i class="fas fa-arrow-right me-1"></i>Acessar
                        </a>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body text-center">
                        <i class="fas fa-tools fa-3x text-success mb-3"></i>
                        <h5>Reparos</h5>
                        <p class="text-muted">Acompanhar reparos em andamento</p>
                        <a href="reparos_new.php" class="btn btn-primary">
                            <i class="fas fa-arrow-right me-1"></i>Acessar
                        </a>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body text-center">
                        <i class="fas fa-handshake fa-3x text-info mb-3"></i>
                        <h5>Propostas</h5>
                        <p class="text-muted">Enviar propostas de reparo</p>
                        <a href="propostas.php" class="btn btn-primary">
                            <i class="fas fa-arrow-right me-1"></i>Acessar
                        </a>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body text-center">
                        <i class="fas fa-user fa-3x text-warning mb-3"></i>
                        <h5>Perfil</h5>
                        <p class="text-muted">Configurar perfil da assistência</p>
                        <a href="perfil_new.php" class="btn btn-primary">
                            <i class="fas fa-arrow-right me-1"></i>Acessar
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Atividades Recentes -->
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-history me-2"></i>Atividades Recentes</h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($atividades)): ?>
                            <div class="text-center py-4">
                                <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                                <p class="text-muted">Nenhuma atividade recente</p>
                            </div>
                        <?php else: ?>
                            <?php foreach ($atividades as $atividade): ?>
                                <div class="activity-item">
                                    <strong><?php echo e($atividade['descricao']); ?></strong>
                                    <br>
                                    <small class="text-muted">
                                        <i class="fas fa-clock me-1"></i>
                                        <?php echo formatDate($atividade['data'], 'd/m/Y H:i'); ?>
                                    </small>
                                    <?php if (!empty($atividade['detalhes'])): ?>
                                        <br>
                                        <small><?php echo e($atividade['detalhes']); ?></small>
                                    <?php endif; ?>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Botão para versão mobile -->
    <div class="mobile-redirect">
        <a href="dashboard_new.php" class="btn btn-primary btn-lg rounded-pill">
            <i class="fas fa-mobile-alt me-2"></i>Versão Mobile
        </a>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Auto-refresh stats every 30 seconds
        setInterval(function() {
            fetch('ajax/get_stats_new.php')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Update stats numbers
                        document.querySelector('.stat-number').textContent = data.stats.solicitacoes_pendentes || 0;
                        // Add more updates as needed
                    }
                })
                .catch(error => console.error('Error:', error));
        }, 30000);
    </script>
</body>
</html>
