<?php
/**
 * Dashboard Principal - Mobile First
 * FixFácil Assistências - Sistema Novo
 */

// Redirecionar para versão mobile-first final
header("Location: dashboard_mobile_final.php?" . $_SERVER['QUERY_STRING']);
exit();

// Código mantido para referência
// Configurar relatório de erros para debug
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Iniciar sessão se não estiver iniciada
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

try {
    require_once 'config/auth.php';
    require_once 'config/database.php';
    require_once 'includes/layout.php';

    // Verificar autenticação
    $auth = getAuth();
    $auth->checkAssistenciaAuth();

    // Obter dados do usuário
    $usuario = $auth->getUsuarioLogado();
    if (!$usuario) {
        throw new Exception("Usuário não encontrado");
    }

    $plano = $auth->getPlanoInfo($usuario['id']);
    if (!$plano) {
        // Plano padrão se não encontrar
        $plano = [
            'nome' => 'Free',
            'taxa_servico' => 25.00
        ];
    }

    $db = getDatabase();
    if (!$db) {
        throw new Exception("Erro na conexão com banco de dados");
    }

} catch (Exception $e) {
    error_log("Erro no dashboard: " . $e->getMessage());
    die("Erro interno do servidor. Verifique os logs para mais detalhes.");
}

// Obter estatísticas
$stats = [];

try {
    // Verificar se assistencia_id existe
    $assistencia_id = $usuario['id'];
    
    // Estatísticas básicas
    $stmt = $db->prepare("SELECT COUNT(*) as total FROM solicitacoes_reparo WHERE status = 'enviado'");
    $stmt->execute();
    $stats['pendentes'] = $stmt->fetch()['total'] ?? 0;
    
    // Ganhos do dia
    $stmt = $db->prepare("SELECT SUM(valor) as total FROM propostas WHERE DATE(data_criacao) = CURDATE() AND status = 'aceita'");
    $stmt->execute();
    $stats['ganhos_hoje'] = $stmt->fetch()['total'] ?? 0;
    
    // Avaliação média
    $stmt = $db->prepare("SELECT AVG(avaliacao) as media FROM avaliacoes WHERE assistencia_id = ?");
    $stmt->execute([$assistencia_id]);
    $stats['avaliacao'] = round($stmt->fetch()['media'] ?? 4.5, 1);
    
    // Reparos em andamento
    $stmt = $db->prepare("SELECT COUNT(*) as total FROM propostas WHERE status = 'em_andamento' AND assistencia_id = ?");
    $stmt->execute([$assistencia_id]);
    $stats['em_andamento'] = $stmt->fetch()['total'] ?? 0;
    
    // Produtos no marketplace
    $stmt = $db->prepare("SELECT COUNT(*) as total FROM produtos WHERE assistencia_id = ?");
    $stmt->execute([$assistencia_id]);
    $stats['produtos'] = $stmt->fetch()['total'] ?? 0;
    
    // Solicitações recentes
    $stmt = $db->prepare("
        SELECT s.*, u.nome as usuario_nome 
        FROM solicitacoes_reparo s 
        JOIN usuarios u ON s.usuario_id = u.id 
        WHERE s.status = 'enviado' 
        ORDER BY s.data_solicitacao DESC 
        LIMIT 3
    ");
    $stmt->execute();
    $solicitacoes_recentes = $stmt->fetchAll();
    
} catch (Exception $e) {
    error_log("Erro ao obter estatísticas: " . $e->getMessage());
    $stats = ['pendentes' => 0, 'ganhos_hoje' => 0, 'avaliacao' => 4.5, 'em_andamento' => 0, 'produtos' => 0];
    $solicitacoes_recentes = [];
}

// Dados da empresa
$empresa_nome = $usuario['nome'] ?? 'Assistência Técnica';
$empresa_logo = strtoupper(substr($empresa_nome, 0, 2));
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FixFácil - Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            min-height: 100vh;
            color: #1e293b;
        }

        .container {
            max-width: 414px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
            position: relative;
            overflow-x: hidden;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }

        .header {
            background: linear-gradient(135deg, #059669 0%, #065f46 100%);
            color: white;
            padding: 24px 20px 20px 20px;
            position: relative;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 20"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="0.5" fill="white" opacity="0.05"/></pattern></defs><rect width="100" height="20" fill="url(%23grain)"/></svg>');
        }

        .header-content {
            position: relative;
            z-index: 1;
        }

        .header-top {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 20px;
        }

        .company-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .company-logo {
            width: 48px;
            height: 48px;
            background: rgba(255,255,255,0.2);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            font-weight: 700;
            backdrop-filter: blur(10px);
        }

        .company-details h1 {
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 2px;
        }

        .company-status {
            font-size: 12px;
            opacity: 0.9;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .status-indicator {
            width: 8px;
            height: 8px;
            background: #10b981;
            border-radius: 50%;
        }

        .header-actions {
            display: flex;
            gap: 8px;
        }

        .action-btn {
            width: 40px;
            height: 40px;
            background: rgba(255,255,255,0.15);
            border: none;
            border-radius: 12px;
            color: white;
            font-size: 18px;
            cursor: pointer;
            backdrop-filter: blur(10px);
            transition: all 0.2s ease;
            position: relative;
            text-decoration: none;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .action-btn:hover {
            background: rgba(255,255,255,0.25);
            color: white;
        }

        .notification-badge {
            position: absolute;
            top: -2px;
            right: -2px;
            background: #ef4444;
            color: white;
            border-radius: 50%;
            width: 16px;
            height: 16px;
            font-size: 10px;
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 12px;
        }

        .stat-card {
            background: rgba(255,255,255,0.15);
            border-radius: 12px;
            padding: 16px 12px;
            text-align: center;
            backdrop-filter: blur(10px);
        }

        .stat-number {
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 4px;
        }

        .stat-label {
            font-size: 11px;
            opacity: 0.8;
        }

        .content {
            padding: 20px;
            padding-bottom: 100px;
        }

        .quick-actions {
            background: white;
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
        }

        .section-title {
            font-size: 18px;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .actions-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
        }

        .quick-action {
            background: #f8fafc;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            padding: 16px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            text-decoration: none;
            color: inherit;
        }

        .quick-action:hover {
            border-color: #059669;
            background: #f0fdf4;
            transform: translateY(-2px);
            color: inherit;
            text-decoration: none;
        }

        .quick-action-icon {
            font-size: 28px;
            margin-bottom: 8px;
        }

        .quick-action-title {
            font-size: 14px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 2px;
        }

        .quick-action-subtitle {
            font-size: 11px;
            color: #64748b;
        }

        .action-badge {
            position: absolute;
            top: 8px;
            right: 8px;
            background: #ef4444;
            color: white;
            border-radius: 10px;
            padding: 2px 6px;
            font-size: 10px;
            font-weight: 600;
        }

        .action-badge.green {
            background: #10b981;
        }

        .action-badge.blue {
            background: #3b82f6;
        }

        .action-badge.new {
            background: #8b5cf6;
            animation: pulse 2s infinite;
        }

        .pending-requests {
            background: white;
            border-radius: 16px;
            margin-bottom: 20px;
            overflow: hidden;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
        }

        .section-header {
            padding: 20px 20px 16px 20px;
            border-bottom: 1px solid #f1f5f9;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .view-all {
            font-size: 14px;
            color: #059669;
            font-weight: 500;
            cursor: pointer;
            text-decoration: none;
        }

        .view-all:hover {
            color: #065f46;
            text-decoration: none;
        }

        .request-item {
            padding: 16px 20px;
            border-bottom: 1px solid #f1f5f9;
            display: flex;
            gap: 12px;
            cursor: pointer;
            transition: background 0.2s ease;
        }

        .request-item:last-child {
            border-bottom: none;
        }

        .request-item:hover {
            background: #f8fafc;
        }

        .request-icon {
            width: 48px;
            height: 48px;
            background: #f0fdf4;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            color: #059669;
            flex-shrink: 0;
        }

        .request-icon.urgent {
            background: #fef2f2;
            color: #ef4444;
        }

        .request-icon.premium {
            background: #fef3c7;
            color: #f59e0b;
        }

        .request-info {
            flex: 1;
        }

        .request-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 4px;
        }

        .request-title {
            font-size: 16px;
            font-weight: 600;
            color: #1e293b;
        }

        .request-time {
            font-size: 12px;
            color: #64748b;
        }

        .request-description {
            font-size: 14px;
            color: #64748b;
            margin-bottom: 8px;
            line-height: 1.3;
        }

        .request-tags {
            display: flex;
            gap: 6px;
            align-items: center;
            flex-wrap: wrap;
        }

        .request-tag {
            background: #f1f5f9;
            color: #64748b;
            border-radius: 6px;
            padding: 2px 6px;
            font-size: 10px;
            font-weight: 500;
        }

        .request-tag.device {
            background: #eff6ff;
            color: #3b82f6;
        }

        .request-tag.location {
            background: #ecfdf5;
            color: #059669;
        }

        .request-price {
            background: #059669;
            color: white;
            border-radius: 6px;
            padding: 2px 6px;
            font-size: 10px;
            font-weight: 600;
        }

        .performance-card {
            background: white;
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
        }

        .performance-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
            margin-bottom: 16px;
        }

        .performance-item {
            text-align: center;
            background: #f8fafc;
            border-radius: 12px;
            padding: 16px;
        }

        .performance-number {
            font-size: 24px;
            font-weight: 700;
            color: #059669;
            margin-bottom: 4px;
        }

        .performance-label {
            font-size: 12px;
            color: #64748b;
            margin-bottom: 2px;
        }

        .performance-change {
            font-size: 10px;
            font-weight: 600;
            color: #10b981;
        }

        .performance-change.negative {
            color: #ef4444;
        }

        .earnings-summary {
            background: linear-gradient(135deg, #f0fdf4, #dcfce7);
            border-radius: 12px;
            padding: 16px;
            text-align: center;
        }

        .earnings-amount {
            font-size: 28px;
            font-weight: 700;
            color: #059669;
            margin-bottom: 4px;
        }

        .earnings-label {
            font-size: 12px;
            color: #065f46;
            margin-bottom: 8px;
        }

        .earnings-period {
            font-size: 10px;
            color: #059669;
        }

        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 100%;
            max-width: 414px;
            background: white;
            border-top: 1px solid #e2e8f0;
            padding: 12px 20px;
            display: flex;
            justify-content: space-around;
            box-shadow: 0 -4px 20px rgba(0,0,0,0.1);
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
            cursor: pointer;
            padding: 8px;
            border-radius: 12px;
            transition: all 0.2s ease;
            position: relative;
            text-decoration: none;
            color: #64748b;
        }

        .nav-item.active {
            background: #f0fdf4;
            color: #059669;
        }

        .nav-item:hover {
            color: #059669;
            text-decoration: none;
        }

        .nav-icon {
            font-size: 20px;
        }

        .nav-label {
            font-size: 10px;
            font-weight: 500;
        }

        .nav-badge {
            position: absolute;
            top: 4px;
            right: 4px;
            background: #ef4444;
            color: white;
            border-radius: 50%;
            width: 16px;
            height: 16px;
            font-size: 10px;
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .floating-action {
            position: fixed;
            bottom: 80px;
            right: 20px;
            width: 56px;
            height: 56px;
            background: linear-gradient(135deg, #059669, #065f46);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            cursor: pointer;
            box-shadow: 0 4px 16px rgba(5, 150, 105, 0.4);
            transition: all 0.3s ease;
            z-index: 100;
            text-decoration: none;
        }

        .floating-action:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 20px rgba(5, 150, 105, 0.5);
            color: white;
            text-decoration: none;
        }

        .no-requests {
            text-align: center;
            padding: 40px 20px;
            color: #64748b;
        }

        .no-requests-icon {
            font-size: 48px;
            margin-bottom: 16px;
            opacity: 0.5;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .content > * {
            animation: fadeIn 0.6s ease;
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.02);
            }
        }

        @media (max-width: 480px) {
            .container {
                max-width: 100%;
            }
            
            .header {
                padding: 20px 15px 16px 15px;
            }
            
            .content {
                padding: 15px;
                padding-bottom: 100px;
            }
            
            .quick-actions {
                padding: 16px;
            }
            
            .section-title {
                font-size: 16px;
            }
            
            .actions-grid {
                gap: 10px;
            }
            
            .quick-action {
                padding: 12px;
            }
            
            .quick-action-icon {
                font-size: 24px;
            }
            
            .quick-action-title {
                font-size: 13px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="header-content">
                <div class="header-top">
                    <div class="company-info">
                        <div class="company-logo"><?php echo htmlspecialchars($empresa_logo); ?></div>
                        <div class="company-details">
                            <h1><?php echo htmlspecialchars($empresa_nome); ?></h1>
                            <div class="company-status">
                                <div class="status-indicator"></div>
                                <span>Online • Verificado</span>
                            </div>
                        </div>
                    </div>
                    <div class="header-actions">
                        <a href="#" class="action-btn" onclick="switchToUser()" title="Acessar área do cliente">
                            👤
                        </a>
                        <a href="logout.php" class="action-btn" title="Sair">
                            🚪
                        </a>
                        <a href="#" class="action-btn" onclick="openNotifications()">
                            🔔
                            <?php if ($stats['pendentes'] > 0): ?>
                                <div class="notification-badge"><?php echo $stats['pendentes']; ?></div>
                            <?php endif; ?>
                        </a>
                        <a href="perfil.php" class="action-btn">⚙️</a>
                    </div>
                </div>

                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number"><?php echo $stats['pendentes']; ?></div>
                        <div class="stat-label">Pendentes</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">R$ <?php echo number_format($stats['ganhos_hoje'], 0, ',', '.'); ?></div>
                        <div class="stat-label">Hoje</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number"><?php echo $stats['avaliacao']; ?>⭐</div>
                        <div class="stat-label">Avaliação</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Content -->
        <div class="content">
            <!-- Quick Actions -->
            <div class="quick-actions">
                <div class="section-title">⚡ Ações rápidas</div>
                <div class="actions-grid">
                    <a href="solicitacoes.php" class="quick-action">
                        <?php if ($stats['pendentes'] > 0): ?>
                            <div class="action-badge"><?php echo $stats['pendentes']; ?></div>
                        <?php endif; ?>
                        <div class="quick-action-icon">📋</div>
                        <div class="quick-action-title">Solicitações</div>
                        <div class="quick-action-subtitle">Novas propostas</div>
                    </a>
                    <a href="reparos.php" class="quick-action">
                        <?php if ($stats['em_andamento'] > 0): ?>
                            <div class="action-badge blue"><?php echo $stats['em_andamento']; ?></div>
                        <?php endif; ?>
                        <div class="quick-action-icon">🔧</div>
                        <div class="quick-action-title">Reparos</div>
                        <div class="quick-action-subtitle">Em andamento</div>
                    </a>
                    <a href="marketplace.php" class="quick-action">
                        <?php if ($stats['produtos'] > 0): ?>
                            <div class="action-badge green"><?php echo $stats['produtos']; ?></div>
                        <?php endif; ?>
                        <div class="quick-action-icon">🛒</div>
                        <div class="quick-action-title">Marketplace</div>
                        <div class="quick-action-subtitle">Meus produtos</div>
                    </a>
                    <a href="assistencia_virtual.php" class="quick-action">
                        <div class="action-badge new">NOVO</div>
                        <div class="quick-action-icon">🎨</div>
                        <div class="quick-action-title">Personalizar</div>
                        <div class="quick-action-subtitle">Criar plataforma</div>
                    </a>
                </div>
            </div>

            <!-- Pending Requests -->
            <div class="pending-requests">
                <div class="section-header">
                    <div class="section-title">📋 Solicitações recentes</div>
                    <a href="solicitacoes.php" class="view-all">Ver todas</a>
                </div>

                <?php if (empty($solicitacoes_recentes)): ?>
                    <div class="no-requests">
                        <div class="no-requests-icon">📋</div>
                        <p>Nenhuma solicitação no momento</p>
                    </div>
                <?php else: ?>
                    <?php foreach ($solicitacoes_recentes as $solicitacao): ?>
                        <div class="request-item" onclick="viewRequest(<?php echo $solicitacao['id']; ?>)">
                            <div class="request-icon <?php echo rand(0, 1) ? 'urgent' : ''; ?>">
                                <?php echo rand(0, 1) ? '🚨' : '📱'; ?>
                            </div>
                            <div class="request-info">
                                <div class="request-header">
                                    <div class="request-title"><?php echo htmlspecialchars($solicitacao['marca'] . ' ' . $solicitacao['modelo']); ?></div>
                                    <div class="request-time">
                                        <?php 
                                        $tempo = time() - strtotime($solicitacao['data_solicitacao']);
                                        echo $tempo < 3600 ? intval($tempo/60) . ' min' : intval($tempo/3600) . 'h';
                                        ?>
                                    </div>
                                </div>
                                <div class="request-description"><?php echo htmlspecialchars(substr($solicitacao['descricao_problema'], 0, 80) . '...'); ?></div>
                                <div class="request-tags">
                                    <div class="request-tag device"><?php echo htmlspecialchars($solicitacao['marca']); ?></div>
                                    <div class="request-tag location">Cliente: <?php echo htmlspecialchars($solicitacao['usuario_nome']); ?></div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>

            <!-- Performance Card -->
            <div class="performance-card">
                <div class="section-title">📊 Desempenho</div>
                <div class="performance-grid">
                    <div class="performance-item">
                        <div class="performance-number"><?php echo $stats['em_andamento']; ?></div>
                        <div class="performance-label">Em andamento</div>
                        <div class="performance-change">+12%</div>
                    </div>
                    <div class="performance-item">
                        <div class="performance-number"><?php echo $stats['produtos']; ?></div>
                        <div class="performance-label">Produtos</div>
                        <div class="performance-change">+5%</div>
                    </div>
                </div>
                <div class="earnings-summary">
                    <div class="earnings-amount">R$ <?php echo number_format($stats['ganhos_hoje'], 0, ',', '.'); ?></div>
                    <div class="earnings-label">Faturamento de hoje</div>
                    <div class="earnings-period">Últimas 24 horas</div>
                </div>
            </div>
        </div>

        <!-- Bottom Navigation -->
        <div class="bottom-nav">
            <a href="dashboard.php" class="nav-item active">
                <div class="nav-icon">🏠</div>
                <div class="nav-label">Início</div>
            </a>
            <a href="solicitacoes.php" class="nav-item">
                <div class="nav-icon">📋</div>
                <div class="nav-label">Solicitações</div>
                <?php if ($stats['pendentes'] > 0): ?>
                    <div class="nav-badge"><?php echo $stats['pendentes']; ?></div>
                <?php endif; ?>
            </a>
            <a href="reparos.php" class="nav-item">
                <div class="nav-icon">🔧</div>
                <div class="nav-label">Reparos</div>
            </a>
            <a href="marketplace.php" class="nav-item">
                <div class="nav-icon">🛒</div>
                <div class="nav-label">Loja</div>
            </a>
            <a href="perfil.php" class="nav-item">
                <div class="nav-icon">👤</div>
                <div class="nav-label">Perfil</div>
            </a>
        </div>

        <!-- Floating Action Button -->
        <a href="solicitacoes.php" class="floating-action" title="Nova solicitação">
            ➕
        </a>
    </div>

    <script>
        function viewRequest(id) {
            window.location.href = 'detalhes_solicitacao.php?id=' + id;
        }

        function openNotifications() {
            // Implementar modal de notificações
            alert('Notificações em desenvolvimento');
        }

        function switchToUser() {
            window.location.href = '../usuario/dashboard.php';
        }

        // Atualizar dados em tempo real
        setInterval(function() {
            // Implementar atualização via AJAX
            fetch('ajax/get_stats.php')
                .then(response => response.json())
                .then(data => {
                    // Atualizar estatísticas
                    document.querySelector('.stats-grid .stat-number').textContent = data.pendentes;
                    // Atualizar badges
                    if (data.pendentes > 0) {
                        document.querySelector('.notification-badge').textContent = data.pendentes;
                    }
                })
                .catch(error => console.error('Erro ao atualizar:', error));
        }, 30000); // Atualizar a cada 30 segundos

        // Adicionar animações
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.quick-action, .request-item');
            cards.forEach(card => {
                card.addEventListener('click', function() {
                    this.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        this.style.transform = '';
                    }, 150);
                });
            });
        });
    </script>
</body>
</html>
    $assistencia_id = $usuario['assistencia_id'] ?? null;
    if (!$assistencia_id) {
        throw new Exception("ID da assistência não encontrado");
    }

    // Solicitações pendentes (todas as solicitações com status 'enviado')
    $result = $db->query("SELECT COUNT(*) as count FROM solicitacoes_reparo WHERE status = 'enviado' AND visivel = 1");
    $row = $result->fetch_assoc();
    $stats['solicitacoes_pendentes'] = $row ? (int)$row['count'] : 0;

    // Propostas enviadas por esta assistência
    $result = $db->query("SELECT COUNT(*) as count FROM propostas_assistencia WHERE assistencia_id = ? AND status = 'enviada'", [$assistencia_id]);
    $row = $result->fetch_assoc();
    $stats['propostas_enviadas'] = $row ? (int)$row['count'] : 0;

    // Reparos em andamento
    $result = $db->query("SELECT COUNT(*) as count FROM propostas_assistencia WHERE assistencia_id = ? AND status = 'Em Andamento'", [$assistencia_id]);
    $row = $result->fetch_assoc();
    $stats['reparos_andamento'] = $row ? (int)$row['count'] : 0;

    // Reparos concluídos este mês
    $result = $db->query("SELECT COUNT(*) as count FROM propostas_assistencia WHERE assistencia_id = ? AND status = 'Concluída' AND MONTH(data_proposta) = MONTH(CURRENT_DATE()) AND YEAR(data_proposta) = YEAR(CURRENT_DATE())", [$assistencia_id]);
    $row = $result->fetch_assoc();
    $stats['reparos_concluidos'] = $row ? (int)$row['count'] : 0;

    // Receita este mês (valor que a assistência recebe após desconto da taxa)
    $taxa_servico = $plano['taxa_servico'] ?? 25;
    $result = $db->query("
        SELECT COALESCE(SUM(preco * (1 - ?/100)), 0) as receita
        FROM propostas_assistencia
        WHERE assistencia_id = ? AND status = 'Concluída' AND pago = 1
        AND MONTH(data_proposta) = MONTH(CURRENT_DATE())
        AND YEAR(data_proposta) = YEAR(CURRENT_DATE())
    ", [$taxa_servico, $assistencia_id]);
    $row = $result->fetch_assoc();
    $stats['receita_mes'] = $row ? (float)$row['receita'] : 0;

    // Propostas aceitas aguardando início
    $result = $db->query("SELECT COUNT(*) as count FROM propostas_assistencia WHERE assistencia_id = ? AND status = 'aceita'", [$assistencia_id]);
    $row = $result->fetch_assoc();
    $stats['propostas_aceitas'] = $row ? (int)$row['count'] : 0;

    // Total de propostas desta assistência
    $result = $db->query("SELECT COUNT(*) as count FROM propostas_assistencia WHERE assistencia_id = ?", [$assistencia_id]);
    $row = $result->fetch_assoc();
    $stats['total_propostas'] = $row ? (int)$row['count'] : 0;

    // Mensagens não lidas (se a tabela existir)
    $result = $db->query("SHOW TABLES LIKE 'mensagens_chat'");
    if ($result->num_rows > 0) {
        $result = $db->query("
            SELECT COUNT(*) as count FROM mensagens_chat mc
            JOIN propostas_assistencia pa ON mc.proposta_id = pa.id
            WHERE pa.assistencia_id = ? AND mc.remetente_tipo = 'usuario' AND mc.lida = 0
        ", [$assistencia_id]);
        $row = $result->fetch_assoc();
        $stats['mensagens_nao_lidas'] = $row ? (int)$row['count'] : 0;
    } else {
        $stats['mensagens_nao_lidas'] = 0;
    }

} catch (Exception $e) {
    error_log("Erro ao obter estatísticas: " . $e->getMessage());
    $stats = [
        'solicitacoes_pendentes' => 0,
        'propostas_enviadas' => 0,
        'reparos_andamento' => 0,
        'reparos_concluidos' => 0,
        'receita_mes' => 0,
        'propostas_aceitas' => 0,
        'total_propostas' => 0,
        'mensagens_nao_lidas' => 0
    ];
}

// Obter atividades recentes
$atividades = [];
try {
    // Buscar solicitações recentes (últimas 3)
    $sql_solicitacoes = "
        SELECT
            'solicitacao' as tipo,
            sr.id,
            CONCAT('Nova solicitação: ', sr.marca, ' ', sr.modelo) as descricao,
            sr.data_solicitacao as data,
            sr.descricao_problema as detalhes
        FROM solicitacoes_reparo sr
        WHERE sr.status = 'enviado' AND sr.visivel = 1
        ORDER BY sr.data_solicitacao DESC
        LIMIT 3
    ";

    $result = $db->query($sql_solicitacoes);
    while ($row = $result->fetch_assoc()) {
        $atividades[] = $row;
    }

    // Buscar propostas recentes desta assistência (últimas 3)
    $sql_propostas = "
        SELECT
            'proposta' as tipo,
            pa.id,
            CONCAT('Proposta ', pa.status, ' - R$ ', FORMAT(pa.preco, 2)) as descricao,
            pa.data_proposta as data,
            CONCAT('Prazo: ', pa.prazo, ' dia(s)') as detalhes
        FROM propostas_assistencia pa
        WHERE pa.assistencia_id = ?
        ORDER BY pa.data_proposta DESC
        LIMIT 3
    ";

    $result = $db->query($sql_propostas, [$assistencia_id]);
    while ($row = $result->fetch_assoc()) {
        $atividades[] = $row;
    }

    // Ordenar todas as atividades por data (mais recente primeiro)
    usort($atividades, function($a, $b) {
        return strtotime($b['data']) - strtotime($a['data']);
    });

    // Limitar a 5 atividades mais recentes
    $atividades = array_slice($atividades, 0, 5);

} catch (Exception $e) {
    error_log("Erro ao obter atividades: " . $e->getMessage());
    $atividades = [];
}

// Inicializar layout
$layout = null;
try {
    if (!class_exists('Layout')) {
        require_once __DIR__ . '/includes/layout.php';
    }
    $layout = new Layout();
} catch (Exception $e) {
    error_log("Erro ao inicializar layout: " . $e->getMessage());
    // Redirecionar para versão móvel responsiva
    header('Location: dashboard_new.php');
    exit();
}

// Verificar se o layout foi criado com sucesso
if (!$layout) {
    header('Location: dashboard_new.php');
    exit();
}
?>

<?php $layout->renderHead("Dashboard - FixFácil Assistências"); ?>

<div class="main-wrapper">
    <?php $layout->renderSidebar('dashboard'); ?>
    
    <main class="main-content">
        <!-- Header -->
        <div class="content-header">
            <h1 class="page-title">
                <i class="fas fa-home me-3"></i>
                Dashboard
            </h1>
            <p class="page-subtitle">
                Bem-vindo de volta, <?php echo htmlspecialchars($usuario['nome']); ?>! 
                Aqui está um resumo das suas atividades.
            </p>
            
            <div class="d-flex align-items-center mt-3">
                <div class="plano-badge plano-<?php echo strtolower($plano['nome']); ?>">
                    <?php if ($plano['nome'] === 'Master'): ?>
                        <i class="fas fa-crown me-2"></i>
                    <?php elseif ($plano['nome'] === 'Premium'): ?>
                        <i class="fas fa-star me-2"></i>
                    <?php else: ?>
                        <i class="fas fa-user me-2"></i>
                    <?php endif; ?>
                    Plano <?php echo $plano['nome']; ?>
                </div>
                <div class="ms-3 text-muted">
                    Taxa de serviço: <strong><?php echo number_format($plano['taxa_servico'], 1); ?>%</strong>
                </div>
            </div>
        </div>
        
        <!-- Estatísticas -->
        <div class="row g-4 mb-4">
            <div class="col-lg-3 col-md-6">
                <div class="card h-100">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <div class="bg-warning bg-opacity-10 p-3 rounded-circle">
                                    <i class="fas fa-inbox text-warning fs-4"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h3 class="mb-1"><?php echo $stats['solicitacoes_pendentes']; ?></h3>
                                <p class="text-muted mb-0">Solicitações Disponíveis</p>
                                <small class="text-muted">Para enviar propostas</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6">
                <div class="card h-100">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <div class="bg-info bg-opacity-10 p-3 rounded-circle">
                                    <i class="fas fa-paper-plane text-info fs-4"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h3 class="mb-1"><?php echo $stats['propostas_enviadas']; ?></h3>
                                <p class="text-muted mb-0">Propostas Enviadas</p>
                                <small class="text-muted">Aguardando resposta</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6">
                <div class="card h-100">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <div class="bg-primary bg-opacity-10 p-3 rounded-circle">
                                    <i class="fas fa-tools text-primary fs-4"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h3 class="mb-1"><?php echo $stats['reparos_andamento']; ?></h3>
                                <p class="text-muted mb-0">Reparos em Andamento</p>
                                <small class="text-muted">Em execução</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6">
                <div class="card h-100">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <div class="bg-success bg-opacity-10 p-3 rounded-circle">
                                    <i class="fas fa-check-circle text-success fs-4"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h3 class="mb-1"><?php echo $stats['reparos_concluidos']; ?></h3>
                                <p class="text-muted mb-0">Concluídos este Mês</p>
                                <small class="text-muted">Finalizados</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Estatísticas Adicionais -->
        <div class="row g-4 mb-4">
            <div class="col-lg-4">
                <div class="card h-100">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <div class="bg-secondary bg-opacity-10 p-3 rounded-circle">
                                    <i class="fas fa-handshake text-secondary fs-4"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h3 class="mb-1"><?php echo $stats['propostas_aceitas']; ?></h3>
                                <p class="text-muted mb-0">Propostas Aceitas</p>
                                <small class="text-muted">Aguardando início</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-4">
                <div class="card h-100">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <div class="bg-dark bg-opacity-10 p-3 rounded-circle">
                                    <i class="fas fa-chart-bar text-dark fs-4"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h3 class="mb-1"><?php echo $stats['total_propostas']; ?></h3>
                                <p class="text-muted mb-0">Total de Propostas</p>
                                <small class="text-muted">Histórico completo</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-4">
                <div class="card h-100">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <div class="bg-purple bg-opacity-10 p-3 rounded-circle" style="background-color: rgba(139, 69, 19, 0.1) !important;">
                                    <i class="fas fa-percentage text-warning fs-4"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h3 class="mb-1"><?php echo number_format($plano['taxa_servico'], 1); ?>%</h3>
                                <p class="text-muted mb-0">Taxa de Serviço</p>
                                <small class="text-muted">Plano <?php echo $plano['nome']; ?></small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Receita e Ações Rápidas -->
        <div class="row g-4 mb-4">
            <div class="col-lg-4">
                <div class="card h-100">
                    <div class="card-body text-center">
                        <div class="bg-success bg-opacity-10 p-4 rounded-circle d-inline-flex mb-3">
                            <i class="fas fa-dollar-sign text-success fs-2"></i>
                        </div>
                        <h2 class="text-success mb-2">R$ <?php echo number_format($stats['receita_mes'], 2, ',', '.'); ?></h2>
                        <p class="text-muted mb-0">Receita este Mês</p>
                        <small class="text-muted">Após taxa de <?php echo number_format($plano['taxa_servico'], 1); ?>%</small>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-8">
                <div class="card h-100">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-bolt me-2"></i>
                            Ações Rápidas
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <a href="solicitacoes.php" class="btn btn-outline-warning w-100">
                                    <i class="fas fa-inbox me-2"></i>
                                    Ver Solicitações
                                    <?php if ($stats['solicitacoes_pendentes'] > 0): ?>
                                        <span class="badge bg-warning ms-2"><?php echo $stats['solicitacoes_pendentes']; ?></span>
                                    <?php endif; ?>
                                </a>
                            </div>
                            <div class="col-md-6">
                                <a href="propostas.php" class="btn btn-outline-info w-100">
                                    <i class="fas fa-paper-plane me-2"></i>
                                    Minhas Propostas
                                </a>
                            </div>
                            <div class="col-md-6">
                                <a href="reparos.php" class="btn btn-outline-primary w-100">
                                    <i class="fas fa-tools me-2"></i>
                                    Reparos Ativos
                                </a>
                            </div>
                            <div class="col-md-6">
                                <a href="carteira.php" class="btn btn-outline-success w-100">
                                    <i class="fas fa-wallet me-2"></i>
                                    Ver Carteira
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Atividades Recentes -->
        <?php if (!empty($atividades)): ?>
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-clock me-2"></i>
                    Atividades Recentes
                </h5>
            </div>
            <div class="card-body">
                <div class="list-group list-group-flush">
                    <?php foreach ($atividades as $atividade): ?>
                    <div class="list-group-item border-0 px-0 py-3">
                        <div class="d-flex align-items-start">
                            <div class="flex-shrink-0">
                                <?php if ($atividade['tipo'] === 'solicitacao'): ?>
                                    <div class="bg-warning bg-opacity-10 p-2 rounded-circle">
                                        <i class="fas fa-inbox text-warning"></i>
                                    </div>
                                <?php else: ?>
                                    <div class="bg-info bg-opacity-10 p-2 rounded-circle">
                                        <i class="fas fa-paper-plane text-info"></i>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <p class="mb-1 fw-medium"><?php echo htmlspecialchars($atividade['descricao']); ?></p>
                                        <?php if (isset($atividade['detalhes'])): ?>
                                            <small class="text-muted d-block"><?php echo htmlspecialchars($atividade['detalhes']); ?></small>
                                        <?php endif; ?>
                                    </div>
                                    <small class="text-muted">
                                        <?php echo date('d/m/Y H:i', strtotime($atividade['data'])); ?>
                                    </small>
                                </div>
                                <?php if ($atividade['tipo'] === 'solicitacao'): ?>
                                    <div class="mt-2">
                                        <a href="solicitacoes.php" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-eye me-1"></i>Ver Detalhes
                                        </a>
                                    </div>
                                <?php else: ?>
                                    <div class="mt-2">
                                        <a href="propostas.php" class="btn btn-sm btn-outline-info">
                                            <i class="fas fa-edit me-1"></i>Gerenciar Proposta
                                        </a>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>

                <div class="text-center mt-3">
                    <a href="solicitacoes.php" class="btn btn-outline-primary me-2">
                        <i class="fas fa-inbox me-1"></i>Ver Todas as Solicitações
                    </a>
                    <a href="propostas.php" class="btn btn-outline-info">
                        <i class="fas fa-paper-plane me-1"></i>Ver Todas as Propostas
                    </a>
                </div>
            </div>
        </div>
        <?php else: ?>
        <div class="card">
            <div class="card-body text-center py-5">
                <div class="mb-3">
                    <i class="fas fa-clock text-muted" style="font-size: 3rem;"></i>
                </div>
                <h5 class="text-muted">Nenhuma atividade recente</h5>
                <p class="text-muted mb-4">Quando houver solicitações ou propostas, elas aparecerão aqui.</p>
                <a href="solicitacoes.php" class="btn btn-primary">
                    <i class="fas fa-search me-1"></i>Buscar Solicitações
                </a>
            </div>
        </div>
        <?php endif; ?>
    </main>
</div>

<?php $layout->renderFooter(); ?>
