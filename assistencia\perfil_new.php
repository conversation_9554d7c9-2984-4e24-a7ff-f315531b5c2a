<?php
/**
 * Nova Página de Perfil - Layout Responsivo Mobile-First
 * FixFácil Assistências - Sistema Novo
 */

require_once 'config/auth.php';
require_once 'config/database.php';

// Verificar autenticação
$auth = getAuth();
$auth->checkAssistenciaAuth();

// Obter dados do usuário
$usuario = $auth->getUsuarioLogado();
$plano = $auth->getPlanoInfo($usuario['id']);
$db = getDatabase();

// Garantir que o usuário tem plano
if (!$plano) {
    $plano = [
        'nome' => 'Básico',
        'max_solicitacoes' => 5,
        'taxa_servico' => 15.00,
        'features' => ['dashboard', 'solicitacoes', 'reparos']
    ];
}

// Processar formulário
$erro = '';
$sucesso = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $acao = $_POST['acao'] ?? '';
    
    if ($acao === 'dados_pessoais') {
        $nome = trim($_POST['nome'] ?? '');
        $email = trim($_POST['email'] ?? '');
        $telefone = trim($_POST['telefone'] ?? '');
        $endereco = trim($_POST['endereco'] ?? '');
        $especialidades = trim($_POST['especialidades'] ?? '');
        $descricao = trim($_POST['descricao'] ?? '');
        
        // Validações
        if (empty($nome)) {
            $erro = 'Nome é obrigatório.';
        } elseif (empty($email) || !filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $erro = 'E-mail válido é obrigatório.';
        } elseif (empty($telefone)) {
            $erro = 'Telefone é obrigatório.';
        } else {
            try {
                // Verificar se email já existe (exceto o próprio)
                $sql = "SELECT id FROM usuarios WHERE email = ? AND id != ?";
                $result = $db->query($sql, [$email, $usuario['id']]);
                
                if ($result->fetch_assoc()) {
                    $erro = 'Este e-mail já está sendo usado por outro usuário.';
                } else {
                    // Atualizar dados
                    $sql = "UPDATE usuarios SET nome = ?, email = ?, telefone = ?, endereco = ?, especialidades = ?, descricao = ? WHERE id = ?";
                    $db->query($sql, [$nome, $email, $telefone, $endereco, $especialidades, $descricao, $usuario['id']]);
                    
                    $sucesso = 'Dados atualizados com sucesso!';
                    
                    // Atualizar dados na sessão
                    $_SESSION['usuario']['nome'] = $nome;
                    $_SESSION['usuario']['email'] = $email;
                    $_SESSION['usuario']['telefone'] = $telefone;
                    $_SESSION['usuario']['endereco'] = $endereco;
                    $_SESSION['usuario']['especialidades'] = $especialidades;
                    $_SESSION['usuario']['descricao'] = $descricao;
                    
                    $usuario = $auth->getUsuarioLogado();
                }
            } catch (Exception $e) {
                $erro = 'Erro ao atualizar dados: ' . $e->getMessage();
            }
        }
    }
    
    elseif ($acao === 'alterar_senha') {
        $senha_atual = $_POST['senha_atual'] ?? '';
        $nova_senha = $_POST['nova_senha'] ?? '';
        $confirmar_senha = $_POST['confirmar_senha'] ?? '';
        
        // Validações
        if (empty($senha_atual) || empty($nova_senha) || empty($confirmar_senha)) {
            $erro = 'Todos os campos de senha são obrigatórios.';
        } elseif ($nova_senha !== $confirmar_senha) {
            $erro = 'Nova senha e confirmação não coincidem.';
        } elseif (strlen($nova_senha) < 6) {
            $erro = 'Nova senha deve ter pelo menos 6 caracteres.';
        } else {
            try {
                // Verificar senha atual
                $sql = "SELECT senha FROM usuarios WHERE id = ?";
                $result = $db->query($sql, [$usuario['id']]);
                $user_data = $result->fetch_assoc();
                
                if (!password_verify($senha_atual, $user_data['senha'])) {
                    $erro = 'Senha atual incorreta.';
                } else {
                    // Atualizar senha
                    $nova_senha_hash = password_hash($nova_senha, PASSWORD_DEFAULT);
                    $sql = "UPDATE usuarios SET senha = ? WHERE id = ?";
                    $db->query($sql, [$nova_senha_hash, $usuario['id']]);
                    
                    $sucesso = 'Senha alterada com sucesso!';
                }
            } catch (Exception $e) {
                $erro = 'Erro ao alterar senha: ' . $e->getMessage();
            }
        }
    }
}

// Obter estatísticas do perfil
$stats = [];
try {
    $sql = "
        SELECT 
            COUNT(DISTINCT pa.id) as total_reparos,
            COUNT(DISTINCT CASE WHEN pa.status = 'Concluída' THEN pa.id END) as reparos_concluidos,
            COUNT(DISTINCT sr.id) as solicitacoes_recebidas,
            COALESCE(AVG(CASE WHEN pa.status = 'Concluída' THEN pa.avaliacao END), 0) as avaliacao_media
        FROM propostas_assistencia pa
        LEFT JOIN solicitacoes_reparo sr ON pa.solicitacao_id = sr.id
        WHERE pa.assistencia_id = ?
    ";
    
    $result = $db->query($sql, [$usuario['assistencia_id']]);
    $stats = $result->fetch_assoc();
    
} catch (Exception $e) {
    error_log("Erro ao obter estatísticas do perfil: " . $e->getMessage());
    $stats = [
        'total_reparos' => 0,
        'reparos_concluidos' => 0, 
        'solicitacoes_recebidas' => 0,
        'avaliacao_media' => 0
    ];
}

// Obter iniciais do nome para o avatar
$iniciais = '';
if (!empty($usuario['nome'])) {
    $nomes = explode(' ', $usuario['nome']);
    $iniciais = strtoupper($nomes[0][0] ?? '');
    if (count($nomes) > 1) {
        $iniciais .= strtoupper($nomes[1][0] ?? '');
    }
}
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Perfil - FixFácil Assistências</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            min-height: 100vh;
            color: #1e293b;
            line-height: 1.5;
        }

        .container {
            max-width: 414px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
            position: relative;
            overflow-x: hidden;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            padding-bottom: 100px;
        }

        /* Header */
        .header {
            background: linear-gradient(135deg, #059669 0%, #065f46 100%);
            color: white;
            padding: 24px 20px 20px 20px;
            position: relative;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 20"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="0.5" fill="white" opacity="0.05"/></pattern></defs><rect width="100" height="20" fill="url(%23grain)"/></svg>');
        }

        .header-content {
            position: relative;
            z-index: 1;
        }

        .header-top {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .page-title {
            font-size: 20px;
            font-weight: 700;
            letter-spacing: -0.025em;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .header-actions {
            display: flex;
            gap: 8px;
        }

        .action-btn {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            padding: 8px 12px;
            border-radius: 8px;
            color: white;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.2s ease;
        }

        .action-btn:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        /* Content */
        .content {
            padding: 20px;
        }

        /* Profile Header */
        .profile-header {
            display: flex;
            align-items: center;
            gap: 16px;
            background: white;
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
        }

        .company-logo {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #059669, #065f46);
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            font-weight: 700;
        }

        .company-info h2 {
            font-size: 18px;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 4px;
        }

        .company-info p {
            font-size: 14px;
            color: #64748b;
            margin-bottom: 8px;
        }

        .status-badge {
            background: #10b981;
            color: white;
            font-size: 11px;
            font-weight: 600;
            padding: 4px 8px;
            border-radius: 6px;
            display: inline-block;
        }

        /* Quick Actions */
        .quick-actions {
            background: white;
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
        }

        .section-title {
            font-size: 18px;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .actions-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
        }

        .action-card {
            background: #f8fafc;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            padding: 16px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            text-decoration: none;
            color: inherit;
        }

        .action-card:hover {
            border-color: #059669;
            background: #f0fdf4;
            transform: translateY(-2px);
        }

        .action-icon {
            font-size: 28px;
            margin-bottom: 8px;
        }

        .action-title {
            font-size: 14px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 2px;
        }

        .action-subtitle {
            font-size: 11px;
            color: #64748b;
        }

        /* Info Section */
        .info-section, .stats-section {
            background: white;
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
        }

        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
        }

        .info-item {
            background: #f8fafc;
            border-radius: 12px;
            padding: 16px;
        }

        .info-label {
            font-size: 12px;
            color: #64748b;
            margin-bottom: 4px;
        }

        .info-value {
            font-size: 14px;
            font-weight: 600;
            color: #1e293b;
        }

        /* Stats Section */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
        }

        .stat-card {
            background: #f8fafc;
            border-radius: 12px;
            padding: 16px;
            text-align: center;
        }

        .stat-number {
            font-size: 18px;
            font-weight: 700;
            color: #059669;
            margin-bottom: 4px;
        }

        .stat-label {
            font-size: 11px;
            color: #64748b;
        }

        /* Management Section */
        .management-section {
            background: white;
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
        }

        .management-list {
            display: flex;
            flex-direction: column;
            gap: 1px;
            background: #f1f5f9;
            border-radius: 12px;
            overflow: hidden;
        }

        .management-item {
            background: white;
            padding: 16px;
            display: flex;
            align-items: center;
            gap: 12px;
            cursor: pointer;
            transition: background 0.2s ease;
            text-decoration: none;
            color: inherit;
        }

        .management-item:hover {
            background: #f8fafc;
        }

        .management-icon {
            width: 40px;
            height: 40px;
            background: #f0fdf4;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            color: #059669;
        }

        .management-info {
            flex: 1;
        }

        .management-title {
            font-size: 14px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 2px;
        }

        .management-subtitle {
            font-size: 12px;
            color: #64748b;
        }

        .management-arrow {
            font-size: 16px;
            color: #94a3b8;
        }

        /* Modals */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
        }

        .modal-content {
            background: white;
            margin: 15% auto;
            padding: 0;
            border-radius: 16px;
            width: 90%;
            max-width: 500px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            background: linear-gradient(135deg, #059669 0%, #065f46 100%);
            color: white;
            padding: 20px;
            border-radius: 16px 16px 0 0;
        }

        .modal-title {
            font-size: 18px;
            font-weight: 700;
            margin: 0;
        }

        .modal-close {
            float: right;
            font-size: 24px;
            cursor: pointer;
            background: none;
            border: none;
            color: white;
            padding: 0;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-body {
            padding: 20px;
        }

        .form-group {
            margin-bottom: 16px;
        }

        .form-label {
            display: block;
            margin-bottom: 4px;
            font-weight: 600;
            color: #374151;
            font-size: 14px;
        }

        .form-input {
            width: 100%;
            padding: 12px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.2s ease;
        }

        .form-input:focus {
            outline: none;
            border-color: #059669;
        }

        .form-textarea {
            width: 100%;
            min-height: 80px;
            padding: 12px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 14px;
            font-family: inherit;
            resize: vertical;
            transition: border-color 0.2s ease;
        }

        .form-textarea:focus {
            outline: none;
            border-color: #059669;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 14px;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .btn-primary {
            background: #059669;
            color: white;
        }

        .btn-primary:hover {
            background: #047857;
        }

        .btn-secondary {
            background: #f3f4f6;
            color: #374151;
            border: 2px solid #e5e7eb;
        }

        .btn-secondary:hover {
            background: #e5e7eb;
        }

        .btn-full {
            width: 100%;
        }

        /* Alerts */
        .alert {
            padding: 12px 16px;
            border-radius: 8px;
            margin-bottom: 16px;
            font-size: 14px;
        }

        .alert-success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #a7f3d0;
        }

        .alert-error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #fecaca;
        }

        /* Menu Inferior */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            max-width: 414px;
            width: 100%;
            background: white;
            border-top: 1px solid #e2e8f0;
            display: flex;
            justify-content: space-around;
            padding: 8px 0;
            z-index: 1000;
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-decoration: none;
            color: #64748b;
            transition: color 0.2s ease;
            padding: 8px 12px;
            border-radius: 8px;
            min-width: 60px;
        }

        .nav-item:hover, .nav-item.active {
            color: #059669;
        }

        .nav-icon {
            font-size: 20px;
            margin-bottom: 4px;
        }

        .nav-label {
            font-size: 11px;
            font-weight: 500;
        }

        /* Mobile First - Melhorias para dispositivos móveis */
        @media (max-width: 480px) {
            .container {
                padding: 12px;
                padding-bottom: 100px;
            }

            .header {
                padding: 20px 16px;
                margin-bottom: 20px;
            }

            .header h1, .page-title {
                font-size: 1.8rem;
                margin-bottom: 8px;
            }

            .header-subtitle {
                font-size: 0.9rem;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 12px;
                margin-top: 16px;
            }

            .stat-card {
                padding: 16px;
            }

            .stat-number {
                font-size: 1.5rem;
            }

            .stat-label {
                font-size: 0.8rem;
            }

            .profile-section {
                padding: 20px 16px;
                margin-bottom: 16px;
            }

            .section-title {
                font-size: 1.2rem;
                margin-bottom: 16px;
            }

            .profile-info {
                gap: 12px;
            }

            .info-item {
                padding: 12px;
            }

            .info-label {
                font-size: 0.8rem;
            }

            .info-value {
                font-size: 0.9rem;
            }

            .action-buttons {
                flex-direction: column;
                gap: 12px;
            }

            .btn {
                width: 100%;
                justify-content: center;
                padding: 14px 20px;
                font-size: 0.9rem;
            }
        }

        /* Tablet */
        @media (min-width: 481px) and (max-width: 767px) {
            .container {
                padding: 16px;
                padding-bottom: 100px;
            }

            .stats-grid {
                grid-template-columns: repeat(3, 1fr);
            }

            .profile-section {
                padding: 24px 20px;
            }
        }

        /* Responsive Desktop */
        @media (min-width: 768px) {
            .container {
                max-width: 1200px;
                padding-bottom: 40px;
            }

            .header {
                padding: 40px 40px 30px 40px;
            }

            .content {
                padding: 30px 40px;
            }

            .actions-grid {
                grid-template-columns: repeat(3, 1fr);
            }

            .stats-grid {
                grid-template-columns: repeat(4, 1fr);
            }

            .bottom-nav {
                display: none;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="header-content">
                <div class="header-top">
                    <h1 class="page-title">
                        <i class="fas fa-user"></i> Perfil da Assistência
                    </h1>
                    <div class="header-actions">
                        <button class="action-btn" onclick="window.location.href='logout.php'" title="Sair">
                            <i class="fas fa-sign-out-alt"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Content -->
        <div class="content">
            <!-- Alerts -->
            <?php if ($erro): ?>
                <div class="alert alert-error">
                    <i class="fas fa-exclamation-circle"></i> <?php echo htmlspecialchars($erro); ?>
                </div>
            <?php endif; ?>

            <?php if ($sucesso): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i> <?php echo htmlspecialchars($sucesso); ?>
                </div>
            <?php endif; ?>

            <!-- Profile Header -->
            <div class="profile-header">
                <div class="company-logo"><?php echo $iniciais; ?></div>
                <div class="company-info">
                    <h2><?php echo htmlspecialchars($usuario['nome']); ?></h2>
                    <p><?php echo htmlspecialchars($usuario['especialidades'] ?? 'Assistência Técnica Especializada'); ?></p>
                    <div class="status-badge">
                        <i class="fas fa-check"></i> Verificado
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="quick-actions">
                <div class="section-title">
                    <i class="fas fa-bolt"></i> Ações Rápidas
                </div>
                <div class="actions-grid">
                    <div class="action-card" onclick="openEditProfile()">
                        <div class="action-icon">
                            <i class="fas fa-edit"></i>
                        </div>
                        <div class="action-title">Editar Perfil</div>
                        <div class="action-subtitle">Dados da empresa</div>
                    </div>
                    <div class="action-card" onclick="openChangePassword()">
                        <div class="action-icon">
                            <i class="fas fa-lock"></i>
                        </div>
                        <div class="action-title">Alterar Senha</div>
                        <div class="action-subtitle">Segurança da conta</div>
                    </div>
                    <div class="action-card" onclick="window.location.href='upgrade_plano.php'">
                        <div class="action-icon">
                            <i class="fas fa-arrow-up"></i>
                        </div>
                        <div class="action-title">Upgrade</div>
                        <div class="action-subtitle">Plano <?php echo htmlspecialchars($plano['nome']); ?></div>
                    </div>
                    <div class="action-card" onclick="window.location.href='relatorios/'">
                        <div class="action-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="action-title">Relatórios</div>
                        <div class="action-subtitle">Análise de dados</div>
                    </div>
                </div>
            </div>

            <!-- Info Section -->
            <div class="info-section">
                <div class="section-title">
                    <i class="fas fa-info-circle"></i> Informações
                </div>
                <div class="info-grid">
                    <div class="info-item">
                        <div class="info-label">E-mail</div>
                        <div class="info-value"><?php echo htmlspecialchars($usuario['email']); ?></div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Telefone</div>
                        <div class="info-value"><?php echo htmlspecialchars($usuario['telefone']); ?></div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Plano</div>
                        <div class="info-value"><?php echo htmlspecialchars($plano['nome']); ?></div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Taxa</div>
                        <div class="info-value"><?php echo number_format($plano['taxa_servico'], 1); ?>%</div>
                    </div>
                </div>
            </div>

            <!-- Stats Section -->
            <div class="stats-section">
                <div class="section-title">
                    <i class="fas fa-chart-bar"></i> Estatísticas
                </div>
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number"><?php echo $stats['total_reparos']; ?></div>
                        <div class="stat-label">Total de Reparos</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number"><?php echo $stats['reparos_concluidos']; ?></div>
                        <div class="stat-label">Concluídos</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number"><?php echo $stats['solicitacoes_recebidas']; ?></div>
                        <div class="stat-label">Solicitações</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number"><?php echo number_format($stats['avaliacao_media'], 1); ?></div>
                        <div class="stat-label">Avaliação</div>
                    </div>
                </div>
            </div>

            <!-- Management Section -->
            <div class="management-section">
                <div class="section-title">
                    <i class="fas fa-cogs"></i> Gerenciamento
                </div>
                <div class="management-list">
                    <a href="dashboard_new.php" class="management-item">
                        <div class="management-icon">
                            <i class="fas fa-home"></i>
                        </div>
                        <div class="management-info">
                            <div class="management-title">Dashboard</div>
                            <div class="management-subtitle">Visão geral dos reparos</div>
                        </div>
                        <div class="management-arrow">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </a>
                    
                    <a href="solicitacoes_new.php" class="management-item">
                        <div class="management-icon">
                            <i class="fas fa-clipboard-list"></i>
                        </div>
                        <div class="management-info">
                            <div class="management-title">Solicitações</div>
                            <div class="management-subtitle">Gerenciar solicitações</div>
                        </div>
                        <div class="management-arrow">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </a>
                    
                    <a href="reparos_new.php" class="management-item">
                        <div class="management-icon">
                            <i class="fas fa-tools"></i>
                        </div>
                        <div class="management-info">
                            <div class="management-title">Reparos</div>
                            <div class="management-subtitle">Acompanhar reparos</div>
                        </div>
                        <div class="management-arrow">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </a>
                    
                    <a href="propostas.php" class="management-item">
                        <div class="management-icon">
                            <i class="fas fa-handshake"></i>
                        </div>
                        <div class="management-info">
                            <div class="management-title">Propostas</div>
                            <div class="management-subtitle">Enviar propostas</div>
                        </div>
                        <div class="management-arrow">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Menu Inferior -->
    <div class="bottom-nav">
        <a href="dashboard_mobile_final.php" class="nav-item">
            <div class="nav-icon">🏠</div>
            <div class="nav-label">Início</div>
        </a>
        <a href="solicitacoes.php" class="nav-item">
            <div class="nav-icon">📋</div>
            <div class="nav-label">Solicitações</div>
        </a>
        <a href="reparos.php" class="nav-item">
            <div class="nav-icon">🔧</div>
            <div class="nav-label">Reparos</div>
        </a>
        <a href="propostas.php" class="nav-item">
            <div class="nav-icon">💼</div>
            <div class="nav-label">Propostas</div>
        </a>
        <a href="marketplace.php" class="nav-item">
            <div class="nav-icon">🛒</div>
            <div class="nav-label">Loja</div>
        </a>
        <a href="carteira.php" class="nav-item">
            <div class="nav-icon">💳</div>
            <div class="nav-label">Carteira</div>
        </a>
        <a href="perfil_new.php" class="nav-item active">
            <div class="nav-icon">👤</div>
            <div class="nav-label">Perfil</div>
        </a>
    </div>

    <!-- Modal Editar Perfil -->
    <div id="editProfileModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">Editar Perfil</h3>
                <button class="modal-close" onclick="closeModal('editProfileModal')">&times;</button>
            </div>
            <div class="modal-body">
                <form method="POST">
                    <input type="hidden" name="acao" value="dados_pessoais">
                    
                    <div class="form-group">
                        <label class="form-label">Nome da Empresa</label>
                        <input type="text" name="nome" class="form-input" 
                               value="<?php echo htmlspecialchars($usuario['nome']); ?>" required>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">E-mail</label>
                        <input type="email" name="email" class="form-input" 
                               value="<?php echo htmlspecialchars($usuario['email']); ?>" required>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">Telefone</label>
                        <input type="tel" name="telefone" class="form-input" 
                               value="<?php echo htmlspecialchars($usuario['telefone']); ?>" required>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">Endereço</label>
                        <input type="text" name="endereco" class="form-input" 
                               value="<?php echo htmlspecialchars($usuario['endereco'] ?? ''); ?>">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">Especialidades</label>
                        <input type="text" name="especialidades" class="form-input" 
                               value="<?php echo htmlspecialchars($usuario['especialidades'] ?? ''); ?>"
                               placeholder="Ex: iPhone, Samsung, Notebooks">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">Descrição</label>
                        <textarea name="descricao" class="form-textarea" 
                                  placeholder="Descreva sua empresa..."><?php echo htmlspecialchars($usuario['descricao'] ?? ''); ?></textarea>
                    </div>
                    
                    <button type="submit" class="btn btn-primary btn-full">
                        <i class="fas fa-save"></i> Salvar Alterações
                    </button>
                </form>
            </div>
        </div>
    </div>

    <!-- Modal Alterar Senha -->
    <div id="changePasswordModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">Alterar Senha</h3>
                <button class="modal-close" onclick="closeModal('changePasswordModal')">&times;</button>
            </div>
            <div class="modal-body">
                <form method="POST">
                    <input type="hidden" name="acao" value="alterar_senha">
                    
                    <div class="form-group">
                        <label class="form-label">Senha Atual</label>
                        <input type="password" name="senha_atual" class="form-input" required>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">Nova Senha</label>
                        <input type="password" name="nova_senha" class="form-input" required>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">Confirmar Nova Senha</label>
                        <input type="password" name="confirmar_senha" class="form-input" required>
                    </div>
                    
                    <button type="submit" class="btn btn-primary btn-full">
                        <i class="fas fa-lock"></i> Alterar Senha
                    </button>
                </form>
            </div>
        </div>
    </div>

    <script>
        function openEditProfile() {
            document.getElementById('editProfileModal').style.display = 'block';
        }

        function openChangePassword() {
            document.getElementById('changePasswordModal').style.display = 'block';
        }

        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }

        // Fechar modal clicando fora
        window.onclick = function(event) {
            const modals = document.getElementsByClassName('modal');
            for (let i = 0; i < modals.length; i++) {
                if (event.target === modals[i]) {
                    modals[i].style.display = 'none';
                }
            }
        }

        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                alert.style.transition = 'opacity 0.5s ease';
                alert.style.opacity = '0';
                setTimeout(() => alert.remove(), 500);
            });
        }, 5000);
    </script>
</body>
</html>
