<?php
/**
 * Enviar Proposta
 * FixFácil Assistências - Sistema Novo
 */

require_once 'config/auth.php';
require_once 'config/database.php';
require_once 'includes/layout.php';

// Verificar autenticação
$auth = getAuth();
$auth->checkAssistenciaAuth();

// Obter dados do usuário
$usuario = $auth->getUsuarioLogado();
$plano = $auth->getPlanoInfo($usuario['id']);
$db = getDatabase();

// Obter ID da solicitação
$solicitacao_id = $_GET['solicitacao_id'] ?? 0;

if (!$solicitacao_id) {
    header('Location: solicitacoes.php');
    exit();
}

// Obter dados da solicitação
$solicitacao = null;
try {
    $sql = "
        SELECT 
            sr.*,
            u.nome as cliente_nome,
            u.telefone as cliente_telefone,
            u.endereco as cliente_endereco
        FROM solicitacoes_reparo sr
        JOIN usuarios u ON sr.usuario_id = u.id
        WHERE sr.id = ? AND sr.status = 'enviado'
    ";
    
    $result = $db->query($sql, [$solicitacao_id]);
    $solicitacao = $result->fetch_assoc();
    
    if (!$solicitacao) {
        header('Location: solicitacoes.php');
        exit();
    }
    
    // Verificar se já enviei proposta
    $sql = "SELECT id FROM propostas_assistencia WHERE solicitacao_id = ? AND assistencia_id = ?";
    $result = $db->query($sql, [$solicitacao_id, $usuario['assistencia_id']]);
    if ($result->fetch_assoc()) {
        header('Location: detalhes_solicitacao.php?id=' . $solicitacao_id);
        exit();
    }
    
} catch (Exception $e) {
    error_log("Erro ao obter solicitação: " . $e->getMessage());
    header('Location: solicitacoes.php');
    exit();
}

// Processar envio da proposta
$erro = '';
$sucesso = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $preco = $_POST['preco'] ?? '';
    $prazo = $_POST['prazo'] ?? '';
    $observacoes = $_POST['observacoes'] ?? '';
    $tipos_peca = $_POST['tipos_peca'] ?? [];
    $retirada_expressa = isset($_POST['retirada_expressa']) ? 1 : 0;
    
    // Validações
    if (empty($preco) || !is_numeric($preco) || $preco <= 0) {
        $erro = 'Preço é obrigatório e deve ser maior que zero.';
    } elseif (empty($prazo) || !is_numeric($prazo) || $prazo <= 0) {
        $erro = 'Prazo é obrigatório e deve ser maior que zero.';
    } else {
        try {
            // Preparar observações com tipos de peça
            $observacoes_completas = $observacoes;
            if (!empty($tipos_peca)) {
                $observacoes_completas .= "\nTipo de Peça: " . implode(', ', $tipos_peca);
            }
            
            // Inserir proposta
            $sql = "
                INSERT INTO propostas_assistencia 
                (solicitacao_id, assistencia_id, preco, prazo, observacoes, retirada_expressa, status, data_proposta)
                VALUES (?, ?, ?, ?, ?, ?, 'enviada', NOW())
            ";
            
            $stmt = $db->prepare($sql);
            $stmt->bind_param('iidisi', 
                $solicitacao_id, 
                $usuario['assistencia_id'], 
                $preco, 
                $prazo, 
                $observacoes_completas, 
                $retirada_expressa
            );
            
            if ($stmt->execute()) {
                $sucesso = 'Proposta enviada com sucesso!';
                // Redirecionar após 2 segundos
                header("refresh:2;url=detalhes_solicitacao.php?id=$solicitacao_id");
            } else {
                $erro = 'Erro ao enviar proposta. Tente novamente.';
            }
            
            $stmt->close();
            
        } catch (Exception $e) {
            error_log("Erro ao enviar proposta: " . $e->getMessage());
            $erro = 'Erro interno. Tente novamente.';
        }
    }
}

// Inicializar layout
$layout = new Layout();
?>

<?php $layout->renderHead("Enviar Proposta - FixFácil Assistências"); ?>

<div class="main-wrapper">
    <?php $layout->renderSidebar('solicitacoes'); ?>
    
    <main class="main-content">
        <!-- Header -->
        <div class="content-header">
            <div class="d-flex align-items-center justify-content-between">
                <div>
                    <h1 class="page-title">
                        <i class="fas fa-paper-plane me-3"></i>
                        Enviar Proposta
                    </h1>
                    <p class="page-subtitle">
                        Solicitação #<?php echo $solicitacao['id']; ?> - 
                        <?php echo htmlspecialchars($solicitacao['marca'] . ' ' . $solicitacao['modelo']); ?>
                    </p>
                </div>
                <div>
                    <a href="detalhes_solicitacao.php?id=<?php echo $solicitacao['id']; ?>" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>
                        Voltar
                    </a>
                </div>
            </div>
        </div>
        
        <?php if ($erro): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <?php echo htmlspecialchars($erro); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>
        
        <?php if ($sucesso): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?php echo htmlspecialchars($sucesso); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>
        
        <div class="row g-4">
            <!-- Formulário de Proposta -->
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-edit me-2"></i>
                            Dados da Proposta
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label for="preco" class="form-label">
                                        Preço do Reparo <span class="text-danger">*</span>
                                    </label>
                                    <div class="input-group">
                                        <span class="input-group-text">R$</span>
                                        <input type="number" class="form-control" id="preco" name="preco" 
                                               step="0.01" min="0" required
                                               value="<?php echo htmlspecialchars($_POST['preco'] ?? ''); ?>">
                                    </div>
                                    <div class="form-text">
                                        Taxa FixFácil: <?php echo number_format($plano['taxa_servico'], 1); ?>% - 
                                        Você receberá: <span id="valor_liquido" class="fw-bold text-success">R$ 0,00</span>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <label for="prazo" class="form-label">
                                        Prazo (dias) <span class="text-danger">*</span>
                                    </label>
                                    <input type="number" class="form-control" id="prazo" name="prazo" 
                                           min="1" max="30" required
                                           value="<?php echo htmlspecialchars($_POST['prazo'] ?? ''); ?>">
                                    <div class="form-text">Prazo em dias úteis para conclusão do reparo</div>
                                </div>
                                
                                <div class="col-12">
                                    <label class="form-label">Tipo de Peça</label>
                                    <div class="row g-2">
                                        <div class="col-md-4">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" 
                                                       name="tipos_peca[]" value="Peça Original Brasil" id="peca1">
                                                <label class="form-check-label" for="peca1">
                                                    Peça Original Brasil
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" 
                                                       name="tipos_peca[]" value="Peça Original China" id="peca2">
                                                <label class="form-check-label" for="peca2">
                                                    Peça Original China
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" 
                                                       name="tipos_peca[]" value="Peça Paralela" id="peca3">
                                                <label class="form-check-label" for="peca3">
                                                    Peça Paralela
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="col-12">
                                    <label for="observacoes" class="form-label">Observações</label>
                                    <textarea class="form-control" id="observacoes" name="observacoes" 
                                              rows="4" placeholder="Descreva detalhes sobre o reparo, garantia, etc..."><?php echo htmlspecialchars($_POST['observacoes'] ?? ''); ?></textarea>
                                </div>
                                
                                <?php if ($auth->hasAccess('retirada_express_prioritaria')): ?>
                                <div class="col-12">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" 
                                               name="retirada_expressa" id="retirada_expressa" value="1">
                                        <label class="form-check-label" for="retirada_expressa">
                                            <i class="fas fa-motorcycle me-1"></i>
                                            Oferecer Retirada Express
                                        </label>
                                    </div>
                                    <div class="form-text">
                                        Disponível apenas para plano Master. Taxa adicional será calculada automaticamente.
                                    </div>
                                </div>
                                <?php endif; ?>
                                
                                <div class="col-12">
                                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                        <a href="detalhes_solicitacao.php?id=<?php echo $solicitacao['id']; ?>" 
                                           class="btn btn-outline-secondary">
                                            Cancelar
                                        </a>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-paper-plane me-2"></i>
                                            Enviar Proposta
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            
            <!-- Resumo da Solicitação -->
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-info-circle me-2"></i>
                            Resumo da Solicitação
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label text-muted">Cliente</label>
                            <p class="fw-bold"><?php echo htmlspecialchars($solicitacao['cliente_nome']); ?></p>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label text-muted">Dispositivo</label>
                            <p class="fw-bold">
                                <?php echo htmlspecialchars($solicitacao['marca'] . ' ' . $solicitacao['modelo']); ?>
                                <span class="badge bg-secondary ms-2"><?php echo htmlspecialchars($solicitacao['memoria']); ?></span>
                            </p>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label text-muted">Problema</label>
                            <p><?php echo htmlspecialchars($solicitacao['descricao_problema']); ?></p>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label text-muted">Método de Entrega</label>
                            <p>
                                <i class="fas fa-truck me-1"></i>
                                <?php echo htmlspecialchars($solicitacao['metodo_entrega']); ?>
                            </p>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label text-muted">Data da Solicitação</label>
                            <p><?php echo date('d/m/Y H:i', strtotime($solicitacao['data_solicitacao'])); ?></p>
                        </div>
                        
                        <hr>
                        
                        <div class="text-center">
                            <h6 class="text-muted">Seu Plano</h6>
                            <div class="plano-badge plano-<?php echo strtolower($plano['nome']); ?>">
                                <?php if ($plano['nome'] === 'Master'): ?>
                                    <i class="fas fa-crown me-1"></i>
                                <?php elseif ($plano['nome'] === 'Premium'): ?>
                                    <i class="fas fa-star me-1"></i>
                                <?php else: ?>
                                    <i class="fas fa-user me-1"></i>
                                <?php endif; ?>
                                <?php echo $plano['nome']; ?>
                            </div>
                            <p class="text-muted mt-2 mb-0">
                                Taxa: <?php echo number_format($plano['taxa_servico'], 1); ?>%
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>
</div>

<?php 
$extraJS = "
<script>
// Calcular valor líquido
function calcularValorLiquido() {
    const preco = parseFloat(document.getElementById('preco').value) || 0;
    const taxa = " . $plano['taxa_servico'] . ";
    const valorLiquido = preco * (1 - taxa / 100);
    
    document.getElementById('valor_liquido').textContent = 
        'R$ ' + valorLiquido.toLocaleString('pt-BR', {minimumFractionDigits: 2, maximumFractionDigits: 2});
}

document.getElementById('preco').addEventListener('input', calcularValorLiquido);

// Calcular na inicialização
calcularValorLiquido();
</script>
";

$layout->renderFooter($extraJS); 
?>
