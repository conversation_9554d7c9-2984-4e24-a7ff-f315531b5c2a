<?php
// get_detalhes_proposta.php

// Ativar exibição de erros (Desative em produção)
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Iniciar a sessão
session_start();

// Verificar autenticação e autorização
if (!isset($_SESSION['usuario_id']) || $_SESSION['tipo_usuario'] != 'assistencia') {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Não autorizado.']);
    exit();
}

$usuario_id = $_SESSION['usuario_id'];

// Verificar se os parâmetros necessários estão presentes
if (!isset($_POST['proposta_id']) || !isset($_POST['solicitacao_id'])) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Parâmetros inválidos.']);
    exit();
}

$proposta_id = intval($_POST['proposta_id']);
$solicitacao_id = intval($_POST['solicitacao_id']);

// Validar IDs
if ($proposta_id <= 0 || $solicitacao_id <= 0) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'IDs inválidos.']);
    exit();
}

// Conexão com o banco de dados
$servername = "localhost";
$username_db = "u680766645_fixfacilnew";
$password_db = "T3cn0l0g1a@";
$dbname = "u680766645_fixfacilnew";

// Criar conexão
$conn = new mysqli($servername, $username_db, $password_db, $dbname);

// Verificar conexão
if ($conn->connect_error) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Falha na conexão com o banco de dados.']);
    exit();
}

$conn->set_charset("utf8");

// Obter o assistencia_id correspondente ao usuario_id
$sql_assistencia = "SELECT id FROM assistencias_tecnicas WHERE usuario_id = ?";
$stmt_assistencia = $conn->prepare($sql_assistencia);
$stmt_assistencia->bind_param("i", $usuario_id);
$stmt_assistencia->execute();
$result_assistencia = $stmt_assistencia->get_result();

if (!($row_assistencia = $result_assistencia->fetch_assoc())) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Assistência técnica não encontrada.']);
    $stmt_assistencia->close();
    $conn->close();
    exit();
}

$assistencia_id = $row_assistencia['id'];
$stmt_assistencia->close();

// Obter detalhes da proposta
$sql_proposta = "SELECT pa.*, sr.descricao_problema, sr.dispositivo, sr.marca, sr.modelo, sr.metodo_entrega, u.nome AS nome_cliente
                 FROM propostas_assistencia pa
                 INNER JOIN solicitacoes_reparo sr ON pa.solicitacao_id = sr.id
                 INNER JOIN usuarios u ON sr.usuario_id = u.id
                 WHERE pa.id = ? AND pa.assistencia_id = ? AND pa.solicitacao_id = ?";

$stmt_proposta = $conn->prepare($sql_proposta);
$stmt_proposta->bind_param("iii", $proposta_id, $assistencia_id, $solicitacao_id);
$stmt_proposta->execute();
$result_proposta = $stmt_proposta->get_result();

if ($proposta = $result_proposta->fetch_assoc()) {
    // Preparar resposta
    $status = $proposta['status'];
    $status_class = '';
    $status_text = '';
    
    switch ($status) {
        case 'enviada':
            $status_class = 'status-badge-enviada';
            $status_text = 'Enviada';
            break;
        case 'aceita':
            $status_class = 'status-badge-aceita';
            $status_text = 'Aceita';
            break;
        case 'Em Andamento':
            $status_class = 'status-badge-andamento';
            $status_text = 'Em Andamento';
            break;
        case 'concluída':
            $status_class = 'status-badge-concluida';
            $status_text = 'Concluída';
            break;
        case 'rejeitada':
            $status_class = 'status-badge-rejeitada';
            $status_text = 'Rejeitada';
            break;
        default:
            $status_class = '';
            $status_text = $status;
            break;
    }
    
    $dados = [
        'proposta_id' => $proposta['id'],
        'preco' => number_format($proposta['preco'], 2, ',', '.'),
        'prazo' => $proposta['prazo'],
        'observacoes' => nl2br(htmlspecialchars($proposta['observacoes'])),
        'status' => $proposta['status'],
        'status_class' => $status_class,
        'status_text' => $status_text,
        'data_proposta' => date('d/m/Y H:i', strtotime($proposta['data_proposta'])),
        'nome_cliente' => htmlspecialchars($proposta['nome_cliente']),
        'dispositivo' => htmlspecialchars($proposta['dispositivo']),
        'marca' => htmlspecialchars($proposta['marca']),
        'modelo' => htmlspecialchars($proposta['modelo']),
        'metodo_entrega' => htmlspecialchars($proposta['metodo_entrega']),
        'descricao_problema' => nl2br(htmlspecialchars($proposta['descricao_problema']))
    ];

    header('Content-Type: application/json');
    echo json_encode(['success' => true, 'data' => $dados]);
} else {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Proposta não encontrada.']);
}

$stmt_proposta->close();
$conn->close();
?>