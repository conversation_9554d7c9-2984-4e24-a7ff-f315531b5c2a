<?php
/**
 * Sistema de Backup Automático
 * FixFácil Assistências - Sistema Novo
 */

require_once '../config/database.php';

class BackupAutomatico {
    private $db;
    private $backup_dir;
    private $max_backups = 30; // Manter últimos 30 backups
    
    public function __construct() {
        $this->db = getDatabase();
        $this->backup_dir = __DIR__ . '/../../backups/';
        
        // Criar diretório de backup se não existir
        if (!is_dir($this->backup_dir)) {
            mkdir($this->backup_dir, 0755, true);
        }
    }
    
    /**
     * Executar backup completo
     */
    public function executarBackup($tipo = 'automatico') {
        try {
            $timestamp = date('Y-m-d_H-i-s');
            $backup_file = $this->backup_dir . "backup_{$timestamp}.sql";
            
            // Gerar backup do banco
            $this->gerarBackupBanco($backup_file);
            
            // Comprimir backup
            $backup_compressed = $this->comprimirBackup($backup_file);
            
            // Registrar backup no banco
            $this->registrarBackup($backup_compressed, $tipo);
            
            // Limpar backups antigos
            $this->limparBackupsAntigos();
            
            // Enviar para armazenamento externo (opcional)
            $this->enviarParaNuvem($backup_compressed);
            
            return [
                'success' => true,
                'arquivo' => basename($backup_compressed),
                'tamanho' => filesize($backup_compressed),
                'data' => date('Y-m-d H:i:s')
            ];
            
        } catch (Exception $e) {
            error_log("Erro no backup: " . $e->getMessage());
            return [
                'success' => false,
                'erro' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Gerar backup do banco de dados
     */
    private function gerarBackupBanco($arquivo) {
        $conn = $this->db->getConnection();
        
        // Obter lista de tabelas
        $tabelas = [];
        $result = $conn->query("SHOW TABLES");
        while ($row = $result->fetch_array()) {
            $tabelas[] = $row[0];
        }
        
        $backup_content = "-- FixFácil Backup\n";
        $backup_content .= "-- Data: " . date('Y-m-d H:i:s') . "\n";
        $backup_content .= "-- Versão: 2.0\n\n";
        
        $backup_content .= "SET FOREIGN_KEY_CHECKS=0;\n\n";
        
        foreach ($tabelas as $tabela) {
            $backup_content .= $this->backupTabela($tabela);
        }
        
        $backup_content .= "SET FOREIGN_KEY_CHECKS=1;\n";
        
        file_put_contents($arquivo, $backup_content);
    }
    
    /**
     * Fazer backup de uma tabela específica
     */
    private function backupTabela($tabela) {
        $conn = $this->db->getConnection();
        $backup = "\n-- Tabela: $tabela\n";
        
        // Estrutura da tabela
        $result = $conn->query("SHOW CREATE TABLE `$tabela`");
        $row = $result->fetch_array();
        $backup .= "DROP TABLE IF EXISTS `$tabela`;\n";
        $backup .= $row[1] . ";\n\n";
        
        // Dados da tabela
        $result = $conn->query("SELECT * FROM `$tabela`");
        
        if ($result->num_rows > 0) {
            $backup .= "INSERT INTO `$tabela` VALUES\n";
            $first = true;
            
            while ($row = $result->fetch_array(MYSQLI_NUM)) {
                if (!$first) {
                    $backup .= ",\n";
                }
                
                $backup .= "(";
                for ($i = 0; $i < count($row); $i++) {
                    if ($i > 0) $backup .= ", ";
                    
                    if ($row[$i] === null) {
                        $backup .= "NULL";
                    } else {
                        $backup .= "'" . $conn->real_escape_string($row[$i]) . "'";
                    }
                }
                $backup .= ")";
                $first = false;
            }
            $backup .= ";\n\n";
        }
        
        return $backup;
    }
    
    /**
     * Comprimir backup
     */
    private function comprimirBackup($arquivo) {
        $arquivo_comprimido = $arquivo . '.gz';
        
        $fp_in = fopen($arquivo, 'rb');
        $fp_out = gzopen($arquivo_comprimido, 'wb9');
        
        while (!feof($fp_in)) {
            gzwrite($fp_out, fread($fp_in, 8192));
        }
        
        fclose($fp_in);
        gzclose($fp_out);
        
        // Remover arquivo original
        unlink($arquivo);
        
        return $arquivo_comprimido;
    }
    
    /**
     * Registrar backup no banco
     */
    private function registrarBackup($arquivo, $tipo) {
        try {
            $sql = "
                INSERT INTO backups_sistema 
                (nome_arquivo, tamanho, tipo, data_criacao, status)
                VALUES (?, ?, ?, NOW(), 'concluido')
            ";
            
            $this->db->query($sql, [
                basename($arquivo),
                filesize($arquivo),
                $tipo
            ]);
        } catch (Exception $e) {
            // Se a tabela não existir, criar
            $this->criarTabelaBackups();
            
            // Tentar novamente
            $this->db->query($sql, [
                basename($arquivo),
                filesize($arquivo),
                $tipo
            ]);
        }
    }
    
    /**
     * Criar tabela de backups se não existir
     */
    private function criarTabelaBackups() {
        $sql = "
            CREATE TABLE IF NOT EXISTS backups_sistema (
                id INT AUTO_INCREMENT PRIMARY KEY,
                nome_arquivo VARCHAR(255) NOT NULL,
                tamanho BIGINT NOT NULL,
                tipo ENUM('automatico', 'manual') DEFAULT 'automatico',
                data_criacao DATETIME NOT NULL,
                status ENUM('concluido', 'erro') DEFAULT 'concluido',
                observacoes TEXT,
                INDEX idx_data_criacao (data_criacao)
            )
        ";
        
        $this->db->query($sql);
    }
    
    /**
     * Limpar backups antigos
     */
    private function limparBackupsAntigos() {
        try {
            // Obter backups antigos
            $sql = "
                SELECT nome_arquivo 
                FROM backups_sistema 
                ORDER BY data_criacao DESC 
                LIMIT $this->max_backups, 999999
            ";
            
            $result = $this->db->query($sql);
            
            while ($row = $result->fetch_assoc()) {
                $arquivo = $this->backup_dir . $row['nome_arquivo'];
                if (file_exists($arquivo)) {
                    unlink($arquivo);
                }
            }
            
            // Remover registros antigos do banco
            $sql = "
                DELETE FROM backups_sistema 
                WHERE id NOT IN (
                    SELECT id FROM (
                        SELECT id FROM backups_sistema 
                        ORDER BY data_criacao DESC 
                        LIMIT $this->max_backups
                    ) as temp
                )
            ";
            
            $this->db->query($sql);
            
        } catch (Exception $e) {
            error_log("Erro ao limpar backups antigos: " . $e->getMessage());
        }
    }
    
    /**
     * Enviar backup para nuvem (opcional)
     */
    private function enviarParaNuvem($arquivo) {
        // Aqui você pode implementar integração com:
        // - Google Drive API
        // - Dropbox API
        // - AWS S3
        // - FTP remoto
        
        try {
            // Exemplo de envio por FTP
            /*
            $ftp_server = "backup.exemplo.com";
            $ftp_user = "usuario";
            $ftp_pass = "senha";
            
            $connection = ftp_connect($ftp_server);
            ftp_login($connection, $ftp_user, $ftp_pass);
            ftp_put($connection, basename($arquivo), $arquivo, FTP_BINARY);
            ftp_close($connection);
            */
            
        } catch (Exception $e) {
            error_log("Erro ao enviar backup para nuvem: " . $e->getMessage());
        }
    }
    
    /**
     * Restaurar backup
     */
    public function restaurarBackup($nome_arquivo) {
        try {
            $arquivo = $this->backup_dir . $nome_arquivo;
            
            if (!file_exists($arquivo)) {
                throw new Exception("Arquivo de backup não encontrado");
            }
            
            // Descomprimir se necessário
            if (pathinfo($arquivo, PATHINFO_EXTENSION) === 'gz') {
                $arquivo_temp = $this->descomprimirBackup($arquivo);
            } else {
                $arquivo_temp = $arquivo;
            }
            
            // Executar SQL
            $sql_content = file_get_contents($arquivo_temp);
            $this->executarSQL($sql_content);
            
            // Limpar arquivo temporário
            if ($arquivo_temp !== $arquivo) {
                unlink($arquivo_temp);
            }
            
            return ['success' => true, 'message' => 'Backup restaurado com sucesso'];
            
        } catch (Exception $e) {
            return ['success' => false, 'erro' => $e->getMessage()];
        }
    }
    
    /**
     * Descomprimir backup
     */
    private function descomprimirBackup($arquivo) {
        $arquivo_temp = str_replace('.gz', '', $arquivo);
        
        $fp_in = gzopen($arquivo, 'rb');
        $fp_out = fopen($arquivo_temp, 'wb');
        
        while (!gzeof($fp_in)) {
            fwrite($fp_out, gzread($fp_in, 8192));
        }
        
        gzclose($fp_in);
        fclose($fp_out);
        
        return $arquivo_temp;
    }
    
    /**
     * Executar comandos SQL
     */
    private function executarSQL($sql_content) {
        $conn = $this->db->getConnection();
        
        // Dividir em comandos individuais
        $commands = explode(';', $sql_content);
        
        foreach ($commands as $command) {
            $command = trim($command);
            if (!empty($command) && !preg_match('/^--/', $command)) {
                $conn->query($command);
            }
        }
    }
    
    /**
     * Listar backups disponíveis
     */
    public function listarBackups() {
        try {
            $sql = "
                SELECT 
                    nome_arquivo,
                    tamanho,
                    tipo,
                    data_criacao,
                    status
                FROM backups_sistema 
                ORDER BY data_criacao DESC
            ";
            
            $result = $this->db->query($sql);
            $backups = [];
            
            while ($row = $result->fetch_assoc()) {
                $row['tamanho_formatado'] = $this->formatarTamanho($row['tamanho']);
                $backups[] = $row;
            }
            
            return $backups;
            
        } catch (Exception $e) {
            return [];
        }
    }
    
    /**
     * Formatar tamanho do arquivo
     */
    private function formatarTamanho($bytes) {
        $units = ['B', 'KB', 'MB', 'GB'];
        $factor = floor((strlen($bytes) - 1) / 3);
        return sprintf("%.2f %s", $bytes / pow(1024, $factor), $units[$factor]);
    }
}

// Executar backup se chamado diretamente
if (basename($_SERVER['PHP_SELF']) === 'backup_automatico.php') {
    $backup = new BackupAutomatico();
    $resultado = $backup->executarBackup();
    
    if ($resultado['success']) {
        echo "Backup realizado com sucesso: " . $resultado['arquivo'] . "\n";
    } else {
        echo "Erro no backup: " . $resultado['erro'] . "\n";
    }
}
?>
