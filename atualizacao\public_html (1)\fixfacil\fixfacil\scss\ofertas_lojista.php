<?php
session_start();
require_once 'db.php';

$mensagem = '';

// Verificar se uma oferta foi confirmada ou excluída
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    if (isset($_POST['confirmar'])) {
        $oferta_id = $_POST['oferta_id'];

        try {
            // <PERSON>car detalhes da oferta
            $queryOferta = "SELECT * FROM lances WHERE id = :oferta_id";
            $stmtOferta = $pdo->prepare($queryOferta);
            $stmtOferta->bindParam(':oferta_id', $oferta_id, PDO::PARAM_INT);
            $stmtOferta->execute();
            $oferta = $stmtOferta->fetch(PDO::FETCH_ASSOC);

            // Criar registro na tabela compras
            $queryCompra = "INSERT INTO compras (lojista_id, fornecedor_id, produto_id, quantidade, valor_pedido, data_programada) 
                            VALUES (:lojista_id, :fornecedor_id, :produto_id, 1, :valor_pedido, :data_programada)";
            $stmtCompra = $pdo->prepare($queryCompra);
            $stmtCompra->bindParam(':lojista_id', $oferta['lojista_id'], PDO::PARAM_INT);
            $stmtCompra->bindParam(':fornecedor_id', $oferta['fornecedor_id'], PDO::PARAM_INT);
            $stmtCompra->bindParam(':produto_id', $oferta['produto_id'], PDO::PARAM_INT);
            $stmtCompra->bindParam(':valor_pedido', $oferta['valor_lance'], PDO::PARAM_STR);
            $data_programada = date('Y-m-d', strtotime('+1 week')); // Exemplo: pedido para daqui a uma semana
            $stmtCompra->bindParam(':data_programada', $data_programada, PDO::PARAM_STR);
            $stmtCompra->execute();

            // Atualizar status da oferta para 'confirmado'
            $queryUpdate = "UPDATE lances SET status_oferta = 'confirmado' WHERE id = :oferta_id";
            $stmtUpdate = $pdo->prepare($queryUpdate);
            $stmtUpdate->bindParam(':oferta_id', $oferta_id, PDO::PARAM_INT);
            $stmtUpdate->execute();

            $mensagem = 'Oferta confirmada e pedido criado com sucesso!';
        } catch (PDOException $e) {
            $mensagem = 'Erro ao confirmar a oferta: ' . $e->getMessage();
        }
    }

    // ... (restante do código para excluir ofertas e mostrar as ofertas pendentes)
}

// Buscar ofertas pendentes do lojista
$query = "SELECT l.*, p.nome as produto_nome FROM lances l 
          INNER JOIN produtos p ON l.produto_id = p.id 
          WHERE l.lojista_id = :lojista_id AND l.status_oferta = 'pendente'";
$stmt = $pdo->prepare($query);
$stmt->bindParam(':lojista_id', $_SESSION['user_id'], PDO::PARAM_INT);
$stmt->execute();
$ofertas = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <title>Ofertas - Lojista</title>
</head>
<body>
    <h1>Ofertas Pendentes</h1>
    
    <?php if ($mensagem): ?>
        <p><?= $mensagem ?></p>
    <?php endif; ?>

    <table>
        <thead>
            <tr>
                <th>Produto</th>
                <th>Valor</th>
                <th>Data do Lance</th>
                <th>Ação</th>
            </tr>
        </thead>
        <tbody>
            <?php foreach ($ofertas as $oferta): ?>
                <tr>
                    <td><?= $oferta['produto_nome'] ?></td>
                    <td><?= number_format($oferta['valor_lance'], 2, ',', '.') ?></td>
                    <td><?= date('d/m/Y H:i:s', strtotime($oferta['data_lance'])) ?></td>
                    <td>
                        <form method="post" action="">
                            <input type="hidden" name="oferta_id" value="<?= $oferta['id'] ?>">
                            <button type="submit" name="confirmar">Confirmar</button>
                        </form>
                    </td>
                </tr>
            <?php endforeach; ?>
        </tbody>
    </table>
</body>
</html>
