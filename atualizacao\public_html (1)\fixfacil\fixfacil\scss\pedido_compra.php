<?php

include_once 'db.php';



if ($_SERVER["REQUEST_METHOD"] == "POST") {

    $produto_id = $_POST['produto_id'];

    $quantidade = $_POST['quantidade'];

    $endereco = $_POST['endereco'];



    $query = "INSERT INTO compras (lojista_id, produto_id, quantidade, endereco) VALUES (?, ?, ?, ?)";

    $stmt = $conn->prepare($query);

    $lojista_id = 1; // Supondo que o ID do lojista seja 1, modifique conforme necessário

    $stmt->bind_param("iiis", $lojista_id, $produto_id, $quantidade, $endereco);



    if ($stmt->execute()) {

        echo "Pedido realizado com sucesso!";

    } else {

        echo "Erro ao processar o pedido.";

    }

    $stmt->close();

}

?>



<!DOCTYPE html>

<html lang="pt-br">

<head>

    <meta charset="UTF-8">

    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <title>Fazer Pedido de Compra</title>

</head>

<body>

    <h2>Fazer Pedido de Compra</h2>

    <form method="post" action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>">

        Produto ID: <input type="text" name="produto_id" required><br>

        Quantidade: <input type="number" name="quantidade" required><br>

        Endereço de Entrega: <input type="text" name="endereco"><br>

        <input type="submit" value="Enviar Pedido">

    </form>

</body>

</html>



<?php

$conn->close();

?>

