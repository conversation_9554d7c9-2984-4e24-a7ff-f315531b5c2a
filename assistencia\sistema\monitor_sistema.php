<?php
/**
 * Sistema de Monitoramento e Logs
 * FixFácil Assistências - Sistema Novo
 */

require_once '../config/auth.php';
require_once '../config/database.php';

class MonitorSistema {
    private $db;
    private $log_dir;
    
    public function __construct() {
        $this->db = getDatabase();
        $this->log_dir = __DIR__ . '/../../logs/';
        
        // Criar diretório de logs se não existir
        if (!is_dir($this->log_dir)) {
            mkdir($this->log_dir, 0755, true);
        }
        
        $this->criarTabelasLogs();
    }
    
    /**
     * Registrar atividade do usuário
     */
    public function registrarAtividade($usuario_id, $tipo, $descricao, $dados_extras = null) {
        try {
            $sql = "
                INSERT INTO logs_atividades 
                (usuario_id, tipo, descricao, dados_extras, ip_address, user_agent, data_atividade)
                VALUES (?, ?, ?, ?, ?, ?, NOW())
            ";
            
            $this->db->query($sql, [
                $usuario_id,
                $tipo,
                $descricao,
                $dados_extras ? json_encode($dados_extras) : null,
                $_SERVER['REMOTE_ADDR'] ?? 'unknown',
                $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
            ]);
            
        } catch (Exception $e) {
            $this->registrarErro('log_atividade', $e->getMessage());
        }
    }
    
    /**
     * Registrar erro do sistema
     */
    public function registrarErro($tipo, $mensagem, $arquivo = null, $linha = null, $contexto = null) {
        try {
            // Log no banco
            $sql = "
                INSERT INTO logs_erros 
                (tipo, mensagem, arquivo, linha, contexto, ip_address, data_erro)
                VALUES (?, ?, ?, ?, ?, ?, NOW())
            ";
            
            $this->db->query($sql, [
                $tipo,
                $mensagem,
                $arquivo,
                $linha,
                $contexto ? json_encode($contexto) : null,
                $_SERVER['REMOTE_ADDR'] ?? 'unknown'
            ]);
            
            // Log em arquivo
            $this->escreverLogArquivo('erro', [
                'tipo' => $tipo,
                'mensagem' => $mensagem,
                'arquivo' => $arquivo,
                'linha' => $linha,
                'contexto' => $contexto,
                'timestamp' => date('Y-m-d H:i:s')
            ]);
            
        } catch (Exception $e) {
            // Fallback para log de arquivo se banco falhar
            error_log("Erro no sistema de logs: " . $e->getMessage());
        }
    }
    
    /**
     * Monitorar performance do sistema
     */
    public function registrarPerformance($pagina, $tempo_execucao, $memoria_usada, $queries_executadas = 0) {
        try {
            $sql = "
                INSERT INTO logs_performance 
                (pagina, tempo_execucao, memoria_usada, queries_executadas, data_acesso)
                VALUES (?, ?, ?, ?, NOW())
            ";
            
            $this->db->query($sql, [
                $pagina,
                $tempo_execucao,
                $memoria_usada,
                $queries_executadas
            ]);
            
        } catch (Exception $e) {
            $this->registrarErro('performance_log', $e->getMessage());
        }
    }
    
    /**
     * Obter estatísticas do sistema
     */
    public function obterEstatisticas($periodo = '24h') {
        try {
            $where_clause = $this->obterClausulaPeriodo($periodo);
            
            // Atividades por tipo
            $sql = "
                SELECT 
                    tipo,
                    COUNT(*) as quantidade
                FROM logs_atividades 
                WHERE $where_clause
                GROUP BY tipo
                ORDER BY quantidade DESC
            ";
            $atividades = $this->db->query($sql)->fetch_all(MYSQLI_ASSOC);
            
            // Erros por tipo
            $sql = "
                SELECT 
                    tipo,
                    COUNT(*) as quantidade
                FROM logs_erros 
                WHERE $where_clause
                GROUP BY tipo
                ORDER BY quantidade DESC
            ";
            $erros = $this->db->query($sql)->fetch_all(MYSQLI_ASSOC);
            
            // Performance média
            $sql = "
                SELECT 
                    AVG(tempo_execucao) as tempo_medio,
                    AVG(memoria_usada) as memoria_media,
                    AVG(queries_executadas) as queries_media,
                    COUNT(*) as total_acessos
                FROM logs_performance 
                WHERE $where_clause
            ";
            $performance = $this->db->query($sql)->fetch_assoc();
            
            // Usuários mais ativos
            $sql = "
                SELECT 
                    u.nome,
                    COUNT(la.id) as atividades
                FROM logs_atividades la
                JOIN usuarios u ON la.usuario_id = u.id
                WHERE $where_clause
                GROUP BY la.usuario_id
                ORDER BY atividades DESC
                LIMIT 10
            ";
            $usuarios_ativos = $this->db->query($sql)->fetch_all(MYSQLI_ASSOC);
            
            return [
                'atividades' => $atividades,
                'erros' => $erros,
                'performance' => $performance,
                'usuarios_ativos' => $usuarios_ativos
            ];
            
        } catch (Exception $e) {
            return null;
        }
    }
    
    /**
     * Verificar saúde do sistema
     */
    public function verificarSaudeSystem() {
        $saude = [
            'status' => 'ok',
            'problemas' => [],
            'metricas' => []
        ];
        
        try {
            // Verificar conexão com banco
            $this->db->query("SELECT 1");
            $saude['metricas']['banco'] = 'ok';
        } catch (Exception $e) {
            $saude['status'] = 'erro';
            $saude['problemas'][] = 'Erro de conexão com banco de dados';
            $saude['metricas']['banco'] = 'erro';
        }
        
        // Verificar espaço em disco
        $espaco_livre = disk_free_space('/');
        $espaco_total = disk_total_space('/');
        $percentual_usado = (($espaco_total - $espaco_livre) / $espaco_total) * 100;
        
        $saude['metricas']['disco'] = [
            'percentual_usado' => round($percentual_usado, 2),
            'espaco_livre' => $this->formatarBytes($espaco_livre)
        ];
        
        if ($percentual_usado > 90) {
            $saude['status'] = 'alerta';
            $saude['problemas'][] = 'Espaço em disco baixo (' . round($percentual_usado, 1) . '% usado)';
        }
        
        // Verificar uso de memória
        $memoria_usada = memory_get_usage(true);
        $memoria_limite = ini_get('memory_limit');
        $memoria_limite_bytes = $this->converterParaBytes($memoria_limite);
        
        $saude['metricas']['memoria'] = [
            'usada' => $this->formatarBytes($memoria_usada),
            'limite' => $memoria_limite,
            'percentual' => round(($memoria_usada / $memoria_limite_bytes) * 100, 2)
        ];
        
        // Verificar logs de erro recentes
        $sql = "SELECT COUNT(*) as erros FROM logs_erros WHERE data_erro >= DATE_SUB(NOW(), INTERVAL 1 HOUR)";
        $result = $this->db->query($sql);
        $erros_recentes = $result->fetch_assoc()['erros'];
        
        $saude['metricas']['erros_recentes'] = $erros_recentes;
        
        if ($erros_recentes > 50) {
            $saude['status'] = 'alerta';
            $saude['problemas'][] = "Muitos erros na última hora ($erros_recentes)";
        }
        
        // Verificar performance
        $sql = "
            SELECT AVG(tempo_execucao) as tempo_medio 
            FROM logs_performance 
            WHERE data_acesso >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
        ";
        $result = $this->db->query($sql);
        $tempo_medio = $result->fetch_assoc()['tempo_medio'];
        
        $saude['metricas']['performance'] = [
            'tempo_medio' => round($tempo_medio, 3)
        ];
        
        if ($tempo_medio > 2.0) {
            $saude['status'] = 'alerta';
            $saude['problemas'][] = "Performance degradada (tempo médio: " . round($tempo_medio, 2) . "s)";
        }
        
        return $saude;
    }
    
    /**
     * Limpar logs antigos
     */
    public function limparLogsAntigos($dias = 30) {
        try {
            $tabelas = ['logs_atividades', 'logs_erros', 'logs_performance'];
            $total_removidos = 0;
            
            foreach ($tabelas as $tabela) {
                $campo_data = $tabela === 'logs_erros' ? 'data_erro' : 
                             ($tabela === 'logs_performance' ? 'data_acesso' : 'data_atividade');
                
                $sql = "DELETE FROM $tabela WHERE $campo_data < DATE_SUB(NOW(), INTERVAL ? DAY)";
                $this->db->query($sql, [$dias]);
                $total_removidos += $this->db->getConnection()->affected_rows;
            }
            
            // Limpar arquivos de log antigos
            $this->limparArquivosLog($dias);
            
            return $total_removidos;
            
        } catch (Exception $e) {
            $this->registrarErro('limpeza_logs', $e->getMessage());
            return 0;
        }
    }
    
    /**
     * Criar tabelas de logs se não existirem
     */
    private function criarTabelasLogs() {
        $sqls = [
            "CREATE TABLE IF NOT EXISTS logs_atividades (
                id INT AUTO_INCREMENT PRIMARY KEY,
                usuario_id INT,
                tipo VARCHAR(50) NOT NULL,
                descricao TEXT NOT NULL,
                dados_extras JSON,
                ip_address VARCHAR(45),
                user_agent TEXT,
                data_atividade DATETIME NOT NULL,
                INDEX idx_usuario (usuario_id),
                INDEX idx_tipo (tipo),
                INDEX idx_data (data_atividade)
            )",
            
            "CREATE TABLE IF NOT EXISTS logs_erros (
                id INT AUTO_INCREMENT PRIMARY KEY,
                tipo VARCHAR(50) NOT NULL,
                mensagem TEXT NOT NULL,
                arquivo VARCHAR(255),
                linha INT,
                contexto JSON,
                ip_address VARCHAR(45),
                data_erro DATETIME NOT NULL,
                INDEX idx_tipo (tipo),
                INDEX idx_data (data_erro)
            )",
            
            "CREATE TABLE IF NOT EXISTS logs_performance (
                id INT AUTO_INCREMENT PRIMARY KEY,
                pagina VARCHAR(255) NOT NULL,
                tempo_execucao DECIMAL(10,6) NOT NULL,
                memoria_usada BIGINT NOT NULL,
                queries_executadas INT DEFAULT 0,
                data_acesso DATETIME NOT NULL,
                INDEX idx_pagina (pagina),
                INDEX idx_data (data_acesso)
            )"
        ];
        
        foreach ($sqls as $sql) {
            try {
                $this->db->query($sql);
            } catch (Exception $e) {
                error_log("Erro ao criar tabela de logs: " . $e->getMessage());
            }
        }
    }
    
    /**
     * Escrever log em arquivo
     */
    private function escreverLogArquivo($tipo, $dados) {
        $arquivo = $this->log_dir . $tipo . '_' . date('Y-m-d') . '.log';
        $linha = date('Y-m-d H:i:s') . ' - ' . json_encode($dados) . "\n";
        file_put_contents($arquivo, $linha, FILE_APPEND | LOCK_EX);
    }
    
    /**
     * Obter cláusula WHERE para período
     */
    private function obterClausulaPeriodo($periodo) {
        switch ($periodo) {
            case '1h':
                return "data_atividade >= DATE_SUB(NOW(), INTERVAL 1 HOUR)";
            case '24h':
                return "data_atividade >= DATE_SUB(NOW(), INTERVAL 1 DAY)";
            case '7d':
                return "data_atividade >= DATE_SUB(NOW(), INTERVAL 7 DAY)";
            case '30d':
                return "data_atividade >= DATE_SUB(NOW(), INTERVAL 30 DAY)";
            default:
                return "data_atividade >= DATE_SUB(NOW(), INTERVAL 1 DAY)";
        }
    }
    
    /**
     * Limpar arquivos de log antigos
     */
    private function limparArquivosLog($dias) {
        $arquivos = glob($this->log_dir . '*.log');
        $data_limite = time() - ($dias * 24 * 60 * 60);
        
        foreach ($arquivos as $arquivo) {
            if (filemtime($arquivo) < $data_limite) {
                unlink($arquivo);
            }
        }
    }
    
    /**
     * Formatar bytes
     */
    private function formatarBytes($bytes) {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        $factor = floor((strlen($bytes) - 1) / 3);
        return sprintf("%.2f %s", $bytes / pow(1024, $factor), $units[$factor]);
    }
    
    /**
     * Converter string de memória para bytes
     */
    private function converterParaBytes($val) {
        $val = trim($val);
        $last = strtolower($val[strlen($val)-1]);
        $val = (int)$val;
        
        switch($last) {
            case 'g': $val *= 1024;
            case 'm': $val *= 1024;
            case 'k': $val *= 1024;
        }
        
        return $val;
    }
}

// Função global para registrar atividades
function registrarAtividade($tipo, $descricao, $dados_extras = null) {
    static $monitor = null;
    
    if ($monitor === null) {
        $monitor = new MonitorSistema();
    }
    
    $usuario_id = $_SESSION['usuario_id'] ?? null;
    if ($usuario_id) {
        $monitor->registrarAtividade($usuario_id, $tipo, $descricao, $dados_extras);
    }
}

// Função global para registrar erros
function registrarErro($tipo, $mensagem, $contexto = null) {
    static $monitor = null;
    
    if ($monitor === null) {
        $monitor = new MonitorSistema();
    }
    
    $backtrace = debug_backtrace();
    $arquivo = $backtrace[0]['file'] ?? null;
    $linha = $backtrace[0]['line'] ?? null;
    
    $monitor->registrarErro($tipo, $mensagem, $arquivo, $linha, $contexto);
}
?>
