{"paging": {"total": 102, "limit": 5, "offset": 0}, "results": [{"metadata": {}, "corporation_id": null, "operation_type": "regular_payment", "point_of_interaction": {"transaction_data": {"transaction_id": null, "bank_info": {"is_same_bank_account_owner": null, "payer": {"account_id": "9224382036864776100", "id": null, "long_name": null}, "collector": {"account_id": "9325372136954476022", "account_holder_name": "<PERSON><PERSON>", "long_name": null, "transfer_account_id": null}}, "bank_transfer_id": null, "financial_institution": null, "qr_code": "00020126330014br.gov.bcb.pix0111129183146095204000053039865406100.005802BR5911Tourmania856006Santos62230519mpqrinter**********6304D89B"}, "type": "OPENPLATFORM", "application_data": {"name": null, "version": null}}, "fee_details": [], "notification_url": null, "date_approved": null, "money_release_schema": null, "payer": {"first_name": null, "last_name": null, "email": "<EMAIL>", "identification": {"number": "********", "type": "DNI"}, "phone": {"area_code": null, "number": null, "extension": null}, "type": null, "entity_type": null, "id": "*********"}, "transaction_details": {"transaction_id": null, "total_paid_amount": 100, "acquirer_reference": null, "installment_amount": 0, "bank_transfer_id": null, "financial_institution": null, "net_received_amount": 0, "overpaid_amount": 0, "external_resource_url": null, "payable_deferral_period": null, "payment_method_reference_id": null}, "statement_descriptor": null, "call_for_authorize_id": null, "installments": 1, "pos_id": null, "external_reference": null, "date_of_expiration": "2021-09-14T08:52:51.000-04:00", "charges_details": [], "id": **********, "payment_type_id": "bank_transfer", "order": {}, "counter_currency": null, "brand_id": null, "status_detail": "expired", "differential_pricing_id": null, "additional_info": {"authentication_code": null, "nsu_processadora": null, "available_balance": null}, "live_mode": false, "marketplace_owner": null, "card": {}, "integrator_id": null, "status": "cancelled", "transaction_amount_refunded": 0, "transaction_amount": 100, "description": "description", "money_release_date": null, "merchant_number": null, "refunds": [], "callback_url": null, "authorization_code": null, "captured": true, "collector_id": *********, "merchant_account_id": null, "taxes_amount": 0, "date_last_updated": "2021-09-14T08:55:41.000-04:00", "coupon_amount": 0, "store_id": null, "date_created": "2021-09-13T08:52:52.000-04:00", "acquirer_reconciliation": [], "sponsor_id": null, "shipping_amount": 0, "issuer_id": null, "payment_method_id": "pix", "binary_mode": false, "platform_id": null, "deduction_schema": null, "processing_mode": "aggregator", "currency_id": "BRL", "shipping_cost": 0}, {"metadata": {}, "corporation_id": null, "operation_type": "regular_payment", "point_of_interaction": {"transaction_data": {"transaction_id": null, "bank_info": {"is_same_bank_account_owner": null, "payer": {"account_id": null, "id": null, "long_name": null}, "collector": {"account_id": null, "account_holder_name": "<PERSON><PERSON>", "long_name": null, "transfer_account_id": null}}, "bank_transfer_id": null, "financial_institution": null, "qr_code": "00020126330014br.gov.bcb.pix0111129183146095204000053039865406100.005802BR5911Tourmania856006Santos62230519mpqrinter**********630413D4"}, "type": "OPENPLATFORM", "application_data": {"name": null, "version": null}}, "fee_details": [], "notification_url": "https://seu-site.com.br/webhooks", "date_approved": null, "money_release_schema": null, "payer": {"first_name": null, "last_name": null, "email": "<EMAIL>", "identification": {"number": "********", "type": "DNI"}, "phone": {"area_code": null, "number": null, "extension": null}, "type": null, "entity_type": null, "id": "*********"}, "transaction_details": {"transaction_id": null, "total_paid_amount": 100, "acquirer_reference": null, "installment_amount": 0, "bank_transfer_id": null, "financial_institution": null, "net_received_amount": 0, "overpaid_amount": 0, "external_resource_url": null, "payable_deferral_period": null, "payment_method_reference_id": null}, "statement_descriptor": null, "call_for_authorize_id": null, "installments": 1, "pos_id": null, "external_reference": "194a5325-87f7-4c47-8a6d-9caaa2e326ee", "date_of_expiration": "2021-09-14T08:53:55.000-04:00", "charges_details": [], "id": **********, "payment_type_id": "bank_transfer", "order": {}, "counter_currency": null, "brand_id": null, "status_detail": "expired", "differential_pricing_id": null, "additional_info": {"authentication_code": null, "ip_address": "127.0.0.1", "nsu_processadora": null, "available_balance": null, "items": [{"quantity": "1", "category_id": "categoryId", "picture_url": "pictureUrl", "description": "description", "id": "id", "title": "title", "unit_price": "100.0"}], "payer": {"address": {"street_number": "1234", "street_name": "streetName", "zip_code": "0000"}, "registration_date": "2021-09-13T09:40:50.333-0300", "phone": {"number": "0000-0000", "area_code": "000"}, "last_name": "User", "first_name": "Test"}, "shipments": {"receiver_address": {"street_number": "1234", "floor": "floor", "apartment": "apartment", "street_name": "streetName", "zip_code": "0000"}}}, "live_mode": false, "marketplace_owner": null, "card": {}, "integrator_id": null, "status": "cancelled", "transaction_amount_refunded": 0, "transaction_amount": 100, "description": "description", "money_release_date": null, "merchant_number": null, "refunds": [], "callback_url": null, "authorization_code": null, "captured": true, "collector_id": *********, "merchant_account_id": null, "taxes_amount": 0, "date_last_updated": "2021-09-14T08:55:54.000-04:00", "coupon_amount": 0, "store_id": null, "date_created": "2021-09-13T08:53:55.000-04:00", "acquirer_reconciliation": [], "sponsor_id": null, "shipping_amount": 0, "issuer_id": null, "payment_method_id": "pix", "binary_mode": false, "platform_id": null, "deduction_schema": null, "processing_mode": "aggregator", "currency_id": "BRL", "shipping_cost": 0}, {"metadata": {}, "corporation_id": null, "operation_type": "regular_payment", "point_of_interaction": {}, "fee_details": [{"amount": 4.99, "fee_payer": "collector", "type": "mercadopago_fee"}], "notification_url": "https://seu-site.com.br/webhooks", "date_approved": "2021-09-13T08:57:18.000-04:00", "money_release_schema": null, "payer": {"first_name": null, "last_name": null, "email": "<EMAIL>", "identification": {"number": "********", "type": "DNI"}, "phone": {"area_code": null, "number": null, "extension": null}, "type": null, "entity_type": null, "id": "*********"}, "transaction_details": {"total_paid_amount": 100, "acquirer_reference": null, "installment_amount": 100, "financial_institution": null, "net_received_amount": 95.01, "overpaid_amount": 0, "external_resource_url": null, "payable_deferral_period": null, "payment_method_reference_id": null}, "statement_descriptor": "STATEMENTDESC", "call_for_authorize_id": null, "installments": 1, "pos_id": null, "external_reference": "85dd4f90-edfe-4b7b-bed5-efb368ca148e", "date_of_expiration": null, "charges_details": [], "id": **********, "payment_type_id": "credit_card", "order": {}, "counter_currency": null, "brand_id": null, "status_detail": "partially_refunded", "differential_pricing_id": null, "additional_info": {"authentication_code": null, "ip_address": "127.0.0.1", "nsu_processadora": null, "available_balance": null, "items": [{"quantity": "1", "category_id": "categoryId", "picture_url": "pictureUrl", "description": "description", "id": "id", "title": "title", "unit_price": "100.0"}], "payer": {"address": {"street_number": "1234", "street_name": "streetName", "zip_code": "0000"}, "registration_date": "2021-09-13T09:57:10.755-0300", "phone": {"number": "0000-0000", "area_code": "000"}, "last_name": "User", "first_name": "Test"}, "shipments": {"receiver_address": {"street_number": "1234", "floor": "floor", "apartment": "apartment", "street_name": "streetName", "zip_code": "0000"}}}, "live_mode": false, "marketplace_owner": null, "card": {"first_six_digits": "503143", "expiration_year": 2031, "date_created": "2021-09-13T08:57:17.000-04:00", "expiration_month": 11, "id": null, "cardholder": {"identification": {"number": "***********", "type": "CPF"}, "name": "APRO"}, "last_four_digits": "6351", "date_last_updated": "2021-09-13T08:57:17.000-04:00"}, "integrator_id": null, "status": "approved", "transaction_amount_refunded": 1, "transaction_amount": 100, "description": "description", "money_release_date": "2021-09-13T08:57:18.000-04:00", "merchant_number": null, "refunds": [{"reason": null, "amount": 1, "metadata": {}, "date_created": "2021-09-13T08:57:35.000-04:00", "external_refund_id": null, "source": {"name": "<PERSON><PERSON><PERSON>", "id": "*********", "type": "collector"}, "unique_sequence_number": null, "refund_mode": "standard", "payment_id": **********, "adjustment_amount": 0, "id": **********, "status": "approved"}], "authorization_code": null, "captured": true, "collector_id": *********, "merchant_account_id": null, "taxes_amount": 0, "date_last_updated": "2021-09-13T08:57:35.000-04:00", "coupon_amount": 0, "store_id": null, "date_created": "2021-09-13T08:57:18.000-04:00", "acquirer_reconciliation": [], "sponsor_id": null, "shipping_amount": 0, "issuer_id": "24", "payment_method_id": "master", "binary_mode": false, "platform_id": null, "deduction_schema": null, "processing_mode": "aggregator", "currency_id": "BRL", "shipping_cost": 0}, {"metadata": {}, "corporation_id": null, "operation_type": "regular_payment", "point_of_interaction": {}, "fee_details": [], "notification_url": "https://seu-site.com.br/webhooks", "date_approved": null, "money_release_schema": null, "payer": {"first_name": null, "last_name": null, "email": "<EMAIL>", "identification": {"number": "********", "type": "DNI"}, "phone": {"area_code": null, "number": null, "extension": null}, "type": null, "entity_type": null, "id": "*********"}, "transaction_details": {"total_paid_amount": 100, "acquirer_reference": null, "installment_amount": 100, "financial_institution": null, "net_received_amount": 0, "overpaid_amount": 0, "external_resource_url": null, "payable_deferral_period": null, "payment_method_reference_id": null}, "statement_descriptor": "STATEMENTDESC", "call_for_authorize_id": null, "installments": 1, "pos_id": null, "external_reference": "a862a6c5-af2c-43f1-9743-f535ba571c49", "date_of_expiration": null, "charges_details": [], "id": 1241012304, "payment_type_id": "credit_card", "order": {}, "counter_currency": null, "brand_id": null, "status_detail": "expired", "differential_pricing_id": null, "additional_info": {"authentication_code": null, "ip_address": "127.0.0.1", "nsu_processadora": null, "available_balance": null, "items": [{"quantity": "1", "category_id": "categoryId", "picture_url": "pictureUrl", "description": "description", "id": "id", "title": "title", "unit_price": "100.0"}], "payer": {"address": {"street_number": "1234", "street_name": "streetName", "zip_code": "0000"}, "registration_date": "2021-09-13T09:57:50.619-0300", "phone": {"number": "0000-0000", "area_code": "000"}, "last_name": "User", "first_name": "Test"}, "shipments": {"receiver_address": {"street_number": "1234", "floor": "floor", "apartment": "apartment", "street_name": "streetName", "zip_code": "0000"}}}, "live_mode": false, "marketplace_owner": null, "card": {"first_six_digits": "503143", "expiration_year": 2031, "date_created": "2021-09-13T08:57:51.000-04:00", "expiration_month": 11, "id": null, "cardholder": {"identification": {"number": "***********", "type": "CPF"}, "name": "APRO"}, "last_four_digits": "6351", "date_last_updated": "2021-09-13T08:57:51.000-04:00"}, "integrator_id": null, "status": "cancelled", "transaction_amount_refunded": 0, "transaction_amount": 100, "description": "description", "money_release_date": null, "merchant_number": null, "refunds": [], "authorization_code": null, "captured": false, "collector_id": *********, "merchant_account_id": null, "taxes_amount": 0, "date_last_updated": "2021-09-18T09:00:06.000-04:00", "coupon_amount": 0, "store_id": null, "date_created": "2021-09-13T08:57:52.000-04:00", "acquirer_reconciliation": [], "sponsor_id": null, "shipping_amount": 0, "issuer_id": "24", "payment_method_id": "master", "binary_mode": false, "platform_id": null, "deduction_schema": null, "processing_mode": "aggregator", "currency_id": "BRL", "shipping_cost": 0}, {"metadata": {}, "corporation_id": null, "operation_type": "regular_payment", "point_of_interaction": {}, "fee_details": [], "notification_url": "https://seu-site.com.br/webhooks", "date_approved": null, "money_release_schema": null, "payer": {"first_name": null, "last_name": null, "email": "<EMAIL>", "identification": {"number": "********", "type": "DNI"}, "phone": {"area_code": null, "number": null, "extension": null}, "type": null, "entity_type": null, "id": "*********"}, "transaction_details": {"total_paid_amount": 100, "acquirer_reference": null, "installment_amount": 100, "financial_institution": null, "net_received_amount": 0, "overpaid_amount": 0, "external_resource_url": null, "payable_deferral_period": null, "payment_method_reference_id": null}, "statement_descriptor": "STATEMENTDESC", "call_for_authorize_id": null, "installments": 1, "pos_id": null, "external_reference": "56ed682f-dcc5-49e8-8d9e-1a52ac421e7f", "date_of_expiration": null, "charges_details": [], "id": 1241012311, "payment_type_id": "credit_card", "order": {}, "counter_currency": null, "brand_id": null, "status_detail": "expired", "differential_pricing_id": null, "additional_info": {"authentication_code": null, "ip_address": "127.0.0.1", "nsu_processadora": null, "available_balance": null, "items": [{"quantity": "1", "category_id": "categoryId", "picture_url": "pictureUrl", "description": "description", "id": "id", "title": "title", "unit_price": "100.0"}], "payer": {"address": {"street_number": "1234", "street_name": "streetName", "zip_code": "0000"}, "registration_date": "2021-09-13T09:57:58.400-0300", "phone": {"number": "0000-0000", "area_code": "000"}, "last_name": "User", "first_name": "Test"}, "shipments": {"receiver_address": {"street_number": "1234", "floor": "floor", "apartment": "apartment", "street_name": "streetName", "zip_code": "0000"}}}, "live_mode": false, "marketplace_owner": null, "card": {"first_six_digits": "503143", "expiration_year": 2031, "date_created": "2021-09-13T08:57:59.000-04:00", "expiration_month": 11, "id": null, "cardholder": {"identification": {"number": "***********", "type": "CPF"}, "name": "APRO"}, "last_four_digits": "6351", "date_last_updated": "2021-09-13T08:57:59.000-04:00"}, "integrator_id": null, "status": "cancelled", "transaction_amount_refunded": 0, "transaction_amount": 100, "description": "description", "money_release_date": null, "merchant_number": null, "refunds": [], "authorization_code": null, "captured": false, "collector_id": *********, "merchant_account_id": null, "taxes_amount": 0, "date_last_updated": "2021-09-18T09:00:16.000-04:00", "coupon_amount": 0, "store_id": null, "date_created": "2021-09-13T08:58:00.000-04:00", "acquirer_reconciliation": [], "sponsor_id": null, "shipping_amount": 0, "issuer_id": "24", "payment_method_id": "master", "binary_mode": false, "platform_id": null, "deduction_schema": null, "processing_mode": "aggregator", "currency_id": "BRL", "shipping_cost": 0}]}