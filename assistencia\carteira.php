<?php
/**
 * <PERSON><PERSON><PERSON> Principal - Mobile First
 * FixFácil Assistências - Padrão HTML
 */

// Configurar relatório de erros para debug
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Iniciar sessão se não estiver iniciada
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Verificar se o usuário está logado
if (!isset($_SESSION['usuario_id']) || $_SESSION['tipo_usuario'] !== 'assistencia') {
    header('Location: ../login.php');
    exit();
}

// Configuração de banco de dados
$host = "localhost";
$username = "u680766645_fixfacilnew";
$password = "T3cn0l0g1a@";
$database = "u680766645_fixfacilnew";

$mysqli = new mysqli($host, $username, $password, $database);

if ($mysqli->connect_error) {
    die("Erro de conexão: " . $mysqli->connect_error);
}

// Obter dados do usuário logado
$usuario_id = $_SESSION['usuario_id'];
$usuario = null;
$plano = null;

try {
    // Buscar dados do usuário
    $sql = "SELECT u.id, u.nome, u.email, u.telefone, u.plano_id, at.id as assistencia_id 
            FROM usuarios u 
            LEFT JOIN assistencias_tecnicas at ON u.id = at.usuario_id 
            WHERE u.id = ?";
    $stmt = $mysqli->prepare($sql);
    $stmt->bind_param("i", $usuario_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $usuario = $result->fetch_assoc();
    
    // Buscar informações do plano
    if ($usuario && $usuario['plano_id']) {
        $sql = "SELECT * FROM planos WHERE id = ?";
        $stmt = $mysqli->prepare($sql);
        $stmt->bind_param("i", $usuario['plano_id']);
        $stmt->execute();
        $result = $stmt->get_result();
        $plano = $result->fetch_assoc();
    }
    
    // Plano padrão se não encontrar
    if (!$plano) {
        $plano = [
            'nome' => 'Free',
            'taxa_servico' => 25.00
        ];
    }
    
} catch (Exception $e) {
    error_log("Erro ao buscar dados do usuário: " . $e->getMessage());
    $usuario = ['nome' => 'Usuário', 'email' => '', 'assistencia_id' => null];
    $plano = ['nome' => 'Free', 'taxa_servico' => 25.00];
}

// Filtros
$periodo = $_GET['periodo'] ?? 'mes_atual';

// Calcular datas baseado no período
switch ($periodo) {
    case 'mes_atual':
        $data_inicio = date('Y-m-01');
        $data_fim = date('Y-m-t');
        break;
    case 'mes_anterior':
        $data_inicio = date('Y-m-01', strtotime('first day of last month'));
        $data_fim = date('Y-m-t', strtotime('last day of last month'));
        break;
    case 'ano_atual':
        $data_inicio = date('Y-01-01');
        $data_fim = date('Y-12-31');
        break;
    default:
        $data_inicio = date('Y-m-01');
        $data_fim = date('Y-m-t');
        break;
}

// Obter estatísticas financeiras
$financeiro = [
    'receita_bruta' => 0,
    'receita_liquida' => 0,
    'taxa_fixfacil' => 0,
    'total_reparos' => 0,
    'ticket_medio' => 0,
    'pagos' => 0,
    'aguardando_pagamento' => 0,
    'em_andamento' => 0
];

try {
    if ($usuario && isset($usuario['assistencia_id'])) {
        // Estatísticas gerais
        $sql = "
            SELECT 
                COUNT(*) as total_reparos,
                COUNT(CASE WHEN status = 'Concluída' AND pago = 1 THEN 1 END) as pagos,
                COUNT(CASE WHEN status = 'Concluída' AND pago = 0 THEN 1 END) as aguardando_pagamento,
                COUNT(CASE WHEN status = 'Em Andamento' THEN 1 END) as em_andamento,
                COALESCE(SUM(CASE WHEN status = 'Concluída' THEN preco END), 0) as receita_bruta,
                COALESCE(SUM(CASE WHEN status = 'Concluída' THEN preco * (1 - ?/100) END), 0) as receita_liquida,
                COALESCE(SUM(CASE WHEN status = 'Concluída' THEN preco * (?/100) END), 0) as taxa_fixfacil
            FROM propostas_assistencia 
            WHERE assistencia_id = ? 
            AND DATE(data_proposta) BETWEEN ? AND ?
        ";
        
        $stmt = $mysqli->prepare($sql);
        $stmt->bind_param("ddiss", $plano['taxa_servico'], $plano['taxa_servico'], $usuario['assistencia_id'], $data_inicio, $data_fim);
        $stmt->execute();
        $result = $stmt->get_result();
        $stats = $result->fetch_assoc();
        
        if ($stats) {
            $financeiro = array_merge($financeiro, $stats);
            $financeiro['ticket_medio'] = $stats['total_reparos'] > 0 ? $stats['receita_bruta'] / $stats['total_reparos'] : 0;
        }
    }
} catch (Exception $e) {
    error_log("Erro ao obter estatísticas financeiras: " . $e->getMessage());
}

// Obter histórico de pagamentos recentes
$pagamentos = [];
try {
    if ($usuario && isset($usuario['assistencia_id'])) {
        $sql = "
            SELECT 
                pa.*,
                sr.dispositivo,
                sr.marca,
                sr.modelo,
                u.nome as cliente_nome,
                (pa.preco * (1 - ?/100)) as valor_recebido
            FROM propostas_assistencia pa
            JOIN solicitacoes_reparo sr ON pa.solicitacao_id = sr.id
            JOIN usuarios u ON sr.usuario_id = u.id
            WHERE pa.assistencia_id = ? 
            AND pa.status = 'Concluída'
            AND DATE(pa.data_conclusao) BETWEEN ? AND ?
            ORDER BY pa.data_conclusao DESC
            LIMIT 10
        ";
        
        $stmt = $mysqli->prepare($sql);
        $stmt->bind_param("diss", $plano['taxa_servico'], $usuario['assistencia_id'], $data_inicio, $data_fim);
        $stmt->execute();
        $result = $stmt->get_result();
        while ($row = $result->fetch_assoc()) {
            $pagamentos[] = $row;
        }
    }
} catch (Exception $e) {
    error_log("Erro ao obter pagamentos: " . $e->getMessage());
}
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FixFacil - Carteira</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            min-height: 100vh;
            color: #1e293b;
        }

        .container {
            max-width: 414px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
            position: relative;
            overflow-x: hidden;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            padding-bottom: 100px;
        }

        .header {
            background: linear-gradient(135deg, #059669 0%, #065f46 100%);
            color: white;
            padding: 24px 20px 20px 20px;
            position: relative;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 20"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="0.5" fill="white" opacity="0.05"/></pattern></defs><rect width="100" height="20" fill="url(%23grain)"/></svg>');
        }

        .header-content {
            position: relative;
            z-index: 1;
        }

        .header-top {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 20px;
        }

        .company-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .company-logo {
            width: 48px;
            height: 48px;
            background: rgba(255,255,255,0.2);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            font-weight: 700;
            backdrop-filter: blur(10px);
        }

        .company-details h1 {
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 2px;
        }

        .company-status {
            font-size: 12px;
            opacity: 0.9;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .status-indicator {
            width: 8px;
            height: 8px;
            background: #10b981;
            border-radius: 50%;
        }

        .header-actions {
            display: flex;
            gap: 8px;
        }

        .action-btn {
            width: 40px;
            height: 40px;
            background: rgba(255,255,255,0.15);
            border: none;
            border-radius: 12px;
            color: white;
            font-size: 18px;
            cursor: pointer;
            backdrop-filter: blur(10px);
            transition: all 0.2s ease;
            position: relative;
            text-decoration: none;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .action-btn:hover {
            background: rgba(255,255,255,0.25);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 12px;
            margin-top: 16px;
        }

        .stat-card {
            background: rgba(255,255,255,0.15);
            border-radius: 12px;
            padding: 16px 12px;
            text-align: center;
            backdrop-filter: blur(10px);
        }

        .stat-number {
            font-size: 18px;
            font-weight: 700;
            margin-bottom: 4px;
        }

        .stat-label {
            font-size: 10px;
            opacity: 0.9;
        }

        .content {
            padding: 20px;
        }

        .section-title {
            font-size: 18px;
            font-weight: 700;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .filter-bar {
            background: #f8fafc;
            border-radius: 12px;
            padding: 12px 16px;
            margin-bottom: 20px;
        }

        .filter-select {
            width: 100%;
            border: none;
            background: none;
            font-size: 16px;
            outline: none;
            color: #1e293b;
        }

        .earnings-summary {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            border-radius: 16px;
            padding: 24px;
            margin-bottom: 20px;
            color: white;
            text-align: center;
        }

        .earnings-amount {
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 8px;
        }

        .earnings-label {
            font-size: 14px;
            opacity: 0.9;
            margin-bottom: 4px;
        }

        .earnings-period {
            font-size: 12px;
            opacity: 0.8;
        }

        .financial-cards {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
            margin-bottom: 20px;
        }

        .financial-card {
            background: white;
            border-radius: 16px;
            padding: 16px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
            text-align: center;
        }

        .financial-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            margin: 0 auto 12px;
        }

        .financial-icon.income {
            background: #dcfce7;
            color: #059669;
        }

        .financial-icon.tax {
            background: #fef3c7;
            color: #f59e0b;
        }

        .financial-icon.pending {
            background: #dbeafe;
            color: #3b82f6;
        }

        .financial-icon.repairs {
            background: #e0e7ff;
            color: #6366f1;
        }

        .financial-value {
            font-size: 18px;
            font-weight: 700;
            margin-bottom: 4px;
            color: #1e293b;
        }

        .financial-label {
            font-size: 12px;
            color: #64748b;
        }

        .transactions-list {
            background: white;
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
        }

        .transaction-item {
            padding: 16px;
            border-bottom: 1px solid #f1f5f9;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .transaction-item:last-child {
            border-bottom: none;
        }

        .transaction-icon {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            flex-shrink: 0;
        }

        .transaction-icon.paid {
            background: #dcfce7;
            color: #059669;
        }

        .transaction-icon.pending {
            background: #fef3c7;
            color: #f59e0b;
        }

        .transaction-info {
            flex: 1;
        }

        .transaction-title {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 2px;
            color: #1e293b;
        }

        .transaction-subtitle {
            font-size: 12px;
            color: #64748b;
        }

        .transaction-amount {
            text-align: right;
        }

        .transaction-value {
            font-size: 14px;
            font-weight: 600;
            color: #059669;
        }

        .transaction-date {
            font-size: 10px;
            color: #64748b;
        }

        .empty-state {
            text-align: center;
            padding: 40px 20px;
        }

        .empty-icon {
            font-size: 48px;
            margin-bottom: 16px;
        }

        .empty-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 8px;
            color: #1e293b;
        }

        .empty-description {
            color: #64748b;
            margin-bottom: 20px;
            line-height: 1.5;
        }

        .floating-action {
            position: fixed;
            bottom: 80px;
            right: 20px;
            width: 56px;
            height: 56px;
            background: linear-gradient(135deg, #059669, #065f46);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            cursor: pointer;
            box-shadow: 0 4px 16px rgba(5, 150, 105, 0.4);
            transition: all 0.3s ease;
            z-index: 100;
            text-decoration: none;
        }

        .floating-action:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 20px rgba(5, 150, 105, 0.5);
        }

        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 100%;
            max-width: 414px;
            background: white;
            border-top: 1px solid #e2e8f0;
            padding: 12px 20px;
            display: flex;
            justify-content: space-around;
            box-shadow: 0 -4px 20px rgba(0,0,0,0.1);
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
            cursor: pointer;
            padding: 8px;
            border-radius: 12px;
            transition: all 0.2s ease;
            position: relative;
            text-decoration: none;
            color: #64748b;
        }

        .nav-item.active {
            background: #f0fdf4;
            color: #059669;
        }

        .nav-icon {
            font-size: 20px;
        }

        .nav-label {
            font-size: 10px;
            font-weight: 500;
        }

        /* Mobile First - Melhorias para dispositivos móveis */
        @media (max-width: 480px) {
            .container {
                padding: 12px;
                padding-bottom: 100px;
            }

            .header {
                padding: 20px 16px;
                margin-bottom: 20px;
            }

            .header h1 {
                font-size: 1.8rem;
                margin-bottom: 8px;
            }

            .header-subtitle {
                font-size: 0.9rem;
            }

            .balance-card {
                padding: 20px 16px;
                margin-bottom: 16px;
            }

            .balance-amount {
                font-size: 2rem;
            }

            .balance-label {
                font-size: 0.9rem;
            }

            .balance-change {
                font-size: 0.8rem;
            }

            .quick-actions {
                grid-template-columns: repeat(2, 1fr);
                gap: 12px;
                margin-bottom: 16px;
            }

            .action-card {
                padding: 16px 12px;
            }

            .action-icon {
                font-size: 1.5rem;
                margin-bottom: 8px;
            }

            .action-title {
                font-size: 0.9rem;
            }

            .action-subtitle {
                font-size: 0.7rem;
            }

            .filters-section {
                padding: 16px;
                margin-bottom: 16px;
            }

            .filter-row {
                flex-direction: column;
                gap: 12px;
            }

            .filter-group {
                width: 100%;
            }

            .filter-select, .filter-input {
                padding: 12px;
                font-size: 0.9rem;
            }

            .filter-btn {
                width: 100%;
                padding: 12px;
                font-size: 0.9rem;
            }

            .transactions-list {
                gap: 12px;
            }

            .transaction-card {
                padding: 16px;
            }

            .transaction-info {
                gap: 12px;
            }

            .transaction-icon {
                width: 40px;
                height: 40px;
                font-size: 1rem;
            }

            .transaction-title {
                font-size: 0.9rem;
            }

            .transaction-subtitle {
                font-size: 0.8rem;
            }

            .transaction-amount {
                font-size: 1rem;
            }

            .transaction-date {
                font-size: 0.7rem;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 12px;
            }

            .stat-card {
                padding: 16px;
            }

            .stat-number {
                font-size: 1.3rem;
            }

            .stat-label {
                font-size: 0.8rem;
            }
        }

        /* Tablet */
        @media (min-width: 481px) and (max-width: 767px) {
            .container {
                padding: 16px;
                padding-bottom: 100px;
            }

            .quick-actions {
                grid-template-columns: repeat(3, 1fr);
            }

            .stats-grid {
                grid-template-columns: repeat(3, 1fr);
            }

            .filter-row {
                flex-direction: row;
                flex-wrap: wrap;
            }

            .filter-group {
                flex: 1;
                min-width: 200px;
            }
        }

        /* Manter sempre formato mobile */
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="header-content">
                <div class="header-top">
                    <div class="company-info">
                        <div class="company-logo">💰</div>
                        <div class="company-details">
                            <h1>Carteira Digital</h1>
                            <div class="company-status">
                                <div class="status-indicator"></div>
                                Plano <?php echo htmlspecialchars($plano['nome']); ?>
                            </div>
                        </div>
                    </div>
                    <div class="header-actions">
                        <button class="action-btn" onclick="showFilterOptions()" title="Filtros">
                            <span>📅</span>
                        </button>
                        <button class="action-btn" onclick="openNotifications()" title="Notificações">
                            <span>🔔</span>
                        </button>
                    </div>
                </div>

                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number"><?php echo $financeiro['total_reparos']; ?></div>
                        <div class="stat-label">Reparos</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number"><?php echo $financeiro['pagos']; ?></div>
                        <div class="stat-label">Pagos</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number"><?php echo $financeiro['aguardando_pagamento']; ?></div>
                        <div class="stat-label">Pendentes</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Content -->
        <div class="content">
            <!-- Filter Bar -->
            <form method="GET" class="filter-bar">
                <select name="periodo" class="filter-select" onchange="this.form.submit()">
                    <option value="mes_atual" <?php echo $periodo === 'mes_atual' ? 'selected' : ''; ?>>
                        📅 Mês Atual
                    </option>
                    <option value="mes_anterior" <?php echo $periodo === 'mes_anterior' ? 'selected' : ''; ?>>
                        📅 Mês Anterior
                    </option>
                    <option value="ano_atual" <?php echo $periodo === 'ano_atual' ? 'selected' : ''; ?>>
                        📅 Ano Atual
                    </option>
                </select>
            </form>

            <!-- Earnings Summary -->
            <div class="earnings-summary">
                <div class="earnings-amount">R$ <?php echo number_format($financeiro['receita_liquida'], 2, ',', '.'); ?></div>
                <div class="earnings-label">Receita Líquida</div>
                <div class="earnings-period">Após taxa de <?php echo number_format($plano['taxa_servico'], 1); ?>%</div>
            </div>

            <!-- Financial Cards -->
            <div class="financial-cards">
                <div class="financial-card">
                    <div class="financial-icon income">💵</div>
                    <div class="financial-value">R$ <?php echo number_format($financeiro['receita_bruta'], 0, ',', '.'); ?></div>
                    <div class="financial-label">Receita Bruta</div>
                </div>
                <div class="financial-card">
                    <div class="financial-icon tax">📊</div>
                    <div class="financial-value">R$ <?php echo number_format($financeiro['taxa_fixfacil'], 0, ',', '.'); ?></div>
                    <div class="financial-label">Taxa FixFácil</div>
                </div>
                <div class="financial-card">
                    <div class="financial-icon pending">⏳</div>
                    <div class="financial-value"><?php echo $financeiro['aguardando_pagamento']; ?></div>
                    <div class="financial-label">Aguardando</div>
                </div>
                <div class="financial-card">
                    <div class="financial-icon repairs">🔧</div>
                    <div class="financial-value">R$ <?php echo number_format($financeiro['ticket_medio'], 0, ',', '.'); ?></div>
                    <div class="financial-label">Ticket Médio</div>
                </div>
            </div>

            <!-- Transactions -->
            <div class="section-title">💳 Histórico de Pagamentos</div>
            
            <?php if (empty($pagamentos)): ?>
                <div class="empty-state">
                    <div class="empty-icon">💳</div>
                    <div class="empty-title">Nenhum pagamento</div>
                    <div class="empty-description">
                        Não há reparos concluídos no período selecionado.
                    </div>
                </div>
            <?php else: ?>
                <div class="transactions-list">
                    <?php foreach ($pagamentos as $pagamento): ?>
                        <div class="transaction-item">
                            <div class="transaction-icon <?php echo $pagamento['pago'] ? 'paid' : 'pending'; ?>">
                                <?php echo $pagamento['pago'] ? '✅' : '⏳'; ?>
                            </div>
                            <div class="transaction-info">
                                <div class="transaction-title">
                                    <?php echo htmlspecialchars($pagamento['marca'] . ' ' . $pagamento['modelo']); ?>
                                </div>
                                <div class="transaction-subtitle">
                                    <?php echo htmlspecialchars($pagamento['cliente_nome']); ?> • 
                                    <?php echo htmlspecialchars($pagamento['dispositivo']); ?>
                                </div>
                            </div>
                            <div class="transaction-amount">
                                <div class="transaction-value">
                                    R$ <?php echo number_format($pagamento['valor_recebido'], 2, ',', '.'); ?>
                                </div>
                                <div class="transaction-date">
                                    <?php echo date('d/m/Y', strtotime($pagamento['data_conclusao'])); ?>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>

        <!-- Floating Action Button -->
        <a href="solicitacoes.php" class="floating-action" title="Nova solicitação">
            <span>+</span>
        </a>

        <!-- Bottom Navigation -->
        <div class="bottom-nav">
            <a href="dashboard_mobile_final.php" class="nav-item">
                <div class="nav-icon">🏠</div>
                <div class="nav-label">Início</div>
            </a>
            <a href="solicitacoes.php" class="nav-item">
                <div class="nav-icon">📋</div>
                <div class="nav-label">Solicitações</div>
            </a>
            <a href="reparos_new.php" class="nav-item">
                <div class="nav-icon">🔧</div>
                <div class="nav-label">Reparos</div>
            </a>
            <a href="propostas.php" class="nav-item">
                <div class="nav-icon">💼</div>
                <div class="nav-label">Propostas</div>
            </a>
            <a href="marketplace.php" class="nav-item">
                <div class="nav-icon">🛒</div>
                <div class="nav-label">Loja</div>
            </a>
            <a href="carteira.php" class="nav-item active">
                <div class="nav-icon">💳</div>
                <div class="nav-label">Carteira</div>
            </a>
        </div>

        <script>
            // Função para mostrar notificações
            function showNotification(message) {
                const existingNotification = document.querySelector('.notification');
                if (existingNotification) {
                    existingNotification.remove();
                }
                
                const notification = document.createElement('div');
                notification.className = 'notification';
                notification.style.cssText = `
                    position: fixed;
                    top: 20px;
                    left: 50%;
                    transform: translateX(-50%);
                    background: #059669;
                    color: white;
                    padding: 12px 20px;
                    border-radius: 8px;
                    font-size: 14px;
                    font-weight: 600;
                    z-index: 2000;
                    box-shadow: 0 4px 12px rgba(5, 150, 105, 0.3);
                    animation: slideDown 0.3s ease;
                    max-width: 90%;
                    text-align: center;
                `;
                notification.textContent = message;
                
                document.body.appendChild(notification);
                
                setTimeout(() => {
                    notification.style.animation = 'slideOut 0.3s ease';
                    setTimeout(() => notification.remove(), 300);
                }, 3000);
            }

            // Função para mostrar filtros
            function showFilterOptions() {
                showNotification('📅 Use o filtro acima para alterar o período');
            }

            // Função para abrir notificações
            function openNotifications() {
                const pendentes = <?php echo $financeiro['aguardando_pagamento']; ?>;
                if (pendentes > 0) {
                    showNotification(`💰 Você tem ${pendentes} pagamento(s) pendente(s)!`);
                } else {
                    showNotification('💰 Todos os pagamentos estão em dia!');
                }
            }

            // Mensagem de boas-vindas
            document.addEventListener('DOMContentLoaded', function() {
                setTimeout(() => {
                    const receita = <?php echo $financeiro['receita_liquida']; ?>;
                    if (receita > 0) {
                        showNotification(`💰 Sua receita líquida este período: R$ ${receita.toLocaleString('pt-BR', {minimumFractionDigits: 2})}`);
                    }
                }, 1000);
            });

            // Adicionar CSS das animações
            const style = document.createElement('style');
            style.textContent = `
                @keyframes slideDown {
                    from {
                        opacity: 0;
                        transform: translateX(-50%) translateY(-20px);
                    }
                    to {
                        opacity: 1;
                        transform: translateX(-50%) translateY(0);
                    }
                }
                
                @keyframes slideOut {
                    from {
                        opacity: 1;
                        transform: translateX(-50%) translateY(0);
                    }
                    to {
                        opacity: 0;
                        transform: translateX(-50%) translateY(-20px);
                    }
                }
            `;
            document.head.appendChild(style);
        </script>
    </div>
</body>
</html>
