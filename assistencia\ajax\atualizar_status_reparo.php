<?php
/**
 * AJAX - Atualizar Status do Reparo
 * FixFácil Assistências - Sistema Novo
 */

header('Content-Type: application/json');

require_once '../config/auth.php';
require_once '../config/database.php';

// Verificar autenticação
$auth = getAuth();
try {
    $auth->checkAssistenciaAuth();
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'Não autorizado']);
    exit();
}

// Obter dados do usuário
$usuario = $auth->getUsuarioLogado();
$db = getDatabase();

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Método não permitido']);
    exit();
}

$proposta_id = $_POST['proposta_id'] ?? 0;
$novo_status = $_POST['novo_status'] ?? '';
$observacoes = $_POST['observacoes_status'] ?? '';

// Validações
if (!$proposta_id || !$novo_status) {
    echo json_encode(['success' => false, 'message' => 'Dados obrigatórios não fornecidos']);
    exit();
}

$status_permitidos = ['Em Andamento', 'Concluída'];
if (!in_array($novo_status, $status_permitidos)) {
    echo json_encode(['success' => false, 'message' => 'Status inválido']);
    exit();
}

try {
    // Verificar se a proposta pertence à assistência
    $sql = "SELECT id, status FROM propostas_assistencia WHERE id = ? AND assistencia_id = ?";
    $result = $db->query($sql, [$proposta_id, $usuario['assistencia_id']]);
    $proposta = $result->fetch_assoc();
    
    if (!$proposta) {
        echo json_encode(['success' => false, 'message' => 'Proposta não encontrada']);
        exit();
    }
    
    // Verificar se a transição de status é válida
    $status_atual = $proposta['status'];
    $transicoes_validas = [
        'aceita' => ['Em Andamento'],
        'Em Andamento' => ['Concluída']
    ];
    
    if (!isset($transicoes_validas[$status_atual]) || 
        !in_array($novo_status, $transicoes_validas[$status_atual])) {
        echo json_encode(['success' => false, 'message' => 'Transição de status inválida']);
        exit();
    }
    
    // Preparar campos para atualização
    $campos_update = ['status = ?'];
    $params = [$novo_status];
    
    if ($novo_status === 'Em Andamento') {
        $campos_update[] = 'data_inicio = NOW()';
    } elseif ($novo_status === 'Concluída') {
        $campos_update[] = 'data_conclusao = NOW()';
    }
    
    // Adicionar observações se fornecidas
    if (!empty($observacoes)) {
        $observacoes_atuais = $proposta['observacoes'] ?? '';
        $novas_observacoes = $observacoes_atuais . "\n\n[" . date('d/m/Y H:i') . "] " . $observacoes;
        $campos_update[] = 'observacoes = ?';
        $params[] = $novas_observacoes;
    }
    
    $params[] = $proposta_id;
    
    // Atualizar status
    $sql = "UPDATE propostas_assistencia SET " . implode(', ', $campos_update) . " WHERE id = ?";
    $db->query($sql, $params);
    
    // Registrar atividade no log (se existir tabela de logs)
    try {
        $sql = "
            INSERT INTO logs_atividades (usuario_id, tipo, descricao, data_atividade)
            VALUES (?, 'status_reparo', ?, NOW())
        ";
        $descricao = "Status do reparo #{$proposta_id} alterado para: {$novo_status}";
        $db->query($sql, [$usuario['id'], $descricao]);
    } catch (Exception $e) {
        // Log opcional, não interrompe o processo
    }
    
    // Enviar notificação para o cliente (se implementado)
    try {
        enviarNotificacaoCliente($proposta_id, $novo_status, $db);
    } catch (Exception $e) {
        // Notificação opcional, não interrompe o processo
    }
    
    echo json_encode([
        'success' => true, 
        'message' => 'Status atualizado com sucesso',
        'novo_status' => $novo_status
    ]);
    
} catch (Exception $e) {
    error_log("Erro ao atualizar status do reparo: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Erro interno do servidor']);
}

/**
 * Função para enviar notificação ao cliente
 */
function enviarNotificacaoCliente($proposta_id, $status, $db) {
    // Obter dados do cliente
    $sql = "
        SELECT 
            u.nome as cliente_nome,
            u.email as cliente_email,
            u.telefone as cliente_telefone,
            sr.dispositivo,
            sr.marca,
            sr.modelo
        FROM propostas_assistencia pa
        JOIN solicitacoes_reparo sr ON pa.solicitacao_id = sr.id
        JOIN usuarios u ON sr.usuario_id = u.id
        WHERE pa.id = ?
    ";
    
    $result = $db->query($sql, [$proposta_id]);
    $dados = $result->fetch_assoc();
    
    if (!$dados) return;
    
    // Preparar mensagem
    $dispositivo = $dados['marca'] . ' ' . $dados['modelo'];
    $mensagens = [
        'Em Andamento' => "Ótimas notícias! O reparo do seu {$dispositivo} foi iniciado. Acompanhe o progresso pelo app FixFácil.",
        'Concluída' => "Seu {$dispositivo} está pronto! O reparo foi concluído com sucesso. Entre em contato para retirada."
    ];
    
    $mensagem = $mensagens[$status] ?? '';
    
    if (empty($mensagem)) return;
    
    // Inserir notificação no banco
    $sql = "
        INSERT INTO notificacoes (usuario_id, tipo, titulo, mensagem, data_criacao)
        SELECT u.id, 'status_reparo', ?, ?, NOW()
        FROM propostas_assistencia pa
        JOIN solicitacoes_reparo sr ON pa.solicitacao_id = sr.id
        JOIN usuarios u ON sr.usuario_id = u.id
        WHERE pa.id = ?
    ";
    
    $titulo = "Status do Reparo Atualizado";
    $db->query($sql, [$titulo, $mensagem, $proposta_id]);
    
    // Aqui você pode adicionar integração com:
    // - WhatsApp API
    // - E-mail
    // - Push notifications
    // - SMS
}
?>
