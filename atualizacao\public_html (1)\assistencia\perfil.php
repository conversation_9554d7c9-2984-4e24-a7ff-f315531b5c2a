<?php
session_start();
if (!isset($_SESSION['usuario_id']) || $_SESSION['tipo_usuario'] != 'assistencia') {
    header('Location: ../login.php');
    exit();
}

$usuario_id = $_SESSION['usuario_id'];
$nome_usuario = $_SESSION['nome'];

// Conexão com o banco de dados
$servername = "localhost";
$username_db = "u680766645_fixfacilnew";
$password_db = "T3cn0l0g1a@";
$dbname = "u680766645_fixfacilnew";

// Ativar exceções para erros do MySQLi
mysqli_report(MYSQLI_REPORT_ERROR | MYSQLI_REPORT_STRICT);

try {
    $conn = new mysqli($servername, $username_db, $password_db, $dbname);
    $conn->set_charset("utf8");
} catch (mysqli_sql_exception $e) {
    die("Falha na conexão: " . $e->getMessage());
}

// Inicializar variáveis para mensagens
$mensagem = "";
$tipo_alerta = "";

// Processar o formulário quando enviado
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    try {
        // Obter dados do formulário e sanitizar
        $nome_empresa = trim($_POST['nome_empresa']);
        $telefone = trim($_POST['telefone']);
        $email = trim($_POST['email']);
        $endereco = trim($_POST['endereco']);
        $site = trim($_POST['site']);

        // Validar campos obrigatórios
        if (empty($nome_empresa) || empty($telefone) || empty($email) || empty($endereco)) {
            throw new Exception("Por favor, preencha todos os campos obrigatórios.");
        }

        // Atualizar informações na tabela assistencias_tecnicas
        $sql_update = "UPDATE assistencias_tecnicas SET nome_empresa = ?, telefone = ?, email = ?, endereco = ?, site = ? WHERE usuario_id = ?";
        $stmt = $conn->prepare($sql_update);
        $stmt->bind_param("sssssi", $nome_empresa, $telefone, $email, $endereco, $site, $usuario_id);

        if ($stmt->execute()) {
            $mensagem = "Informações atualizadas com sucesso!";
            $tipo_alerta = "success";
        } else {
            throw new Exception("Erro ao atualizar as informações.");
        }

        $stmt->close();
    } catch (Exception $e) {
        $mensagem = "Erro: " . $e->getMessage();
        $tipo_alerta = "danger";
    }
}

// Obter as informações atuais da assistência técnica
$sql = "SELECT at.nome_empresa, at.telefone, at.email, at.endereco, at.site
        FROM assistencias_tecnicas at
        WHERE at.usuario_id = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $usuario_id);
$stmt->execute();
$result = $stmt->get_result();
$dados = $result->fetch_assoc();
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <title>Meu Perfil - FixFácil</title>
    <!-- Meta Tags para Responsividade -->
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome 6 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- SweetAlert2 CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Estilos personalizados -->
    <style>
        :root {
            --primary-color: #2563eb;
            --primary-dark: #1d4ed8;
            --secondary-color: #475569;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --light-bg: #f8fafc;
            --card-bg: #ffffff;
            --text-color: #1e293b;
            --text-muted: #64748b;
            --border-radius: 12px;
            --shadow-sm: 0 1px 3px rgba(0,0,0,0.1);
            --shadow-md: 0 4px 6px -1px rgba(0,0,0,0.1), 0 2px 4px -1px rgba(0,0,0,0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0,0,0,0.1), 0 4px 6px -2px rgba(0,0,0,0.05);
        }
        
        body {
            font-family: 'Inter', sans-serif;
            background-color: var(--light-bg);
            color: var(--text-color);
            margin-bottom: 80px;
            padding-top: 70px;
        }
        
        /* Navbar */
        .navbar {
            background-color: var(--card-bg);
            box-shadow: var(--shadow-sm);
            padding: 12px 0;
        }
        
        .navbar-brand {
            font-weight: 700;
            color: var(--primary-color) !important;
        }
        
        .navbar .nav-link {
            color: var(--text-color) !important;
            font-weight: 500;
            padding: 8px 16px;
            border-radius: var(--border-radius);
            transition: all 0.3s ease;
        }
        
        .navbar .nav-link:hover {
            background-color: rgba(37, 99, 235, 0.1);
            color: var(--primary-color) !important;
        }
        
        .navbar .nav-link.active {
            background-color: var(--primary-color);
            color: white !important;
        }
        
        /* Conteúdo Principal */
        .main-content {
            padding: 20px 12px;
        }
        
        .header-section {
            margin-bottom: 24px;
        }
        
        .header-section h1 {
            font-weight: 700;
            font-size: 1.75rem;
            margin-bottom: 8px;
            color: var(--text-color);
        }
        
        .header-section p {
            color: var(--text-muted);
            font-size: 1rem;
            margin-bottom: 0;
        }
        
        /* Cards de conteúdo */
        .content-card {
            background-color: var(--card-bg);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-md);
            padding: 25px;
            margin-bottom: 30px;
        }
        
        .content-card h3 {
            font-weight: 600;
            font-size: 1.25rem;
            margin-bottom: 20px;
            color: var(--text-color);
            padding-bottom: 10px;
            border-bottom: 1px solid rgba(0,0,0,0.05);
        }
        
        /* Formulários */
        .form-label {
            font-weight: 500;
            color: var(--text-color);
            margin-bottom: 8px;
        }
        
        .form-control {
            padding: 12px;
            border-radius: 8px;
            border: 1px solid rgba(0,0,0,0.1);
            font-size: 0.95rem;
            background-color: #fff;
            transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
        }
        
        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }
        
        /* Botões */
        .btn {
            padding: 10px 16px;
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.2s ease;
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }
        
        .btn-primary:hover, .btn-primary:focus {
            background-color: var(--primary-dark);
            border-color: var(--primary-dark);
        }
        
        /* Alertas personalizados */
        .custom-alert {
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 24px;
            border-left: 4px solid;
        }
        
        .alert-success {
            background-color: rgba(16, 185, 129, 0.1);
            border-color: var(--success-color);
            color: #065f46;
        }
        
        .alert-danger {
            background-color: rgba(239, 68, 68, 0.1);
            border-color: var(--danger-color);
            color: #b91c1c;
        }
        
        /* Mobile Menu */
        .mobile-menu {
            position: fixed;
            bottom: 0;
            width: 100%;
            background-color: var(--card-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            z-index: 1000;
            padding: 8px 0;
            display: flex;
            justify-content: space-around;
        }
        
        .mobile-menu .menu-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 8px 0;
            width: 20%;
            text-decoration: none;
        }
        
        .mobile-menu .menu-item i {
            font-size: 20px;
            margin-bottom: 4px;
            color: var(--text-muted);
            transition: color 0.3s ease;
        }
        
        .mobile-menu .menu-item span {
            font-size: 12px;
            color: var(--text-muted);
            transition: color 0.3s ease;
        }
        
        .mobile-menu .menu-item.active i,
        .mobile-menu .menu-item.active span {
            color: var(--primary-color);
            font-weight: 600;
        }
        
        .mobile-menu .menu-item:hover i,
        .mobile-menu .menu-item:hover span {
            color: var(--primary-dark);
        }
        
        /* Esconder menu mobile em desktop */
        @media (min-width: 992px) {
            .mobile-menu {
                display: none;
            }
        }
        
        /* Avatar profile */
        .profile-avatar {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background-color: var(--primary-color);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            margin: 0 auto 20px;
            box-shadow: var(--shadow-md);
        }
        
        /* Ajustes para mobile */
        @media (max-width: 767px) {
            .main-content {
                padding: 15px 10px;
            }
            
            .content-card {
                padding: 20px 15px;
            }
            
            .profile-avatar {
                width: 100px;
                height: 100px;
                font-size: 2.5rem;
            }
        }
    </style>
</head>
<body>
    <!-- Overlay de carregamento -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="spinner"></div>
    </div>

    <!-- Cabeçalho (Navbar) -->
    <nav class="navbar navbar-expand-lg fixed-top navbar-light">
        <div class="container">
            <a class="navbar-brand" href="#">FixFácil</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" 
                    aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="home.php">Painel</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="solicitacoes.php">Solicitações</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="propostas_enviadas.php">Propostas</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="reparos_em_andamento.php">Reparos</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="meumarktplace.php">Marketplace</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="solicitar_pecas.php">Peças</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="carteira.php">Carteira</a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle active" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-user-circle"></i>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item active" href="perfil.php">Meu Perfil</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../logout.php">Sair</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Conteúdo Principal -->
    <div class="container main-content">
        <!-- Cabeçalho da página -->
        <div class="header-section">
            <h1>Meu Perfil</h1>
            <p>Gerencie suas informações de perfil e configurações da conta.</p>
        </div>

        <!-- Exibir Mensagens -->
        <?php if (!empty($mensagem)): ?>
            <div class="custom-alert alert-<?php echo $tipo_alerta; ?>" role="alert">
                <?php echo htmlspecialchars($mensagem); ?>
            </div>
        <?php endif; ?>

        <!-- Formulário de Perfil -->
        <div class="content-card">
            <h3><i class="fas fa-user-circle me-2"></i>Informações da Empresa</h3>
            
            <div class="text-center mb-4">
                <div class="profile-avatar">
                    <i class="fas fa-building"></i>
                </div>
                <h4 class="mb-0"><?php echo htmlspecialchars($nome_usuario); ?></h4>
                <p class="text-muted">Assistência Técnica</p>
            </div>
            
            <form action="perfil.php" method="POST" id="form-perfil">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="nome_empresa" class="form-label">Nome da Empresa <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="nome_empresa" name="nome_empresa" 
                               value="<?php echo isset($dados['nome_empresa']) ? htmlspecialchars($dados['nome_empresa']) : ''; ?>" required>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="telefone" class="form-label">Telefone <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="telefone" name="telefone" 
                               value="<?php echo isset($dados['telefone']) ? htmlspecialchars($dados['telefone']) : ''; ?>" required>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="email" class="form-label">E-mail de Contato <span class="text-danger">*</span></label>
                        <input type="email" class="form-control" id="email" name="email" 
                               value="<?php echo isset($dados['email']) ? htmlspecialchars($dados['email']) : ''; ?>" required>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="site" class="form-label">Website</label>
                        <input type="url" class="form-control" id="site" name="site" 
                               value="<?php echo isset($dados['site']) ? htmlspecialchars($dados['site']) : ''; ?>" 
                               placeholder="https://www.seusite.com.br">
                    </div>
                </div>
                
                <div class="mb-3">
                    <label for="endereco" class="form-label">Endereço <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="endereco" name="endereco" 
                           value="<?php echo isset($dados['endereco']) ? htmlspecialchars($dados['endereco']) : ''; ?>" required>
                </div>
                
                <button type="submit" class="btn btn-primary" id="btn-submit">
                    <i class="fas fa-save me-2"></i>Salvar Alterações
                </button>
            </form>
        </div>
    </div>

    <!-- Menu de navegação móvel -->
    <div class="mobile-menu d-lg-none">
        <a href="home.php" class="menu-item">
            <i class="fas fa-home"></i>
            <span>Painel</span>
        </a>
        <a href="solicitacoes.php" class="menu-item">
            <i class="fas fa-clipboard-list"></i>
            <span>Solicitações</span>
        </a>
        <a href="reparos_em_andamento.php" class="menu-item">
            <i class="fas fa-tools"></i>
            <span>Reparos</span>
        </a>
        <a href="solicitar_pecas.php" class="menu-item">
            <i class="fas fa-cogs"></i>
            <span>Peças</span>
        </a>
        <a href="perfil.php" class="menu-item active">
            <i class="fas fa-user"></i>
            <span>Perfil</span>
        </a>
    </div>

    <!-- Bootstrap JS Bundle com Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- JQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- SweetAlert2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <script>
        $(document).ready(function() {
            // Validação do formulário antes do envio
            $('#form-perfil').on('submit', function(e) {
                const nome_empresa = $('#nome_empresa').val().trim();
                const telefone = $('#telefone').val().trim();
                const email = $('#email').val().trim();
                const endereco = $('#endereco').val().trim();

                if (!nome_empresa || !telefone || !email || !endereco) {
                    e.preventDefault();
                    Swal.fire({
                        icon: 'error',
                        title: 'Campos obrigatórios',
                        text: 'Por favor, preencha todos os campos obrigatórios.',
                        confirmButtonColor: '#2563eb'
                    });
                    return false;
                }
                
                // Validar formato de email
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!emailRegex.test(email)) {
                    e.preventDefault();
                    Swal.fire({
                        icon: 'error',
                        title: 'Email inválido',
                        text: 'Por favor, informe um endereço de email válido.',
                        confirmButtonColor: '#2563eb'
                    });
                    return false;
                }
                
                // Validar formato do site (se preenchido)
                const site = $('#site').val().trim();
                if (site && !site.startsWith('http://') && !site.startsWith('https://')) {
                    $('#site').val('https://' + site);
                }
            });
        });
    </script>
</body>
</html>