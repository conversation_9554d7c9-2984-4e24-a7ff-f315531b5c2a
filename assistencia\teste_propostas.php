<?php
/**
 * Teste da página de propostas
 */

// Inicializar configuração
try {
    require_once 'config.php';
    echo "✓ Configuração carregada com sucesso<br>";
} catch (Exception $e) {
    die("❌ Erro na configuração: " . $e->getMessage());
}

// Verificar conexão
if ($conn->connect_error) {
    die("❌ Falha na conexão com o banco: " . $conn->connect_error);
}
echo "✓ Conexão com banco estabelecida<br>";

// Verificar sessão
echo "<h3>Informações da Sessão:</h3>";
if (isset($_SESSION['usuario_id'])) {
    echo "✓ Usuario ID: " . $_SESSION['usuario_id'] . "<br>";
} else {
    echo "❌ Usuario ID não definido na sessão<br>";
}

if (isset($_SESSION['tipo_usuario'])) {
    echo "✓ Tipo de usuário: " . $_SESSION['tipo_usuario'] . "<br>";
} else {
    echo "❌ Tipo de usuário não definido na sessão<br>";
}

if (isset($_SESSION['assistencia_id'])) {
    echo "✓ Assistência ID: " . $_SESSION['assistencia_id'] . "<br>";
} else {
    echo "❌ Assistência ID não definido na sessão<br>";
}

// Verificar se o usuário está logado corretamente
if (!isset($_SESSION['usuario_id']) || $_SESSION['tipo_usuario'] !== 'assistencia') {
    echo "❌ Usuário não logado como assistência. <a href='../login.php'>Fazer login</a><br>";
} else {
    echo "✓ Usuário logado como assistência<br>";
    
    // Buscar assistencia_id baseado no usuario_id
    $usuario_id = $_SESSION['usuario_id'];
    try {
        $sql = "SELECT id as assistencia_id FROM assistencias_tecnicas WHERE usuario_id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("i", $usuario_id);
        $stmt->execute();
        $result = $stmt->get_result();
        $assistencia_data = $result->fetch_assoc();
        $stmt->close();

        if ($assistencia_data) {
            echo "✓ Assistência encontrada: ID " . $assistencia_data['assistencia_id'] . "<br>";
        } else {
            echo "❌ Assistência não encontrada para o usuário<br>";
        }
    } catch (Exception $e) {
        echo "❌ Erro ao buscar assistência: " . $e->getMessage() . "<br>";
    }
}

// Testar se existe a tabela propostas_assistencia
$sql = "SHOW TABLES LIKE 'propostas_assistencia'";
$result = $conn->query($sql);
if ($result->num_rows > 0) {
    echo "✓ Tabela propostas_assistencia existe<br>";
} else {
    echo "❌ Tabela propostas_assistencia não existe<br>";
}

// Testar se existe a tabela assistencias_tecnicas
$sql = "SHOW TABLES LIKE 'assistencias_tecnicas'";
$result = $conn->query($sql);
if ($result->num_rows > 0) {
    echo "✓ Tabela assistencias_tecnicas existe<br>";
} else {
    echo "❌ Tabela assistencias_tecnicas não existe<br>";
}

echo "<br><a href='propostas.php'>Ir para a página de propostas</a>";
?>
