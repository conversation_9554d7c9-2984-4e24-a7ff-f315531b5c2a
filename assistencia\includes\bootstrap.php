<?php
/**
 * Bootstrap do Sistema
 * FixFácil Assistências
 */

// Definir constantes
define('ENVIRONMENT', 'production'); // Mudar para 'development' para debug
define('APP_ROOT', __DIR__ . '/../');

// Inicializar handler de erros
require_once __DIR__ . '/error_handler.php';
ErrorHandler::setup();

// Verificar saúde do sistema
if (!checkSystemHealth()) {
    handleCriticalError("Sistema temporariamente indisponível. Tente novamente em alguns minutos.", "503");
}

// Inicializar sessão se não existir
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Função para carregar dependências com segurança
function safeRequire($file) {
    $fullPath = APP_ROOT . $file;
    if (!file_exists($fullPath)) {
        throw new Exception("Arquivo necessário não encontrado: $file");
    }
    require_once $fullPath;
}

// Função para verificar se o usuário está logado
function checkAuth() {
    if (!isset($_SESSION['usuario_id']) || $_SESSION['tipo_usuario'] !== 'assistencia') {
        header('Location: ../login.php');
        exit();
    }
}

// Função para obter dados do usuário da sessão
function getUserFromSession() {
    return [
        'id' => $_SESSION['usuario_id'] ?? null,
        'nome' => $_SESSION['nome'] ?? '',
        'email' => $_SESSION['email'] ?? '',
        'telefone' => $_SESSION['telefone'] ?? '',
        'endereco' => $_SESSION['endereco'] ?? '',
        'especialidades' => $_SESSION['especialidades'] ?? '',
        'assistencia_id' => $_SESSION['assistencia_id'] ?? null,
        'tipo_usuario' => $_SESSION['tipo_usuario'] ?? ''
    ];
}

// Função para redirecionar com segurança
function safeRedirect($url) {
    if (headers_sent()) {
        echo "<script>window.location.href='$url';</script>";
    } else {
        header("Location: $url");
    }
    exit();
}

// Função para escape de HTML
function e($value) {
    return htmlspecialchars($value, ENT_QUOTES, 'UTF-8');
}

// Função para formatar data
function formatDate($date, $format = 'd/m/Y') {
    if (empty($date)) return '';
    return date($format, strtotime($date));
}

// Função para formatar moeda
function formatCurrency($value) {
    return 'R$ ' . number_format($value, 2, ',', '.');
}

// Carregar dependências principais
try {
    safeRequire('config/database.php');
    safeRequire('config/auth.php');
} catch (Exception $e) {
    handleCriticalError("Erro ao carregar dependências do sistema", "500");
}
?>
