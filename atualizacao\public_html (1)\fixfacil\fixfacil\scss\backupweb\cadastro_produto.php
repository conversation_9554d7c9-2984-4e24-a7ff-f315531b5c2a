<?php
session_start();
require_once 'db.php';

if (!isset($_SESSION['user_id']) || $_SESSION['nivel_acesso'] != 'fornecedor') {
    header("Location: login.php");
    exit();
}

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $nome = $_POST['nome'];
    $preco = $_POST['preco'];
    $marca = $_POST['marca'];
    $validade = $_POST['validade'];
    $tipo_embalagem = $_POST['tipo_embalagem'];
    $peso_kg = $_POST['peso_kg'];
    $descricao = $_POST['descricao'];

    // Upload da foto
    $foto = $_FILES['foto']['name'];
    $target_dir = "uploads/";
    $target_file = $target_dir . basename($_FILES["foto"]["name"]);
    move_uploaded_file($_FILES["foto"]["tmp_name"], $target_file);

    $fornecedor_id = $_SESSION['user_id']; // Obtém o ID do fornecedor da sessão

    $query = "INSERT INTO produtos (nome, preco, marca, validade, tipo_embalagem, peso_kg, descricao, foto, fornecedor_id) 
              VALUES (:nome, :preco, :marca, :validade, :tipo_embalagem, :peso_kg, :descricao, :foto, :fornecedor_id)";
    $stmt = $pdo->prepare($query);
    $stmt->bindParam(':nome', $nome);
    $stmt->bindParam(':preco', $preco);
    $stmt->bindParam(':marca', $marca);
    $stmt->bindParam(':validade', $validade);
    $stmt->bindParam(':tipo_embalagem', $tipo_embalagem);
    $stmt->bindParam(':peso_kg', $peso_kg);
    $stmt->bindParam(':descricao', $descricao);
    $stmt->bindParam(':foto', $foto);
    $stmt->bindParam(':fornecedor_id', $fornecedor_id);
    $stmt->execute();

    header("Location: fornecedor_dashboard.php");
    exit();
}
?>
<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <title>Cadastro de Produto</title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <link rel="stylesheet" href="styles.css">
</head>
<body>
<style>
        /* Estilos adicionais para a tabela de pedidos */
        .table-scrollable {
            max-height: 400px; /* Altura máxima da tabela com rolagem */
            overflow-y: auto; /* Adiciona rolagem vertical */
        }
        body {
            color: black; /* Altera a cor do texto para preto */
            background-image: url('https://img.freepik.com/vetores-gratis/vetor-de-fundo-de-padrao-geometrico-branco-e-cinza_53876-136510.jpg?size=626&ext=jpg&ga=GA1.1.735520172.1712102400&semt=sph');
            background-size: cover;
            background-position: center;
            background-attachment: fixed;
            background-repeat: no-repeat;
            opacity: 0.9; /* Ajuste a opacidade conforme necessário */
            filter: alpha(opacity=50); /* Para navegadores antigos */
        }
    </style>
</head>

<body>
<nav class="navbar navbar-expand-lg navbar-light bg-light">
    <a class="navbar-brand" href="#">Administrador</a>
    <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarSupportedContent" aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation">
        <span class="navbar-toggler-icon"></span>
    </button>
    <div class="collapse navbar-collapse" id="navbarSupportedContent">
        <ul class="navbar-nav mr-auto">
            <li class="nav-item active">
                <a class="nav-link" href="fornecedor_dashboard.php">Home <span class="sr-only">(current)</span></a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="fornecedor_pedidos.php">Pedidos</a>
            </li>
            <li class="nav-item">
                    <a class="nav-link" href="cadastro_produto.php">Cadastro Produtos</a>
                </li>
            <ul class="navbar-nav ml-auto">
            <li class="nav-item">
                <a class="nav-link" href="?logout=true">Logout</a>
            </li>
        </ul>
        </ul>
    </div>
</nav>
    <div class="container">
        <h2 class="mt-5">Cadastro de Produto</h2>
        <form method="post" action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>" enctype="multipart/form-data" class="mt-4">
            <div class="form-group">
                <label for="nome">Nome do Produto:</label>
                <input type="text" name="nome" class="form-control" required>
            </div>
            <div class="form-group">
                <label for="preco">Preço:</label>
                <input type="number" name="preco" class="form-control" required>
            </div>
            <div class="form-group">
                <label for="marca">Marca:</label>
                <input type="text" name="marca" class="form-control">
            </div>
            <div class="form-group">
                <label for="validade">Validade:</label>
                <input type="date" name="validade" class="form-control">
            </div>
            <div class="form-group">
                <label for="tipo_embalagem">Tipo de Embalagem:</label>
                <select name="tipo_embalagem" class="form-control">
                    <option value="caixa">Caixa</option>
                    <option value="pacote">Pacote</option>
                </select>
            </div>
            <div class="form-group">
                <label for="peso_kg">Peso (em kg):</label>
                <input type="number" name="peso_kg" class="form-control">
            </div>
            <div class="form-group">
                <label for="descricao">Descrição:</label>
                <textarea name="descricao" class="form-control" rows="3"></textarea>
            </div>
            <div class="form-group">
                <label for="foto">Foto:</label>
                <input type="file" name="foto" class="form-control-file">
            </div>
            <button type="submit" class="btn btn-primary">Cadastrar Produto</button>
        </form>
    </div>
</body>
</html>
