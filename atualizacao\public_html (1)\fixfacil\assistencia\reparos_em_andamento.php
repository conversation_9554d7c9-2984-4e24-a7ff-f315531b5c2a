<?php
session_start();
if (!isset($_SESSION['usuario_id']) || $_SESSION['tipo_usuario'] != 'assistencia') {
    header('Location: ../login.php');
    exit();
}

$usuario_id = $_SESSION['usuario_id'];
$nome_usuario = $_SESSION['nome'];

// Conexão com o banco de dados
$servername = "localhost";
$username_db = "u682219090_fixfacilnew";
$password_db = "T3cn0l0g1a@";
$dbname = "u682219090_fixfacilnew";

$conn = new mysqli($servername, $username_db, $password_db, $dbname);

if ($conn->connect_error) {
    die("Falha na conexão: " . $conn->connect_error);
}

$conn->set_charset("utf8");

// Inicializar variáveis para mensagens
$mensagem = "";
$tipo_alerta = "";

// Obter o assistencia_id correspondente ao usuario_id
$sql_assistencia = "SELECT id FROM assistencias_tecnicas WHERE usuario_id = ?";
$stmt_assistencia = $conn->prepare($sql_assistencia);
$stmt_assistencia->bind_param("i", $usuario_id);
$stmt_assistencia->execute();
$result_assistencia = $stmt_assistencia->get_result();
if ($row_assistencia = $result_assistencia->fetch_assoc()) {
    $assistencia_id = $row_assistencia['id'];
} else {
    die("Assistência técnica não encontrada.");
}
$stmt_assistencia->close();

// Obter os reparos em andamento (propostas com status 'em andamento')
$sql = "SELECT pa.id AS proposta_id, pa.preco, pa.prazo, pa.observacoes, pa.status, pa.data_proposta,
        sr.descricao_problema, sr.dispositivo, sr.marca, sr.modelo, u.nome AS nome_cliente
        FROM propostas_assistencia pa
        INNER JOIN solicitacoes_reparo sr ON pa.solicitacao_id = sr.id
        INNER JOIN usuarios u ON sr.usuario_id = u.id
        WHERE pa.assistencia_id = ? AND pa.status = 'em andamento'";
$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $assistencia_id);
$stmt->execute();
$result = $stmt->get_result();
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <title>Reparos em Andamento - FixFácil</title>
    <!-- Meta Tags para Responsividade -->
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <!-- Incluindo Bootstrap CSS -->
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <!-- Font Awesome para ícones -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.1/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css?family=Poppins:300,400,500,600,700&display=swap" rel="stylesheet">
    <!-- Estilos personalizados -->
    <style>
        body {
            font-family: 'Poppins', sans-serif;
            background-color: #f7f9fc;
            margin-bottom: 60px;
            color: #495057;
        }
        /* Navbar */
        .navbar {
            background-color: #fff;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .navbar-brand img {
            width: 150px;
        }
        .navbar-nav .nav-link {
            color: #495057 !important;
            font-weight: 500;
            margin-right: 15px;
        }
        .navbar-nav .nav-link.active {
            color: #007BFF !important;
        }
        .navbar-nav .nav-link:hover {
            color: #0056b3 !important;
        }
        /* Conteúdo Principal */
        .main-content {
            padding: 80px 20px 20px;
        }
        .welcome {
            margin-bottom: 40px;
        }
        .welcome h2 {
            font-weight: 600;
            color: #343a40;
        }
        .card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.05);
            background-color: #fff;
            padding: 30px;
            margin-bottom: 20px;
        }
        /* Footer */
        footer.footer {
            background-color: #fff;
            padding: 20px 0;
            position: fixed;
            width: 100%;
            bottom: 0;
            box-shadow: 0 -2px 4px rgba(0,0,0,0.1);
        }
        footer.footer span {
            color: #6c757d;
        }
        /* Navbar Inferior (Mobile) */
        .footer-nav {
            position: fixed;
            bottom: 0;
            width: 100%;
            background-color: #fff;
            box-shadow: 0 -2px 5px rgba(0,0,0,0.1);
            z-index: 1000;
        }
        .footer-nav .nav-link {
            color: #6c757d;
            text-align: center;
            padding: 10px 0;
            font-size: 12px;
        }
        .footer-nav .nav-link.active {
            color: #007BFF;
        }
        .footer-nav .nav-link i {
            font-size: 20px;
        }
        @media (min-width: 768px) {
            .footer-nav {
                display: none;
            }
        }
    </style>
</head>
<body>
    <!-- Cabeçalho -->
   <nav class="navbar navbar-expand-lg navbar-light fixed-top">
       
        <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarNavAssistencia" 
                aria-controls="navbarNavAssistencia" aria-expanded="false" aria-label="Alternar navegação">
            <span class="navbar-toggler-icon"></span>
        </button>
         <div class="collapse navbar-collapse justify-content-end" id="navbarNavAssistencia">
           <ul class="navbar-nav">
                <li class="nav-item">
                    <a class="nav-link " href="home.php"><i class="fas fa-home"></i> Painel</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="solicitacoes.php"><i class="fas fa-envelope"></i> Solicitações</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="meumarktplace.php"><i class="fas fa-store"></i> Marketplace</a> <!-- Corrigido o link e ícone -->
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="solicitar_pecas.php"><i class="fas fa-plus"></i> Solicitação Peças</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="propostas_enviadas.php"><i class="fas fa-paper-plane"></i> Propostas Enviadas</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link active" href="reparos_em_andamento.php"><i class="fas fa-tools"></i> Reparos em Andamento</a>
                </li>
                 <li class="nav-item">
                    <a class="nav-link " href="carteira.php"><i class="fas fa-wallet"></i> Carteira</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="perfil.php"><i class="fas fa-user-circle"></i> Perfil</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="../logout.php"><i class="fas fa-sign-out-alt"></i> Sair</a>
                </li>
            </ul>
        </div>
    </nav>
    <!-- Conteúdo Principal -->
    <div class="container main-content">
        <div class="welcome text-center">
            <h2>Reparos em Andamento</h2>
            <p class="text-muted">Gerencie os reparos que estão em andamento.</p>
        </div>

        <!-- Exibir Mensagens -->
        <?php if (!empty($mensagem)): ?>
            <div class="alert alert-<?php echo $tipo_alerta; ?>" role="alert">
                <?php echo htmlspecialchars($mensagem); ?>
            </div>
        <?php endif; ?>

        <!-- Lista de Reparos -->
        <div class="card">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>ID Proposta</th>
                            <th>Cliente</th>
                            <th>Dispositivo</th>
                            <th>Marca</th>
                            <th>Modelo</th>
                            <th>Status</th>
                            <th>Data Início</th>
                            <th>Ações</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if ($result->num_rows > 0): ?>
                            <?php while ($reparo = $result->fetch_assoc()): ?>
                                <tr>
                                    <td><?php echo $reparo['proposta_id']; ?></td>
                                    <td><?php echo htmlspecialchars($reparo['nome_cliente']); ?></td>
                                    <td><?php echo htmlspecialchars($reparo['dispositivo']); ?></td>
                                    <td><?php echo htmlspecialchars($reparo['marca']); ?></td>
                                    <td><?php echo htmlspecialchars($reparo['modelo']); ?></td>
                                    <td>
                                        <?php
                                        $status = $reparo['status'];
                                        if ($status == 'em andamento') {
                                            echo '<span class="badge badge-info">Em Andamento</span>';
                                        } elseif ($status == 'concluída') {
                                            echo '<span class="badge badge-success">Concluída</span>';
                                        }
                                        ?>
                                    </td>
                                    <td><?php echo date('d/m/Y H:i', strtotime($reparo['data_proposta'])); ?></td>
                                    <td>
                                        <!-- Botão para atualizar o status do reparo -->
                                        <form method="POST" action="atualizar_status_reparo.php" style="display:inline;">
                                            <input type="hidden" name="proposta_id" value="<?php echo $reparo['proposta_id']; ?>">
                                            <select name="novo_status" class="form-control form-control-sm mb-1">
                                                <option value="em andamento" <?php if ($status == 'em andamento') echo 'selected'; ?>>Em Andamento</option>
                                                <option value="concluída" <?php if ($status == 'concluída') echo 'selected'; ?>>Concluída</option>
                                            </select>
                                            <button type="submit" name="alterar_status" class="btn btn-primary btn-sm btn-block">Atualizar</button>
                                        </form>
                                    </td>
                                </tr>
                            <?php endwhile; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="8" class="text-center">Nenhum reparo em andamento.</td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Barra de Navegação Inferior (Mobile) -->
    <nav class="footer-nav d-md-none">
        <ul class="nav justify-content-around">
            <li class="nav-item">
                <a class="nav-link" href="home.php">
                    <i class="fas fa-home"></i><br>Painel
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="solicitacoes.php">
                    <i class="fas fa-envelope"></i><br>Solicitações
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="propostas_enviadas.php">
                    <i class="fas fa-paper-plane"></i><br>Propostas
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link active" href="reparos_em_andamento.php">
                    <i class="fas fa-tools"></i><br>Reparos
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="perfil.php">
                    <i class="fas fa-user-circle"></i><br>Perfil
                </a>
            </li>
        </ul>
    </nav>

    <!-- Rodapé -->
    <footer class="footer d-none d-md-block">
        <div class="container text-center">
            <span>&copy; <?php echo date("Y"); ?> FixFácil. Todos os direitos reservados.</span>
        </div>
    </footer>

    <!-- Incluindo Bootstrap JS e dependências -->
    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js" ></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.5.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
