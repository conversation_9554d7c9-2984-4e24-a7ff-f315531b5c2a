<?php
session_start();
require_once 'db.php';

// Verifica se o usuário está logado como lojista
if (!isset($_SESSION['user_id']) || $_SESSION['nivel_acesso'] != 'lojista') {
    header("Location: login.php");
    exit();
}

$mensagem = '';

try {
    // Recuperando leilões do lojista com o valor do último lance
    $query = "SELECT le.id as leilao_id, le.produto_id, le.lojista_id, le.status_leilao, 
                     (SELECT valor_lance FROM lances WHERE produto_id = le.produto_id ORDER BY data_lance DESC LIMIT 1) as valor_lance,
                     p.nome as produto_nome, p.preco as produto_preco, 
                     p.marca as produto_marca, p.descricao as produto_descricao
              FROM leilao le 
              LEFT JOIN produtos p ON le.produto_id = p.id 
              WHERE le.lojista_id = :lojista_id";

    $stmt = $pdo->prepare($query);
    $stmt->bindParam(':lojista_id', $_SESSION['user_id']);
    $stmt->execute();
    $leiloes = $stmt->fetchAll(PDO::FETCH_ASSOC);

} catch (PDOException $e) {
    $mensagem = 'Erro ao buscar os leilões: ' . $e->getMessage();
}

?>

<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <title>Listagem de Leilões</title>
</head>
<body>

<h1>Listagem de Leilões</h1>

<?php if ($mensagem): ?>
    <div><?= $mensagem ?></div>
<?php endif; ?>

<table>
    <thead>
        <tr>
            <th>ID</th>
            <th>Produto</th>
            <th>Preço</th>
            <th>Marca</th>
            <th>Descrição</th>
            <th>Status</th>
            <th>Valor do Último Lance</th>
            <th>Ações</th>
        </tr>
    </thead>
    <tbody>
        <?php foreach ($leiloes as $leilao): ?>
            <tr>
                <td><?= $leilao['leilao_id'] ?></td>
                <td><?= $leilao['produto_nome'] ?></td>
                <td><?= $leilao['produto_preco'] ?></td>
                <td><?= $leilao['produto_marca'] ?></td>
                <td><?= $leilao['produto_descricao'] ?></td>
                <td><?= $leilao['status_leilao'] ?></td>
                <td><?= $leilao['valor_lance'] ? 'R$ ' . number_format($leilao['valor_lance'], 2, ',', '.') : 'Sem lance' ?></td>
                <td>
                    <!-- Aqui você pode adicionar botões ou links para fazer um lance -->
                    <form action="fazer_lance.php" method="post">
                        <input type="hidden" name="leilao_id" value="<?= $leilao['leilao_id'] ?>">
                        <input type="number" name="valor_lance" placeholder="Valor do lance">
                        <button type="submit">Fazer Lance</button>
                    </form>
                </td>
            </tr>
        <?php endforeach; ?>
    </tbody>
</table>

</body>
</html>
