<?php
session_start();
require_once 'db.php';

if (!isset($_SESSION['user_id']) || $_SESSION['nivel_acesso'] != 'fornecedor') {
    header("Location: login.php");
    exit();
}

if(isset($_GET['logout']) && $_GET['logout'] == 'true') {
    session_destroy();
    header("Location: login.php");
    exit();
}

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Total de pedidos do fornecedor
    $fornecedor_id = $_SESSION['user_id'];
    $queryTotalPedidos = "SELECT COUNT(*) AS total_pedidos FROM compras WHERE fornecedor_id = :fornecedor_id";
    $stmtTotalPedidos = $pdo->prepare($queryTotalPedidos);
    $stmtTotalPedidos->bindParam(':fornecedor_id', $fornecedor_id);
    $stmtTotalPedidos->execute();
    $totalPedidos = $stmtTotalPedidos->fetch(PDO::FETCH_ASSOC);

    // Últimos pedidos do fornecedor
    $queryUltimosPedidos = "SELECT compras.*, usuarios.nome AS nome_cliente
                            FROM compras
                            JOIN usuarios ON compras.lojista_id = usuarios.id
                            WHERE compras.fornecedor_id = :fornecedor_id
                            ORDER BY compras.id DESC
                            LIMIT 5";
    $stmtUltimosPedidos = $pdo->prepare($queryUltimosPedidos);
    $stmtUltimosPedidos->bindParam(':fornecedor_id', $fornecedor_id);
    $stmtUltimosPedidos->execute();
    $ultimosPedidos = $stmtUltimosPedidos->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    echo "Erro: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <title>Dashboard - Fornecedor</title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<style>
    body {
        background-image: url('https://img.freepik.com/vetores-gratis/vetor-de-fundo-de-padrao-geometrico-branco-e-cinza_53876-136510.jpg?size=626&ext=jpg&ga=GA1.1.735520172.1712102400&semt=sph');
        background-size: cover;
        background-position: center;
        background-attachment: fixed;
        background-repeat: no-repeat;
        opacity: 0.9; /* Ajuste a opacidade conforme necessário */
        filter: alpha(opacity=80); /* Para navegadores antigos */
    }
</style>
<body>

<nav class="navbar navbar-expand-lg navbar-light bg-light">
    <a class="navbar-brand" href="#">Administrador</a>
    <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarSupportedContent" aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation">
        <span class="navbar-toggler-icon"></span>
    </button>
    <div class="collapse navbar-collapse" id="navbarSupportedContent">
        <ul class="navbar-nav mr-auto">
            <li class="nav-item active">
                <a class="nav-link" href="fornecedor_dashboard.php">Home <span class="sr-only">(current)</span></a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="fornecedor_pedidos.php">Pedidos</a>
            </li>
            <li class="nav-item">
                    <a class="nav-link" href="cadastro_produto.php">Cadastro Produtos</a>
                </li>
            <ul class="navbar-nav ml-auto">
            <li class="nav-item">
                <a class="nav-link" href="?logout=true">Logout</a>
            </li>
        </ul>
        </ul>
    </div>
</nav>

<div class="container mt-5">
    <h2>Dashboard - Fornecedor</h2>
    <div class="row mt-5">
        <div class="col-md-6">
            <canvas id="graficoPedidos"></canvas>
        </div>
        <div class="col-md-6">
            <h4>Últimos Pedidos</h4>
            <ul class="list-group">
                <?php foreach ($ultimosPedidos as $pedido): ?>
                    <li class="list-group-item">
                        <a href="informacoes_pedido.php?pedido_id=<?php echo $pedido['id']; ?>">
                            Pedido #<?php echo $pedido['id']; ?> - Cliente: <?php echo $pedido['nome_cliente']; ?>
                        </a>
                    </li>
                <?php endforeach; ?>
            </ul>
        </div>
    </div>
</div>

<script>
    var ctx = document.getElementById('graficoPedidos').getContext('2d');
    var myChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: ['Total de Pedidos'],
            datasets: [{
                label: 'Número de Pedidos',
                data: [<?php echo $totalPedidos['total_pedidos']; ?>],
                backgroundColor: [
                    'rgba(255, 99, 132, 0.2)'
                ],
                borderColor: [
                    'rgba(255, 99, 132, 1)'
                ],
                borderWidth: 1
            }]
        },
        options: {
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
</script>

</body>
</html>
