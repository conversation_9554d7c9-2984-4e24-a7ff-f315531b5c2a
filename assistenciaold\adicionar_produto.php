<?php
/**
 * Página de Adicionar Produto
 * FixFácil Assistências - Sistema Novo
 */

require_once 'config/auth.php';
require_once 'config/database.php';
require_once 'includes/layout.php';

// Verificar autenticação
$auth = getAuth();
$auth->checkAssistenciaAuth();

// Verificar acesso ao marketplace
if (!$auth->hasAccess('marketplace')) {
    header('Location: upgrade_plano.php?feature=marketplace');
    exit();
}

// Obter dados do usuário
$usuario = $auth->getUsuarioLogado();
$plano = $auth->getPlanoInfo($usuario['id']);
$db = getDatabase();

// Processar formulário
$erro = '';
$sucesso = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $nome = trim($_POST['nome'] ?? '');
    $descricao = trim($_POST['descricao'] ?? '');
    $categoria = $_POST['categoria'] ?? '';
    $marca = trim($_POST['marca'] ?? '');
    $preco = floatval($_POST['preco'] ?? 0);
    $quantidade_estoque = intval($_POST['quantidade_estoque'] ?? 0);
    $tipo_peca = $_POST['tipo_peca'] ?? '';
    $compatibilidade = trim($_POST['compatibilidade'] ?? '');
    
    // Validações
    if (empty($nome)) {
        $erro = 'Nome do produto é obrigatório.';
    } elseif (empty($descricao)) {
        $erro = 'Descrição é obrigatória.';
    } elseif (empty($categoria)) {
        $erro = 'Categoria é obrigatória.';
    } elseif ($preco <= 0) {
        $erro = 'Preço deve ser maior que zero.';
    } elseif ($quantidade_estoque < 0) {
        $erro = 'Quantidade em estoque não pode ser negativa.';
    } else {
        try {
            // Upload de imagem (simulado por enquanto)
            $imagem = null;
            if (isset($_FILES['imagem']) && $_FILES['imagem']['error'] === UPLOAD_ERR_OK) {
                // Aqui você implementaria o upload real
                $imagem = 'uploads/produtos/produto_' . time() . '.jpg';
            }
            
            $sql = "
                INSERT INTO produtos_marketplace 
                (assistencia_id, nome, descricao, categoria, marca, preco, quantidade_estoque, 
                 tipo_peca, compatibilidade, imagem, status, data_criacao)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'ativo', NOW())
            ";
            
            $db->query($sql, [
                $usuario['assistencia_id'],
                $nome,
                $descricao,
                $categoria,
                $marca,
                $preco,
                $quantidade_estoque,
                $tipo_peca,
                $compatibilidade,
                $imagem
            ]);
            
            $sucesso = 'Produto adicionado com sucesso!';
            
            // Limpar formulário
            $_POST = [];
            
        } catch (Exception $e) {
            error_log("Erro ao adicionar produto: " . $e->getMessage());
            $erro = 'Erro interno. Tente novamente.';
        }
    }
}

// Inicializar layout
$layout = new Layout();
?>

<?php $layout->renderHead("Adicionar Produto - FixFácil Assistências"); ?>

<div class="main-wrapper">
    <?php $layout->renderSidebar('marketplace'); ?>
    
    <main class="main-content">
        <!-- Header -->
        <div class="content-header">
            <div class="d-flex align-items-center justify-content-between">
                <div>
                    <h1 class="page-title">
                        <i class="fas fa-plus me-3"></i>
                        Adicionar Produto
                    </h1>
                    <p class="page-subtitle">
                        Adicione um novo produto ao marketplace
                    </p>
                </div>
                <div>
                    <a href="marketplace.php" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>
                        Voltar
                    </a>
                </div>
            </div>
        </div>
        
        <?php if ($erro): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <?php echo htmlspecialchars($erro); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>
        
        <?php if ($sucesso): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?php echo htmlspecialchars($sucesso); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>
        
        <div class="row g-4">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-info-circle me-2"></i>
                            Informações do Produto
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" enctype="multipart/form-data">
                            <div class="row g-3">
                                <div class="col-md-8">
                                    <label for="nome" class="form-label">Nome do Produto *</label>
                                    <input type="text" class="form-control" id="nome" name="nome" 
                                           value="<?php echo htmlspecialchars($_POST['nome'] ?? ''); ?>" 
                                           placeholder="Ex: Tela LCD iPhone 12" required>
                                </div>
                                
                                <div class="col-md-4">
                                    <label for="categoria" class="form-label">Categoria *</label>
                                    <select class="form-select" id="categoria" name="categoria" required>
                                        <option value="">Selecione...</option>
                                        <option value="telas" <?php echo ($_POST['categoria'] ?? '') === 'telas' ? 'selected' : ''; ?>>Telas</option>
                                        <option value="baterias" <?php echo ($_POST['categoria'] ?? '') === 'baterias' ? 'selected' : ''; ?>>Baterias</option>
                                        <option value="cameras" <?php echo ($_POST['categoria'] ?? '') === 'cameras' ? 'selected' : ''; ?>>Câmeras</option>
                                        <option value="conectores" <?php echo ($_POST['categoria'] ?? '') === 'conectores' ? 'selected' : ''; ?>>Conectores</option>
                                        <option value="ferramentas" <?php echo ($_POST['categoria'] ?? '') === 'ferramentas' ? 'selected' : ''; ?>>Ferramentas</option>
                                        <option value="capas" <?php echo ($_POST['categoria'] ?? '') === 'capas' ? 'selected' : ''; ?>>Capas e Películas</option>
                                        <option value="outros" <?php echo ($_POST['categoria'] ?? '') === 'outros' ? 'selected' : ''; ?>>Outros</option>
                                    </select>
                                </div>
                                
                                <div class="col-12">
                                    <label for="descricao" class="form-label">Descrição *</label>
                                    <textarea class="form-control" id="descricao" name="descricao" rows="4" 
                                              placeholder="Descreva o produto, suas características e especificações..." required><?php echo htmlspecialchars($_POST['descricao'] ?? ''); ?></textarea>
                                </div>
                                
                                <div class="col-md-6">
                                    <label for="marca" class="form-label">Marca</label>
                                    <input type="text" class="form-control" id="marca" name="marca" 
                                           value="<?php echo htmlspecialchars($_POST['marca'] ?? ''); ?>" 
                                           placeholder="Ex: Apple, Samsung, Xiaomi">
                                </div>
                                
                                <div class="col-md-6">
                                    <label for="tipo_peca" class="form-label">Tipo de Peça</label>
                                    <select class="form-select" id="tipo_peca" name="tipo_peca">
                                        <option value="">Selecione...</option>
                                        <option value="original" <?php echo ($_POST['tipo_peca'] ?? '') === 'original' ? 'selected' : ''; ?>>Original</option>
                                        <option value="compativel" <?php echo ($_POST['tipo_peca'] ?? '') === 'compativel' ? 'selected' : ''; ?>>Compatível</option>
                                        <option value="refurbished" <?php echo ($_POST['tipo_peca'] ?? '') === 'refurbished' ? 'selected' : ''; ?>>Refurbished</option>
                                    </select>
                                </div>
                                
                                <div class="col-md-4">
                                    <label for="preco" class="form-label">Preço (R$) *</label>
                                    <input type="number" class="form-control" id="preco" name="preco" 
                                           value="<?php echo $_POST['preco'] ?? ''; ?>" 
                                           step="0.01" min="0.01" placeholder="0,00" required>
                                </div>
                                
                                <div class="col-md-4">
                                    <label for="quantidade_estoque" class="form-label">Quantidade em Estoque *</label>
                                    <input type="number" class="form-control" id="quantidade_estoque" name="quantidade_estoque" 
                                           value="<?php echo $_POST['quantidade_estoque'] ?? ''; ?>" 
                                           min="0" placeholder="0" required>
                                </div>
                                
                                <div class="col-md-4">
                                    <label for="imagem" class="form-label">Imagem do Produto</label>
                                    <input type="file" class="form-control" id="imagem" name="imagem" 
                                           accept="image/*">
                                    <div class="form-text">Formatos aceitos: JPG, PNG, GIF (máx. 2MB)</div>
                                </div>
                                
                                <div class="col-12">
                                    <label for="compatibilidade" class="form-label">Compatibilidade</label>
                                    <textarea class="form-control" id="compatibilidade" name="compatibilidade" rows="3" 
                                              placeholder="Liste os modelos compatíveis com este produto..."><?php echo htmlspecialchars($_POST['compatibilidade'] ?? ''); ?></textarea>
                                </div>
                                
                                <div class="col-12">
                                    <div class="d-flex gap-2">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save me-2"></i>
                                            Adicionar Produto
                                        </button>
                                        <a href="marketplace.php" class="btn btn-outline-secondary">
                                            <i class="fas fa-times me-2"></i>
                                            Cancelar
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-4">
                <!-- Dicas -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-lightbulb me-2"></i>
                            Dicas para Vender Mais
                        </h5>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled">
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                Use fotos de alta qualidade
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                Descreva detalhadamente o produto
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                Informe a compatibilidade
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                Mantenha preços competitivos
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                Atualize o estoque regularmente
                            </li>
                        </ul>
                    </div>
                </div>
                
                <!-- Informações do Plano -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-info-circle me-2"></i>
                            Seu Plano
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="text-center mb-3">
                            <div class="plano-badge plano-<?php echo strtolower($plano['nome']); ?>">
                                <?php if ($plano['nome'] === 'Master'): ?>
                                    <i class="fas fa-crown me-1"></i>
                                <?php elseif ($plano['nome'] === 'Premium'): ?>
                                    <i class="fas fa-star me-1"></i>
                                <?php endif; ?>
                                Plano <?php echo $plano['nome']; ?>
                            </div>
                        </div>
                        
                        <div class="text-center">
                            <h6>Benefícios do Marketplace:</h6>
                            <ul class="list-unstyled text-start">
                                <li class="mb-1">
                                    <i class="fas fa-check text-success me-2"></i>
                                    Produtos ilimitados
                                </li>
                                <li class="mb-1">
                                    <i class="fas fa-check text-success me-2"></i>
                                    Sem taxa por venda
                                </li>
                                <li class="mb-1">
                                    <i class="fas fa-check text-success me-2"></i>
                                    Gestão de estoque
                                </li>
                                <li class="mb-1">
                                    <i class="fas fa-check text-success me-2"></i>
                                    Relatórios de vendas
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>
</div>

<?php 
$extraJS = "
<script>
// Máscara para preço
document.getElementById('preco').addEventListener('input', function(e) {
    let value = e.target.value;
    // Permitir apenas números e ponto decimal
    value = value.replace(/[^0-9.]/g, '');
    e.target.value = value;
});

// Preview da imagem
document.getElementById('imagem').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            // Aqui você pode adicionar um preview da imagem
            console.log('Imagem selecionada:', file.name);
        };
        reader.readAsDataURL(file);
    }
});
</script>
";

$layout->renderFooter($extraJS); 
?>
