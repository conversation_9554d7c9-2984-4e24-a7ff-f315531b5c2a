<?php
/**
 * AJAX - Limpar Logs Antigos
 * FixFácil Assistências - Sistema Novo
 */

header('Content-Type: application/json');

require_once '../config/auth.php';
require_once '../sistema/monitor_sistema.php';

// Verificar autenticação e permissão de admin
$auth = getAuth();
try {
    $auth->checkAssistenciaAuth();
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'Não autorizado']);
    exit();
}

$usuario = $auth->getUsuarioLogado();

// Verificar se é admin
if ($usuario['email'] !== '<EMAIL>') {
    echo json_encode(['success' => false, 'message' => 'Acesso negado']);
    exit();
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Método não permitido']);
    exit();
}

try {
    $monitor = new MonitorSistema();
    $removidos = $monitor->limparLogsAntigos(30); // Remover logs com mais de 30 dias
    
    echo json_encode([
        'success' => true,
        'message' => "Limpeza concluída! $removidos registros removidos."
    ]);
    
} catch (Exception $e) {
    error_log("Erro ao limpar logs: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Erro interno do servidor']);
}
?>
