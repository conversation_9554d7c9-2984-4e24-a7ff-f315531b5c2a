<?php
/**
 * Login simples para assistência técnica
 */

require_once 'config.php';

// Verificar se já está logado
if (isset($_SESSION['usuario_id']) && $_SESSION['tipo_usuario'] === 'assistencia') {
    header('Location: propostas.php');
    exit();
}

$erro = "";

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = $_POST['email'] ?? '';
    $senha = $_POST['senha'] ?? '';
    
    if (!empty($email) && !empty($senha)) {
        try {
            // Buscar usuário
            $sql = "SELECT id, nome, email, senha, tipo_usuario FROM usuarios WHERE email = ? AND tipo_usuario = 'assistencia' AND status = 1";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param("s", $email);
            $stmt->execute();
            $result = $stmt->get_result();
            
            if ($result->num_rows > 0) {
                $user = $result->fetch_assoc();
                
                // Verificar senha
                if (password_verify($senha, $user['senha'])) {
                    // Login bem-sucedido
                    $_SESSION['usuario_id'] = $user['id'];
                    $_SESSION['nome'] = $user['nome'];
                    $_SESSION['email'] = $user['email'];
                    $_SESSION['tipo_usuario'] = $user['tipo_usuario'];
                    
                    // Buscar assistencia_id
                    $sql = "SELECT id FROM assistencias_tecnicas WHERE usuario_id = ?";
                    $stmt = $conn->prepare($sql);
                    $stmt->bind_param("i", $user['id']);
                    $stmt->execute();
                    $result = $stmt->get_result();
                    
                    if ($result->num_rows > 0) {
                        $assistencia = $result->fetch_assoc();
                        $_SESSION['assistencia_id'] = $assistencia['id'];
                        
                        header('Location: propostas.php');
                        exit();
                    } else {
                        $erro = "Assistência técnica não encontrada.";
                    }
                } else {
                    $erro = "Senha incorreta.";
                }
            } else {
                $erro = "Usuário não encontrado ou não é uma assistência técnica.";
            }
        } catch (Exception $e) {
            $erro = "Erro interno. Tente novamente.";
        }
    } else {
        $erro = "Preencha todos os campos.";
    }
}
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Assistência Técnica</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 400px;
            margin: 100px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .login-box {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="email"], input[type="password"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            box-sizing: border-box;
        }
        button {
            width: 100%;
            padding: 12px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background: #0056b3;
        }
        .error {
            color: #dc3545;
            background: #f8d7da;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .test-data {
            background: #e7f3ff;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="login-box">
        <h2>Login - Assistência Técnica</h2>
        
        <?php if ($erro): ?>
            <div class="error"><?php echo $erro; ?></div>
        <?php endif; ?>
        
        <div class="test-data">
            <strong>Dados de teste:</strong><br>
            Email: <EMAIL><br>
            Senha: (senha da conta)
        </div>
        
        <form method="POST">
            <div class="form-group">
                <label for="email">Email:</label>
                <input type="email" id="email" name="email" required>
            </div>
            
            <div class="form-group">
                <label for="senha">Senha:</label>
                <input type="password" id="senha" name="senha" required>
            </div>
            
            <button type="submit">Entrar</button>
        </form>
        
        <div style="margin-top: 20px; text-align: center;">
            <a href="teste_propostas.php">Teste de Sistema</a> |
            <a href="../login.php">Login Principal</a>
        </div>
    </div>
</body>
</html>
