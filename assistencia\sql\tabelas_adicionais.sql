-- Tabelas Adicionais para o Sistema FixFácil
-- Execute este SQL no seu banco de dados para adicionar as funcionalidades avançadas

-- Tabela para produtos do marketplace
CREATE TABLE IF NOT EXISTS `produtos_marketplace` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `assistencia_id` int(11) NOT NULL,
  `nome` varchar(255) NOT NULL,
  `descricao` text NOT NULL,
  `categoria` enum('telas','baterias','cameras','conectores','ferramentas','capas','outros') NOT NULL,
  `marca` varchar(100) DEFAULT NULL,
  `preco` decimal(10,2) NOT NULL,
  `quantidade_estoque` int(11) NOT NULL DEFAULT 0,
  `tipo_peca` enum('original','compativel','refurbished') DEFAULT NULL,
  `compatibilidade` text DEFAULT NULL,
  `imagem` varchar(500) DEFAULT NULL,
  `status` enum('ativo','pausado','esgotado','excluido') NOT NULL DEFAULT 'ativo',
  `data_criacao` datetime NOT NULL,
  `data_exclusao` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_assistencia` (`assistencia_id`),
  KEY `idx_categoria` (`categoria`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Tabela para vendas do marketplace
CREATE TABLE IF NOT EXISTS `vendas_marketplace` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `produto_id` int(11) NOT NULL,
  `comprador_id` int(11) NOT NULL,
  `quantidade` int(11) NOT NULL,
  `valor_unitario` decimal(10,2) NOT NULL,
  `valor_total` decimal(10,2) NOT NULL,
  `status` enum('pendente','processando','concluida','cancelada') NOT NULL DEFAULT 'pendente',
  `data_venda` datetime NOT NULL,
  `data_entrega` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_produto` (`produto_id`),
  KEY `idx_comprador` (`comprador_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Tabela para mensagens do chat
CREATE TABLE IF NOT EXISTS `mensagens_chat` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `proposta_id` int(11) NOT NULL,
  `remetente_id` int(11) NOT NULL,
  `remetente_tipo` enum('usuario','assistencia','sistema') NOT NULL,
  `mensagem` text NOT NULL,
  `data_envio` datetime NOT NULL,
  `lida` tinyint(1) NOT NULL DEFAULT 0,
  `data_leitura` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_proposta` (`proposta_id`),
  KEY `idx_remetente` (`remetente_id`, `remetente_tipo`),
  KEY `idx_data` (`data_envio`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Tabela para assistências virtuais (Plano Master)
CREATE TABLE IF NOT EXISTS `assistencias_virtuais` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `assistencia_id` int(11) NOT NULL,
  `slug_personalizado` varchar(100) NOT NULL,
  `titulo_pagina` varchar(255) NOT NULL,
  `descricao` text DEFAULT NULL,
  `cor_primaria` varchar(7) NOT NULL DEFAULT '#667eea',
  `cor_secundaria` varchar(7) NOT NULL DEFAULT '#764ba2',
  `logo_url` varchar(500) DEFAULT NULL,
  `ativo` tinyint(1) NOT NULL DEFAULT 1,
  `data_criacao` datetime NOT NULL,
  `data_atualizacao` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `slug_personalizado` (`slug_personalizado`),
  KEY `idx_assistencia` (`assistencia_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Tabela para acessos à assistência virtual
CREATE TABLE IF NOT EXISTS `acessos_assistencia_virtual` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `assistencia_id` int(11) NOT NULL,
  `ip_address` varchar(45) NOT NULL,
  `user_agent` text DEFAULT NULL,
  `data_acesso` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_assistencia` (`assistencia_id`),
  KEY `idx_data` (`data_acesso`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Tabela para logs de atividades
CREATE TABLE IF NOT EXISTS `logs_atividades` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `usuario_id` int(11) DEFAULT NULL,
  `tipo` varchar(50) NOT NULL,
  `descricao` text NOT NULL,
  `dados_extras` json DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `data_atividade` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_usuario` (`usuario_id`),
  KEY `idx_tipo` (`tipo`),
  KEY `idx_data` (`data_atividade`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Tabela para logs de erros
CREATE TABLE IF NOT EXISTS `logs_erros` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `tipo` varchar(50) NOT NULL,
  `mensagem` text NOT NULL,
  `arquivo` varchar(255) DEFAULT NULL,
  `linha` int(11) DEFAULT NULL,
  `contexto` json DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `data_erro` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_tipo` (`tipo`),
  KEY `idx_data` (`data_erro`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Tabela para logs de performance
CREATE TABLE IF NOT EXISTS `logs_performance` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `pagina` varchar(255) NOT NULL,
  `tempo_execucao` decimal(10,6) NOT NULL,
  `memoria_usada` bigint(20) NOT NULL,
  `queries_executadas` int(11) DEFAULT 0,
  `data_acesso` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_pagina` (`pagina`),
  KEY `idx_data` (`data_acesso`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Tabela para backups do sistema
CREATE TABLE IF NOT EXISTS `backups_sistema` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `nome_arquivo` varchar(255) NOT NULL,
  `tamanho` bigint(20) NOT NULL,
  `tipo` enum('automatico','manual') DEFAULT 'automatico',
  `data_criacao` datetime NOT NULL,
  `status` enum('concluido','erro') DEFAULT 'concluido',
  `observacoes` text DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_data_criacao` (`data_criacao`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Tabela para notificações
CREATE TABLE IF NOT EXISTS `notificacoes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `usuario_id` int(11) NOT NULL,
  `tipo` varchar(50) NOT NULL,
  `titulo` varchar(255) NOT NULL,
  `mensagem` text NOT NULL,
  `lida` tinyint(1) NOT NULL DEFAULT 0,
  `data_criacao` datetime NOT NULL,
  `data_leitura` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_usuario` (`usuario_id`),
  KEY `idx_tipo` (`tipo`),
  KEY `idx_lida` (`lida`),
  KEY `idx_data` (`data_criacao`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Tabela para histórico de upgrades
CREATE TABLE IF NOT EXISTS `historico_upgrades` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `assistencia_id` int(11) NOT NULL,
  `plano_anterior_id` int(11) NOT NULL,
  `plano_novo_id` int(11) NOT NULL,
  `data_upgrade` datetime NOT NULL,
  `valor_anterior` decimal(10,2) NOT NULL,
  `valor_novo` decimal(10,2) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_assistencia` (`assistencia_id`),
  KEY `idx_data` (`data_upgrade`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Tabela para carrinho do marketplace
CREATE TABLE IF NOT EXISTS `carrinho_marketplace` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `usuario_id` int(11) NOT NULL,
  `produto_id` int(11) NOT NULL,
  `quantidade` int(11) NOT NULL,
  `data_adicao` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `usuario_produto` (`usuario_id`, `produto_id`),
  KEY `idx_usuario` (`usuario_id`),
  KEY `idx_produto` (`produto_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Inserir dados iniciais se necessário

-- Verificar se existe plano Master, se não, criar
INSERT IGNORE INTO `planos` (`id`, `nome`, `preco_mensal`, `taxa_servico`, `acesso_chat`, `acesso_marketplace`, `retirada_presencial`, `selo_fixfacil`, `link_personalizado`, `retirada_express_prioritaria`) 
VALUES 
(1, 'Free', 0.00, 25.0, 0, 0, 0, 0, 0, 0),
(2, 'Premium', 89.90, 20.0, 1, 1, 1, 1, 0, 0),
(3, 'Master', 159.90, 10.0, 1, 1, 1, 1, 1, 1);

-- Atualizar estrutura da tabela usuarios se necessário
ALTER TABLE `usuarios` 
ADD COLUMN IF NOT EXISTS `plano_id` int(11) DEFAULT 1,
ADD INDEX IF NOT EXISTS `idx_plano` (`plano_id`);

-- Atualizar estrutura da tabela solicitacoes_reparo se necessário
ALTER TABLE `solicitacoes_reparo` 
ADD COLUMN IF NOT EXISTS `origem` varchar(50) DEFAULT 'app',
ADD INDEX IF NOT EXISTS `idx_origem` (`origem`);

-- Atualizar estrutura da tabela propostas_assistencia se necessário
ALTER TABLE `propostas_assistencia` 
ADD COLUMN IF NOT EXISTS `data_inicio` datetime DEFAULT NULL,
ADD COLUMN IF NOT EXISTS `data_conclusao` datetime DEFAULT NULL,
ADD COLUMN IF NOT EXISTS `observacoes` text DEFAULT NULL,
ADD INDEX IF NOT EXISTS `idx_data_inicio` (`data_inicio`),
ADD INDEX IF NOT EXISTS `idx_data_conclusao` (`data_conclusao`);
