<?php
/**
 * Debug da consulta de usuário
 */

session_start();

// Verificar se está logado
if (!isset($_SESSION['usuario_id']) || $_SESSION['tipo_usuario'] !== 'assistencia') {
    echo "<h2>❌ Erro de Autenticação</h2>";
    echo "<p>Você precisa estar logado como assistência.</p>";
    echo "<p><a href='../login.php'><PERSON><PERSON><PERSON></a></p>";
    exit();
}

// Configurações do banco de dados
$servername = "localhost";
$username_db = "u680766645_fixfacilnew";
$password_db = "T3cn0l0g1a@";
$dbname = "u680766645_fixfacilnew";

try {
    $conn = new mysqli($servername, $username_db, $password_db, $dbname);
    $conn->set_charset("utf8mb4");
    
    $usuario_id = $_SESSION['usuario_id'];
    
    echo "<h2>🔍 Debug da Consulta de Usuário</h2>";
    echo "<p><strong>Usuário ID:</strong> $usuario_id</p>";
    
    // Testar consulta original (sem novos campos)
    echo "<h3>1️⃣ Consulta Original (sem novos campos):</h3>";
    
    $sql_original = "
        SELECT 
            u.id,
            u.nome,
            u.email,
            u.telefone,
            u.plano_id,
            at.id as assistencia_id,
            at.nome_empresa,
            at.endereco,
            at.telefone as telefone_empresa,
            at.email as email_empresa,
            at.site
        FROM usuarios u
        LEFT JOIN assistencias_tecnicas at ON u.id = at.usuario_id
        WHERE u.id = ? AND u.tipo_usuario = 'assistencia'
    ";
    
    $stmt = $conn->prepare($sql_original);
    $stmt->bind_param("i", $usuario_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $dados_original = $result->fetch_assoc();
    
    if ($dados_original) {
        echo "<p style='color: green;'>✅ Consulta original funcionou</p>";
        echo "<pre>" . print_r($dados_original, true) . "</pre>";
    } else {
        echo "<p style='color: red;'>❌ Consulta original falhou</p>";
    }
    $stmt->close();
    
    // Testar consulta com novos campos
    echo "<h3>2️⃣ Consulta com Novos Campos:</h3>";
    
    $sql_nova = "
        SELECT 
            u.id,
            u.nome,
            u.email,
            u.telefone,
            u.plano_id,
            at.id as assistencia_id,
            at.nome_empresa,
            at.endereco,
            at.cep,
            at.numero_endereco,
            at.complemento,
            at.bairro,
            at.cidade,
            at.estado,
            at.latitude,
            at.longitude,
            at.ponto_referencia,
            at.telefone as telefone_empresa,
            at.email as email_empresa,
            at.site
        FROM usuarios u
        LEFT JOIN assistencias_tecnicas at ON u.id = at.usuario_id
        WHERE u.id = ? AND u.tipo_usuario = 'assistencia'
    ";
    
    $stmt = $conn->prepare($sql_nova);
    if ($stmt) {
        $stmt->bind_param("i", $usuario_id);
        $stmt->execute();
        $result = $stmt->get_result();
        $dados_nova = $result->fetch_assoc();
        
        if ($dados_nova) {
            echo "<p style='color: green;'>✅ Consulta com novos campos funcionou</p>";
            echo "<pre>" . print_r($dados_nova, true) . "</pre>";
        } else {
            echo "<p style='color: red;'>❌ Consulta com novos campos falhou</p>";
        }
        $stmt->close();
    } else {
        echo "<p style='color: red;'>❌ Erro ao preparar consulta: " . $conn->error . "</p>";
    }
    
    // Verificar se as colunas existem
    echo "<h3>3️⃣ Verificar Colunas da Tabela:</h3>";
    
    $result_colunas = $conn->query("SHOW COLUMNS FROM assistencias_tecnicas");
    $colunas_existentes = [];
    
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Campo</th><th>Tipo</th><th>Nulo</th><th>Padrão</th></tr>";
    
    while ($row = $result_colunas->fetch_assoc()) {
        $colunas_existentes[] = $row['Field'];
        echo "<tr>";
        echo "<td>{$row['Field']}</td>";
        echo "<td>{$row['Type']}</td>";
        echo "<td>{$row['Null']}</td>";
        echo "<td>" . ($row['Default'] ?? 'NULL') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Verificar quais campos novos existem
    echo "<h3>4️⃣ Status dos Novos Campos:</h3>";
    
    $campos_novos = ['cep', 'numero_endereco', 'complemento', 'bairro', 'cidade', 'estado', 'latitude', 'longitude', 'ponto_referencia'];
    
    echo "<ul>";
    foreach ($campos_novos as $campo) {
        if (in_array($campo, $colunas_existentes)) {
            echo "<li style='color: green;'>✅ $campo - Existe</li>";
        } else {
            echo "<li style='color: red;'>❌ $campo - Não existe</li>";
        }
    }
    echo "</ul>";
    
    // Dados diretos da assistência
    echo "<h3>5️⃣ Dados Diretos da Assistência:</h3>";
    
    if (isset($dados_original['assistencia_id'])) {
        $assistencia_id = $dados_original['assistencia_id'];
        $sql_assistencia = "SELECT * FROM assistencias_tecnicas WHERE id = ?";
        $stmt = $conn->prepare($sql_assistencia);
        $stmt->bind_param("i", $assistencia_id);
        $stmt->execute();
        $result = $stmt->get_result();
        $dados_assistencia = $result->fetch_assoc();
        
        if ($dados_assistencia) {
            echo "<p style='color: green;'>✅ Dados da assistência encontrados</p>";
            echo "<pre>" . print_r($dados_assistencia, true) . "</pre>";
        } else {
            echo "<p style='color: red;'>❌ Dados da assistência não encontrados</p>";
        }
        $stmt->close();
    } else {
        echo "<p style='color: orange;'>⚠️ ID da assistência não encontrado</p>";
    }
    
    $conn->close();
    
} catch (Exception $e) {
    echo "<h3 style='color: red;'>❌ Erro:</h3>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<br><hr>";
echo "<p><a href='perfil.php'>← Voltar ao Perfil</a></p>";
?>
