<?php
/**
 * Teste das Páginas da Assistência
 * Verifica se todas as páginas estão funcionando corretamente
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔧 Teste das Páginas da Assistência</h1>";

// 1. Testar conexão com banco
echo "<h2>1. ✅ Teste de Conexão com Banco</h2>";

try {
    require_once 'config/database.php';
    $db = getDatabase();
    echo "✅ Conexão com banco estabelecida<br>";
    
    // Testar query simples
    $result = $db->query("SELECT COUNT(*) as total FROM usuarios WHERE tipo_usuario = 'assistencia'");
    $row = $result->fetch_assoc();
    echo "✅ Total de assistências: " . $row['total'] . "<br>";
    
} catch (Exception $e) {
    echo "❌ Erro na conexão: " . $e->getMessage() . "<br>";
}

// 2. Verificar estrutura das tabelas
echo "<h2>2. ✅ Verificação de Tabelas</h2>";

$tabelas_necessarias = [
    'usuarios' => 'Usuários do sistema',
    'assistencias_tecnicas' => 'Dados das assistências',
    'planos' => 'Planos disponíveis',
    'assinaturas_assistencias' => 'Assinaturas ativas',
    'solicitacoes_reparo' => 'Solicitações de reparo',
    'propostas_assistencia' => 'Propostas enviadas',
    'celulares_usuarios' => 'Celulares cadastrados'
];

try {
    foreach ($tabelas_necessarias as $tabela => $descricao) {
        $result = $db->query("SHOW TABLES LIKE '$tabela'");
        if ($result->num_rows > 0) {
            // Contar registros
            $count_result = $db->query("SELECT COUNT(*) as total FROM $tabela");
            $count_row = $count_result->fetch_assoc();
            echo "✅ <strong>$tabela</strong>: $descricao (" . $count_row['total'] . " registros)<br>";
        } else {
            echo "❌ <strong>$tabela</strong>: NÃO existe<br>";
        }
    }
} catch (Exception $e) {
    echo "❌ Erro ao verificar tabelas: " . $e->getMessage() . "<br>";
}

// 3. Verificar dados de exemplo
echo "<h2>3. ✅ Verificação de Dados</h2>";

try {
    // Assistências cadastradas
    $result = $db->query("
        SELECT at.id, at.nome_empresa, u.nome, u.email 
        FROM assistencias_tecnicas at 
        JOIN usuarios u ON at.usuario_id = u.id 
        LIMIT 5
    ");
    
    echo "<h4>Assistências Cadastradas:</h4>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
    echo "<tr><th>ID</th><th>Empresa</th><th>Responsável</th><th>Email</th></tr>";
    
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row['id'] . "</td>";
        echo "<td>" . htmlspecialchars($row['nome_empresa']) . "</td>";
        echo "<td>" . htmlspecialchars($row['nome']) . "</td>";
        echo "<td>" . htmlspecialchars($row['email']) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Planos disponíveis
    $result = $db->query("SELECT * FROM planos WHERE status = 'ativo'");
    echo "<h4>Planos Disponíveis:</h4>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
    echo "<tr><th>Nome</th><th>Preço</th><th>Taxa</th><th>Chat</th><th>Marketplace</th></tr>";
    
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($row['nome']) . "</td>";
        echo "<td>R$ " . number_format($row['preco_mensal'], 2, ',', '.') . "</td>";
        echo "<td>" . $row['taxa_servico'] . "%</td>";
        echo "<td>" . ($row['acesso_chat'] ? 'Sim' : 'Não') . "</td>";
        echo "<td>" . ($row['acesso_marketplace'] ? 'Sim' : 'Não') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Solicitações recentes
    $result = $db->query("
        SELECT sr.id, sr.marca, sr.modelo, sr.status, sr.data_solicitacao, u.nome 
        FROM solicitacoes_reparo sr 
        JOIN usuarios u ON sr.usuario_id = u.id 
        WHERE sr.visivel = 1 
        ORDER BY sr.data_solicitacao DESC 
        LIMIT 5
    ");
    
    echo "<h4>Solicitações Recentes:</h4>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
    echo "<tr><th>ID</th><th>Dispositivo</th><th>Cliente</th><th>Status</th><th>Data</th></tr>";
    
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row['id'] . "</td>";
        echo "<td>" . htmlspecialchars($row['marca'] . ' ' . $row['modelo']) . "</td>";
        echo "<td>" . htmlspecialchars($row['nome']) . "</td>";
        echo "<td>" . $row['status'] . "</td>";
        echo "<td>" . date('d/m/Y H:i', strtotime($row['data_solicitacao'])) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
} catch (Exception $e) {
    echo "❌ Erro ao verificar dados: " . $e->getMessage() . "<br>";
}

// 4. Testar páginas principais
echo "<h2>4. 🔗 Teste das Páginas</h2>";

$paginas = [
    'dashboard.php' => 'Dashboard Principal',
    'dashboard_simples.php' => 'Dashboard Simples (Teste)',
    'solicitacoes.php' => 'Lista de Solicitações',
    'propostas.php' => 'Minhas Propostas',
    'reparos.php' => 'Reparos em Andamento',
    'carteira.php' => 'Carteira e Pagamentos',
    'perfil.php' => 'Perfil da Assistência'
];

echo "<ul>";
foreach ($paginas as $arquivo => $descricao) {
    if (file_exists($arquivo)) {
        echo "<li>✅ <a href='$arquivo' target='_blank'>$descricao</a> - Arquivo existe</li>";
    } else {
        echo "<li>❌ <strong>$arquivo</strong> - Arquivo NÃO existe</li>";
    }
}
echo "</ul>";

// 5. Verificar sessão
echo "<h2>5. 👤 Verificação de Sessão</h2>";

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

if (isset($_SESSION['usuario_id'])) {
    echo "✅ Usuário logado: ID " . $_SESSION['usuario_id'] . "<br>";
    echo "✅ Tipo: " . ($_SESSION['tipo_usuario'] ?? 'não definido') . "<br>";
    
    if ($_SESSION['tipo_usuario'] === 'assistencia') {
        echo "✅ Acesso autorizado para assistência<br>";
        echo "🔗 <a href='dashboard.php'>Ir para Dashboard</a><br>";
    } else {
        echo "⚠️ Usuário não é do tipo assistência<br>";
    }
} else {
    echo "⚠️ Nenhum usuário logado<br>";
    echo "🔗 <a href='../login.php'>Fazer login</a><br>";
}

// 6. Informações do sistema
echo "<h2>6. 🖥️ Informações do Sistema</h2>";

echo "PHP Version: " . phpversion() . "<br>";
echo "MySQL Extension: " . (extension_loaded('mysqli') ? 'Carregada' : 'NÃO carregada') . "<br>";
echo "Session Status: " . session_status() . "<br>";
echo "Timezone: " . date_default_timezone_get() . "<br>";
echo "Data/Hora atual: " . date('d/m/Y H:i:s') . "<br>";

// 7. Teste de autenticação
echo "<h2>7. 🔐 Teste de Autenticação</h2>";

try {
    require_once 'config/auth.php';
    $auth = getAuth();
    echo "✅ Classe de autenticação carregada<br>";
    
    if (isset($_SESSION['usuario_id'])) {
        $usuario = $auth->getUsuarioLogado();
        if ($usuario) {
            echo "✅ Dados do usuário obtidos:<br>";
            echo "- Nome: " . htmlspecialchars($usuario['nome']) . "<br>";
            echo "- Email: " . htmlspecialchars($usuario['email']) . "<br>";
            echo "- Assistência ID: " . ($usuario['assistencia_id'] ?? 'Não encontrado') . "<br>";
            
            $plano = $auth->getPlanoInfo($usuario['id']);
            if ($plano) {
                echo "✅ Plano ativo: " . $plano['nome'] . " (Taxa: " . $plano['taxa_servico'] . "%)<br>";
            } else {
                echo "⚠️ Plano não encontrado<br>";
            }
        } else {
            echo "❌ Erro ao obter dados do usuário<br>";
        }
    }
    
} catch (Exception $e) {
    echo "❌ Erro na autenticação: " . $e->getMessage() . "<br>";
}

?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    background: #f8fafc;
}

h1 {
    color: #1e40af;
    border-bottom: 3px solid #3b82f6;
    padding-bottom: 10px;
}

h2 {
    color: #374151;
    margin-top: 30px;
    padding: 10px;
    background: white;
    border-left: 4px solid #10b981;
    border-radius: 4px;
}

h4 {
    color: #4b5563;
    margin-top: 20px;
}

table {
    background: white;
    margin: 10px 0;
    border-radius: 4px;
    overflow: hidden;
    font-size: 14px;
}

th {
    background: #f3f4f6;
    padding: 10px;
    font-weight: bold;
    text-align: left;
}

td {
    padding: 8px 10px;
    border-bottom: 1px solid #e5e7eb;
}

a {
    color: #2563eb;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}

ul {
    background: white;
    padding: 20px;
    border-radius: 4px;
    border-left: 4px solid #3b82f6;
}

li {
    margin: 5px 0;
}
</style>
