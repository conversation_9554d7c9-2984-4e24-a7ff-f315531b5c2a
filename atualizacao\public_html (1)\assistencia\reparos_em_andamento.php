<?php
session_start();
if (!isset($_SESSION['usuario_id']) || $_SESSION['tipo_usuario'] != 'assistencia') {
    header('Location: ../login.php');
    exit();
}

$usuario_id = $_SESSION['usuario_id'];
$nome_usuario = $_SESSION['nome'];

// Conexão com o banco de dados
$servername = "localhost";
$username_db = "u680766645_fixfacilnew";
$password_db = "T3cn0l0g1a@";
$dbname = "u680766645_fixfacilnew";

$conn = new mysqli($servername, $username_db, $password_db, $dbname);

if ($conn->connect_error) {
    die("Falha na conexão: " . $conn->connect_error);
}

$conn->set_charset("utf8");

// Inicializar variáveis para mensagens
$mensagem = "";
$tipo_alerta = "";

// Obter o assistencia_id correspondente ao usuario_id
$sql_assistencia = "SELECT id FROM assistencias_tecnicas WHERE usuario_id = ?";
$stmt_assistencia = $conn->prepare($sql_assistencia);
$stmt_assistencia->bind_param("i", $usuario_id);
$stmt_assistencia->execute();
$result_assistencia = $stmt_assistencia->get_result();
if ($row_assistencia = $result_assistencia->fetch_assoc()) {
    $assistencia_id = $row_assistencia['id'];
} else {
    die("Assistência técnica não encontrada.");
}
$stmt_assistencia->close();

// Processar solicitações via AJAX
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action'])) {
    $response = array('success' => false, 'message' => '');

    if ($_POST['action'] == 'solicitar_retirada') {
        $proposta_id = isset($_POST['proposta_id']) ? intval($_POST['proposta_id']) : 0;
        if ($proposta_id > 0) {
            // Verificar se já existe uma solicitação de retirada para esta proposta
            $sql_check = "SELECT id FROM retiradas_express WHERE proposta_id = ?";
            $stmt_check = $conn->prepare($sql_check);
            $stmt_check->bind_param('i', $proposta_id);
            $stmt_check->execute();
            $result_check = $stmt_check->get_result();
            if ($result_check->num_rows > 0) {
                $response['message'] = 'Já existe uma solicitação de retirada para esta proposta.';
            } else {
                // Inserir nova solicitação de retirada
                $sql = "INSERT INTO retiradas_express (proposta_id, assistencia_id, data_solicitacao, status) VALUES (?, ?, NOW(), 'pendente')";
                $stmt = $conn->prepare($sql);
                $stmt->bind_param('ii', $proposta_id, $assistencia_id);
                if ($stmt->execute()) {
                    $response['success'] = true;
                    $response['message'] = 'Solicitação de Retirada Express realizada com sucesso!';
                } else {
                    $response['message'] = 'Erro ao registrar a solicitação.';
                }
                $stmt->close();
            }
            $stmt_check->close();
        } else {
            $response['message'] = 'ID da proposta inválido.';
        }
    } elseif ($_POST['action'] == 'atualizar_status') {
        $proposta_id = isset($_POST['proposta_id']) ? intval($_POST['proposta_id']) : 0;
        $novo_status = isset($_POST['novo_status']) ? $_POST['novo_status'] : '';
        
        // Validar status
        $status_validos = ['em andamento', 'concluída', 'rejeitada'];
        if ($proposta_id > 0 && in_array($novo_status, $status_validos)) {
            $sql_update = "UPDATE propostas_assistencia SET status = ? WHERE id = ? AND assistencia_id = ?";
            $stmt_update = $conn->prepare($sql_update);
            $stmt_update->bind_param('sii', $novo_status, $proposta_id, $assistencia_id);
            if ($stmt_update->execute() && $stmt_update->affected_rows > 0) {
                $response['success'] = true;
                $response['message'] = 'Status atualizado com sucesso para "' . ucfirst($novo_status) . '"!';
            } else {
                $response['message'] = 'Erro ao atualizar o status ou nenhuma alteração feita.';
            }
            $stmt_update->close();
        } else {
            $response['message'] = 'Dados inválidos para atualização.';
        }
    } else {
        $response['message'] = 'Ação inválida.';
    }

    header('Content-Type: application/json');
    echo json_encode($response);
    exit();
}

// Obter os reparos em andamento (propostas com status 'em andamento' ou 'Em Andamento')
$sql = "SELECT pa.id AS proposta_id, pa.preco, pa.prazo, pa.observacoes, pa.status, pa.data_proposta,
        sr.id AS solicitacao_id, sr.descricao_problema, sr.dispositivo, sr.marca, sr.modelo, u.nome AS nome_cliente,
        re.id AS retirada_id, re.status AS retirada_status
        FROM propostas_assistencia pa
        INNER JOIN solicitacoes_reparo sr ON pa.solicitacao_id = sr.id
        INNER JOIN usuarios u ON sr.usuario_id = u.id
        LEFT JOIN retiradas_express re ON pa.id = re.proposta_id
        WHERE pa.assistencia_id = ? AND (pa.status = 'em andamento' OR pa.status = 'Em Andamento')
        ORDER BY pa.data_proposta DESC";
$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $assistencia_id);
$stmt->execute();
$result = $stmt->get_result();

// Obter contagens por status
$sql_count = "SELECT 
                SUM(CASE WHEN (status = 'em andamento' OR status = 'Em Andamento') THEN 1 ELSE 0 END) AS andamento,
                SUM(CASE WHEN status = 'concluída' THEN 1 ELSE 0 END) AS concluida,
                COUNT(*) AS total
              FROM propostas_assistencia 
              WHERE assistencia_id = ?";
$stmt_count = $conn->prepare($sql_count);
$stmt_count->bind_param("i", $assistencia_id);
$stmt_count->execute();
$result_count = $stmt_count->get_result();
$counts = $result_count->fetch_assoc();

// Obter contagem de retiradas
$sql_retiradas = "SELECT COUNT(*) AS total FROM retiradas_express WHERE assistencia_id = ?";
$stmt_retiradas = $conn->prepare($sql_retiradas);
$stmt_retiradas->bind_param("i", $assistencia_id);
$stmt_retiradas->execute();
$result_retiradas = $stmt_retiradas->get_result();
$retiradas_count = $result_retiradas->fetch_assoc();
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <title>Reparos em Andamento - FixFácil</title>
    <!-- Meta Tags para Responsividade -->
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome 6 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- SweetAlert2 CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">
    <!-- DataTables CSS -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.4/css/dataTables.bootstrap5.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Estilos personalizados -->
    <style>
        :root {
            --primary-color: #2563eb;
            --primary-dark: #1d4ed8;
            --secondary-color: #475569;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --light-bg: #f8fafc;
            --card-bg: #ffffff;
            --text-color: #1e293b;
            --text-muted: #64748b;
            --border-radius: 12px;
            --shadow-sm: 0 1px 3px rgba(0,0,0,0.1);
            --shadow-md: 0 4px 6px -1px rgba(0,0,0,0.1), 0 2px 4px -1px rgba(0,0,0,0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0,0,0,0.1), 0 4px 6px -2px rgba(0,0,0,0.05);
        }
        
        body {
            font-family: 'Inter', sans-serif;
            background-color: var(--light-bg);
            color: var(--text-color);
            margin-bottom: 80px;
            padding-top: 70px;
        }
        
        /* Navbar */
        .navbar {
            background-color: var(--card-bg);
            box-shadow: var(--shadow-sm);
            padding: 12px 0;
        }
        
        .navbar-brand {
            font-weight: 700;
            color: var(--primary-color) !important;
        }
        
        .navbar .nav-link {
            color: var(--text-color) !important;
            font-weight: 500;
            padding: 8px 16px;
            border-radius: var(--border-radius);
            transition: all 0.3s ease;
        }
        
        .navbar .nav-link:hover {
            background-color: rgba(37, 99, 235, 0.1);
            color: var(--primary-color) !important;
        }
        
        .navbar .nav-link.active {
            background-color: var(--primary-color);
            color: white !important;
        }
        
        /* Conteúdo Principal */
        .main-content {
            padding: 20px 12px;
        }
        
        .header-section {
            margin-bottom: 24px;
        }
        
        .header-section h1 {
            font-weight: 700;
            font-size: 1.75rem;
            margin-bottom: 8px;
            color: var(--text-color);
        }
        
        .header-section p {
            color: var(--text-muted);
            font-size: 1rem;
            margin-bottom: 0;
        }
        
        /* Cards de resumo */
        .stats-card {
            background-color: var(--card-bg);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-md);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            padding: 20px;
            height: 100%;
            position: relative;
            overflow: hidden;
        }
        
        .stats-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-lg);
        }
        
        .stats-card .card-icon {
            position: absolute;
            top: 15px;
            right: 15px;
            font-size: 2.5rem;
            opacity: 0.2;
            color: inherit;
        }
        
        .stats-card .card-title {
            font-weight: 600;
            font-size: 0.9rem;
            margin-bottom: 10px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .stats-card .card-value {
            font-weight: 700;
            font-size: 2rem;
            margin-bottom: 0;
        }
        
        .status-andamento { color: var(--primary-color); }
        .status-concluida { color: var(--success-color); }
        .status-retiradas { color: var(--warning-color); }
        .status-total { color: var(--secondary-color); }
        
        /* Tabela de propostas */
        .tabela-container {
            background-color: var(--card-bg);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-md);
            padding: 20px;
            margin-bottom: 30px;
            overflow: hidden;
        }
        
        .table-striped > tbody > tr:nth-of-type(odd) > * {
            background-color: rgba(0, 0, 0, 0.02);
        }
        
        .table > :not(caption) > * > * {
            padding: 12px 16px;
        }
        
        .status-badge {
            padding: 6px 12px;
            border-radius: 50px;
            font-weight: 600;
            font-size: 0.75rem;
            text-transform: uppercase;
        }
        
        .status-badge-andamento {
            background-color: rgba(37, 99, 235, 0.1);
            color: var(--primary-color);
        }
        
        .status-badge-concluida {
            background-color: rgba(16, 185, 129, 0.1);
            color: var(--success-color);
        }
        
        .status-badge-retirada {
            background-color: rgba(245, 158, 11, 0.1);
            color: var(--warning-color);
        }
        
        .status-badge-pendente {
            background-color: rgba(209, 213, 219, 0.3);
            color: var(--text-muted);
        }
        
        /* Botões de ação */
        .action-btn {
            padding: 6px 12px;
            border-radius: var(--border-radius);
            font-weight: 500;
            font-size: 0.8rem;
            margin-right: 5px;
            margin-bottom: 5px;
            display: inline-flex;
            align-items: center;
        }
        
        .action-btn i {
            margin-right: 5px;
        }
        
        /* Modal */
        .modal-content {
            border-radius: var(--border-radius);
            border: none;
            box-shadow: var(--shadow-lg);
        }
        
        .modal-header {
            background-color: var(--primary-color);
            color: white;
            border-top-left-radius: var(--border-radius);
            border-top-right-radius: var(--border-radius);
            border-bottom: none;
            padding: 20px;
        }
        
        .modal-title {
            font-weight: 600;
        }
        
        .modal-header .btn-close {
            color: white;
            background: transparent url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='white'%3e%3cpath d='M.293.293a1 1 0 011.414 0L8 6.586 14.293.293a1 1 0 111.414 1.414L9.414 8l6.293 6.293a1 1 0 01-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 01-1.414-1.414L6.586 8 .293 1.707a1 1 0 010-1.414z'/%3e%3c/svg%3e") center/1em auto no-repeat;
        }
        
        /* Mobile Menu */
        .mobile-menu {
            position: fixed;
            bottom: 0;
            width: 100%;
            background-color: var(--card-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            z-index: 1000;
            padding: 8px 0;
            display: flex;
            justify-content: space-around;
        }
        
        .mobile-menu .menu-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 8px 0;
            width: 20%;
            text-decoration: none;
        }
        
        .mobile-menu .menu-item i {
            font-size: 20px;
            margin-bottom: 4px;
            color: var(--text-muted);
            transition: color 0.3s ease;
        }
        
        .mobile-menu .menu-item span {
            font-size: 12px;
            color: var(--text-muted);
            transition: color 0.3s ease;
        }
        
        .mobile-menu .menu-item.active i,
        .mobile-menu .menu-item.active span {
            color: var(--primary-color);
            font-weight: 600;
        }
        
        .mobile-menu .menu-item:hover i,
        .mobile-menu .menu-item:hover span {
            color: var(--primary-dark);
        }
        
        /* Esconder menu mobile em desktop */
        @media (min-width: 992px) {
            .mobile-menu {
                display: none;
            }
        }
        
        /* Ajustes para mobile */
        @media (max-width: 767px) {
            .main-content {
                padding: 15px 10px;
            }
            
            .stats-card {
                margin-bottom: 15px;
            }
            
            .action-btn {
                width: 100%;
                margin-right: 0;
                margin-bottom: 8px;
                justify-content: center;
            }
            
            .tabela-container {
                padding: 15px 10px;
            }
            
            .table > :not(caption) > * > * {
                padding: 10px 12px;
            }
            
            .modal-dialog {
                margin: 10px;
            }
        }

        /* Formulário de atualização de status customizado */
        .status-select-container {
            position: relative;
            margin-bottom: 10px;
        }
        
        .custom-select {
            display: block;
            width: 100%;
            padding: 10px 14px;
            border: 1px solid rgba(0, 0, 0, 0.1);
            border-radius: 8px;
            background-color: white;
            font-size: 0.9rem;
            font-weight: 500;
            appearance: none;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px 12px;
            transition: all 0.2s ease;
        }
        
        .custom-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }
        
        /* Alerta personalizado */
        .custom-alert {
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 24px;
            border-left: 4px solid;
        }
        
        .alert-success {
            background-color: rgba(16, 185, 129, 0.1);
            border-color: var(--success-color);
            color: #065f46;
        }
        
        .alert-danger {
            background-color: rgba(239, 68, 68, 0.1);
            border-color: var(--danger-color);
            color: #b91c1c;
        }
        
        .alert-info {
            background-color: rgba(37, 99, 235, 0.1);
            border-color: var(--primary-color);
            color: #1e40af;
        }
        
        /* Placeholder para sem dados */
        .sem-dados {
            text-align: center;
            padding: 40px 20px;
        }
        
        .sem-dados i {
            font-size: 3rem;
            color: var(--text-muted);
            margin-bottom: 15px;
        }
        
        .sem-dados h4 {
            font-weight: 600;
            margin-bottom: 10px;
        }
        
        .sem-dados p {
            color: var(--text-muted);
            max-width: 500px;
            margin: 0 auto;
        }
        
        /* Loading overlay */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(255, 255, 255, 0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.3s ease, visibility 0.3s ease;
        }
        
        .loading-overlay.show {
            opacity: 1;
            visibility: visible;
        }
        
        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid rgba(37, 99, 235, 0.1);
            border-radius: 50%;
            border-top-color: var(--primary-color);
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <!-- Overlay de carregamento -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="spinner"></div>
    </div>

    <!-- Cabeçalho (Navbar) -->
    <nav class="navbar navbar-expand-lg fixed-top navbar-light">
        <div class="container">
            <a class="navbar-brand" href="#">FixFácil</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" 
                    aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="home.php">Painel</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="solicitacoes.php">Solicitações</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="propostas_enviadas.php">Propostas</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="reparos_em_andamento.php">Reparos</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="meumarktplace.php">Marketplace</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="solicitar_pecas.php">Peças</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="carteira.php">Carteira</a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-user-circle"></i>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="perfil.php">Meu Perfil</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../logout.php">Sair</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Conteúdo Principal -->
    <div class="container main-content">
        <!-- Cabeçalho da página -->
        <div class="header-section">
            <h1>Reparos em Andamento</h1>
            <p>Gerencie os dispositivos que estão sendo reparados pela sua assistência técnica.</p>
        </div>

        <!-- Exibir Mensagens -->
        <?php if (!empty($mensagem)): ?>
            <div class="custom-alert alert-<?php echo $tipo_alerta; ?>" role="alert">
                <?php echo htmlspecialchars($mensagem); ?>
            </div>
        <?php endif; ?>

        <!-- Cards de Resumo -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stats-card">
                    <i class="fas fa-tools card-icon status-andamento"></i>
                    <div class="card-title status-andamento">Em Andamento</div>
                    <div class="card-value status-andamento"><?php echo $counts['andamento']; ?></div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stats-card">
                    <i class="fas fa-check-circle card-icon status-concluida"></i>
                    <div class="card-title status-concluida">Concluídos</div>
                    <div class="card-value status-concluida"><?php echo $counts['concluida']; ?></div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stats-card">
                    <i class="fas fa-truck card-icon status-retiradas"></i>
                    <div class="card-title status-retiradas">Retiradas Express</div>
                    <div class="card-value status-retiradas"><?php echo $retiradas_count['total']; ?></div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stats-card">
                    <i class="fas fa-chart-bar card-icon status-total"></i>
                    <div class="card-title status-total">Total</div>
                    <div class="card-value status-total"><?php echo $counts['total']; ?></div>
                </div>
            </div>
        </div>

        <!-- Lista de Reparos -->
        <div class="tabela-container">
            <div class="table-responsive">
                <table class="table table-striped" id="tabela-reparos">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Cliente</th>
                            <th>Dispositivo</th>
                            <th>Status</th>
                            <th>Início</th>
                            <th>Retirada</th>
                            <th>Ações</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if ($result->num_rows > 0): ?>
                            <?php while ($reparo = $result->fetch_assoc()): ?>
                                <tr id="reparo-<?php echo $reparo['proposta_id']; ?>">
                                    <td>#<?php echo $reparo['proposta_id']; ?></td>
                                    <td><?php echo htmlspecialchars($reparo['nome_cliente']); ?></td>
                                    <td>
                                        <?php echo htmlspecialchars($reparo['dispositivo']); ?><br>
                                        <small class="text-muted"><?php echo htmlspecialchars($reparo['marca'] . ' ' . $reparo['modelo']); ?></small>
                                    </td>
                                    <td>
                                        <span class="status-badge status-badge-andamento">Em Andamento</span>
                                    </td>
                                    <td><?php echo date('d/m/Y', strtotime($reparo['data_proposta'])); ?></td>
                                    <td>
                                        <?php if (is_null($reparo['retirada_id'])): ?>
                                            <span class="status-badge status-badge-pendente">Não Solicitada</span>
                                        <?php else: ?>
                                            <span class="status-badge status-badge-retirada"><?php echo ucfirst($reparo['retirada_status']); ?></span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="d-flex flex-column flex-md-row">
                                            <button class="btn btn-primary action-btn btn-atualizar-status mb-2 mb-md-0 me-md-2" data-id="<?php echo $reparo['proposta_id']; ?>" data-bs-toggle="modal" data-bs-target="#modalAtualizarStatus">
                                                <i class="fas fa-edit"></i> Atualizar Status
                                            </button>
                                            <?php if (is_null($reparo['retirada_id'])): ?>
                                                <button class="btn btn-warning action-btn btn-solicitar-retirada" data-id="<?php echo $reparo['proposta_id']; ?>">
                                                    <i class="fas fa-truck"></i> Solicitar Retirada
                                                </button>
                                            <?php else: ?>
                                                <button class="btn btn-secondary action-btn" disabled>
                                                    <i class="fas fa-check"></i> Retirada Solicitada
                                                </button>
                                            <?php endif; ?>
                                            <button class="btn btn-info action-btn btn-detalhes mt-2 mt-md-0 ms-md-2" data-id="<?php echo $reparo['proposta_id']; ?>" data-solicitacao="<?php echo $reparo['solicitacao_id']; ?>" data-bs-toggle="modal" data-bs-target="#modalDetalhes">
                                                <i class="fas fa-eye"></i> Detalhes
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            <?php endwhile; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="7">
                                    <div class="sem-dados">
                                        <i class="fas fa-tools"></i>
                                        <h4>Nenhum reparo em andamento</h4>
                                        <p>Não há dispositivos em reparo no momento. Reparos começam quando uma proposta tem seu status alterado para "Em Andamento".</p>
                                    </div>
                                </td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Modal para Atualizar Status -->
    <div class="modal fade" id="modalAtualizarStatus" tabindex="-1" aria-labelledby="modalAtualizarStatusLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="modalAtualizarStatusLabel">Atualizar Status do Reparo</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Fechar"></button>
                </div>
                <div class="modal-body">
                    <form id="formAtualizarStatus">
                        <input type="hidden" id="proposta_id" name="proposta_id" value="">
                        <div class="mb-3">
                            <label for="novo_status" class="form-label">Selecione o novo status:</label>
                            <div class="status-select-container">
                                <select id="novo_status" name="novo_status" class="custom-select">
                                    <option value="em andamento">Em Andamento</option>
                                    <option value="concluída">Concluído</option>
                                    <option value="rejeitada">Cancelado</option>
                                </select>
                            </div>
                        </div>
                        <div id="status-feedback" class="text-danger mb-3" style="display: none;"></div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="button" class="btn btn-primary" id="btnSalvarStatus">Salvar Alterações</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal para Detalhes do Reparo -->
    <div class="modal fade" id="modalDetalhes" tabindex="-1" aria-labelledby="modalDetalhesLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="modalDetalhesLabel">Detalhes do Reparo</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Fechar"></button>
                </div>
                <div class="modal-body">
                    <div class="text-center p-4" id="loading-detalhes">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Carregando...</span>
                        </div>
                        <p class="mt-2">Carregando detalhes...</p>
                    </div>
                    <div id="detalhes-conteudo" style="display: none;">
                        <!-- O conteúdo será preenchido via JavaScript -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fechar</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Menu Mobile (fixo na parte inferior) -->
    <div class="mobile-menu d-lg-none">
        <a href="home.php" class="menu-item">
            <i class="fas fa-home"></i>
            <span>Painel</span>
        </a>
        <a href="solicitacoes.php" class="menu-item">
            <i class="fas fa-inbox"></i>
            <span>Solicitações</span>
        </a>
        <a href="propostas_enviadas.php" class="menu-item">
            <i class="fas fa-paper-plane"></i>
            <span>Propostas</span>
        </a>
        <a href="reparos_em_andamento.php" class="menu-item active">
            <i class="fas fa-tools"></i>
            <span>Reparos</span>
        </a>
        <a href="perfil.php" class="menu-item">
            <i class="fas fa-user"></i>
            <span>Perfil</span>
        </a>
    </div>

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="https://cdn.datatables.net/1.13.4/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.4/js/dataTables.bootstrap5.min.js"></script>
    <script>
        // Suprimir avisos do DataTables (use apenas durante desenvolvimento)
$.fn.dataTable.ext.errMode = 'none';
        $(document).ready(function() {
            // Inicializar DataTables
            const table = $('#tabela-reparos').DataTable({
                language: {
                    url: '//cdn.datatables.net/plug-ins/1.13.4/i18n/pt-BR.json',
                },
                responsive: true,
                paging: true,
                ordering: true,
                info: true,
                pageLength: 10,
                lengthMenu: [5, 10, 25, 50],
                dom: '<"top"f>rt<"bottom"ilp><"clear">',
                columnDefs: [
                    { orderable: false, targets: -1 } // Desativar ordenação na coluna de ações
                ]
            });
            
            // Função para mostrar overlay de carregamento
            function showLoading() {
                $('#loadingOverlay').addClass('show');
            }

            // Função para esconder overlay de carregamento
            function hideLoading() {
                $('#loadingOverlay').removeClass('show');
            }

            // Atualizar status do reparo
            $('.btn-atualizar-status').on('click', function() {
                const propostaId = $(this).data('id');
                $('#proposta_id').val(propostaId);
                $('#status-feedback').hide();
            });

            // Salvar alteração de status
            $('#btnSalvarStatus').on('click', function() {
                const propostaId = $('#proposta_id').val();
                const novoStatus = $('#novo_status').val();
                
                if (!novoStatus) {
                    $('#status-feedback').text('Selecione um status válido.').show();
                    return;
                }
                
                showLoading();
                
                $.ajax({
                    url: 'reparos_em_andamento.php',
                    type: 'POST',
                    data: {
                        action: 'atualizar_status',
                        proposta_id: propostaId,
                        novo_status: novoStatus
                    },
                    dataType: 'json',
                    success: function(response) {
                        hideLoading();
                        
                        if (response.success) {
                            $('#modalAtualizarStatus').modal('hide');
                            
                            Swal.fire({
                                icon: 'success',
                                title: 'Sucesso!',
                                text: response.message,
                                timer: 2000,
                                timerProgressBar: true
                            }).then(() => {
                                // Recarregar a página para mostrar as alterações
                                location.reload();
                            });
                        } else {
                            $('#status-feedback').text(response.message).show();
                        }
                    },
                    error: function() {
                        hideLoading();
                        $('#status-feedback').text('Erro de conexão. Tente novamente.').show();
                    }
                });
            });

            // Solicitar retirada express
            $('.btn-solicitar-retirada').on('click', function() {
                const propostaId = $(this).data('id');
                
                Swal.fire({
                    title: 'Solicitar Retirada Express?',
                    text: 'Isso enviará uma solicitação para a retirada do dispositivo após o reparo.',
                    icon: 'question',
                    showCancelButton: true,
                    confirmButtonColor: '#f59e0b',
                    cancelButtonColor: '#6c757d',
                    confirmButtonText: 'Sim, solicitar',
                    cancelButtonText: 'Cancelar'
                }).then((result) => {
                    if (result.isConfirmed) {
                        showLoading();
                        
                        $.ajax({
                            url: 'reparos_em_andamento.php',
                            type: 'POST',
                            data: {
                                action: 'solicitar_retirada',
                                proposta_id: propostaId
                            },
                            dataType: 'json',
                            success: function(response) {
                                hideLoading();
                                
                                if (response.success) {
                                    Swal.fire({
                                        icon: 'success',
                                        title: 'Sucesso!',
                                        text: response.message,
                                        timer: 2000,
                                        timerProgressBar: true
                                    }).then(() => {
                                        // Atualizar botão na interface
                                        const btn = $(`button.btn-solicitar-retirada[data-id="${propostaId}"]`);
                                        btn.replaceWith(`
                                            <button class="btn btn-secondary action-btn" disabled>
                                                <i class="fas fa-check"></i> Retirada Solicitada
                                            </button>
                                        `);
                                    });
                                } else {
                                    Swal.fire({
                                        icon: 'error',
                                        title: 'Erro',
                                        text: response.message
                                    });
                                }
                            },
                            error: function() {
                                hideLoading();
                                Swal.fire({
                                    icon: 'error',
                                    title: 'Erro',
                                    text: 'Ocorreu um erro de conexão. Tente novamente mais tarde.'
                                });
                            }
                        });
                    }
                });
            });

            // Visualizar detalhes do reparo
            $('.btn-detalhes').on('click', function() {
                const propostaId = $(this).data('id');
                const solicitacaoId = $(this).data('solicitacao');
                
                // Mostrar loader e esconder conteúdo anterior
                $('#loading-detalhes').show();
                $('#detalhes-conteudo').hide();
                
                // Obter detalhes da proposta e da solicitação
                $.ajax({
                    url: 'get_detalhes_proposta.php',
                    type: 'POST',
                    data: {
                        proposta_id: propostaId,
                        solicitacao_id: solicitacaoId
                    },
                    dataType: 'json',
                    success: function(response) {
                        $('#loading-detalhes').hide();
                        
                        if (response.success) {
                            const dados = response.data;
                            let html = `
                            <div class="row">
                                <div class="col-md-6">
                                    <h5>Informações do Reparo</h5>
                                    <div class="detalhe-proposta">
                                        <div class="detalhe-item">
                                            <div class="detalhe-label">ID da Proposta:</div>
                                            <div class="detalhe-valor">#${dados.proposta_id}</div>
                                        </div>
                                        <div class="detalhe-item">
                                            <div class="detalhe-label">Preço:</div>
                                            <div class="detalhe-valor">R$ ${dados.preco}</div>
                                        </div>
                                        <div class="detalhe-item">
                                            <div class="detalhe-label">Prazo:</div>
                                            <div class="detalhe-valor">${dados.prazo} dias</div>
                                        </div>
                                        <div class="detalhe-item">
                                            <div class="detalhe-label">Status:</div>
                                            <div class="detalhe-valor">
                                                <span class="status-badge ${dados.status_class}">${dados.status_text}</span>
                                            </div>
                                        </div>
                                        <div class="detalhe-item">
                                            <div class="detalhe-label">Data de Início:</div>
                                            <div class="detalhe-valor">${dados.data_proposta}</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <h5>Informações do Cliente e Dispositivo</h5>
                                    <div class="detalhe-proposta">
                                        <div class="detalhe-item">
                                            <div class="detalhe-label">Cliente:</div>
                                            <div class="detalhe-valor">${dados.nome_cliente}</div>
                                        </div>
                                        <div class="detalhe-item">
                                            <div class="detalhe-label">Dispositivo:</div>
                                            <div class="detalhe-valor">${dados.dispositivo}</div>
                                        </div>
                                        <div class="detalhe-item">
                                            <div class="detalhe-label">Marca/Modelo:</div>
                                            <div class="detalhe-valor">${dados.marca} ${dados.modelo}</div>
                                        </div>
                                        <div class="detalhe-item">
                                            <div class="detalhe-label">Método Entrega:</div>
                                            <div class="detalhe-valor">${dados.metodo_entrega}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row mt-3">
                                <div class="col-12">
                                    <h5>Observações do Reparo</h5>
                                    <div class="detalhe-proposta">
                                        <p>${dados.observacoes}</p>
                                    </div>
                                </div>
                            </div>
                            <div class="row mt-3">
                                <div class="col-12">
                                    <h5>Problema Relatado</h5>
                                    <div class="detalhe-proposta">
                                        <p>${dados.descricao_problema}</p>
                                    </div>
                                </div>
                            </div>`;
                            
                            $('#detalhes-conteudo').html(html).fadeIn();
                        } else {
                            $('#detalhes-conteudo').html(`
                                <div class="alert alert-danger">
                                    <i class="fas fa-exclamation-circle me-2"></i> 
                                    ${response.message || 'Erro ao carregar detalhes do reparo.'}
                                </div>
                            `).fadeIn();
                        }
                    },
                    error: function() {
                        $('#loading-detalhes').hide();
                        $('#detalhes-conteudo').html(`
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-circle me-2"></i> 
                                Ocorreu um erro ao buscar os detalhes do reparo. Tente novamente mais tarde.
                            </div>
                        `).fadeIn();
                    }
                });
            });
        });
    </script>
</body>
</html>