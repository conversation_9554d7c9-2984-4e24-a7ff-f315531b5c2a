<?php
/**
 * Preview do Novo Design Profissional
 * FixFácil Assistências - Sistema Novo
 */

// Simular dados para preview
session_start();
$_SESSION['usuario_id'] = 1;
$_SESSION['tipo_usuario'] = 'assistencia';

// Mock das classes para preview
class MockAuth {
    public function getUsuarioLogado() {
        return [
            'id' => 1,
            'nome' => '<PERSON>',
            'email' => '<EMAIL>',
            'nome_empresa' => 'TechFix Assistência',
            'assistencia_id' => 1
        ];
    }
    
    public function getPlanoInfo($id) {
        return [
            'nome' => 'Premium',
            'acesso_chat' => 1,
            'acesso_marketplace' => 1
        ];
    }
    
    public function hasAccess($feature) {
        return true;
    }
}

function getAuth() {
    return new MockAuth();
}

class MockLayout {
    private $auth;
    private $usuario;
    private $plano;
    
    public function __construct() {
        $this->auth = getAuth();
        $this->usuario = $this->auth->getUsuarioLogado();
        $this->plano = $this->auth->getPlanoInfo($this->usuario['id']);
    }
    
    public function renderHead($title = "FixFácil Assistências", $extraCSS = "") {
        ?>
        <!DOCTYPE html>
        <html lang="pt-BR">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title><?php echo htmlspecialchars($title); ?></title>
            
            <!-- CSS Framework -->
            <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
            <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
            
            <!-- CSS Personalizado -->
            <style>
                :root {
                    --primary: #2563eb;
                    --primary-dark: #1d4ed8;
                    --secondary: #64748b;
                    --success: #059669;
                    --warning: #d97706;
                    --danger: #dc2626;
                    --info: #0284c7;
                    --light: #f8fafc;
                    --dark: #0f172a;
                    --gray-50: #f9fafb;
                    --gray-100: #f3f4f6;
                    --gray-200: #e5e7eb;
                    --gray-300: #d1d5db;
                    --gray-600: #4b5563;
                    --gray-700: #374151;
                    --gray-800: #1f2937;
                    --gray-900: #111827;
                    --sidebar-width: 260px;
                }
                
                body {
                    background: var(--gray-50);
                    min-height: 100vh;
                    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                    color: var(--gray-900);
                }
                
                .main-wrapper {
                    display: flex;
                    min-height: 100vh;
                }
                
                .sidebar {
                    width: var(--sidebar-width);
                    background: white;
                    border-right: 1px solid var(--gray-200);
                    position: fixed;
                    height: 100vh;
                    overflow-y: auto;
                    z-index: 1000;
                    box-shadow: 0 0 15px rgba(0, 0, 0, 0.05);
                }
                
                .sidebar-header {
                    padding: 1.5rem;
                    border-bottom: 1px solid var(--gray-200);
                    background: white;
                    color: var(--gray-900);
                }
                
                .sidebar-brand {
                    font-size: 1.25rem;
                    font-weight: 600;
                    margin: 0;
                    color: var(--primary);
                }
                
                .sidebar-user {
                    margin-top: 0.75rem;
                    font-size: 0.875rem;
                    color: var(--gray-600);
                }
                
                .plano-badge {
                    display: inline-flex;
                    align-items: center;
                    padding: 0.25rem 0.75rem;
                    border-radius: 0.375rem;
                    font-weight: 500;
                    font-size: 0.75rem;
                    margin-top: 0.5rem;
                    text-transform: uppercase;
                    letter-spacing: 0.025em;
                    background: #dbeafe;
                    color: var(--primary);
                    border: 1px solid #93c5fd;
                }
                
                .nav-link {
                    display: flex;
                    align-items: center;
                    padding: 0.75rem 1rem;
                    color: var(--gray-700);
                    text-decoration: none;
                    border-radius: 0.375rem;
                    font-weight: 500;
                    font-size: 0.875rem;
                    margin: 0.125rem 1rem;
                }
                
                .nav-link:hover {
                    background: var(--gray-100);
                    color: var(--primary);
                }
                
                .nav-link.active {
                    background: var(--primary);
                    color: white;
                    font-weight: 600;
                }
                
                .nav-link i {
                    width: 18px;
                    margin-right: 0.75rem;
                    font-size: 1rem;
                }
                
                .main-content {
                    flex: 1;
                    margin-left: var(--sidebar-width);
                    padding: 1.5rem;
                    background: var(--gray-50);
                }
                
                .content-header {
                    background: white;
                    border-radius: 0.5rem;
                    padding: 1.5rem;
                    margin-bottom: 1.5rem;
                    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
                    border: 1px solid var(--gray-200);
                }
                
                .page-title {
                    font-size: 1.5rem;
                    font-weight: 600;
                    color: var(--gray-900);
                    margin: 0;
                    line-height: 1.25;
                }
                
                .page-subtitle {
                    color: var(--gray-600);
                    margin: 0.5rem 0 0 0;
                    font-size: 0.875rem;
                }
                
                .card {
                    background: white;
                    border: 1px solid var(--gray-200);
                    border-radius: 0.5rem;
                    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
                }
                
                .card-header {
                    background: var(--gray-50);
                    border-bottom: 1px solid var(--gray-200);
                    padding: 1rem 1.5rem;
                    font-weight: 600;
                    color: var(--gray-900);
                }
                
                .btn-primary {
                    background: var(--primary);
                    border: 1px solid var(--primary);
                    border-radius: 0.375rem;
                    padding: 0.5rem 1rem;
                    font-weight: 500;
                    font-size: 0.875rem;
                }
            </style>
        </head>
        <body>
        <?php
    }
}

$layout = new MockLayout();
?>

<?php $layout->renderHead("Preview - Novo Design FixFácil"); ?>

<div class="main-wrapper">
    <!-- Sidebar -->
    <nav class="sidebar">
        <div class="sidebar-header">
            <h1 class="sidebar-brand">
                <i class="fas fa-wrench me-2"></i>
                FixFácil
            </h1>
            <div class="sidebar-user">
                <div style="font-weight: 600; color: var(--gray-900);">
                    João Silva
                </div>
                <div style="font-size: 0.75rem; color: var(--gray-500); margin-top: 0.25rem;">
                    TechFix Assistência
                </div>
                <div class="plano-badge">
                    <i class="fas fa-star me-1"></i>
                    Premium
                </div>
            </div>
        </div>
        
        <div class="sidebar-nav py-3">
            <a href="#" class="nav-link active">
                <i class="fas fa-home"></i>
                Dashboard
            </a>
            <a href="#" class="nav-link">
                <i class="fas fa-inbox"></i>
                Solicitações
                <span class="badge bg-danger ms-auto">3</span>
            </a>
            <a href="#" class="nav-link">
                <i class="fas fa-paper-plane"></i>
                Propostas
            </a>
            <a href="#" class="nav-link">
                <i class="fas fa-tools"></i>
                Reparos
            </a>
            <a href="#" class="nav-link">
                <i class="fas fa-wallet"></i>
                Carteira
            </a>
            <a href="#" class="nav-link">
                <i class="fas fa-store"></i>
                Marketplace
            </a>
            <a href="#" class="nav-link">
                <i class="fas fa-comments"></i>
                Chat
            </a>
            <a href="#" class="nav-link">
                <i class="fas fa-user"></i>
                Perfil
            </a>
        </div>
    </nav>
    
    <!-- Main Content -->
    <main class="main-content">
        <!-- Header -->
        <div class="content-header">
            <h1 class="page-title">
                <i class="fas fa-eye me-3"></i>
                Preview do Novo Design
            </h1>
            <p class="page-subtitle">
                Design profissional e empresarial para o painel da FixFácil
            </p>
        </div>
        
        <!-- Content -->
        <div class="row g-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Características do Novo Design</h5>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled">
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                <strong>Cores profissionais:</strong> Azul corporativo e tons de cinza
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                <strong>Tipografia limpa:</strong> Inter font para melhor legibilidade
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                <strong>Espaçamentos consistentes:</strong> Grid system profissional
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                <strong>Ícones minimalistas:</strong> FontAwesome com estilo business
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                <strong>Sombras sutis:</strong> Depth sem exageros
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Melhorias Implementadas</h5>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled">
                            <li class="mb-2">
                                <i class="fas fa-arrow-right text-primary me-2"></i>
                                Removidos gradientes fantasiosos
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-arrow-right text-primary me-2"></i>
                                Sidebar com fundo branco limpo
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-arrow-right text-primary me-2"></i>
                                Badges de plano mais discretos
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-arrow-right text-primary me-2"></i>
                                Botões com estilo corporativo
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-arrow-right text-primary me-2"></i>
                                Layout responsivo mantido
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row g-4 mt-4">
            <div class="col-12">
                <div class="alert alert-info">
                    <h6 class="alert-heading">
                        <i class="fas fa-info-circle me-2"></i>
                        Sobre o Novo Design
                    </h6>
                    <p class="mb-0">
                        Este novo design foi criado para transmitir profissionalismo e confiança, 
                        características essenciais para uma startup no mercado de assistências técnicas. 
                        O layout clean e as cores corporativas ajudam a estabelecer credibilidade com 
                        clientes e parceiros de negócio.
                    </p>
                </div>
            </div>
        </div>
        
        <div class="row g-4 mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Links para Testar</h5>
                    </div>
                    <div class="card-body">
                        <div class="btn-group" role="group">
                            <a href="dashboard.php" class="btn btn-primary">
                                <i class="fas fa-home me-1"></i> Dashboard Real
                            </a>
                            <a href="solicitacoes.php" class="btn btn-outline-primary">
                                <i class="fas fa-inbox me-1"></i> Solicitações
                            </a>
                            <a href="chat.php" class="btn btn-outline-primary">
                                <i class="fas fa-comments me-1"></i> Chat
                            </a>
                            <a href="marketplace.php" class="btn btn-outline-primary">
                                <i class="fas fa-store me-1"></i> Marketplace
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

</body>
</html>
