# FixFácil Assistências - Sistema Novo

## 🚀 Sistema Completamente Reconstruído

Este é o novo sistema da FixFácil Assistências, construído do zero com base na estrutura real do banco de dados.

## 📁 Estrutura do Sistema

### Arquivos Principais
- `dashboard.php` - Dashboard principal com estatísticas
- `solicitacoes.php` - Lista de solicitações de reparo
- `detalhes_solicitacao.php` - Detalhes de uma solicitação específica
- `enviar_proposta.php` - Formulário para enviar propostas
- `index.php` / `home.php` - Redirecionam para o dashboard

### Configuração
- `config/database.php` - Configuração e classe do banco de dados
- `config/auth.php` - Sistema de autenticação e autorização

### Layout
- `includes/layout.php` - Sistema de layout responsivo com sidebar

## 🎨 Características do Design

### Layout Moderno
- **Design Glassmorphism** com efeitos de blur e transparência
- **Sidebar responsiva** que se adapta a mobile
- **Gradientes modernos** em roxo e azul
- **Cards com hover effects** e animações suaves

### Sistema de Planos
- **Badges dinâmicos** para cada plano (Free, Premium, Master)
- **Controle de acesso** baseado no plano do usuário
- **Cálculo automático** de taxas de serviço

### Responsividade
- **Mobile-first** design
- **Sidebar colapsável** em dispositivos móveis
- **Cards adaptáveis** que se reorganizam automaticamente

## 🔧 Funcionalidades

### Dashboard
- ✅ Estatísticas em tempo real
- ✅ Contadores de notificação
- ✅ Ações rápidas
- ✅ Atividades recentes
- ✅ Cálculo de receita com taxa

### Solicitações
- ✅ Lista filtrada por status
- ✅ Busca por texto
- ✅ Visualização de vídeos
- ✅ Informações completas do cliente
- ✅ Status das propostas

### Propostas
- ✅ Formulário completo
- ✅ Cálculo automático de valor líquido
- ✅ Tipos de peça
- ✅ Retirada express (plano Master)
- ✅ Validações em tempo real

## 🗄️ Banco de Dados

O sistema foi construído baseado na estrutura real do banco:

### Tabelas Principais
- `usuarios` - Dados dos usuários
- `assistencias_tecnicas` - Dados das assistências
- `assinaturas_assistencias` - Planos e assinaturas
- `planos` - Configuração dos planos
- `solicitacoes_reparo` - Solicitações dos clientes
- `propostas_assistencia` - Propostas das assistências

### Relacionamentos
- Usuário → Assistência Técnica (1:1)
- Assistência → Assinatura → Plano (1:1:1)
- Solicitação → Propostas (1:N)

## 🚀 Como Usar

1. **Acesse o sistema**: `assistencia/dashboard.php`
2. **Faça login** com suas credenciais de assistência
3. **Navegue pelo menu** lateral para acessar as funcionalidades
4. **Visualize solicitações** e envie propostas
5. **Acompanhe estatísticas** no dashboard

## 🎯 Próximas Funcionalidades

As próximas páginas a serem criadas seguirão o mesmo padrão:
- `propostas.php` - Gerenciar propostas enviadas
- `reparos.php` - Acompanhar reparos em andamento
- `carteira.php` - Visualizar ganhos e pagamentos
- `marketplace.php` - Vender produtos (Premium/Master)
- `chat.php` - Chat com clientes (Premium/Master)
- `perfil.php` - Gerenciar dados da assistência

## 💡 Tecnologias

- **PHP 7.4+** - Backend
- **MySQL/MariaDB** - Banco de dados
- **Bootstrap 5.3** - Framework CSS
- **Font Awesome 6** - Ícones
- **JavaScript ES6** - Interatividade
- **CSS3** - Animações e efeitos

## 🔒 Segurança

- ✅ Autenticação por sessão
- ✅ Verificação de tipo de usuário
- ✅ Prepared statements (SQL injection)
- ✅ Escape de dados (XSS)
- ✅ Controle de acesso por plano

## 📱 Compatibilidade

- ✅ Chrome/Edge/Firefox/Safari
- ✅ iOS Safari
- ✅ Android Chrome
- ✅ Tablets e desktops
- ✅ Telas de 320px a 4K

---

**Sistema desenvolvido com foco em performance, segurança e experiência do usuário.**
